import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/alert_screen.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/ta_admin/change_context.dart';
import 'package:tangoworkplace/providers/ta_admin/change_context_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../common/component_utils.dart';
import '../home/<USER>';
import '../../../utils/common_utils.dart';

class TaChange extends GetView<ChangeContextController> {
  static const routName = '/TaChange';
  final talabel = Get.find<LabelController>();
  final _controller = Get.put(ChangeContextController());

  TaChange({super.key});

  @override
  Widget build(BuildContext context) {
    _controller.initialSetUp();

    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text(
            'Change Preferences ',
            style: ComponentUtils.appbartitlestyle,
          ),
          backgroundColor: Colors.white,
          elevation: 5,
          leading: IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
            onPressed: () {
              Get.off(() => HomeScreen());
            },
          ),
        ),
        body: Stack(children: <Widget>[
          Positioned(
            top: 00,
            right: 0,
            left: 0,
            bottom: 0,
            child: ClipRRect(
              child: Container(
                color: Colors.white,
                padding: const EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 0.0),
                child: _buildPreferences(context),
              ),
            ),
          )
        ]));
  }

  Future<void> _onSavePreferences(BuildContext context) async {
    _controller.onSavePreferences().then((result) {
      if (result == 'success') {
        Get.delete<LabelController>();
        Get.put(LabelController());
        talabel.init.value = true;
        bool isLargeScreen = CommonUtils.isTablet(context);
        if (isLargeScreen) {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => HomeScreen()),
            (Route<dynamic> route) => false,
          );
        } else {
          Get.off(() => HomeScreen());
        }
      } else {
        showDialog(
            context: context,
            builder: (BuildContext context) =>
                getAlertDialog("Error...", 'Error', context));
      }
    });
  }

  Widget _buildPreferences(BuildContext context) {
    bool isLargeScreen = CommonUtils.isTablet(context);

    return Obx(
      () => _controller.isInitialLoading.value
          ? const Center(
              child: ProgressIndicatorCust(),
            )
          : SingleChildScrollView(
              child: Form(
                key: _controller.formKey,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      if (isLargeScreen)
                        Text(
                          'Change Preferences',
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: CommonUtils.createMaterialColor(
                                  const Color(0XFFb10c00))),
                        ),
                      if (isLargeScreen)
                        const SizedBox(
                          height: 30,
                        ),
                      Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            if (_controller.clientList.isNotEmpty)
                              _buildDropDownView(
                                  label: 'Client',
                                  hint: 'Select a Client',
                                  value: _controller.clientIdSelected.value
                                      ?.toString(),
                                  items: _controller.clientList,
                                  context: context,
                                  onChanged: (dynamic val) async {
                                    if (val != null &&
                                        _controller.clientList.isNotEmpty) {
                                      await _controller.onChangeClient(val);
                                    } else {
                                      debugPrint('changed  val $val');
                                    }
                                  }),
                            SizedBox(
                              height: _controller.isBrandEnabled.value == 'Y'
                                  ? 10.0
                                  : 0.0,
                            ),
                            Visibility(
                              visible: _controller.isBrandEnabled.value == 'Y',
                              child: _buildDropDownView(
                                  label: 'Brand',
                                  hint: 'Select a Brand',
                                  value: _controller.brandIdSelected.value
                                      ?.toString(),
                                  items: _controller.brandList,
                                  context: context,
                                  onChanged: (dynamic val) async {
                                    if (val != null &&
                                        _controller.brandList.isNotEmpty) {
                                      await _controller.onChangeBrand(val);
                                    } else {
                                      debugPrint('changed  val $val');
                                    }
                                  }),
                            ),
                            SizedBox(
                              height: _controller.isBuEnabled.value == 'Y'
                                  ? 10.0
                                  : 0.0,
                            ),
                            Visibility(
                                visible: _controller.isBuEnabled.value == 'Y',
                                child: _buildDropDownView(
                                    label: 'BU',
                                    hint: 'Select a BU',
                                    value: _controller.buIdSelected.value
                                        ?.toString(),
                                    items: _controller.buList,
                                    context: context,
                                    onChanged: (dynamic val) async {
                                      if (val != null &&
                                          _controller.buList.isNotEmpty) {
                                        await _controller.onChangeBu(val);
                                      } else {
                                        debugPrint('changed  val $val');
                                      }
                                    })),
                            const SizedBox(
                              height: 10.0,
                            ),
                            _buildDropDownView(
                                label: 'Country',
                                hint: 'Select a Country',
                                value: _controller.contryCodeSelected.value
                                    ?.toString(),
                                items: _controller.countryList,
                                context: context,
                                onChanged: (dynamic val) async {
                                  if (val != null &&
                                      _controller.countryList.isNotEmpty) {
                                    await _controller.onChangeCountry(val);
                                  } else {
                                    debugPrint('changed  val $val');
                                  }
                                }),
                            const SizedBox(
                              height: 10.0,
                            ),
                            _buildDropDownView(
                                label: 'Language',
                                hint: 'Select a Language',
                                value: _controller.languageSelected.value,
                                items: _controller.languageList,
                                context: context,
                                onChanged: (dynamic val) {
                                  if (val != null &&
                                      _controller.languageList.isNotEmpty) {
                                    _controller.onChangeLanguage(val);
                                  } else {
                                    debugPrint('changed  val $val');
                                  }
                                }),
                            const SizedBox(
                              height: 10.0,
                            ),
                          ]),
                      _controller.isLoading.value
                          ? (_controller.isInitialLoading.value
                              ? Container()
                              : const ProgressIndicatorCust())
                          : _buttonBar(context),
                    ]),
              ),
            ),
    );
  }

  Widget _buttonBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Align(
            alignment: Alignment.bottomRight,
            child: TaButton(
              type: 'elevate',
              buttonText: 'Cancel',
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => HomeScreen(),
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 8),
          Align(
            alignment: Alignment.bottomRight,
            child: TaButton(
              type: 'elevate',
              buttonText: 'Save',
              onPressed: () async {
                final isValid = _controller.validateForm(onSavedPressed: true);
                if (!isValid) {
                  return;
                }
                _controller.formKey.currentState!.save();
                await _onSavePreferences(context);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropDownView(
      {required String label,
      required String hint,
      required String? value,
      required Function(Object?) onChanged,
      required List<ContextChange>? items,
      required BuildContext context}) {
    if (items != null && items.isNotEmpty) {
      return TaFormDropdown(
        label: label,
        emptttext: hint,
        value: items.firstWhereOrNull((item) => item.value == value)?.value,
        onChanged: onChanged,
        validate: (dynamic value) {
          if (value == null) {
            return 'Please select a $label';
          }
          return null;
        },
        listflag: true,
        items: items.map((var x) {
          return DropdownMenuItem(
            value: x.value,
            child: Text(
              x.text ?? '',
              style: const TextStyle(fontSize: 12),
            ),
          );
        }).toList(),
      );
    } else {
      return TaFormDropdown(
        label: label,
        emptttext: hint,
        value: 'xxxx',
        validate: (dynamic value) {
          if (value == null || value == 'xxxx') {
            return 'Please select a $label';
          }
          return null;
        },
        listflag: true,
        items: [
          DropdownMenuItem(
            value: 'xxxx',
            child: Text(hint,
                style: const TextStyle(fontSize: 14, color: Colors.black)),
          )
        ],
      );
    }
  }
}
