import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import '../home/<USER>';

class UserTerms extends StatelessWidget {
  static const routName = '/UserTerms';
  final String? terms;
  final String? version;
  const UserTerms({super.key, this.terms, this.version});
  @override
  Widget build(BuildContext context) {
    bool isLargeScreen = CommonUtils.isTablet(context);
    // bool isload = false;
    return Scaffold(
      appBar: AppBar(
          title: Text('Welcome, ${SharedPrefUtils.readPrefStr(ConstHelper.userNamevar)}',
              style: TextStyle(fontSize: isLargeScreen ? 16 : 12, fontWeight: FontWeight.bold)),
          toolbarHeight: 50,
          elevation: 1),
      bottomNavigationBar: BottomAppBar(
        elevation: 1.0,
        shape: const CircularNotchedRectangle(),
        color: Colors.white,
        child: Container(
          margin: EdgeInsets.only(left: 12.0, right: 12.0),
          height: 45.0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              // isload
              //     ? ProgressIndicatorCust()
              //     : new Container(
              //         height: 10,
              //       ),
              Align(
                  alignment: Alignment.bottomRight,
                  child: ElevatedButton(
                      style: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                          if (states.contains(MaterialState.pressed))
                            return CommonUtils.createMaterialColor(Colors.redAccent);
                          return CommonUtils.createMaterialColor(Color(0XFFb10c00));
                          // Use the component's default.
                        },
                      )),
                      onPressed: () {
                        CommonUtils.logout(context);
                      },
                      child: Text(
                        'Reject',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ))
                  // : SizedBox(width: 10),
                  ),
              SizedBox(width: 8),
              Align(
                  alignment: Alignment.bottomRight,
                  child: ElevatedButton(
                      style: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                          if (states.contains(MaterialState.pressed))
                            return CommonUtils.createMaterialColor(Colors.redAccent);
                          return CommonUtils.createMaterialColor(Color(0XFFb10c00));
                          // Use the component's default.
                        },
                      )),
                      onPressed: () async {
                        var result = await (callaccetptuserterms(this.version) as Future<Map<dynamic, dynamic>>);
                        if (result['result'] == 'success') {
                          LabelController talabel = Get.put(LabelController());
                          talabel.init.value = true;
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) => HomeScreen(),
                            ),
                          );
                        }
                      },
                      child: Text(
                        'Accept',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ))
                  // : SizedBox(width: 10),
                  ),
              SizedBox(width: 8),
            ],
          ),
        ),
      ),
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Html(
              data: terms!,
            ),
          ),
        ),
      ),
    );
  }
}

Future<Map?> callaccetptuserterms(String? version) async {
  Map<String, dynamic>? result;
  try {
    Map<String, String> headers = SharedPrefUtils.getHeaders();

    String url = ApiService.getApiServerurl() + acceptusertermsurl;
    debugPrint('Updated url $url');
    String body = '{"userversion": $version }';
    var uri = Uri.parse(url);
    final response = await http.post(uri, body: body, headers: headers);
    if (response.statusCode == 200) {
      result = jsonDecode(response.body);
      debugPrint('callaccetptuserterms     $result');
      debugPrint('$result');
    } else {
      result = {"result": "error"};
    }
  } catch (error) {
    debugPrint('$error');
    result = {"result": "error"};
  }
  return result;
}
