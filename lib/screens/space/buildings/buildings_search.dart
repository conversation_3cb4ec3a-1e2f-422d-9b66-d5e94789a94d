import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/space/buildings/building_details.dart';

import '../../../common/component_utils.dart';
import '../../../models/spacemgmt/buildings/buildingsview_data.dart';
import '../../../providers/spacemanagement/buildings/building_controller.dart';
import '../../sitemanagement/site/search/site_filter_widget.dart';
import 'buildings_filter_widget.dart';

class BuildingsSearch extends StatelessWidget {
  String? source;
  BuildingsSearch({super.key, this.source});
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  BuildingsSearchController buildingsSearchState = Get.put(BuildingsSearchController());

  BuildingsSearchController buildingCtrlr = Get.find<BuildingsSearchController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshBuildings() async {
    await buildingCtrlr.getBuildingSearchData(searchText: buildingCtrlr.searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_BUILDINGS')!.value!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        actions: [
          IconButton(
              onPressed: () async {
                Get.to(() => BuildingsFilterWidget());
                buildingCtrlr.filterwidgetloading.value = true;
                await talabel.getlabels('TMCMOBILE_BUILDINGSSEARCH', 'Building_search', filtertype: 'tab');
                Future.delayed(const Duration(seconds: 1), () async {
                  buildingCtrlr.filterwidgetloading.value = false;
                });
              },
              icon: Icon(
                Icons.search,
                color: ComponentUtils.primecolor,
              )),
          // Obx(
          //   () => talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION') != null
          //       ? IconButton(
          //           onPressed: () {
          //             navigationBottomSheet(context);
          //           },
          //           icon: Icon(
          //             Icons.apps,
          //             color: ComponentUtils.primecolor,
          //           ))
          //       : Container(),
          // ),
        ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            // Get.off(() => HomeScreen());
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          filterChipContainer(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshBuildings,
              child: GetX<BuildingsSearchController>(
                initState: (state) async {
                  buildingCtrlr.isLoading(true);
                  await talabel.getlabels('TMCMOBILE_BUILDINGSSEARCH', 'Building_search', filtertype: 'tab');
                  buildingCtrlr.getBuildingSearchData(action: 'onload');
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.buildinglist.isEmpty
                          ? Center(
                              child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.buildinglist[index]);
                              },
                              itemCount: _.buildinglist.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterChipContainer() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.only(left: 12, right: 10, top: 5, bottom: 2),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Wrap(
                alignment: WrapAlignment.start,
                spacing: 6.0,
                runSpacing: 6.0,
                children: List<Widget>.generate(buildingCtrlr.filterList.length, (int index) {
                  return buildingCtrlr.filterList[index];
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, BuildingViewData b) {
    const primary = Color(0xff696b9e);
    const secondary = Color(0xfff29a94);

    return GestureDetector(
      onTap: () async {
        // Get.to(BuildingDetails(
        //   storeId: b.storeId,
        //   storeName: b.storeName,
        //   subEntityType: b.locSubEntityType,
        // ));
        // String mode =
        //     await buildingCtrlr.getUserEntityEditAccess(entityid: s.siteId.toString(), entitytype: 'SITE', roFlag: s.readOnlyFlag);

        // if (mode == 'error') {
        //   Get.to(
        //     () => EntityErrorPg(
        //       entitytype: 'Site',
        //       entityid: s.siteId,
        //       entityname: s.siteName,
        //     ),
        //   );
        // } else {
        //   Get.to(() => SiteDetails(
        //         site: s,
        //         nav_from: 'search',
        //         siteid: s.siteId,
        //         sitename: s.siteName,
        //         tabroles: buildingCtrlr.tabroles.value,
        //         parent_mode: mode,
        //       ));
        // }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    b.storeName ?? '',
                    style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  const SizedBox(
                    height: 6,
                  ),
                  if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_BUILDINGNUM') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_BUILDINGNUM')?.value ?? 'Building ID:',
                        b.storeNumber?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_PROPERTY') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_PROPERTY')?.value ?? 'Property Name:',
                        b.propertyName?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_CADRENTABLEAREA') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_CADRENTABLEAREA')?.value ?? 'Rentable Area:',
                        b.cadRentableArea?.toString() ?? '0.00',
                        dtype: 'num',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_FLOORCOUNT') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_FLOORCOUNT')?.value ?? 'Floor Count:',
                        b.floorcount?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_TOTALCAPACITY') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_TOTALCAPACITY')?.value ?? 'Total Cappacity:',
                        b.totalCapacity?.toString() ?? '0.00',
                        dtype: 'num',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_TOTALHEADCOUNT') != null)
                    ComponentUtils.listVertRow(
                        talabel.get('TMCMOBILE_BUILDINGSSEARCH_LIST_TOTALHEADCOUNT')?.value ?? 'Total Head Count:',
                        b.totalHeadCount?.toString() ?? '',
                        labelStyle: TextStyle(
                          color: primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        )),
                  const SizedBox(
                    height: 6,
                  ),
                  Row(
                    children: <Widget>[
                      Icon(
                        Icons.location_on,
                        color: secondary,
                        size: 15,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Expanded(
                        child: Text((b.address ?? '') + ' ' + (b.city ?? '') + ' ' + (b.state ?? ' '),
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: primary,
                              fontSize: 12,
                              //letterSpacing: .1,
                            )),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
