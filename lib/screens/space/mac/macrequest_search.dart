import 'package:flutter/material.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/utils.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/location/mac/macrequestsearch.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectsearch_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/projectmanagement/project/project_details_screen.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:geolocator/geolocator.dart';

import '../../../common/component_utils.dart';
import '../../../providers/location/mac/macrequest_controller.dart';

class MacRequestSearch extends StatelessWidget {
  MacRequestSearch({super.key});

  MacRequestController macreqState = Get.put(MacRequestController());
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  MacRequestController macreqCtrl = Get.find<MacRequestController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshMacRequests() async {
    await macreqCtrl.loadMacRequestSearchData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Mac Request', //Text(talabel.get('TMCMOBILE_HOME_MAC').value,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          // TaSearchInputText(
          //     makeSearch: (searchtext) {
          //       projectSearchCtrlr.fetchProjects(searchText: searchtext.toString());
          //     },
          //     searchController: _searchController,
          //     hintSearch: 'Search '),

          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshMacRequests,
              child: GetX<MacRequestController>(
                initState: (state) async {
                  await macreqCtrl.loadMacRequestSearchData();
                },
                builder: (_) {
                  return _.ismacsearchlistloading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.macrequestlist.isEmpty
                          ? Center(
                              child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.macrequestlist[index]);
                              },
                              itemCount: _.macrequestlist.length,
                            );
                },
              ),
            ),
          ),

          // Expanded(
          //   child: GetBuilder<ProjectSearchController>(builder: (ctrlr) {
          //     return ctrlr.isLoading.value
          //         ? ProgressIndicatorCust()
          //         : (ctrlr.projects != null || ctrlr.projects.isNotEmpty)
          //             ? ListView.builder(
          //                 itemCount: ctrlr.projects.length,
          //                 itemBuilder: (context, index) {
          //                   return listitem(context, ctrlr.projects[index]);
          //                 })
          //             : Center(
          //                 child: Text('No Projects Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
          //               );
          //   }),
          // ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, MacRequestSearchRec m) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          // Get.to(
          //     () => ProjectDetails(
          //           proj: p,
          //         ),
          //     arguments: [
          //       {"projectId": p.projectId},
          //       {"porojectview": p}
          //     ]);
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${m.requestType}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.shield,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(m.requestNumber!, style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        // Row(
                        //   children: <Widget>[
                        //     Icon(
                        //       Icons.bolt,
                        //       color: secondary,
                        //       size: 15,
                        //     ),
                        //     SizedBox(
                        //       width: 5,
                        //     ),
                        Text(m.status ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        //   ],
                        // ),
                      ],
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.assistant_photo,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(m.requestedStartDate ?? '',
                              style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        Text(m.requestedEndDate ?? '',
                            style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                      ],
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      children: <Widget>[
                        Icon(
                          Icons.location_on,
                          color: secondary,
                          size: 15,
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Expanded(
                          child: Text(m.assginedTo!,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: primary,
                                fontSize: 12,
                                letterSpacing: .1,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
