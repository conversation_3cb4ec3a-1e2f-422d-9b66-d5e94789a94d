import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/common/widgets/alert_screen.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/connections.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/web_view_screen.dart';
import '../../home/<USER>';
import '../../../screens/space/reservations/reservations_detail_screen.dart';
import '../../../models/reservations.dart';
import '../../../providers/reservation_provider.dart';
import '../../../utils/constvariables.dart';
import '../../../utils/preferences_utils.dart';
import 'package:auto_size_text/auto_size_text.dart';

import 'package:intl/intl.dart';
import '../../../utils/common_utils.dart';

class ReservationsScreen extends StatefulWidget {
  static const routName = '/reservations';

  const ReservationsScreen({super.key});

  @override
  _ReservationsScreenState createState() => _ReservationsScreenState();
}

class _ReservationsScreenState extends State<ReservationsScreen> {
  int selectedIndex = 0; //to handle which item is currently selected in the bottom app bar
  var _isLoading = false;
  var _isInit = true;
  var _isCompletedLoading = false;
  var _hasInitialized = false;

  TextEditingController comments = TextEditingController();
  LabelController talabel = Get.find<LabelController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Future<void> didChangeDependencies() async {
    if (_isInit) {
      setState(() {
        _isLoading = true;
      });
      Provider.of<ReservationProvider>(context, listen: false).getReservationsData(context).then((_) {
        setState(() {
          _isLoading = false;
        });
      });
      await talabel.getlabels('TMCMOBILE_RESERVATION', 'reservation', ruletype: 'reservation');
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    comments.dispose();
    super.dispose();
  }

  _cadviewer(String buildingid, String floorid, String spaceid, String startdate, String enddate) {
    CommonUtils.reservationCadViewer(context, buildingid, floorid, spaceid, startdate, enddate);
  }

  @override
  Widget build(BuildContext context) {
    bool isLargeScreen = CommonUtils.isTablet(context);
    final reservProvider = Provider.of<ReservationProvider>(context, listen: true);
    return DefaultTabController(
        length: 2,
        child: Scaffold(
            floatingActionButton: const NewReservationButton(),
            floatingActionButtonLocation: FloatingActionButtonLocation.miniCenterDocked,
            backgroundColor: Colors.grey,
            bottomNavigationBar: BottomAppBar(
              shape: const CircularNotchedRectangle(),
              //color of the BottomAppBar
              color: Colors.white,
              child: Container(
                margin: EdgeInsets.only(left: 12.0, right: 12.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    IconButton(
                      //update the bottom app bar view each time an item is clicked
                      onPressed: () => Navigator.pushNamed(context, HomeScreen.routName),
                      iconSize: 27.0,
                      icon: Icon(
                        Icons.home,
                        //darken the icon if it is selected or else give it a different color
                        color: selectedIndex == 0
                            ? CommonUtils.createMaterialColor(Color(0XFFb10c00))
                            : CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                      ),
                    ),
                    //to leave space in between the bottom app bar items and below the FAB
                    SizedBox(
                      width: 50.0,
                    ),
                    IconButton(
                      onPressed: () {},
                      iconSize: 27.0,
                      icon: Icon(Icons.search, color: Colors.white
                          // selectedIndex == 2
                          //     ? CommonUtils.createMaterialColor(Color(0XFFb10c00))
                          //     : CommonUtils.createMaterialColor(Color(0XFFb10c00)),
                          ),
                    ),
                  ],
                ),
              ),
            ),
            appBar: AppBar(
              bottom: TabBar(
                onTap: (index) {
                  if (index == 1) {
                    print('Get completed Reservations$_hasInitialized');
                    if (!_hasInitialized) {
                      setState(() {
                        _isCompletedLoading = false;
                      });
                      Provider.of<ReservationProvider>(context, listen: false)
                          .getCompletedReservationsImpl(context)
                          .then((_) {
                        setState(() {
                          _isCompletedLoading = true;
                          print('Get completed Reservations$_isCompletedLoading');
                        });
                      });
                      _hasInitialized = true;
                    }
                  }
                },
                indicatorColor: Colors.transparent,
                tabs: const [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.timer),
                        SizedBox(width: 5.0),
                        Text('Upcoming'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.check_box_outlined),
                        SizedBox(width: 5.0),
                        Text('Completed'),
                      ],
                    ),
                  ),

                  // Tab(icon: Icon(Icons.check_box_outlined), text: 'Completed'),
                ],
              ),
              // title: Text('My Reservations'),
              elevation: 0,
              backgroundColor: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
              //brightness: Brightness.dark,
              toolbarHeight: 1,
              automaticallyImplyLeading: false,
            ),
            body: TabBarView(children: [
              Stack(children: <Widget>[
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
                        color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                        shape: BoxShape.rectangle),
                    height: 125,
                  ),
                ),
                Positioned(
                    top: 00,
                    right: 15,
                    left: 15,
                    bottom: 10,
                    child: ClipRRect(
                        borderRadius: BorderRadius.circular(9),
                        child: Container(
                            padding: const EdgeInsets.all(0),
                            color: Colors.white,
                            child: Consumer<ReservationProvider>(
                                builder: (BuildContext context, resvProvider, Widget? child) {
                              return Builder(builder: (context) {
                                if (!_isLoading) {
                                  print('Inside reservation search build');
                                  return MediaQuery.removePadding(
                                    removeTop: true,
                                    removeBottom: true,
                                    context: context,
                                    child: (reservProvider.post != null && resvProvider.post!.isNotEmpty)
                                        ? ListView.separated(
                                            padding: EdgeInsets.zero,
                                            itemCount: reservProvider.post != null ? reservProvider.post!.length : 0,
                                            itemBuilder: (context, index) {
                                              Reservations reser = reservProvider.post![index];
                                              bool showCheckinbutton =
                                                  isCurrrentDate(reser.startDate!, reser.endDate!, reser.status);
                                              return ListTile(
                                                  contentPadding:
                                                      const EdgeInsets.only(top: 0.0, bottom: 0.0, left: 8, right: 8),
                                                  key: UniqueKey(),
                                                  onTap: () {
                                                    Navigator.of(context).pushNamed(ReservationAdd.routName,
                                                        arguments: {'id': reser.reservationId, 'mode': 'edit'});
                                                  },
                                                  focusColor: Colors.blueGrey,
                                                  title: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: <Widget>[
                                                      Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                                                        Text('RES 00${reser.reservationId} ${reser.reservationTitle}',
                                                            style: TextStyle(
                                                              fontSize: getFontSize(1.5),
                                                              fontWeight: FontWeight.bold,
                                                            )),
                                                        if (!showCheckinbutton && reser.status == 'CHECKEDIN')
                                                          Expanded(
                                                            child: Text('${reser.status}',
                                                                textAlign: TextAlign.end,
                                                                style: TextStyle(
                                                                    fontSize: 14,
                                                                    fontWeight: FontWeight.bold,
                                                                    color: CommonUtils.createMaterialColor(
                                                                        const Color(0XFF006400)))),
                                                          ),
                                                        if (showCheckinbutton)
                                                          Expanded(
                                                            child: Container(
                                                              alignment: Alignment.topRight,
                                                              padding: EdgeInsets.zero,
                                                              height: 30,
                                                              width: 120,
                                                              child: ElevatedButton(
                                                                style: ElevatedButton.styleFrom(
                                                                  backgroundColor: CommonUtils.createMaterialColor(
                                                                      const Color(0XFFb10c00)),
                                                                  //primary: CommonUtils.createMaterialColor(Color(0XFF006400))
                                                                ),
                                                                //   icon: Icon(Icons
                                                                //     .check_sharp),
                                                                child: Text(
                                                                  'Check In',
                                                                  style: TextStyle(fontSize: getFontSize(1.3)),
                                                                ),
                                                                onPressed: () {
                                                                  updateStatus(
                                                                      context, reser.reservationId, 'CHECK-IN');
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                      ]),

                                                      // if (!showCheckinbutton)
                                                      const SizedBox(height: 10),
                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        children: [
                                                          Icon(
                                                            Icons.location_on_rounded,
                                                            size: 35,
                                                            color: Colors.grey[400],
                                                          ),
                                                          Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text('${reser.buildingName} - ${reser.floorName}',
                                                                  style: TextStyle(
                                                                    fontSize: getFontSize(1.5),
                                                                  )),
                                                              const SizedBox(
                                                                height: 5,
                                                              ),
                                                              Text('${reser.spaceDisplayName}',
                                                                  style: TextStyle(
                                                                    fontSize: getFontSize(1.5),
                                                                  )),
                                                            ],
                                                          ),
                                                          const SizedBox(
                                                            height: 10.0,
                                                          ),
                                                          Expanded(
                                                            child: GestureDetector(
                                                              onTap: () {
                                                                _cadviewer(
                                                                    '${reser.buildingId}',
                                                                    '${reser.floorId}',
                                                                    '${reser.spaceId}',
                                                                    '${reser.startDate}',
                                                                    '${reser.endDate}');
                                                              },
                                                              child: Align(
                                                                  alignment: Alignment.centerRight,
                                                                  child: Padding(
                                                                    padding: const EdgeInsets.all(5.0),
                                                                    child: isLargeScreen
                                                                        ? const Image(
                                                                            image:
                                                                                AssetImage('lib/icons/plan-icon.png'),
                                                                            width: 45,
                                                                            height: 45,
                                                                            fit: BoxFit.fitHeight)
                                                                        : const Image(
                                                                            image:
                                                                                AssetImage('lib/icons/plan-icon.png')),
                                                                  )),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                        height: 10.0,
                                                      ),
                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        children: [
                                                          Icon(
                                                            Icons.timer_sharp,
                                                            size: 35,
                                                            color: Colors.grey[400],
                                                          ),
                                                          const SizedBox(width: 5),
                                                          Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text(
                                                                  DateFormat('MMM dd, yyyy hh:mm a').format(
                                                                      DateFormat('MM/dd/yyyy hh:mm a')
                                                                          .parse(reser.startDate!)),
                                                                  style: TextStyle(
                                                                    fontSize: getFontSize(1.5),
                                                                  )),
                                                              const SizedBox(
                                                                height: 5,
                                                              ),
                                                              Text(
                                                                  DateFormat('MMM dd, yyyy hh:mm a').format(
                                                                      DateFormat('MM/dd/yyyy hh:mm a')
                                                                          .parse(reser.endDate!)),
                                                                  style: TextStyle(
                                                                    fontSize: getFontSize(1.5),
                                                                  )),
                                                            ],
                                                          ),
                                                          const SizedBox(
                                                            height: 10.0,
                                                          ),
                                                          Expanded(
                                                            child: GestureDetector(
                                                              onTap: () {
                                                                showDialog(
                                                                    context: context,
                                                                    builder: (ctx) => AlertDialog(
                                                                          title: const Text(""),
                                                                          content: Text(
                                                                              'Do you really want to cancel the reservation:  ${reser.reservationTitle} ?'),
                                                                          actions: <Widget>[
                                                                            TextButton(
                                                                              child: const Text('Yes'),
                                                                              onPressed: () {
                                                                                updateStatus(context,
                                                                                    reser.reservationId, 'CANCELED');
                                                                                Navigator.of(context).pop();
                                                                              },
                                                                            ),
                                                                            TextButton(
                                                                              child: const Text('No'),
                                                                              onPressed: () {
                                                                                Navigator.of(context).pop();
                                                                              },
                                                                            ),
                                                                          ],
                                                                        ));
                                                              },
                                                              child: Align(
                                                                  alignment: Alignment.centerRight,
                                                                  child: Padding(
                                                                    padding: const EdgeInsets.all(0.0),
                                                                    child: isLargeScreen
                                                                        ? const Image(
                                                                            image: AssetImage('lib/icons/cancel.png'),
                                                                            width: 45,
                                                                            height: 45,
                                                                            fit: BoxFit.fitHeight)
                                                                        : const Image(
                                                                            image: AssetImage('lib/icons/cancel.png')),
                                                                  )),
                                                            ),

                                                            /* child: IconButton(
                                                          alignment: Alignment
                                                              .topRight,
                                                        padding:
                                                            EdgeInsets.zero,
                                                       
                                                           icon: isLargeScreen ? Image(image: AssetImage('lib/icons/cancel.png'), width: 45, height: 45, fit: BoxFit.fitHeight) : Image(image: AssetImage('lib/icons/cancel.png')),
                                                         
                                                        color: CommonUtils
                                                            .createMaterialColor(
                                                                Color(
                                                                    0XFF394251)),
                                                        onPressed: () {
                                                          return showDialog(
                                                                context: context,
                                                                builder: (ctx) => AlertDialog(
                                                                      title: Text(""),
                                                                      content: Text('Do you really want to cancel the reservation:  ${reser.reservationTitle} ?'),
                                                                      actions: <Widget>[
                                                                        TextButton(
                                                                          child: Text('Yes'),
                                                                          onPressed: () {
                                                                            updateStatus(context, reser.reservationId, 'CANCELLED');
                                                                            Navigator.of(context).pop();
                                                                          },
                                                                        ),
                                                                        TextButton(
                                                                          child: Text('No'),
                                                                          onPressed: () {
                                                                            Navigator.of(context).pop();
                                                                          },
                                                                        ),
                                                                      ],
                                                                    ));
                                                          }),*/
                                                          ),
                                                        ],
                                                      ),

                                                      const SizedBox(
                                                        height: 10,
                                                      ),

                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        children: [
                                                          Icon(
                                                            Icons.person,
                                                            size: 35,
                                                            color: Colors.grey[400],
                                                          ),
                                                          const SizedBox(
                                                            width: 10,
                                                          ),
                                                          Text('${reser.assignees}' ' ...',
                                                              style: TextStyle(
                                                                fontSize: getFontSize(1.5),
                                                              )),
                                                        ],
                                                      ),

                                                      /*    Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    ElevatedButton(
                                                      onPressed: () {
                                                        updateStatus(
                                                            context,
                                                            reser.reservationId,
                                                            'CHECKEDIN');
                                                      },
                                                      child: Text(
                                                          '  Ready to Check-in  '),
                                                      style: TextButton.styleFrom(
                                                          padding:
                                                              EdgeInsets.all(0),
                                                          textStyle: TextStyle(
                                                            fontSize: 14,
                                                          ),
                                                          primary: Colors.black,
                                                          backgroundColor:
                                                              Colors.green[200]),
                                                    ),*/
                                                      /*  Text('Status      ',
                                                        style: TextStyle(
                                                            fontSize:
                                                                14.0)),
                                                    Text(
                                                        '${reser.status}',
                                                        style:
                                                            TextStyle(
                                                          fontSize:
                                                              14.0,
                                                        )),*/
                                                      //],
                                                      //),
                                                    ],
                                                  ));
                                            },
                                            separatorBuilder: (BuildContext context, int index) => Divider(
                                                indent: 5,
                                                endIndent: 5,
                                                thickness: 2,
                                                color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0))),
                                          )
                                        : Center(
                                            child: Text('No Reservation Data',
                                                style: TextStyle(fontSize: getFontSize(1.5))),
                                          ),
                                  );
                                } else {
                                  return const ProgressIndicatorCust();
                                }
                              });
                              // By default, show a loading spinner.
                            })))),
              ]),
              Stack(children: <Widget>[
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
                        color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                        shape: BoxShape.rectangle),
                    height: 125,
                  ),
                ),
                Positioned(
                    top: 00,
                    right: 15,
                    left: 15,
                    bottom: 10,
                    child: ClipRRect(
                        borderRadius: BorderRadius.circular(9),
                        child: Container(
                            color: Colors.white,
                            child: Consumer<ReservationProvider>(
                              builder: (BuildContext context, resvProvider, Widget? child) {
                                return Builder(builder: (context) {
                                  if (_isCompletedLoading) {
                                    print('Inside reservation completed build');
                                    return MediaQuery.removePadding(
                                      removeTop: true,
                                      removeBottom: true,
                                      context: context,
                                      child: (reservProvider.completedReservations != null &&
                                              resvProvider.completedReservations!.isNotEmpty)
                                          ? ListView.separated(
                                              padding: EdgeInsets.zero,
                                              itemCount: reservProvider.completedReservations == null
                                                  ? 0
                                                  : reservProvider.completedReservations!.length,
                                              itemBuilder: (context, index) {
                                                Reservations reser = reservProvider.completedReservations![index];
                                                return ListTile(
                                                    contentPadding:
                                                        const EdgeInsets.only(top: 0.0, bottom: 0.0, left: 8, right: 8),
                                                    key: UniqueKey(),
                                                    focusColor: Colors.blueGrey,
                                                    title: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: <Widget>[
                                                        Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                                                          Align(
                                                            alignment: Alignment.topRight,
                                                            child: Text(
                                                                'RES 00${reser.reservationId} ${reser.reservationTitle}',
                                                                style: TextStyle(
                                                                  fontSize: getFontSize(1.5),
                                                                  fontWeight: FontWeight.bold,
                                                                )),
                                                          ),
                                                          Expanded(
                                                            child: Container(
                                                              alignment: Alignment.topRight,
                                                              padding: EdgeInsets.zero,
                                                              height: 30,
                                                              width: 120,
                                                              child: ElevatedButton(
                                                                style: ElevatedButton.styleFrom(
                                                                    backgroundColor: Colors.grey[800]),
                                                                //   icon: Icon(Icons
                                                                //      .refresh),
                                                                onPressed: () {
                                                                  Navigator.of(context)
                                                                      .pushNamed(ReservationAdd.routName, arguments: {
                                                                    'id': reser.reservationId,
                                                                    'type': reser.reservationType!.toLowerCase(),
                                                                    'source': 'rebook',
                                                                    'mode': 'create'
                                                                  });
                                                                },
                                                                child: Text('Rebook',
                                                                    style: TextStyle(fontSize: getFontSize(1.5))),
                                                              ),
                                                            ),
                                                          ),
                                                        ]),
                                                        const SizedBox(
                                                          height: 5.0,
                                                        ),
                                                        Row(
                                                          mainAxisAlignment: MainAxisAlignment.start,
                                                          children: [
                                                            Icon(
                                                              Icons.location_on_rounded,
                                                              size: 35,
                                                              color: Colors.grey[400],
                                                            ),
                                                            Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              children: [
                                                                Text('${reser.buildingName} - ${reser.floorName}',
                                                                    style: TextStyle(
                                                                      fontSize: getFontSize(1.5),
                                                                    )),
                                                                const SizedBox(
                                                                  height: 5,
                                                                ),
                                                                Text('${reser.spaceDisplayName}',
                                                                    style: TextStyle(
                                                                      fontSize: getFontSize(1.5),
                                                                    )),
                                                              ],
                                                            ),
                                                            Expanded(
                                                              child: IconButton(
                                                                  alignment: Alignment.centerRight,
                                                                  padding: const EdgeInsets.all(5),
                                                                  tooltip: 'view floorplan',
                                                                  icon: isLargeScreen
                                                                      ? const Image(
                                                                          image: AssetImage('lib/icons/plan-icon.png'),
                                                                          width: 45,
                                                                          height: 45,
                                                                          fit: BoxFit.fill)
                                                                      : const Image(
                                                                          image: AssetImage('lib/icons/plan-icon.png')),
                                                                  iconSize: 35,
                                                                  color: CommonUtils.createMaterialColor(
                                                                      const Color(0XFF394251)),
                                                                  onPressed: () => _cadviewer(
                                                                      '${reser.buildingId}',
                                                                      '${reser.floorId}',
                                                                      '${reser.spaceId}',
                                                                      '${reser.startDate}',
                                                                      '${reser.endDate}')),
                                                            ),
                                                          ],
                                                        ),
                                                        const SizedBox(
                                                          height: 5.0,
                                                        ),
                                                        Row(
                                                          mainAxisAlignment: MainAxisAlignment.start,
                                                          children: [
                                                            Icon(
                                                              Icons.person,
                                                              size: 35,
                                                              color: Colors.grey[400],
                                                            ),
                                                            const SizedBox(
                                                              width: 10,
                                                            ),
                                                            Text('${reser.assignees}' ' ...',
                                                                style: TextStyle(
                                                                  fontSize: getFontSize(1.5),
                                                                )),
                                                          ],
                                                        ),
                                                        const SizedBox(
                                                          height: 5.0,
                                                        ),
                                                        Row(
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          children: [
                                                            Column(
                                                              crossAxisAlignment: CrossAxisAlignment.center,
                                                              children: [
                                                                Text('Reserved On',
                                                                    style: TextStyle(
                                                                        fontSize: getFontSize(1.5),
                                                                        fontWeight: FontWeight.bold)),
                                                                Text(
                                                                    DateFormat('MMM dd, yyyy hh:mm a').format(
                                                                        DateFormat('MM/dd/yyyy hh:mm a')
                                                                            .parse(reser.startDate!)),
                                                                    style: TextStyle(fontSize: getFontSize(1.5))),
                                                              ],
                                                            ),
                                                            Expanded(
                                                              child: Container(
                                                                alignment: Alignment.topRight,
                                                                padding: EdgeInsets.zero,
                                                                height: 30,
                                                                width: 120,
                                                                child: OverflowBar(
                                                                    spacing: 0,
                                                                    alignment: MainAxisAlignment.end,
                                                                    children: <Widget>[
                                                                      IconButton(
                                                                        padding: EdgeInsets.zero,
                                                                        icon: Icon(Icons.thumb_up_alt_outlined),
                                                                        iconSize: isLargeScreen ? 40 : 30,
                                                                        color: reser.rating == 1
                                                                            ? Colors.green
                                                                            : CommonUtils.createMaterialColor(
                                                                                const Color(0XFF394251)),
                                                                        onPressed: () {
                                                                          if (reser.rating != 1) {
                                                                            showComments(
                                                                                1, reser.spaceId, reser.reservationId);
                                                                          }
                                                                          setState(() {});
                                                                          if (reser.rating != 1) {
                                                                            reser.rating = 1;
                                                                          } else {
                                                                            reser.rating = -1;
                                                                          }
                                                                        },
                                                                      ),
                                                                      if (isLargeScreen) const SizedBox(width: 20),
                                                                      IconButton(
                                                                          padding: EdgeInsets.zero,
                                                                          icon:
                                                                              const Icon(Icons.thumb_down_alt_outlined),
                                                                          iconSize: isLargeScreen ? 40 : 30,
                                                                          color: reser.rating == 0
                                                                              ? Colors.red
                                                                              : CommonUtils.createMaterialColor(
                                                                                  const Color(0XFF394251)),
                                                                          onPressed: () {
                                                                            if (reser.rating != 0) {
                                                                              showComments(0, reser.spaceId,
                                                                                  reser.reservationId);
                                                                            }
                                                                            setState(() {});
                                                                            if (reser.rating != 0) {
                                                                              reser.rating = 0;
                                                                            } else {
                                                                              reser.rating = -1;
                                                                            }
                                                                          }),
                                                                    ]),
                                                              ),
                                                            )
                                                          ],
                                                        ),

                                                        /*Column(
                                            children: <Widget>[
                                              // Widget to display the list of project
                                              ListTile(
                                                  contentPadding:
                                                      EdgeInsets.fromLTRB(
                                                          10, 0, 10, 0),
                                                  key: UniqueKey(),
                                                  focusColor: Colors.blueGrey,
                                                  title: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .stretch,
                                                    children: <Widget>[
                                                      Row(
                                                        children: <Widget>[
                                                          Expanded(
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: <Widget>[
                                                                Text(
                                                                    'RES #${reser.reservationId} - ${reser.reservationTitle}',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          14.0,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                    )),
                                                                SizedBox(
                                                                    height: 10.0,
                                                                    width: 0),
                                                                Text(
                                                                    '${reser.spaceDisplayName}',
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            14)),
                                                                SizedBox(
                                                                  height: 10.0,
                                                                ),
                                                                Text(
                                                                    'Scheduled   ${(reser.startDate)}',
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            14)),
                                                                SizedBox(
                                                                  height: 10.0,
                                                                ),
                                                                Text(
                                                                    'Status          ${reser.status}',
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            14)),
                                                              ],
                                                            ),
                                                          ),
                                                          Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .end,
                                                            children: <Widget>[
                                                              ButtonTheme(
                                                                  child: new ButtonBar(
                                                                      alignment:
                                                                          MainAxisAlignment
                                                                              .center,
                                                                      children: <
                                                                          Widget>[
                                                                    TextButton(
                                                                      onPressed:
                                                                          () {},
                                                                      child: Text(
                                                                          'Re-book',
                                                                          style: TextStyle(
                                                                              fontSize:
                                                                                  12,
                                                                              fontWeight:
                                                                                  FontWeight.bold)),
                                                                      style: TextButton.styleFrom(
                                                                          padding:
                                                                              EdgeInsets.all(
                                                                                  5),
                                                                          primary:
                                                                              Colors
                                                                                  .black,
                                                                          backgroundColor:
                                                                              CommonUtils.createMaterialColor(Color(0XFFC0C0C0))),
                                                                    ),
                                                                    IconButton(
                                                                        padding:
                                                                            EdgeInsets
                                                                                .zero,
                                                                        tooltip:
                                                                            'view floorplan',
                                                                        icon: Image
                                                                            .asset(
                                                                                'lib/icons/cadviewer.png'),
                                                                        iconSize:
                                                                            30,
                                                                        color: CommonUtils
                                                                            .createMaterialColor(Color(
                                                                                0XFF394251)),
                                                                        onPressed: () => _cadviewer(
                                                                            '${reser.buildingId}',
                                                                            '${reser.floorId}',
                                                                            '${reser.spaceId}'))
                                                                  ])),
                                                              ButtonTheme(
                                                                  child: new ButtonBar(
                                                                      alignment:
                                                                          MainAxisAlignment
                                                                              .center,
                                                                      children: <
                                                                          Widget>[
                                                                    IconButton(
                                                                      padding:
                                                                          EdgeInsets
                                                                              .zero,
                                                                      icon: new Icon(
                                                                          Icons
                                                                              .thumb_up_alt_outlined),
                                                                      iconSize:
                                                                          30,
                                                                      color: reser.rating ==
                                                                              1
                                                                          ? Colors
                                                                              .green
                                                                          : CommonUtils.createMaterialColor(
                                                                              Color(0XFF394251)),
                                                                      onPressed:
                                                                          () {
                                                                        if (reser
                                                                                .rating !=
                                                                            1) {
                                                                          showComments(
                                                                              1,
                                                                              reser.spaceId,
                                                                              reser.reservationId);
                                                                        }
                                                                        this.setState(
                                                                            () {});
                                                                        if (reser
                                                                                .rating !=
                                                                            1) {
                                                                          reser.rating =
                                                                              1;
                                                                        } else {
                                                                          reser.rating =
                                                                              -1;
                                                                        }
                                                                      },
                                                                    ),
                                                                    IconButton(
                                                                        padding:
                                                                            EdgeInsets
                                                                                .zero,
                                                                        icon: new Icon(
                                                                            Icons
                                                                                .thumb_down_alt_outlined),
                                                                        iconSize:
                                                                            30,
                                                                        color: reser.rating ==
                                                                                0
                                                                            ? Colors
                                                                                .red
                                                                            : CommonUtils.createMaterialColor(Color(
                                                                                0XFF394251)),
                                                                        onPressed:
                                                                            () {
                                                                          if (reser.rating !=
                                                                              0) {
                                                                            showComments(
                                                                                0,
                                                                                reser.spaceId,
                                                                                reser.reservationId);
                                                                          }
                                                                          this.setState(
                                                                              () {});
                                                                          if (reser.rating !=
                                                                              0) {
                                                                            reser.rating =
                                                                                0;
                                                                          } else {
                                                                            reser.rating =
                                                                                -1;
                                                                          }
                                                                        }),
                                                                  ]))
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ))
                                            ],
                                          );*/
                                                      ],
                                                    ));
                                              },
                                              separatorBuilder: (BuildContext context, int index) => Divider(
                                                  indent: 5,
                                                  endIndent: 5,
                                                  thickness: 2,
                                                  color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0))),
                                            )
                                          : Center(
                                              child: Text(
                                                'No Reservation Data',
                                                style: TextStyle(fontSize: getFontSize(1.5)),
                                              ),
                                            ),
                                    );
                                  } else {
                                    return const ProgressIndicatorCust();
                                  }
                                });
                              },
                            )
                            // By default, show a loading spinner.
                            )))
              ]),
            ])));
  }

  showComments(int rating, int? spaceid, int? reservationid) {
    comments.text = '';
    showModalBottomSheet(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: CommonUtils.createMaterialColor(Colors.white),
        context: context,
        builder: (BuildContext context) {
          return GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);

              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: SingleChildScrollView(
              child: ClipRRect(
                  child: Container(
                padding: const EdgeInsets.all(10),
                height: 250,
                child: Form(
                    child: Column(children: <Widget>[
                  const Padding(padding: EdgeInsets.symmetric(vertical: 10.0)),
                  TextFormField(
                      maxLines: 5,
                      controller: comments,
                      style: const TextStyle(fontSize: 12),
                      decoration: InputDecoration(
                          suffixIcon: SizedBox(
                            height: 0,
                            width: 0,
                          ),
                          labelText: 'Comments',
                          border: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(Radius.circular(17)),
                              borderSide: const BorderSide(color: Colors.red)),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 25, vertical: 13),
                          hintStyle: const TextStyle(color: Colors.black87, fontSize: 15))),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Align(
                        alignment: Alignment.bottomRight,
                        child: ElevatedButton(
                            style: ButtonStyle(backgroundColor: WidgetStateProperty.resolveWith<Color>(
                              (Set<WidgetState> states) {
                                if (states.contains(WidgetState.pressed))
                                  return CommonUtils.createMaterialColor(Colors.redAccent);
                                return CommonUtils.createMaterialColor(
                                    const Color(0XFFb10c00)); // Use the component's default.
                              },
                            )),
                            onPressed: () {
                              Navigator.pop(context);
                              updateComments(context, reservationid, comments.text, rating, spaceid);
                            },
                            child: const Text(
                              'Save',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ))),
                  )
                ])),
              )),
            ),
          );
        });
  }

  void updateStatus(BuildContext context, int? reservationid, String status) async {
    String result = await (Provider.of<ReservationProvider>(context, listen: false)
        .updateStatus(context, reservationid, status) as Future<String>);
    if (result.isEmpty) {
      showDialog(
          context: context,
          builder: (BuildContext context) => getAlertDialog("Error Occurred", 'Unable to update Status', context));
    } else {
      setState(() {
        _isInit = true;
      });
    }
  }

  void updateComments(BuildContext context, int? reservationid, String comments, int rating, int? spaceid) async {
    if (comments.isNotEmpty) comments = comments.trim();
    print('update comments$comments');
    String result = await (Provider.of<ReservationProvider>(context, listen: false)
        .updateComments(context, reservationid, comments, rating, spaceid) as Future<String>);
    if (result.isEmpty) {
      showDialog(
          context: context,
          builder: (BuildContext context) => getAlertDialog("Error Occurred", 'Unable to update Comments', context));
    } else {
      setState(() {
        _isInit = true;
      });
    }
  }

  bool isCurrrentDate(String startdate, String enddate, String? status) {
    bool showcheckin = false;
    try {
      DateTime today = DateTime.now();
      DateTime start = DateFormat('MM/dd/yyyy hh:mm a').parse(startdate);
      DateTime end = DateFormat('MM/dd/yyyy hh:mm a').parse(enddate);

      if ((today.isBefore(end) && today.isAfter(start)) && (status != 'CHECK-IN')) {
        print("Inside current");
        showcheckin = true;
      }
    } catch (error) {
      print(error);
    }
    return showcheckin;
  }

  double getFontSize(double multiplier) {
    double unitHeightValue = MediaQuery.of(context).size.height * 0.01;
    //  print(multiplier * unitHeightValue);
    return multiplier * unitHeightValue;
  }
}

class NewReservationButton extends StatelessWidget {
  const NewReservationButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
        mini: true,
        backgroundColor: const Color(0XFFb10c00),
        foregroundColor: Colors.white,
        onPressed: () {
          Navigator.of(context).pushNamed(ReservationAdd.routName, arguments: {'type': 'Workspace', 'mode': 'create'});
          // showModalBottomSheet(
          //     shape: RoundedRectangleBorder(
          //       borderRadius: BorderRadius.circular(10.0),
          //     ),
          //     backgroundColor: CommonUtils.createMaterialColor(Color(0XFFb10c00)),
          //     context: context,
          //     builder: (BuildContext context) {
          //       return ClipRRect(
          //         child: Container(
          //           height: 70,
          //           child: ListView(
          //             physics: NeverScrollableScrollPhysics(),
          //             children: <Widget>[
          //               ListTile(
          //                 onTap: () {
          //                   Navigator.pop(context);
          //                   Navigator.of(context).pushNamed(ReservationAdd.routName, arguments: {'type': 'Workspace'});
          //                 },
          //                 title: Text(
          //                   "New Workspace Reservation",
          //                   style: TextStyle(color: Colors.white, fontSize: 14),
          //                 ),
          //                 leading: Icon(
          //                   Icons.workspaces_filled,
          //                   color: Colors.white,
          //                 ),
          //               ),
          //               ListTile(
          //                 title: Text(
          //                   "New Meeting Reservation",
          //                   style: TextStyle(color: Colors.white, fontSize: 14),
          //                 ),
          //                 leading: Icon(
          //                   Icons.meeting_room_rounded,
          //                   color: Colors.white,
          //                 ),
          //                 onTap: () {
          //                   Navigator.pop(context);
          //                   Navigator.of(context).pushNamed(
          //                     ReservationAdd.routName,
          //                     arguments: {'type': 'Meeting'},
          //                   );
          //                 },
          //               ),
          //             ],
          //           ),
          //         ),
          //       );
          //     });
        },
        child: const Icon(
          Icons.add,
          size: 35.0,
        ));
  }
}
