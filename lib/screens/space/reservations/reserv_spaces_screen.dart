import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/common/entity_data.dart';
import '../../../models/servicerequest/sr_space.dart';
import '../../../models/spacemgmt/buildings/activereservespaces_data.dart';
import '../../../providers/maintenance/servicerequest_controller.dart';
import '../../../providers/spacemanagement/reservation/reservation_controller.dart';
import '../../../providers/spacemanagement/reservation/reservationdetails_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';

class ReserveSpaces extends GetView<ReservationDetailsController> {
  ReserveSpaces({
    super.key,
  });

  LabelController talabel = Get.find<LabelController>();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(
          'Available Spaces',
          style: ComponentUtils.appbartitlestyle,
        ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            // srCtrl.iptlovsearchCtrl.text = '';
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          TaSearchInputText(
              makeSearch: (searchtext) async {}, searchController: controller.spaceSearchCtrl, hintSearch: 'Search '),
          Expanded(
            //child: RefreshIndicator(
            //onRefresh: _refreshTargets,
            child: GetX<ReservationDetailsController>(
              initState: (state) async {
                await controller.getSpaceData();
              },
              builder: (_) {
                return _.spaceDataLoading.isTrue
                    ? const ProgressIndicatorCust()
                    : _.spaceLovData.isEmpty
                        ? Center(
                            child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return spacelistitem(context, _.spaceLovData[index]);
                            },
                            itemCount: _.spaceLovData.length,
                          );
              },
              // ),
            ),
          ),
        ],
      ),
    );
  }

  Widget spacelistitem(BuildContext context, ActiveReserveSpaces s) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          Get.back();

          controller.rsrvRow.value.spaceId = s.spaceId;
          controller.rsrvRow.value.spaceDisplayName = s.spaceDisplayName;
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text('${s.spaceDisplayName}',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                fontWeight: FontWeight.bold,
                              )),
                          Text('${s.spaceNumber}',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                fontWeight: FontWeight.bold,
                              )),
                        ]),
                    const SizedBox(height: 4.0, width: 0),
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(s.spaceCategory ?? '',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                //fontWeight: FontWeight.bold,
                              )),
                          Text(s.spaceUse ?? '',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                                // fontWeight: FontWeight.bold,
                              )),
                        ]),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
