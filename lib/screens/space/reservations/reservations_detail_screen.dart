import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/models/recent_reservations.dart';
import 'package:tangoworkplace/models/ta_admin/page_rules.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/space/find/find_detail_screen.dart';
import 'package:tangoworkplace/screens/space/property_search.dart';
import 'package:tangoworkplace/screens/space/space_search.dart';
import '../../../common/component_utils.dart';
import '../../../common/widgets/alert_screen.dart';
import '../../../models/building.dart';
import '../../../models/floor.dart';
import '../../../models/property.dart';
import '../../../models/reservations.dart';
import '../../../models/space.dart';
import '../../../providers/reservation_provider.dart';
import '../../../utils/constvariables.dart';
import '../../../utils/preferences_utils.dart';
import '../../../utils/common_utils.dart';
import 'package:intl/intl.dart';

import '../assigneduser_search.dart';

class ReservationAdd extends StatefulWidget {
  static const routName = '/reservationsadd';

  const ReservationAdd({super.key});
  @override
  _ReservationAddState createState() => _ReservationAddState();
}

class _ReservationAddState extends State<ReservationAdd> {
  var _reservationObj = Reservations();
  LabelController talabel = Get.find<LabelController>();
  ReservationRules? rsrvrules;
  int? alldayStartHour;
  int? alldayStartMin;
  int? alldayEndHour;
  int? alldayEndMin;
  bool _isInit = true;
  final bool _isLoading = false;
  bool _reserv = false;
  bool _allDay = true;
  var requestId;
  var reservationType;
  var source;
  String? assignedPersonId;
  var mode;
  bool bookFlag = true;
  var personId = '';

  final _formKey = GlobalKey<FormState>();
  final _createdByController = TextEditingController();
  final _assignedToController = TextEditingController();
  final _titleController = TextEditingController();
  final _reservationType = TextEditingController();
  final _reservationNameController = TextEditingController();
  final _properyNameController = TextEditingController();
  final _buildingNameController = TextEditingController();
  final _floorNameController = TextEditingController();
  final _spaceNameController = TextEditingController();

  final startDateFieldState = GlobalKey<FormFieldState>();
  final endDateFieldState = GlobalKey<FormFieldState>();

  String? _servicesitems;
  int? _propertyname = 0, _buildingname, _floorname, _spacename;
  String? _spacedisplayname;
  List? _spaceAttributes;

  String? dateTime;
  DateTime selectedStartDate = DateTime.now();
  DateTime selectedEndDate = DateTime.now();
  DateTime selectedDate = DateTime.now();
  TimeOfDay? selectedTime = const TimeOfDay(hour: 00, minute: 00);

  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _dateController1 = TextEditingController();

  final TextEditingController _startdateController = TextEditingController();
  final TextEditingController _enddateController = TextEditingController();
  Future<List<Property>>? propertyLov;
  List<Building> buildingLov = [];
  List<Floor> floorLov = [];
  Future<List<Space>>? spaceLov;

  final _startDateFocusNode = FocusNode();
  final _endDateFocusNode = FocusNode();

  Future<Null> _selectDate(BuildContext context, String source) async {
    debugPrint('_selectDate -------${talabel.reservationrules.value.toJson()}');
    rsrvrules = talabel.reservationrules.value;

    DateTime eeDate;
    if ('end' == source) {
      debugPrint('_selectDate -------${rsrvrules!.toJson()}');
      if (rsrvrules != null && (rsrvrules!.bwStartEndDate != null && rsrvrules!.bwStartEndDate! > 0)) {
        eeDate = selectedStartDate.add(Duration(days: rsrvrules!.bwStartEndDate!));
      } else {
        eeDate = DateTime(2101);
      }
    } else {
      if (rsrvrules != null && (rsrvrules!.advanceAllowedDays != null && rsrvrules!.advanceAllowedDays! > 0)) {
        eeDate = selectedStartDate.add(Duration(days: rsrvrules!.advanceAllowedDays!));
      } else {
        eeDate = DateTime(2101);
      }
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: source == 'end' ? selectedStartDate : selectedDate,
      initialEntryMode: DatePickerEntryMode.calendar,
      firstDate: source == 'end' ? selectedStartDate : DateTime.now(),
      lastDate: eeDate,
    );
    if (picked != null) {
      setState(() {
        if (source == 'end') {
          selectedEndDate = picked;
        } else {
          selectedStartDate = picked;
        }
      });

      _selectTime(context, source);
    }
  }

  Future<Null> _selectTime(BuildContext context, String source) async {
    TimeOfDay? picked;
    if (!_allDay) {
      picked = await showTimePicker(
          context: context, initialTime: selectedTime!, initialEntryMode: TimePickerEntryMode.dial);
    }
    if (picked != null && !_allDay) {
      setState(() {
        selectedTime = picked;
        selectedTime!.replacing(hour: selectedTime!.hourOfPeriod);

        if (source == 'end') {
          DateTime dt = DateTime(selectedEndDate.year, selectedEndDate.month, selectedEndDate.day, selectedTime!.hour,
              selectedTime!.minute);
          String d = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
          _dateController1.text = d;
          String d1 = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
          _enddateController.text = d1;
        } else {
          DateTime dt = DateTime(selectedStartDate.year, selectedStartDate.month, selectedStartDate.day,
              selectedTime!.hour, selectedTime!.minute);
          String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
          _dateController.text = d1;
          String d2 = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
          print(d2);
          _startdateController.text = d2;

          DateTime enddateformat = DateTime(selectedStartDate.year, selectedStartDate.month, selectedStartDate.day,
              selectedTime!.hour, selectedTime!.minute + 30);

          String d = DateFormat('MM/dd/yyyy hh:mm a').format(enddateformat);
          _dateController1.text = d;
          String d11 = DateFormat('yyyy-MM-dd hh:mm a').format(enddateformat);
          _enddateController.text = d11;
        }
      });
    } else {
      setState(() {
        //  selectedTime = picked;
        // selectedTime.replacing(hour: selectedTime.hourOfPeriod);
        if (source == 'end') {
          DateTime enddateformat = DateTime(selectedEndDate.year, selectedEndDate.month, selectedEndDate.day,
              _allDay ? (alldayEndHour ?? 23) : 23, _allDay ? (alldayEndMin ?? 59) : 59);
          String d = DateFormat('MM/dd/yyyy hh:mm a').format(enddateformat);
          _dateController1.text = d;
          String d1 = DateFormat('yyyy-MM-dd hh:mm a').format(enddateformat);
          _enddateController.text = d1;
        } else {
          DateTime startdateformat = DateTime(selectedStartDate.year, selectedStartDate.month, selectedStartDate.day,
              _allDay ? (alldayStartHour ?? 00) : 00, _allDay ? (alldayStartMin ?? 00) : 00);
          String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(startdateformat);
          _dateController.text = d1;
          String d2 = DateFormat('yyyy-MM-dd hh:mm a').format(startdateformat);
          print(d2);
          _startdateController.text = d2;

          DateTime enddateformat = DateTime(selectedStartDate.year, selectedStartDate.month, selectedStartDate.day,
              _allDay ? (alldayEndHour ?? 23) : 23, _allDay ? (alldayEndMin ?? 59) : 59);
          String d = DateFormat('MM/dd/yyyy hh:mm a').format(enddateformat);
          _dateController1.text = d;
          String d3 = DateFormat('yyyy-MM-dd hh:mm a').format(enddateformat);
          _enddateController.text = d3;
        }
      });
    }
  }

  Future<String> getPersonId() async {
    String user = await SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
    String pid = '';
    pid = await Provider.of<ReservationProvider>(context, listen: false).getPersonId(user);

    return pid;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Future<void> didChangeDependencies() async {
    if (_isInit) {
      Map? arguments = ModalRoute.of(context)!.settings.arguments as Map?;
      if (arguments != null) {
        requestId = arguments['id'];
        reservationType = arguments['type'];
        source = arguments['source'];
        mode = arguments['mode'];
      }
      debugPrint('Inside reervation detail>>>>> $requestId $source $reservationType');
      debugPrint('rsrvrules  $rsrvrules');

      await talabel.getlabels('TMCMOBILE_RESERVATION', 'reservation', ruletype: 'reservation');

      rsrvrules = talabel.reservationrules.value;
      debugPrint('iiiii -------${talabel.reservationrules.value.toJson()}');
      alldayStartHour = rsrvrules?.alldayStartHour;
      alldayStartMin = rsrvrules?.alldayStartMin;
      alldayEndHour = rsrvrules?.alldayEndHour;
      alldayEndMin = rsrvrules?.alldayEndMin;
      debugPrint(
          'alldayStartHour $alldayStartHour  alldayStartMin $alldayStartMin  alldayEndHour $alldayEndHour  alldayEndMin $alldayEndMin');

      personId = await getPersonId();
      debugPrint('personid>>>>>>>>>>$personId');
      if (requestId != null) {
        if (source == 'rebook') {
          _reservationObj = Provider.of<ReservationProvider>(context, listen: false).findCompletedById(requestId);
        } else {
          _reservationObj = Provider.of<ReservationProvider>(context, listen: false).findById(requestId);
        }
        print('Reser requestId ${_reservationObj.reservationId}');
        if (mode == 'edit') {
          debugPrint('----------------------${_reservationObj.status!}');
          bookFlag = (_reservationObj.status == 'COMPLETED' || _reservationObj.status == 'CHECK-IN') ? false : true;
        }

        List? assigneesVal = await (Provider.of<ReservationProvider>(context, listen: false)
            .getAssignees(context, _reservationObj.reservationId));

        if (assigneesVal != null && assigneesVal.isNotEmpty) {
          _assignedToController.text = assigneesVal[1];
          assignedPersonId = assigneesVal[0];
        }
        _createdByController.text = _reservationObj.createdBy!;
        _dateController.text = _reservationObj.startDate!;
        _dateController1.text = _reservationObj.endDate!;
        _reservationNameController.text = _reservationObj.reservationTitle!;

        if (source == 'rebook') {
          DateTime currentDate = DateTime.now();
          DateTime dt = DateTime(currentDate.year, currentDate.month, currentDate.day, 08, 00);
          String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
          _dateController.text = d1;
          DateTime dt1 = DateTime(currentDate.year, currentDate.month, currentDate.day, 08, 30);

          String d2 = DateFormat('MM/dd/yyyy hh:mm a').format(dt1);
          _dateController1.text = d2;

          String startdateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
          _startdateController.text = startdateformatted;

          String enddateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt1);
          _enddateController.text = enddateformatted;
          _reservationType.text = reservationType;
          print('$reservationType');
        } else {
          String startdateformatted =
              DateFormat('yyyy-MM-dd hh:mm a').format(DateFormat('MM/dd/yyyy hh:mm a').parse(_dateController.text));
          print(startdateformatted);
          _startdateController.text = startdateformatted;

          String enddateformatted =
              DateFormat('yyyy-MM-dd hh:mm a').format(DateFormat('MM/dd/yyyy hh:mm a').parse(_dateController1.text));
          print(enddateformatted);
          _enddateController.text = enddateformatted;
        }
        _propertyname = _reservationObj.propertyId;
        _buildingname = _reservationObj.buildingId;
        _floorname = _reservationObj.floorId;
        _spacename = _reservationObj.spaceId;
        _reserv = _reservationObj.selfReserve == 'Y' ? true : false;
        _allDay = _reservationObj.allDay == 'Y' ? true : false;
        _properyNameController.text = _reservationObj.propertyName!;
        _buildingNameController.text = _reservationObj.buildingName!;
        _floorNameController.text = _reservationObj.floorName!;
        _spaceNameController.text = _reservationObj.spaceDisplayName!;
        _spacedisplayname = _reservationObj.spaceDisplayName;
        _spaceAttributes = _reservationObj.spaceAttributes;
        setState(() {});
      } else {
        debugPrint('---------------create---------------------');
        String user = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
        _reservationType.text = reservationType;
        _createdByController.text = user;
        _assignedToController.text = user;
        if (_reserv) assignedPersonId = personId;
        debugPrint('assignedPersonId>>> $assignedPersonId');
        if (assignedPersonId == null || assignedPersonId == '') {
          _reserv = false;
          _assignedToController.text = '';
        }
        _reservationNameController.text = '$reservationType reservation';
        DateTime currentDate = DateTime.now();

        DateTime dt =
            DateTime(currentDate.year, currentDate.month, currentDate.day, alldayStartHour ?? 0, alldayStartMin ?? 0);
        String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
        _dateController.text = d1;
        DateTime dt1 =
            DateTime(currentDate.year, currentDate.month, currentDate.day, alldayEndHour ?? 23, alldayEndMin ?? 59);

        String d2 = DateFormat('MM/dd/yyyy hh:mm a').format(dt1);
        _dateController1.text = d2;

        String startdateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt);
        _startdateController.text = startdateformatted;

        String enddateformatted = DateFormat('yyyy-MM-dd hh:mm a').format(dt1);
        _enddateController.text = enddateformatted;
      }
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _dateController.dispose();
    _dateController1.dispose();
    _startDateFocusNode.dispose();
    _endDateFocusNode.dispose();
    _assignedToController.dispose();
    _titleController.dispose();
    _reservationType.dispose();
    _reservationNameController.dispose();
    _properyNameController.dispose();
    _buildingNameController.dispose();
    _floorNameController.dispose();
    _spaceNameController.dispose();
    super.dispose();
  }

  Future<void> _saveForm() async {
    final isValid = _formKey.currentState!.validate();
    if (!isValid) {
      return;
    }

    _formKey.currentState!.save();
    print(assignedPersonId);
    _reservationObj = Reservations(
        reservationId: _reservationObj.reservationId,
        reservationNumber: _reservationObj.reservationNumber,
        reservationType: reservationType,
        reservationTitle: _reservationNameController.text,
        startDate: _dateController.text,
        endDate: _dateController1.text,
        status: 'SCHEDULED',
        propertyId: _propertyname,
        buildingId: _buildingname,
        spaceId: _spacename,
        floorId: _floorname,
        description: '',
        rating: -1,
        selfReserve: _reserv ? 'Y' : 'N',
        allDay: _allDay ? 'Y' : 'N',
        serviceItems: _servicesitems,
        isRecurring: 'N',
        createdBy: SharedPrefUtils.readPrefStr(ConstHelper.userNamevar),
        propertyName: _properyNameController.text,
        buildingName: _buildingNameController.text,
        floorName: _floorNameController.text,
        spaceDisplayName: _spacedisplayname,
        assignees: assignedPersonId);
    print('Building ${_reservationObj.buildingId}');
    print('Floor ${_reservationObj.floorId}');
    print('Space ${_reservationObj.spaceId}');
    print('Property ${_reservationObj.propertyId}');
    // setState(() {
    //   _isLoading = true;
    // });
    ProgressUtil.showLoaderDialog(context);
    if (_reservationObj.reservationId != null && source != 'rebook') {
      try {
        String result = await (Provider.of<ReservationProvider>(context, listen: false).updateReservation(
          _reservationObj,
          _reservationObj.reservationId,
        ) as Future<String>);

        if (result.isNotEmpty) {
          print(result);
          ProgressUtil.closeLoaderDialog(context);
          Navigator.pop(context, () {
            setState(() {});
          });
        } else {
          ProgressUtil.closeLoaderDialog(context);
          showDialog(
              context: context,
              builder: (BuildContext context) =>
                  getAlertDialog("Error occured", 'Error occured while updating reservation', context));
        }
      } catch (error) {
        ProgressUtil.closeLoaderDialog(context);
        showDialog(
            context: context,
            builder: (BuildContext context) =>
                getAlertDialog("Error occured", 'Error coccured while updating reservation', context));
      }
    } else {
      try {
        await Provider.of<ReservationProvider>(context, listen: false).addReservation(_reservationObj);
        ProgressUtil.closeLoaderDialog(context);
        Navigator.pop(context, () {
          setState(() {});
        });
      } catch (error) {
        ProgressUtil.closeLoaderDialog(context);
        showDialog(
            context: context, builder: (BuildContext context) => getAlertDialog("Error occured", '$error', context));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(
          // actions: <Widget>[
          //   Padding(
          //       padding: EdgeInsets.only(right: 20.0),
          //       child: GestureDetector(
          //         onTap: _saveForm,
          //         child: Icon(
          //           Icons.check,
          //           size: 26.0,
          //         ),
          //       ))
          // ],
          title: _reservationObj.reservationId != null && source != 'rebook'
              ? const Text(
                  'Update Reservation',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                )
              : Text(
                  'New $reservationType Reservation',
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
          elevation: 0,
          backgroundColor: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          //brightness: Brightness.dark,
          // toolbarHeight: 60,
        ),
        body: Stack(children: <Widget>[
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              decoration: BoxDecoration(
                  borderRadius:
                      const BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
                  color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                  shape: BoxShape.rectangle),
              height: 130,
            ),
          ),
          Positioned(
            top: 00,
            right: 15,
            left: 15,
            bottom: 10,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(9),
              child: Container(
                color: Colors.grey[300],
                padding: const EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 0.0),
                child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                        TextFormField(
                          controller: _reservationNameController,
                          style: const TextStyle(fontSize: 14),
                          validator: (value) {
                            if (value == null) {
                              return 'Please enter reservation name';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            labelText: 'Title',
                            labelStyle: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                            prefixIcon: Icon(Icons.description,
                                color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 10),
                        const Text("  Assignee(s)",
                            style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.black54)),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Flexible(
                              child: TextFormField(
                                enabled: false,
                                controller: _createdByController,
                                style: const TextStyle(fontSize: 14),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  labelText: 'Requested By',
                                  labelStyle: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                                  prefixIcon: Icon(Icons.person,
                                      color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                              ),
                            ),
                            /* Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    //  SizedBox(width: 12, height: 0),
                                    Container(
                                      width: 90,
                                      color: Colors.white,
                                      padding: EdgeInsets.zero,
                                      child: Icon(Icons.person_add,
                                          color:
                                              CommonUtils.createMaterialColor(
                                                  Color(0XFFb10c00))),
                                    ),
                                    // SizedBox(width: 5, height: 0),
                                    Text(
                                      'Self',
                                      style: TextStyle(
                                          backgroundColor: Colors.white,
                                          fontSize: 12,
                                          color: Colors.black),
                                    ),
                                    SwitchListTile(
                                      dense: true,
                                      // activeTrackColor: Colors.blueGrey,
                                      // activeColor: Colors.blueGrey,
                                      tileColor: Colors.white,
                                      contentPadding: EdgeInsets.all(0),
                                      /* title: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          //  SizedBox(width: 12, height: 0),
                                          Container(
                                            padding: EdgeInsets.zero,
                                            child: Icon(Icons.person_add,
                                                color: CommonUtils
                                                    .createMaterialColor(
                                                        Color(0XFFb10c00))),
                                          ),
                                          SizedBox(width: 5, height: 0),
                                          Text(
                                            'Self',
                                            style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.black),
                                          ),
                                        ],
                                      ),*/
                                      value: _reserv ?? true,
                                      onChanged: (bool value) {
                                        setState(() {
                                          _reserv = value;
                                          if (value) {
                                            _assignedToController.text =
                                                SharedPrefUtils.readPrefStr(
                                                    userNamevar);
                                            assignedPersonId = '21222';
                                          } else {
                                            _assignedToController.text = "";
                                            assignedPersonId = "";
                                          }
                                        });
                                      },
                                      // secondary:  Icon(Icons.person_add,color : CommonUtils.createMaterialColor(Color(0XFFb10c00))),
                                    ),
                                  ],
                                ),*/
                            /*  Flexible(
                                  //   padding: EdgeInsets.zero,
                                  child: Container(
                                    width: 60,
                                    padding: EdgeInsets.zero,
                                    child: SwitchListTile(
                                      dense: true,
                                      // activeTrackColor: Colors.blueGrey,
                                      // activeColor: Colors.blueGrey,
                                      tileColor: Colors.white,
                                      contentPadding: EdgeInsets.all(0),
                                      /* title: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          //  SizedBox(width: 12, height: 0),
                                          Container(
                                            padding: EdgeInsets.zero,
                                            child: Icon(Icons.person_add,
                                                color: CommonUtils
                                                    .createMaterialColor(
                                                        Color(0XFFb10c00))),
                                          ),
                                          SizedBox(width: 5, height: 0),
                                          Text(
                                            'Self',
                                            style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.black),
                                          ),
                                        ],
                                      ),*/
                                      value: _reserv ?? true,
                                      onChanged: (bool value) {
                                        setState(() {
                                          _reserv = value;
                                          if (value) {
                                            _assignedToController.text =
                                                SharedPrefUtils.readPrefStr(
                                                    userNamevar);
                                            assignedPersonId = '21222';
                                          } else {
                                            _assignedToController.text = "";
                                            assignedPersonId = "";
                                          }
                                        });
                                      },
                                      // secondary:  Icon(Icons.person_add,color : CommonUtils.createMaterialColor(Color(0XFFb10c00))),
                                    ),
                                  ),
                                ),*/
                          ],
                        ),
                        Container(
                          padding: EdgeInsets.zero,
                          child: SwitchListTile.adaptive(
                            //dense: false,
                            // activeTrackColor: Colors.blueGrey,
                            // activeColor: Colors.blueGrey,
                            tileColor: Colors.white,
                            contentPadding: EdgeInsets.zero,
                            title: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const SizedBox(width: 12, height: 0),
                                Icon(Icons.person_add, color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                                const SizedBox(width: 15, height: 0),
                                const Text(
                                  'I am Reserving',
                                  textAlign: TextAlign.justify,
                                  style: TextStyle(fontSize: 14, color: Colors.black),
                                ),
                              ],
                            ),
                            value: _reserv ?? false,

                            onChanged: (bool value) {
                              setState(() {
                                //_reserv = value;
                                if (value) {
                                  var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
                                  assignedPersonId = personId;
                                  debugPrint('personId  $personId');
                                  if (assignedPersonId == null || assignedPersonId == '' || assignedPersonId == '0') {
                                    debugPrint('assignedPersonId error $assignedPersonId');
                                    _assignedToController.text = "";
                                    assignedPersonId = "";
                                    _reserv == false;
                                    ComponentUtils.showpopup(
                                        msg: "You cannot self reserve. No person information found!", type: 'Error');
                                  } else {
                                    _assignedToController.text = username;

                                    _reserv = value;
                                  }
                                  debugPrint('assignedPersonId $assignedPersonId');
                                } else {
                                  _assignedToController.text = "";
                                  assignedPersonId = "";
                                  _reserv = value;
                                }
                              });
                            },
                            // secondary:  Icon(Icons.person_add,color : CommonUtils.createMaterialColor(Color(0XFFb10c00))),
                          ),
                        ),
                        TextFormField(
                          readOnly: true,
                          enabled: _reserv == true ? false : true,
                          style: const TextStyle(fontSize: 14),
                          controller: _assignedToController,
                          textInputAction: TextInputAction.next,
                          onSaved: (value) {
                            _assignedToController.text = value!;
                          },

                          decoration: InputDecoration(
                            border: InputBorder.none,
                            suffixIcon: !_reserv
                                ? IconButton(
                                    onPressed: () {
                                      fetchAssignedToList(context);
                                    },
                                    icon: const Icon(Icons.arrow_forward_ios))
                                : null,
                            labelText: 'Requested for',
                            labelStyle: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                            prefixIcon:
                                Icon(Icons.people, color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          // The validator receives the text that the user has entered.
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please enter requested for';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 10),
                        const Text("  Duration",
                            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.black54)),
                        const SizedBox(
                          height: 10,
                        ),
                        SwitchListTile.adaptive(
                          tileColor: Colors.white,
                          contentPadding: EdgeInsets.zero,
                          title: Row(
                            children: [
                              const SizedBox(width: 12, height: 0),
                              Icon(Icons.alarm, color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                              const SizedBox(width: 15, height: 0),
                              const Text(
                                'All Day?',
                                style: TextStyle(fontSize: 14),
                              ),
                            ],
                          ),
                          value: _allDay ?? true,
                          onChanged: (bool value) {
                            setState(() {
                              _allDay = value;
                              if (value) {
                                DateTime dt = DateTime(selectedStartDate.year, selectedStartDate.month,
                                    selectedStartDate.day, alldayStartHour ?? 00, alldayStartMin ?? 00);
                                String d = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
                                _dateController.text = d;
                                DateTime datedt = DateTime(selectedStartDate.year, selectedStartDate.month,
                                    selectedStartDate.day, alldayEndHour ?? 23, alldayEndMin ?? 59);
                                String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(datedt);
                                _dateController1.text = d1;
                              } else {
                                DateTime dt = DateTime(
                                    selectedStartDate.year, selectedStartDate.month, selectedStartDate.day, 08, 00);
                                String d = DateFormat('MM/dd/yyyy hh:mm a').format(dt);
                                _dateController.text = d;
                                DateTime datedt = DateTime(
                                    selectedStartDate.year, selectedStartDate.month, selectedStartDate.day, 08, 30);
                                String d1 = DateFormat('MM/dd/yyyy hh:mm a').format(datedt);
                                _dateController1.text = d1;
                              }
                            });
                          },
                          //  secondary:  Icon(Icons.alarm,color : CommonUtils.createMaterialColor(Color(0XFFb10c00))),
                          // dense:true,
                          // controlAffinity: ListTileControlAffinity.trailing,
                        ),
                        TextFormField(
                          readOnly: true,
                          key: startDateFieldState,
                          style: const TextStyle(fontSize: 14),
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please select Start Date';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                              labelStyle: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                              border: InputBorder.none,
                              labelText: 'Start Date',
                              prefixIcon: Icon(Icons.date_range_outlined,
                                  color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                              filled: true,
                              fillColor: Colors.white,
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.arrow_forward_ios),
                                onPressed: () {
                                  _selectDate(context, 'start');
                                },
                              )),
                          controller: _dateController,
                          onFieldSubmitted: (_) {
                            FocusScope.of(context).requestFocus(_endDateFocusNode);
                          },
                        ),
                        TextFormField(
                          readOnly: true,
                          key: endDateFieldState,
                          style: const TextStyle(fontSize: 14),
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please select End Date';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                              labelStyle: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                              border: InputBorder.none,
                              labelText: 'End Date',
                              prefixIcon: Icon(Icons.date_range_outlined,
                                  color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                              filled: true,
                              fillColor: Colors.white,
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.arrow_forward_ios),
                                onPressed: () {
                                  _selectDate(context, 'end');
                                },
                              )),
                          controller: _dateController1,
                        ),
                        const SizedBox(height: 10),
                        const Text("  Location",
                            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.black54)),
                        const SizedBox(
                          height: 10,
                        ),
                        TextFormField(
                          readOnly: true,
                          controller: _properyNameController,
                          style: const TextStyle(fontSize: 14),
                          validator: (value) {
                            if (value!.isEmpty) {
                              print('property value$value');
                              return 'Please select property';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            suffixIcon: IconButton(
                              onPressed: () {
                                final isvalid = startDateFieldState.currentState!.validate();
                                print('isValid123 $isvalid');
                                if (startDateFieldState.currentState!.validate() &&
                                    endDateFieldState.currentState!.validate()) {
                                  print('inside1');
                                  fetchProperty(context, _startdateController.text, _enddateController.text,
                                      requestId: requestId, d1: _dateController.text, d2: _dateController1.text);
                                }
                                return;
                              },
                              icon: const Icon(Icons.arrow_forward_ios),
                            ),
                            border: InputBorder.none,
                            labelStyle: const TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                            hintStyle: const TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                            labelText: 'Property',
                            prefixIcon: Icon(Icons.location_city,
                                color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          onTap: () {
                            final isvalid = startDateFieldState.currentState!.validate();
                            print('isValid123 $isvalid');
                            if (startDateFieldState.currentState!.validate() &&
                                endDateFieldState.currentState!.validate()) {
                              fetchProperty(context, _startdateController.text, _enddateController.text,
                                  requestId: requestId, d1: _dateController.text, d2: _dateController1.text);
                            }
                            return;
                          },
                        ),
                        /* child: FutureBuilder<List<Property>>(
                                    future: propertyLov,
                                    builder: (context, AsyncSnapshot snapshot) {
                                      if (snapshot.data == null) {
                                        return CircularProgressIndicator();
                                      } else {
                                        return DropdownButtonFormField<int>(
                                          hint: Text(
                                            'Select a Property',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          onSaved: (val) => _propertyname = val,
                                          value:
                                              _reservationObj.reservationId !=
                                                      null
                                                  ? _initValues['propertyId']
                                                  : null,
                                          items: snapshot.data
                                              .map<DropdownMenuItem<int>>(
                                                  (Property x) {
                                            return DropdownMenuItem<int>(
                                              value: x.propertyId,
                                              child: Text(
                                                x.propertyName,
                                                style: TextStyle(fontSize: 12),
                                              ),
                                            );
                                          }).toList(),
                                          onChanged: (val) {
                                            setState(() {
                                              _floorname = null;
                                              _buildingname = null;
                                              _propertyname = null;
                                              _propertyname = val;
                                              buildingLov = null;
                                              buildingLov = Provider.of<
                                                          ReservationProvider>(
                                                      context,
                                                      listen: false)
                                                  .getBuildingByProperty(val);
                                            });
                                          },
                                          validator: (value) {
                                            if (value == null) {
                                              return 'Please select a property';
                                            }
                                            return null;
                                          },
                                          decoration: InputDecoration(
                                              labelStyle:
                                                  TextStyle(fontSize: 12),
                                              floatingLabelBehavior:
                                                  FloatingLabelBehavior.always,
                                              labelText: 'Property',
                                              icon: Icon(Icons.build),
                                              filled: true,
                                              fillColor: Colors.white70,
                                              enabledBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(12.0)),
                                                borderSide: BorderSide(
                                                    color: CommonUtils
                                                        .createMaterialColor(
                                                            Color(0XFFC0C0C0)),
                                                    width: 2),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(10.0)),
                                                borderSide: BorderSide(
                                                    color: CommonUtils
                                                        .createMaterialColor(
                                                            Color(0XFFC0C0C0))),
                                              )),
                                        );
                                      }
                                    })*/

                        /* Consumer<ReservationProvider>(builder:
                                (BuildContext context, resvProvider,
                                    Widget child) {
                              return DropdownButtonFormField(
                                hint: Text(
                                  'Select a building',
                                  style: TextStyle(fontSize: 12),
                                ),
                                onSaved: (val) => _buildingname = val,
                                value: _reservationObj.reservationId != null
                                    ? _initValues['buildingId']
                                    : _buildingname,
                                items: buildingLov.map<DropdownMenuItem<int>>(
                                    (Building dropDownStringItem) {
                                  print(
                                      'Generating Building $dropDownStringItem');
                                  return DropdownMenuItem<int>(
                                    value: dropDownStringItem.buildingId,
                                    child: Text(dropDownStringItem.buildingName,
                                        style: TextStyle(fontSize: 12)),
                                  );
                                }).toList(),
                                onChanged: (val) {
                                  setState(() {
                                    _floorname = null;
                                    floorLov = null;
                                    _buildingname = null;
                                    _buildingname = val;

                                    floorLov = Provider.of<ReservationProvider>(
                                            context,
                                            listen: false)
                                        .getFloorByBuilding(val);
                                  });
                                },
                                validator: (value) {
                                  if (value == null) {
                                    return 'Please select a building';
                                  }
                                  return null;
                                },
                                decoration: InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    labelStyle: TextStyle(fontSize: 12),
                                    labelText: 'Building',
                                    icon: Icon(Icons.build),
                                    filled: true,
                                    fillColor: Colors.white70,
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(12.0)),
                                      borderSide: BorderSide(
                                          color:
                                              CommonUtils.createMaterialColor(
                                                  Color(0XFFC0C0C0)),
                                          width: 2),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(10.0)),
                                      borderSide: BorderSide(
                                          color:
                                              CommonUtils.createMaterialColor(
                                                  Color(0XFFC0C0C0))),
                                    )),
                              );
                            }),*/
                        TextFormField(
                          readOnly: true,
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please select building';
                            }
                            return null;
                          },
                          controller: _buildingNameController,
                          style: const TextStyle(fontSize: 14),
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            labelStyle: const TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                            labelText: 'Building',
                            prefixIcon:
                                Icon(Icons.business, color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                        ),
                        /* Consumer<ReservationProvider>(builder:
                                (BuildContext context, resvProvider,
                                    Widget child) {
                              return DropdownButtonFormField(
                                hint: Text('Select a Floor',
                                    style: TextStyle(fontSize: 12)),
                                onSaved: (val) => _floorname = val,
                                value: _reservationObj.reservationId != null
                                    ? _initValues['floorId']
                                    : _floorname,
                                items: floorLov.map<DropdownMenuItem<int>>(
                                    (Floor dropDownStringItem) {
                                  print('Generating Floor $dropDownStringItem');
                                  return DropdownMenuItem<int>(
                                    value: dropDownStringItem.floorId,
                                    child: Text(dropDownStringItem.floorName,
                                        style: TextStyle(fontSize: 12)),
                                  );
                                }).toList(),
                                onChanged: (val) {
                                  setState(() {
                                    print('Floor value $val');
                                    _floorname = val;

                                    spaceLov = Provider.of<ReservationProvider>(
                                            context,
                                            listen: false)
                                        .getSpace(
                                            context,
                                            _buildingname,
                                            _floorname,
                                            _startdateController.text,
                                            _enddateController.text);
                                  });
                                },
                                validator: (value) {
                                  if (value == null) {
                                    return 'Please select a floor';
                                  }
                                  return null;
                                },
                                decoration: InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    labelStyle: TextStyle(fontSize: 12),
                                    labelText: 'Floor',
                                    icon: Icon(Icons.build_circle),
                                    filled: true,
                                    fillColor: Colors.white70,
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(12.0)),
                                      borderSide: BorderSide(
                                          color:
                                              CommonUtils.createMaterialColor(
                                                  Color(0XFFC0C0C0)),
                                          width: 2),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(10.0)),
                                      borderSide: BorderSide(
                                          color:
                                              CommonUtils.createMaterialColor(
                                                  Color(0XFFC0C0C0))),
                                    )),
                              );
                            }),*/
                        Container(
                          color: Colors.white,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Flexible(
                                child: TextFormField(
                                  readOnly: true,
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return 'Please select floor';
                                    }
                                    return null;
                                  },
                                  controller: _floorNameController,
                                  style: const TextStyle(fontSize: 14),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    labelStyle:
                                        const TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                    labelText: 'Floor',
                                    prefixIcon: Icon(Icons.apartment_outlined,
                                        color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                                    filled: true,
                                    fillColor: Colors.white,
                                  ),
                                ),
                              ),
                              Visibility(
                                visible: _floorname != null,
                                child: Container(
                                  color: Colors.white,
                                  child: GestureDetector(
                                    onTap: () {
                                      CommonUtils.reservationCadViewer(
                                          context,
                                          _buildingname.toString(),
                                          _floorname.toString(),
                                          _spacename.toString(),
                                          _dateController.text,
                                          _dateController1.text);
                                    },
                                    child: const Align(
                                        alignment: Alignment.centerRight,
                                        child: Padding(
                                          padding: EdgeInsets.all(5.0),
                                          child: Image(image: AssetImage('lib/icons/plan-icon.png')),
                                        )),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        /*  Consumer<ReservationProvider>(builder:
                                (BuildContext context, resvProvider,
                                    Widget child) {
                              return FutureBuilder<List<Space>>(
                                  future: spaceLov,
                                  builder: (context, AsyncSnapshot snapshot) {
                                    if (snapshot.data == null) {
                                      return DropdownButtonFormField(
                                          hint: Text('Select a Space',
                                              style: TextStyle(fontSize: 12)),
                                          value: null,
                                          items: [
                                            DropdownMenuItem<int>(
                                              value: null,
                                              child: Text('Select a space',
                                                  style:
                                                      TextStyle(fontSize: 12)),
                                            )
                                          ],
                                          validator: (value) {
                                            print('Validing space val $value');
                                            if (value == null) {
                                              return 'Please select a space';
                                            }
                                            return null;
                                          },
                                          decoration: InputDecoration(
                                              floatingLabelBehavior:
                                                  FloatingLabelBehavior.always,
                                              labelText: 'Space',
                                              icon: Icon(Icons.shop),
                                              filled: true,
                                              fillColor: Colors.white70,
                                              enabledBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(12.0)),
                                                borderSide: BorderSide(
                                                    color: CommonUtils
                                                        .createMaterialColor(
                                                            Color(0XFFC0C0C0)),
                                                    width: 2),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(10.0)),
                                                borderSide: BorderSide(
                                                    color: CommonUtils
                                                        .createMaterialColor(
                                                            Color(0XFFC0C0C0))),
                                              )));
                                    } else {
                                      // print(
                                      //     'Inside items $_spacename ${_reservationObj.requestId}');
                                      return DropdownButtonFormField(
                                        hint: Text('Select a Space',
                                            style: TextStyle(fontSize: 12)),
                                        onSaved: (val) => _spacename = val,
                                        value: _reservationObj.reservationId !=
                                                null
                                            ? _initValues['spaceId']
                                            : _spacename,
                                        items: snapshot.data
                                            .map<DropdownMenuItem<int>>(
                                                (Space x) {
                                          //  print(' ${x.spaceId}');
                                          return DropdownMenuItem<int>(
                                            value: x.spaceId,
                                            child: Text('${x.spaceDisplayName}',
                                                style: TextStyle(fontSize: 12)),
                                          );
                                        }).toList(),
                                        onChanged: (val) {
                                          setState(() {
                                            _spacename = val;
                                          });
                                        },
                                        validator: (value) {
                                          print('Validing space va; $value');
                                          if (value == null) {
                                            return 'Please select a space';
                                          }
                                          return null;
                                        },
                                        decoration: InputDecoration(
                                            floatingLabelBehavior:
                                                FloatingLabelBehavior.always,
                                            labelStyle: TextStyle(fontSize: 12),
                                            labelText: 'Space',
                                            icon: Icon(Icons.shop),
                                            filled: true,
                                            fillColor: Colors.white70,
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(12.0)),
                                              borderSide: BorderSide(
                                                  color: CommonUtils
                                                      .createMaterialColor(
                                                          Color(0XFFC0C0C0)),
                                                  width: 2),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(10.0)),
                                              borderSide: BorderSide(
                                                  color: CommonUtils
                                                      .createMaterialColor(
                                                          Color(0XFFC0C0C0))),
                                            )),
                                      );
                                    }
                                  });
                            }),*/
                        Container(
                          color: Colors.white,
                          child: Row(children: [
                            Flexible(
                              child: TextFormField(
                                onTap: () => Navigator.of(context).pushNamed(
                                  FindDetails.routName,
                                  arguments: FindDetArgmnts('Space', _spacename),
                                ),
                                readOnly: true,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Please select space';
                                  }
                                  return null;
                                },
                                controller: _spaceNameController,
                                style: const TextStyle(fontSize: 14),
                                decoration: InputDecoration(
                                  labelStyle:
                                      const TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold),
                                  /* suffixIcon: IconButton(
                                          onPressed: () {
                                            // if (_formKey.currentState.validate()) {
                                            print('inside1');
                                            fetchSpace(
                                                context,
                                                _buildingname,
                                                _floorname,
                                                _dateController.text,
                                                _dateController1.text);
                                            //}
                                          },
                                          icon: Icon(Icons.arrow_forward_ios),
                                        ),*/
                                  border: InputBorder.none,
                                  labelText: 'Space',
                                  prefixIcon: Icon(Icons.workspaces_filled,
                                      color: CommonUtils.createMaterialColor(const Color(0XFFb10c00))),
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                              ),
                            ),
                            // SizedBox(width: 10),
                            if (_spaceAttributes != null)
                              Container(
                                color: Colors.white,
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: _spaceAttributes!
                                        .map((item) => Padding(
                                              padding: const EdgeInsets.all(8.0),
                                              child: Image(
                                                width: 32,
                                                height: 32,
                                                image: AssetImage('lib/icons/$item.png'),
                                              ),
                                            ))
                                        .toList()),
                              ),
                          ]),
                        ),
                        /*   Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 10.0),
                              child: ElevatedButton(
                                  style: ButtonStyle(backgroundColor:
                                      MaterialStateProperty.resolveWith<Color>(
                                    (Set<MaterialState> states) {
                                      if (states
                                          .contains(MaterialState.pressed))
                                        return CommonUtils.createMaterialColor(
                                            Color(0XFF394251));
                                      return CommonUtils.createMaterialColor(Color(
                                          0XFF394251)); // Use the component's default.
                                    },
                                  )),
                                  onPressed: _saveForm,
                                  child: Text(
                                    'Save',
                                    style: TextStyle(fontSize: 12),
                                  )),
                            )*/
                        const SizedBox(
                          height: 2.0,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TaButton(
                              type: 'elevate',
                              buttonText: 'Back',
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                            const SizedBox(
                              width: 2.0,
                            ),
                            Visibility(
                                visible: bookFlag,
                                child: TaButton(
                                  type: 'elevate',
                                  buttonText: 'Book Now',
                                  onPressed: _saveForm,
                                )),
                          ],
                        ),
                        const SizedBox(
                          height: 1.0,
                        ),
                      ]),
                    )),
              ),
            ),
          )
        ]));
  }

  void fetchProperty(BuildContext context, String startdate, String enddate, {var requestId, var d1, var d2}) async {
    RecentReservations? p =
        await Navigator.pushNamed(context, PropertyScreen.routName, arguments: [startdate, enddate, requestId, d1, d2])
            as RecentReservations?;
    if (p != null) {
      _properyNameController.text = p.propertyname!;
      _propertyname = p.propertyid;
      _buildingname = p.buildingid;
      _floorname = p.floorid;
      _buildingNameController.text = p.buildingname!;
      _floorNameController.text = p.floorname!;
      _spaceNameController.text = p.spaceDisplayName!;
      _spacename = p.spaceid;
      _spacedisplayname = p.spaceDisplayName;
      _spaceAttributes = p.spaceAttributes;
      debugPrint('space attributes  $_spaceAttributes');
      setState(() {});
      //  _reservationNameController.text = _reservationNameController.text +
      //     '.$_buildingname' +
      //    '.' +
      //   '$_floorname';
    }
  }

  void fetchSpace(BuildContext context, int buildingid, int floorid, String startdate, String enddate) async {
    startdate = _startdateController.text;
    enddate = _enddateController.text;
    Space? p = await Navigator.pushNamed(context, SpaceSearch.routName,
        arguments: [startdate, enddate, buildingid, floorid, requestId]) as Space?;
    if (p != null) {
      _spaceNameController.text = p.spaceDisplayName!;
      _spacename = p.spaceId;
      _spacedisplayname = p.spaceDisplayName;
      //   _reservationNameController.text =
      //      _reservationNameController.text + '.$_spacename';
    }
  }

  void fetchAssignedToList(BuildContext context) async {
    List? assignedUsersList = await Navigator.pushNamed(context, AssignedUserSearch.routName,
        arguments: [_assignedToController.text, assignedPersonId]) as List?;

    if (assignedUsersList != null && assignedUsersList.isNotEmpty) {
      _assignedToController.text = assignedUsersList[0];

      assignedPersonId = assignedUsersList[1];
      print(assignedPersonId);
    }
  }
}
