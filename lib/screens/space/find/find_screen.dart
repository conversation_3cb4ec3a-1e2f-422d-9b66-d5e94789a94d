import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/spacemgmt/findperson_data.dart';
import 'package:tangoworkplace/models/spacemgmt/findspace_data.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:toggle_switch/toggle_switch.dart';

import '../../../common/component_utils.dart';
import '../../../providers/spacemanagement/find/find_controller.dart';
import 'find_detail_screen.dart';

class Find extends StatelessWidget {
  Find({
    super.key,
  });
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  FindController findState = Get.put(FindController());

  FindController findCtrlr = Get.find<FindController>();
  LabelController talabel = Get.find<LabelController>();

  Future _refreshSites() async {
    await findCtrlr.getfindData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: TaSearchInputText(
            plachold: 'appbar',
            height: 30.0,
            makeSearch: (searchtext) async {
              await findCtrlr.getfindData();
            },
            searchController: findCtrlr.searchController,
            hintSearch: 'Search '),

        // Text(
        //   talabel.get('TMCMOBILE_HOME_FIND')!.value!,
        //   style: ComponentUtils.appbartitlestyle,
        // ),
        // actions: [
        //   IconButton(
        //       onPressed: () async {
        //         // Get.to(() => SiteFilterWidget());
        //         // siteSearchCtrlr.filterwidgetloading.value = true;
        //         // await talabel.getlabels('TMCMOBILE_SITESEARCH', 'Site_search', filtertype: 'tab');
        //         // Future.delayed(const Duration(seconds: 1), () async {
        //         //   siteSearchCtrlr.filterwidgetloading.value = false;
        //         // });
        //       },
        //       icon: Icon(
        //         Icons.search,
        //         color: ComponentUtils.primecolor,
        //       )),
        // ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          //filterChipContainer(),
          Obx(
            () => Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  margin: const EdgeInsets.only(left: 15, top: 10, bottom: 10),
                  child: ToggleSwitch(
                    minWidth: 100.0,
                    minHeight: 30.0,
                    customTextStyles: const [
                      TextStyle(fontWeight: FontWeight.bold),
                      TextStyle(fontWeight: FontWeight.bold),
                    ],
                    fontSize: ComponentUtils.getFontSize(1.3),
                    radiusStyle: true,
                    cornerRadius: 20.0,
                    initialLabelIndex: findCtrlr.findtype.value,
                    activeBgColor: [ComponentUtils.primecolor],
                    activeFgColor: Colors.white,
                    inactiveBgColor: Colors.white60,
                    inactiveFgColor: Colors.grey[900],
                    totalSwitches: 2,
                    labels: const ['Spaces', 'Persons'],
                    onToggle: (index) async {
                      findCtrlr.findtype.value = index ?? 0;
                      await findCtrlr.getfindData();
                    },
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshSites,
              child: GetX<FindController>(
                initState: (state) async {
                  findCtrlr.isLoading(true);
                  //await talabel.getlabels('TMCMOBILE_SITESEARCH', 'Site_search', filtertype: 'tab');
                  findCtrlr.getfindData(action: 'onload');
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : findCtrlr.findtype.value == 0
                          ? _.spacelist.isEmpty
                              ? Center(
                                  child:
                                      Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                              : ListView.builder(
                                  itemBuilder: (context, index) {
                                    return spacelistitem(context, _.spacelist[index]);
                                  },
                                  itemCount: _.spacelist.length,
                                )
                          : _.personlist.isEmpty
                              ? Center(
                                  child:
                                      Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                              : ListView.builder(
                                  itemBuilder: (context, index) {
                                    return personlistitem(context, _.personlist[index]);
                                  },
                                  itemCount: _.personlist.length,
                                );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterChipContainer() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.only(left: 12, right: 10, top: 5, bottom: 2),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Wrap(
                alignment: WrapAlignment.start,
                spacing: 6.0,
                runSpacing: 6.0,
                children: List<Widget>.generate(findCtrlr.filterList.length, (int index) {
                  return findCtrlr.filterList[index];
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget spacelistitem(BuildContext context, FindSpaceData s) {
    const primary = Color(0xff696b9e);
    const secondary = Color(0xfff29a94);

    return GestureDetector(
      onTap: () async {
        Navigator.of(context).pushNamed(
          FindDetails.routName,
          arguments: FindDetArgmnts('Space', s.spaceId),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    s.spaceDisplayName ?? '',
                    style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  const SizedBox(
                    height: 6,
                  ),
                  Text(
                    'Space Use: ${s.spaceUse ?? ''}',
                    style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3),
                  ),
                  const SizedBox(
                    height: 6,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Row(children: [
                        Icon(
                          Icons.apartment,
                          color: secondary,
                          size: 15,
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Text(s.buildingName ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                      ]),
                    ],
                  ),

                  // Expanded(
                  //   child: Text('${s.address ?? ''}' + ' ' + '${s.city ?? ''}' + ' ' + '${s.state ?? ' '}',
                  //       overflow: TextOverflow.ellipsis,
                  //       style: TextStyle(
                  //         color: primary,
                  //         fontSize: 12,
                  //         letterSpacing: .1,
                  //       )),
                  // ),
                ],
              ),
            ),
            IconButton(
              alignment: Alignment.centerRight,
              padding: EdgeInsets.zero,
              tooltip: 'view floorplan',
              icon: const Image(image: AssetImage('lib/icons/plan-icon.png')),
              iconSize: 35,
              color: CommonUtils.createMaterialColor(const Color(0XFF394251)),
              onPressed: () {
                CommonUtils.findCadviewer(context,
                    action: 'find',
                    path: s.isFlexibleSeating != 'Y'
                        ? '?floorId=${s.floorId}&buildingId=${s.buildingId}&spaceId=${s.spaceId}'
                        : s.isFlexibleSeating == 'Y'
                            ? '?floorId=${s.floorId}&buildingId=${s.buildingId}&areaId=${s.spaceAreaId}'
                            : '');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget personlistitem(BuildContext context, FindPersonData s) {
    const primary = Color(0xff696b9e);
    const secondary = Color(0xfff29a94);

    return GestureDetector(
        onTap: () async {
          Navigator.of(context).pushNamed(
            FindDetails.routName,
            arguments: FindDetArgmnts('Person', s.personId),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${s.lastName ?? ''} ${s.firstName ?? ''}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.person_pin,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(s.location?.toString() ?? '',
                              style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                      ],
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.shield,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(s.departmentName?.toString() ?? '',
                              style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                      ],
                    ),

                    // Expanded(
                    //   child: Text('${s.address ?? ''}' + ' ' + '${s.city ?? ''}' + ' ' + '${s.state ?? ' '}',
                    //       overflow: TextOverflow.ellipsis,
                    //       style: TextStyle(
                    //         color: primary,
                    //         fontSize: 12,
                    //         letterSpacing: .1,
                    //       )),
                    // ),
                  ],
                ),
              ),
              if (!(s.locationType?.toUpperCase() == 'SPACE' && (s.spaceId == null || s.spaceId == '')))
                IconButton(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.zero,
                  tooltip: 'view floorplan',
                  icon: const Image(image: AssetImage('lib/icons/plan-icon.png')),
                  iconSize: 35,
                  color: CommonUtils.createMaterialColor(const Color(0XFF394251)),
                  onPressed: () {
                    CommonUtils.findCadviewer(context,
                        action: 'find',
                        path: (s.locationType?.toUpperCase() == 'SPACE' && (s.spaceId != null || s.spaceId != ''))
                            ? '?floorId=${s.floorId}&buildingId=${s.buildingId}&spaceId=${s.spaceId}'
                            : s.locationType?.toUpperCase() == 'AREA'
                                ? '?floorId=${s.floorId}&buildingId=${s.buildingId}&areaId=${s.spaceId}'
                                : '');
                  },
                ),
            ],
          ),
        ));
  }
}
