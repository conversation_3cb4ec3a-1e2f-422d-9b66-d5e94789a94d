import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:tangoworkplace/common/common_import.dart';
import '../../../common/component_utils.dart';
import 'find_detail_screen.dart';
import '../../../models/find.dart';
import '../../../providers/find_provider.dart';
import '../../home/<USER>';
import '../../../providers/reservation_provider.dart';
import '../../../models/comments.dart';

class FindScreen extends StatefulWidget {
  static const routName = '/find';

  const FindScreen({super.key});

  @override
  _FindScreenState createState() => _FindScreenState();
}

class _FindScreenState extends State<FindScreen> {
  int selectedIndex = 0; //to handle which item is currently selected in the bottom app bar
  var _isPerLoading = false;
  var _isInit = true;
  var sessionid;

  final _searchController = TextEditingController();
  //findperson filters
  final _nameController = TextEditingController();
  final _deptController = TextEditingController();
  //findspaces filters
  final _bildingNameController = TextEditingController();
  final _floorNameController = TextEditingController();
  final _spaceNumController = TextEditingController();
  final _spaceTypeController = TextEditingController();
  final _spaceNameController = TextEditingController();
  //Scroll controllers
  final _findPersonScrollCtrl = ScrollController();
  final _findSpacesScrollCtrl = ScrollController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    //findperson scroll
    _findPersonScrollCtrl.addListener(() {
      if (_findPersonScrollCtrl.position.pixels == _findPersonScrollCtrl.position.maxScrollExtent) {
        Provider.of<FindProvider>(context, listen: false)
            .getMoreFindPersonsData(_searchController.text, _nameController.text, _deptController.text, loadMore: true);
      }
    });
    //findspaces scroll
    _findSpacesScrollCtrl.addListener(() {
      if (_findSpacesScrollCtrl.position.pixels == _findSpacesScrollCtrl.position.maxScrollExtent) {
        Provider.of<FindProvider>(context, listen: false).getMoreFindSpacesData(
            _searchController.text,
            _bildingNameController.text,
            _floorNameController.text,
            _spaceNumController.text,
            _spaceTypeController.text,
            _spaceNameController.text,
            loadMore: true);
      }
    });
  }

  @override
  void didChangeDependencies() async {
    if (_isInit) {
      setState(() {
        _isPerLoading = true;
      });
      sessionid = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
      Provider.of<FindProvider>(context, listen: false).getFindPersonsData('', '', '').then((_) {
        setState(() {
          _isPerLoading = false;
        });
      });
    }
    _isInit = false;
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _nameController.dispose();
    _deptController.dispose();
    _bildingNameController.dispose();
    _floorNameController.dispose();
    _spaceNumController.dispose();
    _spaceTypeController.dispose();
    _spaceNameController.dispose();
    _findPersonScrollCtrl.dispose();
    _findSpacesScrollCtrl.dispose();

    super.dispose();
  }

  clearFilterinputs(String type) {
    if (type == 'spaces') {
      _bildingNameController.text = '';
      _floorNameController.text = '';
      _spaceNumController.text = '';
      _spaceTypeController.text = '';
      _spaceNameController.text = '';
    } else if (type == 'persons') {
      _nameController.text = '';
      _deptController.text = '';
    }
  }

  _cadviewer(String buildingid, String floorid, String spaceid) {
    var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
    var host = ApiService.getServerurl();
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => WebViewContainer(
                '$cadViewerurl?floorId=$floorid&spaceId=$spaceid&buildingId=$buildingid&action=MAC&JSESSIONID=$sessionid',
                'View Floor Plan',
                host)));
  }

  @override
  Widget build(BuildContext context) {
    final findProvState = Provider.of<FindProvider>(context, listen: true);
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.miniCenterDocked,
      appBar: AppBar(
        actions: [
          IconButton(
            onPressed: () {
              debugPrint('find type  ${findProvState.isTab}');

              if (findProvState.isTab == 'persons') {
                clearFilterinputs('persons');
                findPersonsFilter(findProvState);
              } else {
                clearFilterinputs('spaces');
                findSpacesFilter(findProvState);
              }
            },
            iconSize: 27.0,
            icon: Icon(
              Icons.filter_alt,
              color: selectedIndex == 0
                  ? CommonUtils.createMaterialColor(const Color(0XFFb10c00))
                  : CommonUtils.createMaterialColor(const Color(0XFF394251)),
            ),
          ),
        ],
        // iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: SearchInputText(
            makeSearch: (data) {
              if (findProvState.isTab != 'spaces') {
                clearFilterinputs('persons');
                findProvState.getFindPersonsData(data.toString(), '', '');
              } else {
                clearFilterinputs('spaces');
                findProvState.getFindSpacesData(data.toString(), '', '', '', '', '');
              }
            },
            searchController: _searchController,
            hintSearch: 'Find...'),

        //Text('Find ', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
        // ),
      ),
      key: _scaffoldKey,
      body: Column(children: <Widget>[
        Expanded(
          child: DefaultTabController(
            initialIndex: 0,
            length: 2,
            child: Column(
              children: <Widget>[
                Material(
                  elevation: 0,
                  color: Colors.white,
                  child: Container(
                    child: TabBar(
                      labelColor: ComponentUtils.tablabelcolor,
                      unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                      indicatorColor: ComponentUtils.tabindicatorColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.normal,
                          fontSize: 14,
                          color: HexColor('#3953A4')),
                      onTap: (index) {
                        if (index == 1) {
                          findProvState.setTab('spaces');
                          findProvState.getFindSpacesData(_searchController.text, '', '', '', '', '');
                        } else if (index == 0) {
                          findProvState.setTab('persons');
                          findProvState.getFindPersonsData(_searchController.text, '', '');
                        }
                      },
                      tabs: [
                        Tab(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('Persons  ',
                                  style: TextStyle(
                                    fontSize: DeviceUtils.taFontSize(1.6, context),
                                    fontWeight: FontWeight.bold,
                                  )),
                              if (findProvState.findPersonsCnt != null && findProvState.findPersonsCnt! > 0)
                                Text('(${findProvState.findPersonsCnt})'),
                            ],
                          ),
                        ),
                        Tab(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('Spaces  ',
                                  style: TextStyle(
                                    fontSize: DeviceUtils.taFontSize(1.6, context),
                                    fontWeight: FontWeight.bold,
                                  )),
                              if (findProvState.findSpacesCnt != null && findProvState.findSpacesCnt! > 0)
                                Text('(${findProvState.findSpacesCnt})'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      Stack(children: <Widget>[
                        Positioned(
                          top: 00,
                          right: 0,
                          left: 0,
                          bottom: 0,
                          child: ClipRRect(
                              //borderRadius: BorderRadius.circular(9),
                              child: Container(
                            // color: Colors.white,
                            child: !_isPerLoading
                                ? Consumer<FindProvider>(builder: (BuildContext context, findProvState, Widget? child) {
                                    return Builder(builder: (context) {
                                      if (!findProvState.isLoading) {
                                        debugPrint('Persons list Build');
                                        return findProvState.findPersonsList != null &&
                                                findProvState.findPersonsList!.isNotEmpty
                                            ? ListView.builder(
                                                controller: _findPersonScrollCtrl,
                                                itemBuilder: (context, index) {
                                                  if (index == findProvState.findPersonsList!.length) {
                                                    return const ProgressIndicatorCust();
                                                  }
                                                  return findPersonListTile(findProvState.findPersonsList![index]);
                                                },
                                                itemCount: findProvState.findPersonsList!.length +
                                                    (findProvState.hasMore ? 1 : 0),
                                              )
                                            : Center(
                                                child: Text('No Persons Data',
                                                    style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                                              );
                                      } else {
                                        return const ProgressIndicatorCust();
                                      }
                                    });
                                    // By default, show a loading spinner.
                                  })
                                : const ProgressIndicatorCust(),
                          )),
                        ),
                      ]),
                      Stack(children: <Widget>[
                        Positioned(
                          top: 00,
                          right: 0,
                          left: 0,
                          bottom: 0,
                          child: ClipRRect(
                            // borderRadius: BorderRadius.circular(9),
                            child: Container(
                                //color: Colors.white,
                                child: Consumer<FindProvider>(
                              builder: (BuildContext context, fdProvider, Widget? child) {
                                return Builder(builder: (context) {
                                  if (!findProvState.isLoading) {
                                    debugPrint('Inside spaces');
                                    return findProvState.findSpacesList != null &&
                                            findProvState.findSpacesList!.isNotEmpty
                                        ? ListView.builder(
                                            controller: _findSpacesScrollCtrl,
                                            itemBuilder: (context, index) {
                                              if (index == findProvState.findSpacesList?.length) {
                                                return const ProgressIndicatorCust();
                                              }
                                              return findSpaceListTile(fdProvider.findSpacesList![index]);
                                            },
                                            itemCount:
                                                findProvState.findSpacesList!.length + (findProvState.hasMore ? 1 : 0),
                                          )
                                        : Center(
                                            child: Text('No Spaces Data',
                                                style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))),
                                          );
                                  } else {
                                    return const ProgressIndicatorCust();
                                  }
                                });
                              },
                            )
                                // By default, show a loading spinner.
                                ),
                          ),
                        ),
                      ]),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ]),
    );
  }

  showPictures(int? spaceid, String? location) {
    int currentIndex = 0;
    bool isPicturesLoaded = false;
    final reservProvider = Provider.of<ReservationProvider>(context, listen: false);

    reservProvider.getPictures(context, spaceid, 'SPACE').then((_) {
      setState(() {
        isPicturesLoaded = true;
      });
    });
    showModalBottomSheet(
        // isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: CommonUtils.createMaterialColor(Colors.white),
        context: context,
        builder: (BuildContext context) {
          return GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);

              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: SingleChildScrollView(
              child: ClipRRect(
                  child: Container(
                padding: const EdgeInsets.all(10),
                height: MediaQuery.of(context).size.height / 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Photos of $location',
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.blueGrey)),
                    const SizedBox(height: 5),
                    Expanded(
                      child: Consumer<ReservationProvider>(
                        builder: (BuildContext context, resvProvider, Widget? child) {
                          return Builder(builder: (context) {
                            if (isPicturesLoaded) {
                              print('Inside pictures build');
                              return CarouselSlider(
                                  options: CarouselOptions(
                                      // height: 200.0,
                                      autoPlay: true,
                                      autoPlayInterval: const Duration(seconds: 3),
                                      autoPlayAnimationDuration: const Duration(milliseconds: 800),
                                      autoPlayCurve: Curves.fastOutSlowIn,
                                      pauseAutoPlayOnTouch: true,
                                      //aspectRatio: 2.0,
                                      onPageChanged: (index, reason) {
                                        setState(() {
                                          currentIndex = index;
                                        });
                                      }),
                                  items: reservProvider.picturesList!.map((p) {
                                    return Builder(builder: (BuildContext context) {
                                      return SizedBox(
                                        height: MediaQuery.of(context).size.height * 0.30,
                                        width: MediaQuery.of(context).size.width,
                                        child: Card(
                                          color: Colors.white70,
                                          child: ComponentUtils.apiImage(p.picId, height: 200.0, width: 200.0),
                                        ),
                                      );
                                    });
                                  }).toList());
                            } else {
                              return const ProgressIndicatorCust();
                            }
                          });
                        },
                      ),
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  Widget findSpaceListTile(FindSpace? fs) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,

      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 15),
      //padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      child: Column(
        children: <Widget>[
          // Widget to display the list of project
          ListTile(
            // trailing: GestureDetector(
            //   onTap: () {
            //     showPictures(fs.spaceId, fs.spaceDisplayName);
            //   },
            //   child: FadeInImage(
            //       image: NetworkImage(
            //           'https://${SharedPrefUtils.readPrefStr(hostNameVar)}/tamobilerestservices/resources/rest/1.0/getimage?picId=${fs.picId}',
            //           headers: SharedPrefUtils.getHeaders()),
            //       placeholder: AssetImage('lib/icons/no_image_icon.gif')),
            // ),
            // contentPadding: EdgeInsets.fromLTRB(10, 0, 10, 0),
            key: UniqueKey(),
            onTap: () {
              Navigator.of(context).pushNamed(
                FindDetails.routName,
                arguments: FindDetArgmnts('Space', fs?.spaceId),
              );
            },
            focusColor: Colors.blueGrey,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text('${fs?.buildingName}-${fs?.floorName}',
                              style: TextStyle(
                                  color: primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: DeviceUtils.taFontSize(1.6, context))),
                          const SizedBox(height: 4.0, width: 0),
                          Text('Space    ${fs?.spaceDisplayName}',
                              style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.4, context))),
                          const SizedBox(
                            height: 4.0,
                          ),
                          Text('Space Use      ${fs?.spaceUse}',
                              style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.4, context))),
                          const SizedBox(
                            height: 4.0,
                          ),
                          Text('Type                ${fs?.spaceType}',
                              style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.4, context))),
                          const SizedBox(
                            height: 0.0,
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: <Widget>[
                        IconButton(
                          alignment: Alignment.centerRight,
                          padding: EdgeInsets.zero,
                          tooltip: 'view floorplan',
                          icon: const Image(image: AssetImage('lib/icons/plan-icon.png')),
                          iconSize: 35,
                          color: CommonUtils.createMaterialColor(const Color(0XFF394251)),
                          //onPressed: () {},
                          onPressed: () =>
                              //  CommonUtils.webviewviewer(
                              //     pagename: 'View Floor Plan',
                              //     source: 'find',
                              //     path: '?floorId=${fs?.floorId}&spaceId=${fs?.spaceId}&buildingId=${fs?.buildingId}'),
                              CommonUtils.cadviewer(context, '${fs?.buildingId}', '${fs?.floorId}', '${fs?.spaceId}',
                                  action: 'find'),
                        ),
                        // GestureDetector(
                        //   onTap: () {
                        //     showPictures(fs?.spaceId, fs?.spaceDisplayName);
                        //   },
                        //   child: ComponentUtils.apiImage(fs?.picId, height: 50.0, width: 50.0),
                        // ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            subtitle: GestureDetector(
              onTap: () {
                if (fs?.noOfReviews != 0) showCommentsList(fs?.spaceId, '${fs?.spaceDisplayName}');
              },
              child: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text('${fs?.noOfReviews ?? 'No'} Reviews',
                    style: TextStyle(
                      decoration: fs?.noOfReviews != 0 ? TextDecoration.underline : TextDecoration.none,
                      fontSize: 12.0,
                      color: Colors.blueGrey,
                    )),
              ),
            ),
          ),
        ],
      ),
    );
  }

  showCommentsList(int? spaceid, String location) {
    bool isCommentsLoaded = false;
    final reservProvider = Provider.of<ReservationProvider>(context, listen: false);
    //spaceid = 95369;
    reservProvider.getComments(context, spaceid, 'SPACE').then((_) {
      setState(() {
        isCommentsLoaded = true;
      });
    });
    showModalBottomSheet(
        // isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: CommonUtils.createMaterialColor(Colors.white),
        context: context,
        builder: (BuildContext context) {
          return GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);

              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: SingleChildScrollView(
              child: ClipRRect(
                  child: Container(
                padding: const EdgeInsets.all(10),
                height: 250,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Reviews for $location',
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.blueGrey)),
                    const SizedBox(height: 5),
                    Expanded(
                      child: Consumer<ReservationProvider>(
                        builder: (BuildContext context, resvProvider, Widget? child) {
                          return Builder(builder: (context) {
                            if (isCommentsLoaded) {
                              print('Inside comments build');
                              return MediaQuery.removePadding(
                                removeTop: true,
                                removeBottom: true,
                                context: context,
                                child: ListView.separated(
                                  padding: EdgeInsets.zero,
                                  itemCount:
                                      reservProvider.commentsList == null ? 0 : reservProvider.commentsList!.length,
                                  itemBuilder: (context, index) {
                                    Comments c = reservProvider.commentsList![index];
                                    return ListTile(
                                        contentPadding: const EdgeInsets.only(top: 0.0, bottom: 0.0, left: 8, right: 8),
                                        key: UniqueKey(),
                                        focusColor: Colors.blueGrey,
                                        title: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                                              Icon(
                                                Icons.person,
                                                size: 25,
                                                color: Colors.grey[400],
                                              ),
                                              const SizedBox(width: 5),
                                              const SizedBox(
                                                width: 5.0,
                                              ),
                                              Align(
                                                alignment: Alignment.topRight,
                                                child: Text(c.reviewedBy!,
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                    )),
                                              ),
                                            ]),
                                            const SizedBox(
                                              height: 5.0,
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: [
                                                Icon(
                                                  Icons.timer_sharp,
                                                  size: 25,
                                                  color: Colors.grey[400],
                                                ),
                                                const SizedBox(
                                                  width: 8,
                                                ),
                                                Text(c.reviewDate!,
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                    )),
                                              ],
                                            ),
                                            const SizedBox(
                                              height: 5.0,
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: [
                                                Text(c.comments!,
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                    )),
                                              ],
                                            ),
                                          ],
                                        ));
                                  },
                                  separatorBuilder: (BuildContext context, int index) => Divider(
                                      indent: 5,
                                      endIndent: 5,
                                      thickness: 2,
                                      color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0))),
                                ),
                              );
                            } else {
                              return const ProgressIndicatorCust();
                            }
                          });
                        },
                      ),
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  Widget findPersonListTile(FindPerson fp) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,

      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 15),
      //padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      child: Column(
        children: <Widget>[
          // Widget to display the list of project
          ListTile(
            key: UniqueKey(),
            onTap: () {
              Navigator.of(context).pushNamed(
                FindDetails.routName,
                arguments: FindDetArgmnts('Person', fp.personId),
              );
            },
            focusColor: Colors.blueGrey,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text('${fp.lastName} ${fp.firstName}',
                              style: TextStyle(
                                  color: primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: DeviceUtils.taFontSize(1.6, context))),
                          const SizedBox(height: 4.0, width: 0),
                          Text('Location     ${fp.location}',
                              style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.4, context))),
                          const SizedBox(height: 4.0, width: 0),
                          Text('Dept            ${(fp.departmentName)}',
                              style: TextStyle(color: primary, fontSize: DeviceUtils.taFontSize(1.4, context))),
                          const SizedBox(
                            height: 0.0,
                          ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.start,
                          //   children: [
                          //     Text.rich(TextSpan(
                          //       text: 'View',
                          //       style: TextStyle(
                          //         decoration: TextDecoration.underline,
                          //         color: CommonUtils.createMaterialColor(
                          //             Color(0XFF00B8D4)),
                          //       ),
                          //     )),
                          //     Text('  |  ',
                          //         style: TextStyle(
                          //           fontSize: 14.0,
                          //           fontWeight: FontWeight.bold,
                          //         )),
                          //     Text.rich(TextSpan(
                          //       text: 'Details',
                          //       style: TextStyle(
                          //         decoration: TextDecoration.underline,
                          //         color: CommonUtils.createMaterialColor(
                          //             Color(0XFF00B8D4)),
                          //       ),
                          //     )),
                          //   ],
                          // )
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        IconButton(
                            alignment: Alignment.centerRight,
                            padding: EdgeInsets.zero,
                            tooltip: 'view floorplan',
                            icon: const Image(image: AssetImage('lib/icons/plan-icon.png')),
                            iconSize: 35,
                            color: CommonUtils.createMaterialColor(const Color(0XFF394251)),
                            //onPressed: () {},
                            onPressed: () =>
                                // CommonUtils.webviewviewer(
                                //     pagename: 'View Floor Plan',
                                //     source: 'find',
                                //     path: '?floorId=${fp.floorId}&spaceId=${fp.spaceId}&buildingId=${fp.buildingId}')),

                                CommonUtils.cadviewer(context, '${fp.buildingId}', '${fp.floorId}', '${fp.spaceId}',
                                    action: 'find')),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  findPersonsFilter(FindProvider fp) {
    showModalBottomSheet(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: Colors.white,
        context: context,
        builder: (BuildContext context) {
          return ClipRRect(
            child: Container(
              padding: const EdgeInsets.only(top: 15.0),
              height: 250,
              child: ClipRRect(
                  child: SingleChildScrollView(
                child: Column(crossAxisAlignment: CrossAxisAlignment.end, children: <Widget>[
                  InputTextField(
                    title: 'Name',
                    controller: _nameController,
                    textInputAct: TextInputAction.done,
                  ),
                  InputTextField(
                    title: 'Dept',
                    controller: _deptController,
                    textInputAct: TextInputAction.done,
                  ),
                  const SizedBox(
                    height: 5.0,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 20.0),
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                          //primary: CommonUtils.createMaterialColor(Color(0XFFb10c00)), // background
                          //onPrimary: Colors.white
                        ),
                        onPressed: () {
                          //debugPrint('input  ' + _nameController.text);
                          fp.getFindPersonsData(
                              _searchController.text ?? '', _nameController.text ?? '', _deptController.text ?? '');
                          Navigator.pop(context);
                        },
                        child: const Text(
                          'Filter',
                          style: TextStyle(fontSize: 15),
                        )),
                  )
                ]),
              )),
            ),
          );
        });
  }

  findSpacesFilter(FindProvider fp) {
    showModalBottomSheet(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        backgroundColor: Colors.white,
        context: context,
        builder: (BuildContext context) {
          return ClipRRect(
            child: Container(
              padding: const EdgeInsets.only(top: 15.0),
              height: 440,
              child: ClipRRect(
                  child: SingleChildScrollView(
                child: Column(crossAxisAlignment: CrossAxisAlignment.end, children: <Widget>[
                  InputTextField(
                    title: 'Building Name',
                    controller: _bildingNameController,
                    textInputAct: TextInputAction.done,
                  ),
                  InputTextField(
                    title: 'Floor Name',
                    controller: _floorNameController,
                    textInputAct: TextInputAction.done,
                  ),
                  InputTextField(
                    title: 'Space Number',
                    controller: _spaceNumController,
                    textInputAct: TextInputAction.done,
                  ),
                  InputTextField(
                    title: 'Space Type',
                    controller: _spaceTypeController,
                    textInputAct: TextInputAction.done,
                  ),
                  InputTextField(
                    title: 'Space Name',
                    controller: _spaceNameController,
                    textInputAct: TextInputAction.done,
                  ),
                  const SizedBox(
                    height: 5.0,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 20.0),
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                          //primary: CommonUtils.createMaterialColor(Color(0XFFb10c00)), // background
                          // onPrimary: Colors.white
                        ),
                        onPressed: () {
                          //debugPrint('input  ' + _nameController.text);
                          fp.getFindSpacesData(
                              _searchController.text ?? '',
                              _bildingNameController.text ?? '',
                              _floorNameController.text ?? '',
                              _spaceNumController.text ?? '',
                              _spaceTypeController.text ?? '',
                              _spaceNumController.text ?? '');
                          Navigator.pop(context);
                        },
                        child: const Text(
                          'Filter',
                          style: TextStyle(fontSize: 15),
                        )),
                  )
                ]),
              )),
            ),
          );
        });
  }
}

//reusable search inputtext
class SearchInputText extends StatelessWidget {
  final makeSearch;
  final searchController;
  final hintSearch;

  const SearchInputText({super.key, this.makeSearch, this.searchController, this.hintSearch});

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 1.0,
      borderRadius: const BorderRadius.all(Radius.circular(20)),
      child: SizedBox(
        height: 30, // Set desired height
        //width: , // Set desired width
        child: TextField(
          controller: searchController,
          cursorColor: Theme.of(context).primaryColor,
          textInputAction: TextInputAction.search,
          style: const TextStyle(color: Colors.black, fontSize: 14),
          decoration: InputDecoration(
              hintText: hintSearch,
              hintStyle: const TextStyle(color: Colors.black38, fontSize: 12),
              prefixIcon: const Material(
                elevation: 0.0,
                borderRadius: BorderRadius.all(Radius.circular(30)),
                child: Icon(
                  Icons.search,
                  size: 20,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 11),
              isDense: true),
          onSubmitted: makeSearch,
        ),
      ),
    );
  }
}

class InputTextField extends StatelessWidget {
  final title;
  final value;
  final icon;
  final dateSelect;
  final maxLines;
  final controller;
  final textInputAct;
  const InputTextField(
      {super.key,
      this.title,
      this.value,
      this.icon,
      this.dateSelect,
      this.maxLines,
      this.controller,
      this.textInputAct});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 10,
      ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextField(
          controller: controller,
          maxLines: maxLines,
          cursorColor: Theme.of(context).primaryColor,
          textInputAction: textInputAct ?? TextInputAction.none,
          style: const TextStyle(color: Colors.black, fontSize: 14),
          // onChanged: (text) {
          //   widget.onChanged(text);
          // },
          decoration: InputDecoration(
            suffixIcon: SizedBox(
              height: 0,
              width: 0,
            ),
            labelText: title,
            border: OutlineInputBorder(
                borderRadius: const BorderRadius.all(Radius.circular(17)),
                borderSide: const BorderSide(color: Colors.red)),
            contentPadding: const EdgeInsets.symmetric(horizontal: 25, vertical: 13),
            hintStyle: const TextStyle(color: Colors.black87, fontSize: 15),
            //floatingLabelBehavior:
          ),
        ),
      ),
    );
  }
}
