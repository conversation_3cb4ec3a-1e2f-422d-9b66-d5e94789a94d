import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/aq_widgets/aq_space.dart';
import 'package:tangoworkplace/models/agilequest/reserve/aq_invitee.dart';
import 'package:tangoworkplace/models/agilequest/user/aq_user_view_data.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/agilequest/participants/aq_add_participants_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';

class AqAddParticipantsScreen extends GetView<AqAddParticipantsController> {
  AqAddParticipantsScreen({super.key, required this.invitees, required this.invitessSubmitted});

  final List<AqInvitee> invitees;
  final Function invitessSubmitted;
  final LabelController talabel = Get.put(LabelController());

  @override
  Widget build(BuildContext context) {
    controller.setupInitial(invitees);

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(talabel.getAq(key: '', defaultTxt: 'Add participants')!.tag),
            ),
            IconButton(
              icon: const Icon(Icons.done),
              color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
              onPressed: () {
                controller.clearEverything();
                invitessSubmitted(controller.inviteesSelected.toList());
                Get.back();
              },
            ),
          ],
        ),
        backgroundColor: Colors.white,
        titleTextStyle: ComponentUtils.appbartitlestyle,
      ),
      body: _buildSearchWidget(),
    );
  }

  Widget _buildSearchWidget() {
    return Obx(
      () => Column(
        children: [
          TaInputText(
              onChanged: (searchText) {
                controller.searchForUsers();
              },
              withDebounce: true,
              maxLines: 1,
              controller: controller.searchController,
              hint: talabel.getAq(key: '', defaultTxt: 'Search')!.tag),
          controller.isLoading.value
              ? const Expanded(
                  child: Center(
                    child: Center(
                      child: ProgressIndicatorCust(),
                    ),
                  ),
                )
              : controller.isLoading.value
                  ? const ProgressIndicatorCust()
                  : Expanded(
                      child: controller.inviteesFound.isNotEmpty
                          ? ListView.builder(
                              itemCount: controller.inviteesFound.length,
                              scrollDirection: Axis.vertical,
                              shrinkWrap: true,
                              itemBuilder: (BuildContext context, int index) {
                                final invitee = controller.inviteesFound[index];
                                return _buildParticipantItemWidget(invitee);
                              },
                            )
                          : Center(
                              child: Text(talabel.getAq(key: '', defaultTxt: 'No Results')!.tag),
                            ),
                    ),
        ],
      ),
    );
  }

  Widget _buildParticipantItemWidget(AqUserViewData invitee) {
    if (controller.inviteesSelected.map((element) => element.emailAddress).contains(invitee.emailAddress)) {
      return GestureDetector(
        onTap: () => controller.deselectUser(invitee.emailAddress ?? ''),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${invitee.getUserName()} \n ${invitee.emailAddress}',
                        style: const TextStyle(color: Color(0XFFb10c00), fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                    ),
                    const AqSpace(),
                    const Icon(
                      Icons.remove_circle,
                      color: Color(0XFFb10c00),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      return GestureDetector(
        onTap: () => controller.selectUser(invitee),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                    style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 14),
                    '${invitee.getUserName()} \n ${invitee.emailAddress}'),
              ),
            ],
          ),
        ),
      );
    }
  }
}
