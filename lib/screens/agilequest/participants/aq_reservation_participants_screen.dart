import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/reserve/aq_invitee.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/agilequest/participants/aq_add_participants_controller.dart';
import '../../../providers/agilequest/reserve/aq_reserve_details_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/agilquest/aq_resource_data.dart';
import 'aq_add_participants_screen.dart';

class AqReservationParticipantsScreen extends GetView<AqReserveDetailsController> {
  AqReservationParticipantsScreen({
    super.key,
  });

  final LabelController talabel = Get.put(LabelController());

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
          padding: const EdgeInsets.only(top: 10, left: 15, right: 15),
          child: Row(
            children: [
              Expanded(
                child: Text(talabel.getAq(key: 'aq.users.availability.invitees', defaultTxt: AqText.invitess)!.tag,
                    style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 18)),
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Add invitee',
                onPressed: () {
                  Get.put(AqAddParticipantsController());
                  Get.to(AqAddParticipantsScreen(
                    invitees: controller.inviteesSelected,
                    invitessSubmitted: (List<AqInvitee> invitessChanged) => controller.inviteesUpdated(invitessChanged),
                  ));
                },
              ),
            ],
          ),
        ),
        Obx(
          () => Expanded(
            child: controller.inviteesSelected.isEmpty
                ? const Center(child: Text('No Data'))
                : ListView.builder(
                    shrinkWrap: true,
                    padding: const EdgeInsets.all(0),
                    itemCount: controller.inviteesSelected.length,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (BuildContext context, int index) {
                      final invitee = controller.inviteesSelected[index];
                      final textToDisplay = invitee.firstName != null && invitee.lastName != null
                          ? '${invitee.firstName} ${invitee.lastName} \n ${invitee.emailAddress}'
                          : invitee.emailAddress;
                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.white,
                        ),
                        width: double.infinity,
                        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Text(
                                    textToDisplay,
                                    style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 14),
                                  ),
                                ),
                                SizedBox(
                                  width: 40.0,
                                  child: IconButton(
                                    icon: const Icon(Icons.remove_circle, color: Color(0XFFb10c00)),
                                    onPressed: () {
                                      controller.deleteInvitee(invitee);
                                    },
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
        ),
      ],
    );
  }
}
