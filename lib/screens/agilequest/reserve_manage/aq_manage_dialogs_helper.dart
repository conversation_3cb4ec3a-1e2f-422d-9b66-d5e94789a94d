
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/aq_cancel_range_controller.dart';
import 'package:tangoworkplace/screens/agilequest/reserve_manage/aq_cancel_range_screen.dart';

import '../../../common/widgets/aq_widgets/aq_space.dart';
import '../../../common/widgets/components.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/aq_reserve_const.dart';
import '../../../providers/agilequest/aqhome_controller.dart';
import '../../../providers/agilequest/reserve/utils/aq_reservation_cancel_validator.dart';
import '../../../providers/agilequest/reserve/utils/aq_reservation_extension.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../services/agilequest/backendData/requests/reserve/aq_reservation_action_request.dart';
import '../../../utils/agilquest/aq_resource_data.dart';

class AqManageDialogsHelper {

  static void onCancelReserve(AqReserveViewData rv, {required Function onAction, required Function onCancelRange}) {
     LabelController talabel = Get.find<LabelController>();
     AqHomeController aqHomeCtrl = Get.find<AqHomeController>();

    var validator = ReservationCancelValidator(rv, aqHomeCtrl.aqUserData.value);
    if (validator.canShowCancelRecurrent()) {
      onCancelRecurrent(rv, onAction);
    } else {
      if (validator.canShowCancelLTR()) {
        onShowCancelLTR(rv, onAction, onCancelRange);
      } else {
        reserveConfirmDialog(
            rv: rv,
            contentTxt: talabel
                    .getAq(
                        key: 'aq.reservations.actions.cancel.acknowledge',
                        defaultTxt:
                            AqText.dialog_cancel_reservation_confirmation)
                    ?.tag ??
                '',
            confirmTxt: talabel
                    .getAq(key: 'aq.web.common.labels.yes', defaultTxt: 'Yes')
                    ?.tag ??
                '',
            cancelTxt: talabel
                    .getAq(key: 'aq.web.common.labels.no', defaultTxt: 'No')
                    ?.tag ??
                '',
            onCancel: () {
              Get.back();
            },
            onConfirm: () async {
              Get.back();
              await onAction(
                AqReservationAction(
                    sysidReservation: rv.sysidReservation,
                    sysidReservationActionType:
                        ReservationActionType.CANCEL.id),
              );
            });
      }
    }
  }

  static void onShowCancelLTR(AqReserveViewData rv, Function onAction, Function onCancelRange) {
     LabelController talabel = Get.find<LabelController>();
    var canceldatetimeTxt = talabel
            .getAq(
                key: 'aq.reservations.actions.cancelSelectedDateRange',
                defaultTxt: "Cancel A Selected Date Range")
            ?.tag ??
        '';
    Get.defaultDialog(
        title: rv.reservationName ?? '',
        titleStyle: const TextStyle(fontSize: 16),
        content: Row(
          children: [
            Flexible(
              child: Text(
                talabel
                        .getAq(
                            key: 'aq.reservations.manage.gap.types.selection',
                            defaultTxt:
                                'This appointment is part of a Reservation that spans more than one day. What would you like to Cancel?')
                        ?.tag ??
                    '',
                style: const TextStyle(fontSize: 15),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          Column(
            children: [
              if (ReservationExtension.re.isStarted(rv))
                TaButton(
                  type: 'elevate',
                  buttonText: talabel
                          .getAq(
                              key:
                                  'aq.reservations.manage.actions.cancel.today',
                              defaultTxt: 'Cancel Today')
                          ?.tag ??
                      '',
                  onPressed: () async {
                    Get.back();

                    await onAction(
                      AqReservationAction(
                          sysidReservation: rv.sysidReservation,
                          sysidReservationActionType:
                              ReservationActionType.CANCEL.id,
                          applyToRemaining: false),
                    );
                  },
                ),
              if (ReservationExtension.re.isStarted(rv)) const AqSpace(),
              TaButton(
                type: 'elevate',
                buttonText: canceldatetimeTxt,
                onPressed: () async {
                  Get.back();
                  onCancelDateRange(canceldatetimeTxt, rv, onCancelRange);
                },
              ),
              const AqSpace(),
              TaButton(
                  type: 'elevate',
                  buttonText: talabel
                          .getAq(
                              key:
                                  'aq.reservations.manage.actions.cancel.reservation',
                              defaultTxt: "Cancel Reservation")
                          ?.tag ??
                      '',
                  onPressed: () async {
                    Get.back();
                    await onAction(
                      AqReservationAction(
                          sysidReservation: rv.sysidReservation,
                          sysidReservationActionType:
                              ReservationActionType.CANCEL.id),
                    );
                  }),
              const AqSpace(),
            ],
          ),
        ]);
  }

  static void onCancelDateRange(String hdrTxt, AqReserveViewData rv, Function onCancelRange) {
     LabelController talabel = Get.find<LabelController>();
    final cancelRangeController = AqCancelRangeController();
    Get.put(cancelRangeController);

    Get.defaultDialog(
        title: hdrTxt,
        titleStyle: const TextStyle(fontSize: 16),
        content: AqCancelRangeScreen(rv: rv),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: AqText.button_close, 
                onPressed: () async {
                  Get.back();
                },
              ),
              const AqSpace(),
              Obx(
                () => TaButton(
                  type: 'elevate',
                  readOnly:
                      !cancelRangeController.isSubmitEnabledForCancelRange.value,
                  buttonText: talabel.getAq(key: 'aq.mobile.cmd.save',defaultTxt: AqText.save)?.tag ?? '',
                  onPressed: () async {
                    Get.back();
                    await onCancelRange(
                        rv,
                        cancelRangeController
                            .startDateTimeForRangeCancelationSelected.value,
                        cancelRangeController
                            .endDateTimeForRangeCancelationSelected.value);
                  },
                ),
              ),
            ],
          ),
        ]);
  }

  static void onCancelRecurrent(AqReserveViewData rv, Function onAction) {
    LabelController talabel = Get.find<LabelController>();
    Get.defaultDialog(
        title: rv.reservationName ?? '',
        titleStyle: const TextStyle(fontSize: 16),
        content: Row(
          children: [
            Flexible(
              child: Text(
                talabel
                        .getAq(
                            key:
                                'aq.reservations.manage.recurring.cancel.instanceOrSeries',
                            defaultTxt:
                                'This is one appointment in a Series. What would you like to Cancel?')
                        ?.tag ??
                    '',
                style: const TextStyle(fontSize: 12),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          Column(
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel
                        .getAq(
                            key:
                                'aq.reservations.manage.actions.cancel.instance',
                            defaultTxt: 'Cancel This Instance')
                        ?.tag ??
                    '',
                onPressed: () {
                  Get.back();

                  reserveConfirmDialog(
                      rv: rv,
                      contentTxt: talabel
                              .getAq(
                                  key:
                                      'aq.reservations.actions.cancel.acknowledge',
                                  defaultTxt:
                                      'Do you want to Cancel this Reservation?')
                              ?.tag ??
                          '',
                      confirmTxt: talabel
                              .getAq(
                                  key: 'aq.web.common.labels.yes',
                                  defaultTxt: 'Yes')
                              ?.tag ??
                          '',
                      cancelTxt: talabel
                              .getAq(
                                  key: 'aq.web.common.labels.no',
                                  defaultTxt: 'No')
                              ?.tag ??
                          '',
                      onCancel: () {
                        debugPrint('-------on cancel-------');
                        Get.back();
                      },
                      onConfirm: () async {
                        debugPrint('-------on confirm-------');
                        Get.back();
                        await onAction(
                          AqReservationAction(
                              sysidReservation: rv.sysidReservation,
                              // sysidReservationActionType: ReservationActionType.CANCEL_INSTANCE.id, //to do need more info
                              sysidReservationActionType:
                                  ReservationActionType.CANCEL.id,
                              applyToRemaining: false),
                        );
                      });
                },
              ),
              const AqSpace(),
              TaButton(
                type: 'elevate',
                buttonText: talabel
                        .getAq(
                            key:
                                'aq.reservations.manage.actions.cancel.entire.series',
                            defaultTxt: "Cancel The Entire Series")
                        ?.tag ??
                    '',
                onPressed: () async {
                  Get.back();
                  reserveConfirmDialog(
                      rv: rv,
                      contentTxt: talabel
                              .getAq(
                                  key:
                                      'aq.reservations.actions.cancelSeries.acknowledge',
                                  defaultTxt: '''Do you want to Cancel this entire Series? Doing so will Cancel the Series' +
                                      ' from this instance forward. Reservations in the Series that are prior to this' +
                                      ' one will not be Canceled. Is this what you want to do?''')
                              ?.tag ??
                          '',
                      confirmTxt: talabel
                              .getAq(
                                  key: 'aq.web.common.labels.yes',
                                  defaultTxt: 'Yes')
                              ?.tag ??
                          '',
                      cancelTxt: talabel
                              .getAq(
                                  key: 'aq.web.common.labels.no',
                                  defaultTxt: 'No')
                              ?.tag ??
                          '',
                      onCancel: () {
                        Get.back();
                      },
                      onConfirm: () async {
                        Get.back();
                        await onAction(
                          AqReservationAction(
                              sysidReservation: rv.sysidReservation,
                              // sysidReservationActionType: ReservationActionType.CANCEL_SERIES.id,//to do need more info
                              sysidReservationActionType:
                                  ReservationActionType.CANCEL.id,
                              applyToRemaining: true),
                        );
                      });
                },
              ),
              const AqSpace(),
            ],
          ),
        ]);
  }

  static void reserveConfirmDialog(
      {AqReserveViewData? rv,
      String? contentTxt,
      String? confirmTxt,
      String? cancelTxt,
      Function? onConfirm,
      Function? onCancel}) {
    Get.defaultDialog(
        title: rv?.reservationName ?? '',
        titleStyle: const TextStyle(fontSize: 16),
        content: Row(
          children: [
            Flexible(
              child: Text(
                contentTxt ?? '',
                style: const TextStyle(fontSize: 15),
                maxLines: 5,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: cancelTxt,
                onPressed: onCancel,
              ),
              const SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: confirmTxt,
                onPressed: onConfirm,
              ),
              const SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }
}