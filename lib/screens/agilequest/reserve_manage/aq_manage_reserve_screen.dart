import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';
import 'package:tangoworkplace/models/agilequest/location/aq_location_data.dart';
import 'package:tangoworkplace/models/agilequest/resource/aq_resource_category.dart';
import 'package:tangoworkplace/models/agilequest/types/reserve/aq_reserve_details_view_mode.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/utils/aq_manage_reserve_search_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/agilequest/home/<USER>';
import 'package:tangoworkplace/screens/agilequest/reserve_manage/aq_manage_dialogs_helper.dart';
import 'package:tangoworkplace/screens/agilequest/reserve_manage/aq_manage_reserve_search_screen.dart';
import 'package:tangoworkplace/utils/agilquest/aq_resource_data.dart';

import '../../../common/component_utils.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/aq_reserve_const.dart';
import '../../../providers/agilequest/aqhome_controller.dart';
import '../../../providers/agilequest/reserve/aq_manage_reserve_controller.dart';
import '../../../providers/agilequest/reserve/aq_reserve_details_controller.dart';
import '../../../services/agilequest/backendData/requests/reserve/aq_reservation_action_request.dart';
import '../reserve_details/aq_reserve_details_screen.dart';

class AqManageReserve extends GetView<AqManageReserveController> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final AqManageReserveController aqReserveState = Get.put(AqManageReserveController());

  final LabelController talabel = Get.find<LabelController>();
  final AqHomeController aqHomeCtrl = Get.find<AqHomeController>();

  AqManageReserve({super.key});

  var cancelBtnTxt = '';
  var checkinBtnTxt = '';
  var checkoutBtnTxt = '';
  var copyBtnTxt = '';
  var endBtnTxt = '';
  var deleteBtnTxt = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Reservations', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,

            ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => AqHome());
          },
        ),
        actions: [searchPage()],
      ),
      key: _scaffoldKey,
      body: DefaultTabController(
        initialIndex: 0,
        length: 5,
        child: GetX<AqManageReserveController>(
          initState: (state) async {
            cancelBtnTxt = talabel.getAq(key: 'aq.reservations.manage.actions.cancel', defaultTxt: AqText.btn_title_cancel)!.tag;
            checkinBtnTxt = talabel.getAq(key: 'aq.reservations.manage.actions.checkin', defaultTxt: AqText.btn_title_check_in)!.tag;
            checkoutBtnTxt = talabel.getAq(key: 'aq.reservations.manage.actions.checkout', defaultTxt: AqText.btn_title_check_out)!.tag;
            copyBtnTxt = talabel.getAq(key: 'aq.mobile.cmd.copy', defaultTxt: AqText.btn_title_copy)!.tag;
            endBtnTxt = talabel.getAq(key: 'aq.reservations.manage.actions.end', defaultTxt: AqText.btn_title_end)!.tag;
            deleteBtnTxt = AqText.btn_delete;

            await controller.fetchReservations();
          },
          builder: (s) {
            return Column(
              children: <Widget>[
                Material(
                  elevation: 5,
                  color: Colors.white,
                  child: TabBar(
                    onTap: (value) async {
                      debugPrint('tab on tap >>>>>$value');
                      controller.tabtype.value = value;
                      await controller.fetchReservations(filter: value);
                    },
                    isScrollable: true,
                    labelColor: ComponentUtils.tablabelcolor,
                    unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                    indicatorColor: ComponentUtils.tabindicatorColor,
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelStyle: TextStyle(
                        fontWeight: FontWeight.bold, fontStyle: FontStyle.normal, fontSize: 14, color: ComponentUtils.tablabelcolor),
                    tabs: [
                      SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(Icons.manage_accounts),
                            const SizedBox(width: 6),
                            Text(talabel.getAq(key: 'aq.reservations.manage.status.active', defaultTxt: 'Active')!.tag),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(Icons.diversity_3),
                            const SizedBox(width: 6),
                            Text(talabel.getAq(key: 'aq.reservations.manage.status.delegate', defaultTxt: 'Delegate')!.tag),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(Icons.history_edu),
                            const SizedBox(width: 6),
                            Text(talabel.getAq(key: 'aq.reservations.manage.status.draft', defaultTxt: 'Draft')!.tag),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(Icons.hourglass_top),
                            const SizedBox(width: 6),
                            Text(talabel.getAq(key: 'aq.reservations.manage.status.requests', defaultTxt: 'Requests')!.tag),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(Icons.sports_score),
                            const SizedBox(width: 6),
                            Text(talabel.getAq(key: 'aq.reservations.manage.status.completed', defaultTxt: 'Completed')!.tag),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    children: [reserveList(), reserveList(), reserveList(), reserveList(), reserveList()],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget reserveList() {
    return Container(
      padding: const EdgeInsets.only(top: 10),
      child: controller.isLoading.isTrue
          ? const ProgressIndicatorCust()
          : controller.rsrvDateData.isNotEmpty
              ? RefreshIndicator(
                  onRefresh: () => controller.updatedView(),
                  child: ListView.builder(
                    itemCount: controller.rsrvDateData.length,
                    itemBuilder: (BuildContext context, int index) {
                      String? dateVal = controller.rsrvDateData.keys.elementAt(index);
                      List itemsInCategory = controller.rsrvDateData[dateVal]!;

                      return Column(
                        children: [
                          groupDateContainer(dateVal),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const ClampingScrollPhysics(),
                            itemCount: itemsInCategory.length,
                            itemBuilder: (BuildContext context, int index) {
                              return listItem(context, itemsInCategory[index]);
                            },
                          ),
                        ],
                      );
                    },
                  ),
                )
              : Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!)))),
    );
  }

  Widget rsrvList() {
    return Column(
      children: <Widget>[
        Expanded(
          child: controller.isLoading.isTrue
              ? const ProgressIndicatorCust()
              : controller.reserveData.isEmpty
                  ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                  : ListView.builder(
                      itemBuilder: (context, index) {
                        return listItem(context, controller.reserveData[index]);
                      },
                      itemCount: controller.reserveData.length,
                    ),
        ),
      ],
    );
  }

  Widget groupDateContainer(String dateVal) {
    //var date = dateVal != '' ? DateFormat("MM/dd/yyyy").format(dateVal as DateTime) : '';
    dateVal = DateFormat("yMMMMd").format(DateTime.now()) == dateVal ? 'Today' : dateVal;
    return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            width: 160,
            height: 30,
            margin: const EdgeInsets.only(left: 18),
            decoration: BoxDecoration(
              color: Colors.black45, //ComponentUtils.primecolor,
              borderRadius: BorderRadius.circular(10),
            ),
            constraints: const BoxConstraints(
              minWidth: 26,
              minHeight: 14,
            ),
            child: Center(
              child: Text(
                dateVal,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ]);
  }

  Widget searchPage() {
    return MenuItemButton(
        child: Icon(
          Icons.search,
          color: ComponentUtils.primecolor,
        ),
        onPressed: () {
          Get.put(AqManageReserveSearchController());

          Get.to(
            () => AqManageReserveSearchScreen(
              initialStartDate: controller.startDateSelected ?? AqDate.fromDate(DateTime.now(), DateTime.now().timeZoneName),
              initialEndDate:
                  controller.endDateSelected ?? AqDate.fromDate(DateTime.now().add(const Duration(days: 14)), DateTime.now().timeZoneName),
              initialCategory: controller.categorySelected,
              initialLocation: controller.locationSelected,
              onSearch: (AqLocationData location, AqResourceCategory category, AqDate start, AqDate end) {
                controller.filterReservations(start: start, end: end, location: location, category: category);
              },
            ),
          );
        });
  }

  Widget listItem(BuildContext context, AqReserveViewData rv) {
    const primary = Colors.white; // Color(0xff696b9e);
    const secondary = Colors.white; // Color(0xfff29a94);
    return GestureDetector(
      onTap: () async {
        if (controller.isReserveRecur(rv)) {
          onViewReserve(rv);
        } else {
          Get.put(AqReserveDetailsController());
          Get.to(() => AqReserveDetails(
                reservationView: rv,
                updateSearchResults: () {
                  controller.updatedView();
                },
              ));
        }
      },
      child: Stack(
        children: [
          Column(
            children: [
              rv.resourceImage == null
                  ? Container(
                      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 1),
                      padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
                      alignment: Alignment.center,
                      width: double.infinity,
                      //height: 330,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12.0),
                        child: Image.asset(
                          'lib/icons/aq_tango_rsrv.jpg',
                          fit: BoxFit.fill,
                        ),
                      ),
                    )
                  : Container(
                      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 1),
                      padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      alignment: Alignment.center,
                      width: double.infinity,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12.0),
                        child: Image.network(
                          rv.resourceImage.toString(),
                          fit: BoxFit.fill,
                          errorBuilder: (context, error, stackTrace) => const Image(
                            image: AssetImage('lib/icons/aq_tango_rsrv.jpg'),
                          ),
                        ),
                      ),
                    ),
              if (controller.reserveBtnActions(rv))
                Container(
                  height: 40,
                ),
            ],
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            //top: 0,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(bottomLeft: Radius.circular(12), bottomRight: Radius.circular(12)),
                color: Colors.black45,
              ),
              width: double.infinity,
              margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                ' ${rv.reservationName}',
                                style: const TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                              const SizedBox(
                                height: 6,
                              ),
                              Row(children: [
                                const Icon(
                                  Icons.location_on,
                                  color: secondary,
                                  size: 15,
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(rv.tempAddress ?? '', style: const TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                              ]),
                              const SizedBox(
                                height: 6,
                              ),
                              Row(
                                children: <Widget>[
                                  const Icon(
                                    Icons.schedule,
                                    color: secondary,
                                    size: 15,
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Text('From: ${rv.aqStartTime!.displayDateString(talabel, withTimeZone: true)}',
                                      style: const TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                                ],
                              ),
                              const SizedBox(
                                height: 6,
                              ),
                              Row(
                                children: <Widget>[
                                  const Icon(
                                    Icons.schedule,
                                    color: secondary,
                                    size: 15,
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Text('To: ${rv.aqEndTime!.displayDateString(talabel, withTimeZone: true)}',
                                      style: const TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                                ],
                              ),
                              const SizedBox(
                                height: 6,
                              ),
                              Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                                Text(rv.tempStatus ?? '', style: const TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                                if (controller.isReserveRecur(rv))
                                  const Icon(
                                    Icons.sync,
                                    color: secondary,
                                    size: 15,
                                  ),
                                if (controller.isReservationPartOfEvent(rv))
                                  const Icon(
                                    Icons.event_repeat,
                                    color: secondary,
                                    size: 15,
                                  ),
                              ]),
                            ],
                          ),
                        ),
                        Obx(
                          () => Container(
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(bottomLeft: Radius.circular(12), bottomRight: Radius.circular(12)),
                              color: Colors.black,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: <Widget>[
                                if (controller.isCancelBtn(rv))
                                  Container(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: TaButton(
                                        buttonText: cancelBtnTxt,
                                        type: 'elevate',
                                        onPressed: () {
                                          AqManageDialogsHelper.onCancelReserve(
                                            rv,
                                            onAction: (AqReservationAction action) => controller.callAction(action),
                                            onCancelRange: (AqReserveViewData rv, AqDate stopTime, AqDate restartTime) =>
                                                controller.changeReservDateRange(
                                              rv,
                                              stopTime,
                                              restartTime,
                                            ),
                                          );
                                        }),
                                  ),
                                if (controller.isDeleteBtn())
                                  Container(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: TaButton(
                                        buttonText: deleteBtnTxt,
                                        type: 'elevate',
                                        onPressed: () {
                                          AqManageDialogsHelper.reserveConfirmDialog(
                                              rv: rv,
                                              contentTxt: AqText.dialog_draft_delete_confirmation,
                                              confirmTxt: talabel.getAq(key: 'aq.web.common.labels.yes', defaultTxt: 'Yes')?.tag ?? '',
                                              cancelTxt: talabel.getAq(key: 'aq.web.common.labels.no', defaultTxt: 'No')?.tag ?? '',
                                              onCancel: () async {
                                                Get.back();
                                              },
                                              onConfirm: () async {
                                                Get.back();
                                                await controller.deleteDraftResrevation(rv);
                                              });
                                        }),
                                  ),
                                if (controller.isCheckInBtn(rv))
                                  Container(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: TaButton(
                                        buttonText: checkinBtnTxt,
                                        //talabel.getAq(key: 'aq.reservations.manage.actions.checkin', defaultTxt: 'Check In')?.tag,
                                        type: 'elevate',
                                        onPressed: () async {
                                          var reservationTimeStarted =
                                              rv.aqStartTime?.toDate() != null ? (DateTime.now().isAfter(rv.aqStartTime!.toDate())) : false;
                                          if (reservationTimeStarted) {
                                            await controller.callAction(
                                              AqReservationAction(
                                                  sysidReservation: rv.sysidReservation,
                                                  sysidReservationActionType: ReservationActionType.CHECK_IN.id),
                                            );
                                          } else {
                                            AqManageDialogsHelper.reserveConfirmDialog(
                                                rv: rv,
                                                contentTxt: talabel
                                                        .getAq(
                                                            key: 'aq.reservations.actions.checkIn.acknowledge',
                                                            defaultTxt: AqText.dialog_check_in_reservation_confirmation)
                                                        ?.tag ??
                                                    '',
                                                confirmTxt: talabel.getAq(key: 'aq.web.common.labels.yes', defaultTxt: 'Yes')?.tag ?? '',
                                                cancelTxt: talabel.getAq(key: 'aq.web.common.labels.no', defaultTxt: 'No')?.tag ?? '',
                                                onCancel: () async {
                                                  Get.back();
                                                  await controller.callAction(
                                                    AqReservationAction(
                                                        sysidReservation: rv.sysidReservation,
                                                        sysidReservationActionType: ReservationActionType.CHECK_IN_NO_ROLL.id),
                                                  );
                                                },
                                                onConfirm: () async {
                                                  Get.back();
                                                  await controller.callAction(
                                                    AqReservationAction(
                                                        sysidReservation: rv.sysidReservation,
                                                        sysidReservationActionType: ReservationActionType.CHECK_IN.id),
                                                  );
                                                });
                                          }
                                        }),
                                  ),
                                if (controller.isCheckOutBtn(rv))
                                  Container(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: TaButton(
                                        buttonText: checkoutBtnTxt,
                                        //talabel.getAq(key: 'aq.reservations.manage.actions.checkout', defaultTxt: 'Check Out')?.tag,
                                        type: 'elevate',
                                        onPressed: () async {
                                          await controller.callAction(
                                            AqReservationAction(
                                                sysidReservation: rv.sysidReservation,
                                                sysidReservationActionType: ReservationActionType.CHECK_OUT.id),
                                          );
                                        }),
                                  ),
                                if (controller.isCopyBtn(rv))
                                  Container(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: TaButton(
                                        buttonText: copyBtnTxt,
                                        //talabel.getAq(key: 'aq.mobile.cmd.copy', defaultTxt: 'Copy')?.tag,
                                        type: 'elevate',
                                        onPressed: () {
                                          AqReservationAction(
                                              sysidReservation: rv.sysidReservation,
                                              sysidReservationActionType: ReservationActionType.CHECK_IN.id);
                                          debugPrint('btn click');
                                        }),
                                  ),
                                if (controller.isEndBtn(rv))
                                  Container(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: TaButton(
                                        buttonText: talabel.getAq(key: 'aq.reservations.manage.actions.end', defaultTxt: 'End')?.tag,
                                        type: 'elevate',
                                        onPressed: () async {
                                          await controller.callAction(AqReservationAction(
                                              sysidReservation: rv.sysidReservation,
                                              sysidReservationActionType: ReservationActionType.END.id));
                                        }),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  onViewReserve(AqReserveViewData rv) {
    Get.defaultDialog(
        title: rv.reservationName ?? '',
        titleStyle: const TextStyle(fontSize: 16),
        content: Row(
          children: [
            Flexible(
              child: Text(
                talabel
                        .getAq(
                            key: 'aq.reservations.manage.recurring.dialog.instanceOrSeries',
                            defaultTxt: 'This is one appointment in a series. What would you like to open?')
                        ?.tag ??
                    '',
                style: const TextStyle(fontSize: 15),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText:
                    talabel.getAq(key: 'aq.mobile.open.recurring.instance.button.title', defaultTxt: 'Just This Instance')?.tag ?? '',
                onPressed: () async {
                  debugPrint('-------Just This Instance-------');
                  Get.back();
                  Get.put(AqReserveDetailsController());
                  Get.to(() => AqReserveDetails(
                        reservationView: rv,
                        updateSearchResults: () {
                          controller.updatedView();
                        },
                      ));
                },
              ),
              const SizedBox(
                width: 4.0,
              ),
              TaButton(
                  type: 'elevate',
                  buttonText:
                      talabel.getAq(key: 'aq.mobile.open.recurring.series.button.title', defaultTxt: "The Entire Series")?.tag ?? '',
                  onPressed: () async {
                    Get.back();
                    Get.put(AqReserveDetailsController());
                    Get.to(() => AqReserveDetails(
                          reservationView: rv,
                          mode: AqReserveDetailsViewMode.RECURRING,
                          updateSearchResults: () {
                            controller.updatedView();
                          },
                        ));
                  }),
              const SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }
}
