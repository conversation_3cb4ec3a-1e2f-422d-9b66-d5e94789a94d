import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/talabel.dart';
import 'package:tangoworkplace/models/agilequest/location/aq_location_data.dart';
import 'package:tangoworkplace/models/agilequest/resource/aq_resource_category.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/location/aq_location_search_controller.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/utils/aq_manage_reserve_search_controller.dart';
import 'package:tangoworkplace/screens/agilequest/location/aq_location_search_screen.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/component_widgets/taforminputdate.dart';
import '../../../common/widgets/components.dart';
import '../../../models/agilequest/date/aq_date.dart';
import '../../../utils/common_utils.dart';

class AqManageReserveSearchScreen extends GetView<AqManageReserveSearchController> {
  final AqDate initialStartDate;
  final AqDate initialEndDate;
  final AqResourceCategory? initialCategory;
  final AqLocationData? initialLocation;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final Function onSearch;

  AqManageReserveSearchScreen({
    super.key,
    required this.initialStartDate,
    required this.initialEndDate,
    this.initialCategory,
    this.initialLocation,
    required this.onSearch,
  });

  @override
  Widget build(BuildContext context) {
    controller.initialLoad(
      initialStartDate,
      initialEndDate,
      initialCategory: initialCategory,
      initialLocation: initialLocation,
    );

    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Search criteria', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Obx(() {
          if (controller.isLoading.value) {
            return const ProgressIndicatorCust();
          } else {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                TaLabel(
                  label: "Where",
                  controller: controller.locationController,
                  onFieldTap: () {
                    Get.put(AqLocationSearchController());
                    Get.to(() => AqLocationSearchScreen(
                          onSearchResult: (AqLocationData? selectedLocation) {
                            controller.locationChanged(selectedLocation);
                          },
                          initialLocationSelection: controller.selectedLocation.value,
                        ));
                  },
                ),
                TaFormDropdown(
                    label: "What",
                    emptttext: controller.categorySelected.value.name,
                    onChanged: (selectedCategoryId) {
                      if (selectedCategoryId != null) {
                        controller.categoryChanged(selectedCategoryId);
                      }
                    },
                    onSaved: (selectedCategoryId) {
                      if (selectedCategoryId != null) {
                        controller.categoryChanged(selectedCategoryId);
                      }
                    },
                    listflag: true,
                    readonly: false,
                    items: controller.availableCategories.map((AqResourceCategory category) {
                      return DropdownMenuItem(
                        value: category.sysidCategory,
                        child: Text(
                          category.name,
                          style: const TextStyle(fontSize: 14, color: Colors.black),
                        ),
                      );
                    }).toList()),
                TaFormInputDate(
                  label: "Start Date",
                  controller: controller.startDateController,
                  dateSelect: controller.startDateSelected.value.toDate(),
                  onChanged: (start) {
                    controller.updateStartTime(start);
                  },
                  readOnly: false,
                  onSaved: (val) {
                    controller.updateStartTime(val);
                  },
                ),
                TaFormInputDate(
                  label: "End Date",
                  controller: controller.endDateController,
                  dateSelect: controller.endDateSelected.value.toDate(),
                  onChanged: (end) {
                    controller.updateEndTime(end);
                  },
                  readOnly: false,
                  onSaved: (val) {
                    controller.updateEndTime(val);
                  },
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      TaButton(
                        type: 'elevate',
                        buttonText: 'Cancel',
                        readOnly: false,
                        onPressed: () => Get.back(),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      TaButton(
                        type: 'elevate',
                        buttonText: 'Search',
                        readOnly: false,
                        onPressed: () {
                          onSearch(
                            controller.selectedLocation.value,
                            controller.categorySelected.value,
                            controller.startDateSelected.value,
                            controller.endDateSelected.value,
                          );
                          Get.back();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            );
          }
        }),
      ),
    );
  }
}
