
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/aq_cancel_range_controller.dart';

import '../../../common/widgets/aq_widgets/aq_space.dart';
import '../../../common/widgets/component_widgets/taforminputdate.dart';
import '../../../common/widgets/component_widgets/taforminputtime.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/dates/aq_date_pattern_type.dart';
import '../../../providers/ta_admin/label_controller.dart';

class AqCancelRangeScreen extends GetView<AqCancelRangeController> {

  final LabelController talabel = Get.find<LabelController>();
  final AqReserveViewData rv;
  
  AqCancelRangeScreen({super.key, required this.rv});

  @override
  Widget build(BuildContext context) {

    controller.setupRangeForCancelation(rv);
    
    return Form(
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(talabel
                  .getAq(
                      key:
                          'aq.reservations.actions.cancelSelectedDateRange.instructions',
                      defaultTxt:
                          'You may Cancel a specific block of time without Canceling your entire Reservation. Please specify the Dates/Times you would like to Cancel.')!
                  .tag),
              const AqSpace(),
              Text(
                  talabel
                          .getAq(
                              key: 'aq.mobile.date.from.title',
                              defaultTxt: "From")
                          ?.tag ??
                      '',
                  style: const TextStyle(fontSize: 14)),
              const AqSpace(),
              Obx(
                () => Row(
                  children: [
                    SizedBox(
                      width: 150,
                      child: TaFormInputDate(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 1,
                          vertical: 1,
                        ),
                        label: 'Date',
                        firstDate: rv.aqStartTime?.toDate()
                            .subtract(const Duration(days: 0)),
                        lastDate: controller
                            .endDateTimeBorderSelected.value
                            .toDate()
                            .subtract(const Duration(days: 0)),
                        dateSelect: controller
                            .startDateTimeForRangeCancelationSelected.value
                            .toDate(),
                        controller: controller.cancelFromDateCtrl,
                        onChanged: (date) {
                          controller
                              .changeStartDateForRangeCancelation(date);
                        },
                        readOnly: false,
                        onSaved: (date) {
                          controller
                              .changeStartDateForRangeCancelation(date);
                        },
                        validate: (value) {
                          if (value == null) {
                            return 'This attribute is Required';
                          }
                          return null;
                        },
                      ),
                    ),
                    SizedBox(
                      width: 130,
                      child: TaFormInputTime(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 1,
                          vertical: 1,
                        ),
                        label: 'Time',
                        controller: controller.cancelFromTimeCtrl,
                        readOnly: false,
                        onChanged: (time) {
                          controller
                              .changeStartTimeForRangeCancelation(time);
                        },
                        onSaved: (time) {
                          controller
                              .changeStartTimeForRangeCancelation(time);
                        },
                        validate: (value) {
                          if (value == null || value == '') {
                            return 'This attribute is Required';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ),
              const AqSpace(),
              Text(
                  talabel
                          .getAq(
                              key: 'aq.mobile.date.to.title', defaultTxt: "To")
                          ?.tag ??
                      '',
                  style: const TextStyle(fontSize: 14)),
              const AqSpace(),
              Obx(
                () => Row(
                  children: [
                    SizedBox(
                      width: 150,
                      child: TaFormInputDate(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 1,
                          vertical: 1,
                        ),
                        label: 'Date',
                        dateSelect: controller
                            .endDateTimeForRangeCancelationSelected.value
                            .toDate(),
                        firstDate: controller
                            .startDateTimeForRangeCancelationSelected.value
                            .toDate()
                            .subtract(const Duration(days: 0)),
                        lastDate: rv.aqEndTime
                            ?.toDate()
                            .subtract(const Duration(days: 0)),
                        controller: controller.cancelToDateCtrl,
                        onChanged: (date) {
                          controller
                              .changeEndDateForRangeCancelation(date);
                        },
                        readOnly: false,
                        onSaved: (date) {
                          controller
                              .changeEndDateForRangeCancelation(date);
                        },
                        validate: (value) {
                          if (value == null) {
                            return 'This attribute is Required';
                          }
                          return null;
                        },
                      ),
                    ),
                    SizedBox(
                      width: 130,
                      child: TaFormInputTime(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 1,
                          vertical: 1,
                        ),
                        label: 'Time',
                        valueToPassOnTap: controller
                            .endDateTimeForRangeCancelationSelected.value
                            .displayDateString(talabel,
                                pattern: AqDatePatternType.TIME, skipNoEndDate: true),
                        controller: controller.cancelToTimeCtrl,
                        readOnly: false,
                        onChanged: (time) {
                          controller
                              .changeEndTimeForRangeCancelation(time);
                        },
                        onSaved: (time) {
                          controller
                              .changeEndTimeForRangeCancelation(time);
                        },
                        validate: (value) {
                          if (value == null || value == '') {
                            return 'This attribute is Required';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
  }

}