import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/talabel.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../models/agilequest/objects/dates/aq_dates_selection_criteria.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/reserve/aq_reserve_details_view_mode.dart';
import '../../../providers/agilequest/date/aq_date_selection_controller.dart';
import '../../../providers/agilequest/reserve/aq_reserve_details_controller.dart';
import '../../../utils/agilquest/aq_resource_data.dart';
import '../date_selection/aq_date_selection_screen.dart';

class AqReservationInfoScreen extends GetView<AqReserveDetailsController> {
  final LabelController talabel = Get.find<LabelController>();
  final AqReserveViewData reservationView;
  final AqReserveDetailsViewMode mode;

  AqReservationInfoScreen({super.key, required this.reservationView, this.mode = AqReserveDetailsViewMode.STANDARD});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        padding: const EdgeInsets.only(top: 20, left: 15, right: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Obx(
          () => Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                TaInputText(
                  title: talabel
                      .getAq(key: 'aq.mobile.common.labels.reservationName', defaultTxt: AqText.reservation_title_name)!
                      .tag,
                  maxLines: 1,
                  controller: controller.reservationNameTextController,
                  onChanged: (value) => controller.reservationNameChanged(value),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 20, bottom: 10),
                  padding: const EdgeInsets.only(top: 10, bottom: 20),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black26),
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Column(
                        children: [
                          IconButton(
                              onPressed: () {},
                              icon: Icon(
                                Icons.manage_accounts,
                                color: ComponentUtils.primecolor,
                                size: 30,
                              )),
                          Text(reservationView.tempStatus ?? ''),
                        ],
                      ),
                      Column(
                        children: [
                          IconButton(
                              onPressed: () {},
                              icon: Icon(
                                Icons.lock,
                                color: ComponentUtils.primecolor,
                                size: 30,
                              )),
                          const Text('private'),
                        ],
                      )
                    ],
                  ),
                ),
                TaLabel(
                  label: talabel
                      .getAq(
                          key: 'aq.web.calendar.datetime.label.startAndEndDateTime',
                          defaultTxt: AqText.start_end_date_time)!
                      .tag,
                  icon: Icons.edit,
                  controller: controller.reservationDatesTextController,
                  onFieldTap: () {
                    final datesCriteria = controller.datesSelected.value;
                    if (datesCriteria != null) {
                      Get.put(AqDateSelectionController());
                      _showDateDialog(context, datesCriteria);
                    }
                  },
                ),
                TaInputText(
                  title: talabel
                      .getAq(
                          key: 'aq.mobile.common.labels.reservationOwner', defaultTxt: AqText.reservation_title_owner)!
                      .tag,
                  readOnly: true,
                  controller: controller.reservationOwnerTextController,
                ),
                if (controller.isQuantityVisible.value)
                  TaInputNumber(
                    title: talabel
                        .getAq(key: 'aq.mobile.common.labels.quantity', defaultTxt: AqText.reservation_title_quantity)!
                        .tag,
                    controller: controller.reservationQuantityTextController,
                    maxCharacters: 5,
                    onChanged: (value) => controller.reservationQuantityChanged(value),
                  ),
                if (controller.isDeliveryVisible.value)
                  TaInputText(
                    title: talabel
                        .getAq(
                            key: 'aq.reservations.details.deliveryLocation.label',
                            defaultTxt: AqText.reservation_title_delivery_location)!
                        .tag,
                    controller: controller.reservationDeliveryTextController,
                    maxLines: 1,
                    onChanged: (value) => controller.reservationDeliveryChanged(value),
                  ),
                TaInputText(
                  title: talabel.getAq(key: 'aq.mobile.common.labels.comments', defaultTxt: AqText.comments)!.tag,
                  controller: controller.reservationCommentsTextController,
                  onChanged: (value) => controller.reservationCommentsChanged(value),
                  maxLines: 10,
                  minLines: 5,
                ),
              ]),
        ),
      ),
    );
  }

  Future<void> _showDateDialog(BuildContext context, AqDatesSelectionCriteria criteria) {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: AqDateSelectionScreen(
            initialDatesCriteria: criteria,
            onDateSelected: (AqDatesSelectionCriteria? datesSelected) {
              if (datesSelected != null) {
                controller.datesUpdated(datesSelected);
              }
            },
          ),
        );
      },
    );
  }
}
