import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/aq_reservation_actions_controller.dart';

import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../models/agilequest/date/aq_date.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/aq_reserve_const.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../services/agilequest/backendData/requests/reserve/aq_reservation_action_request.dart';
import '../../../utils/agilquest/aq_resource_data.dart';
import '../reserve_manage/aq_manage_dialogs_helper.dart';

class AqReservationActionsScreen
    extends GetView<AqReservationActionsController> {

  AqReservationActionsScreen({super.key, required this.reservation, required this.actionPerformed});

  final VoidCallback actionPerformed;
  final AqReserveViewData reservation;
  final LabelController talabel = Get.find<LabelController>();

  @override
  Widget build(BuildContext context) {
    controller.initialSetup(actionPerformed);

    return Expanded(
      child: Container(
          margin: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          padding: const EdgeInsets.only(top: 20, left: 15, right: 15),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
      child: SingleChildScrollView(
          child: Obx(
            () => controller.isLoading.value ? const Center(
              child: Center(child: ProgressIndicatorCust()),
            ) : Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                if (controller.isCancelBtn(reservation))
                  Container(
                    padding: const EdgeInsets.only(right: 10),
                    child: TaButton(
                        buttonText: 'Cancel', 
                        type: 'elevate',
                        onPressed: () {
                          AqManageDialogsHelper.onCancelReserve(
                                            reservation,
                                            onAction:
                                                (AqReservationAction action) =>
                                                    controller
                                                        .callAction(action),
                                            onCancelRange:
                                                (AqReserveViewData rv,
                                                        AqDate stopTime,
                                                        AqDate restartTime) =>
                                                    controller
                                                        .changeReservDateRange(
                                              rv,
                                              stopTime,
                                              restartTime,
                                            ),
                                          );
                        }),
                  ),
                if (controller.isDeleteBtn(reservation))
                  Container(
                    padding: const EdgeInsets.only(right: 10),
                    child: TaButton(
                        buttonText: 'Delete',
                        type: 'elevate',
                        onPressed: () {
                          AqManageDialogsHelper.reserveConfirmDialog(
                              rv: reservation,
                              contentTxt:
                                  AqText.dialog_draft_delete_confirmation,
                              confirmTxt: talabel
                                      .getAq(
                                          key: 'aq.web.common.labels.yes',
                                          defaultTxt: 'Yes')
                                      ?.tag ??
                                  '',
                              cancelTxt: talabel
                                      .getAq(
                                          key: 'aq.web.common.labels.no',
                                          defaultTxt: 'No')
                                      ?.tag ??
                                  '',
                              onCancel: () async {
                                Get.back();
                              },
                              onConfirm: () async {
                                Get.back();
                                await controller
                                    .deleteDraftResrevation(reservation);
                              });
                        }),
                  ),
                if (controller.isCheckInBtn(reservation))
                  Container(
                    padding: const EdgeInsets.only(right: 10),
                    child: TaButton(
                        buttonText: 'Check In',
                        //talabel.getAq(key: 'aq.reservations.manage.actions.checkin', defaultTxt: 'Check In')?.tag,
                        type: 'elevate',
                        onPressed: () async {
                          var reservationTimeStarted =
                              reservation.aqStartTime?.toDate() != null
                                  ? (DateTime.now().isAfter(
                                      reservation.aqStartTime!.toDate()))
                                  : false;
                          if (reservationTimeStarted) {
                            await controller.callAction(
                              AqReservationAction(
                                  sysidReservation:
                                      reservation.sysidReservation,
                                  sysidReservationActionType:
                                      ReservationActionType.CHECK_IN.id),
                            );
                          } else {
                            AqManageDialogsHelper.reserveConfirmDialog(
                                rv: reservation,
                                contentTxt: talabel
                                        .getAq(
                                            key:
                                                'aq.reservations.actions.checkIn.acknowledge',
                                            defaultTxt: AqText
                                                .dialog_check_in_reservation_confirmation)
                                        ?.tag ??
                                    '',
                                confirmTxt: talabel
                                        .getAq(
                                            key: 'aq.web.common.labels.yes',
                                            defaultTxt: 'Yes')
                                        ?.tag ??
                                    '',
                                cancelTxt: talabel
                                        .getAq(
                                            key: 'aq.web.common.labels.no',
                                            defaultTxt: 'No')
                                        ?.tag ??
                                    '',
                                onCancel: () async {
                                  Get.back();
                                  await controller.callAction(
                                    AqReservationAction(
                                        sysidReservation:
                                            reservation.sysidReservation,
                                        sysidReservationActionType:
                                            ReservationActionType
                                                .CHECK_IN_NO_ROLL.id),
                                  );
                                },
                                onConfirm: () async {
                                  Get.back();
                                  await controller.callAction(
                                    AqReservationAction(
                                        sysidReservation:
                                            reservation.sysidReservation,
                                        sysidReservationActionType:
                                            ReservationActionType.CHECK_IN.id),
                                  );
                                });
                          }
                        }),
                  ),
                if (controller.isCheckOutBtn(reservation))
                  Container(
                    padding: const EdgeInsets.only(right: 10),
                    child: TaButton(
                        buttonText: 'Check Out',
                        //talabel.getAq(key: 'aq.reservations.manage.actions.checkout', defaultTxt: 'Check Out')?.tag,
                        type: 'elevate',
                        onPressed: () async {
                          await controller.callAction(
                            AqReservationAction(
                                sysidReservation: reservation.sysidReservation,
                                sysidReservationActionType:
                                    ReservationActionType.CHECK_OUT.id),
                          );
                        }),
                  ),
                if (controller.isCopyBtn(reservation))
                  Container(
                    padding: const EdgeInsets.only(right: 10),
                    child: TaButton(
                        buttonText: 'Copy',
                        //talabel.getAq(key: 'aq.mobile.cmd.copy', defaultTxt: 'Copy')?.tag,
                        type: 'elevate',
                        onPressed: () {
                          AqReservationAction(
                              sysidReservation: reservation.sysidReservation,
                              sysidReservationActionType:
                                  ReservationActionType.CHECK_IN.id);
                          debugPrint('btn click');
                        }),
                  ),
                if (controller.isEndBtn(reservation))
                  Container(
                    padding: const EdgeInsets.only(right: 10),
                    child: TaButton(
                        buttonText: talabel
                            .getAq(
                                key: 'aq.reservations.manage.actions.end',
                                defaultTxt: 'End')
                            ?.tag,
                        type: 'elevate',
                        onPressed: () async {
                          await controller.callAction(AqReservationAction(
                              sysidReservation: reservation.sysidReservation,
                              sysidReservationActionType:
                                  ReservationActionType.END.id));
                        }),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
