import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/screens/agilequest/floor_plan/aq_floor_test.dart';

import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/screens/agilequest/floor_plan/aq_floor_test1.dart';
import '../../../common/component_utils.dart';
import '../../../providers/agilequest/floorplan/aq_floor_plan_controller.dart';
import '../../home/<USER>';
import 'aq_floor_test2.dart';

class AqFloorPlan extends StatelessWidget {
  AqFloorPlan({super.key});
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  AqFloorPlanController aqFloorPlanState = Get.put(AqFloorPlanController());
  AqFloorPlanController aqFloorPlanCtrl = Get.find<AqFloorPlanController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Floor Plan', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.off(() => HomeScreen());
          },
        ),
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          GetX<AqFloorPlanController>(
            initState: (state) async {
              aqFloorPlanCtrl.isLoading.value = true;
              Future.delayed(const Duration(seconds: 1), () async {
                await aqFloorPlanCtrl.loadSvgImage();
              });
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? const ProgressIndicatorCust()
                  : Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.white10), color: Colors.white.withOpacity(0.2)),
                        child: InteractiveViewer(
                          //clipBehavior: Clip.none,
                          //alignment: Alignment.topLeft,
                          scaleEnabled: true,
                          panEnabled: true,
                          constrained: false,
                          // boundaryMargin: const EdgeInsets.all(150),
                          maxScale: 10,
                          minScale: 0.1,

                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            //crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              //Obx(
                              //   () =>
                              // Transform.scale(
                              //   scale: aqFloorPlanCtrl.scaleFactor.value,
                              //   child:

                              FittedBox(
                                fit: BoxFit.fitWidth,
                                // alignment: Alignment.centerLeft,
                                child: SizedBox(
                                  width: MediaQuery.of(context).size.width * 3,
                                  height: MediaQuery.of(context).size.height * 0.90,
                                  child: Aqtestcopy(),
                                ),
                              ),

                              // ),
                            ],
                          ),
                        ),
                      ),
                    );
            },
          ),
        ],
      ),
      floatingActionButton: Obx(
        () => Stack(
          fit: StackFit.expand,
          children: [
            Positioned(
              bottom: 10,
              right: 5,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  //aqFloorPlanCtrl.scaleFactor.value > 0.25
                  SizedBox(
                    height: 30,
                    child: FloatingActionButton(
                      heroTag: 'zoom out',
                      backgroundColor:
                          aqFloorPlanCtrl.scaleFactor.value > 0.25 ? context.theme.primaryColor : Colors.grey,
                      onPressed: () {
                        aqFloorPlanCtrl.scaleFactor.value > 0.25 ? aqFloorPlanCtrl.scaleFactor.value -= 0.25 : '';
                      },
                      child: const Icon(
                        Icons.remove,
                        size: 20,
                      ),
                    ),
                  ),
                  //if (aqFloorPlanCtrl.scaleFactor.value < 1.0)
                  SizedBox(
                    height: 30,
                    child: FloatingActionButton(
                      backgroundColor:
                          aqFloorPlanCtrl.scaleFactor.value < 1.0 ? context.theme.primaryColor : Colors.grey,
                      heroTag: 'zoom in',
                      onPressed: () {
                        aqFloorPlanCtrl.scaleFactor.value < 1.0 ? aqFloorPlanCtrl.scaleFactor.value += 0.25 : '';
                      },
                      child: const Icon(
                        Icons.add,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 10,
              left: 25,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  //aqFloorPlanCtrl.scaleFactor.value > 0.25
                  SizedBox(
                    height: 30,
                    child: FloatingActionButton(
                      heroTag: 'layers',
                      // backgroundColor: aqFloorPlanCtrl.scaleFactor.value > 0.25 ? context.theme.primaryColor : Colors.grey,
                      onPressed: () {
                        debugPrint('------------');
                        layerspopup();
                      },
                      child: const Icon(
                        Icons.layers,
                        size: 20,
                      ),
                    ),
                  ),
                  //if (aqFloorPlanCtrl.scaleFactor.value < 1.0)
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  layerspopup() {
    // aqFloorPlanCtrl.layerMap.value = {
    //   'base_layer': aqFloorPlanCtrl.baselayer.value ?? false,
    //   'space_layer': aqFloorPlanCtrl.spacelayer.value ?? false
    // };
    var flag = false;
    Get.defaultDialog(
        barrierDismissible: false,
        title: 'layers',
        titleStyle: const TextStyle(fontSize: 16),
        content: Obx(
          () => Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Base Layer',
                      style: TextStyle(
                          //color: primary,
                          fontSize: 12),
                    ),
                    Checkbox(
                        value: aqFloorPlanCtrl.baselayer.value,
                        onChanged: ((bool? value) {
                          if (value != null) {
                            aqFloorPlanCtrl.baselayer.value = value;
                            // aqFloorPlanCtrl.layerMap.value['base_layer'] = value ?? false;
                            flag = true;
                          }
                        }))
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Space Layer',
                      style: TextStyle(
                          //color: primary,
                          fontSize: 12),
                    ),
                    Checkbox(
                        value: aqFloorPlanCtrl.spacelayer.value,
                        onChanged: ((bool? value) {
                          if (value != null) {
                            aqFloorPlanCtrl.spacelayer.value = value;
                            //aqFloorPlanCtrl.layerMap.value['space_layer'] = value ?? false;
                            flag = true;
                          }
                        }))
                  ],
                ),
              ],
            ),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                  type: 'elevate',
                  buttonText: 'Close',
                  onPressed: () {
                    aqFloorPlanCtrl.baselayer.value = aqFloorPlanCtrl.layerMap.value['base_layer']!;
                    aqFloorPlanCtrl.spacelayer.value = aqFloorPlanCtrl.layerMap.value['space_layer']!;
                    Get.back();
                  }),
              const SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Apply',
                onPressed: () async {
                  debugPrint('layer map >>>> ${aqFloorPlanCtrl.layerMap.value}');
                  if (flag) {
                    aqFloorPlanCtrl.layerMap.value['base_layer'] = aqFloorPlanCtrl.baselayer.value;
                    aqFloorPlanCtrl.layerMap.value['space_layer'] = aqFloorPlanCtrl.spacelayer.value;
                    Get.back();
                    await aqFloorPlanCtrl.loadSvgImage();
                  }
                },
              ),
              const SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }
}
