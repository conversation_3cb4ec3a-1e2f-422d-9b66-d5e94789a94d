import 'dart:math';
import 'dart:ui' as shape;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_drawing/path_drawing.dart';
import 'package:touchable/touchable.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/agilequest/floorplan/aq_floor_plan_controller.dart';

class Aqtestcopy extends StatelessWidget {
  final XmlTag? xmltag;
  AqFloorPlanController aqFloorPlanCtrl = Get.find<AqFloorPlanController>();
  Aqtestcopy({
    super.key,
    this.xmltag,
  });
  var points = <Offset>[];
  Path path = Path();
  var paint = Paint();

  @override
  Widget build(BuildContext context) {
    return Container(
      // padding: EdgeInsets.all(20),
      // decoration: BoxDecoration(
      //     color: Colors.white,
      //     borderRadius: BorderRadius.circular(10.0),
      //     boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
      child: InkWell(
        key: GlobalKey(),
        child: Container(
          color: Colors.transparent,
          child: Obx(
            () => Transform.scale(
              scale: aqFloorPlanCtrl.scaleFactor.value,
              child: CanvasTouchDetector(
                key: GlobalKey(),
                gesturesToOverride: const [GestureType.onTapDown],
                builder: (context) {
                  return CustomPaint(
                    isComplex: true,
                    willChange: false,
                    painter: DataPainter(
                      context: context,
                      key: GlobalKey(),
                      points: points,
                      type: xmltag?.tag,
                      paintdata: paint,
                      path: path,
                      layer: xmltag?.layer,
                    ),
                  );
                },
                // ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class DataPainter extends CustomPainter {
  var xmlTags;
  final BuildContext context;
  final GlobalKey key;
  List<Offset>? points;
  String? type;
  Paint? paintdata;
  Path? path;
  String? layer;

  DataPainter({
    this.xmlTags,
    required this.key,
    required this.context,
    this.points,
    this.type,
    this.paintdata,
    this.path,
    this.layer,
  });

  AqFloorPlanController aqFloorPlanCtrl = Get.find<AqFloorPlanController>();
  @override
  void paint(Canvas canvas, Size size) {
    final touchyCanvas = TouchyCanvas(context, canvas);
    String pointsData;
    for (XmlTag xmltag in aqFloorPlanCtrl.allLayers.value) {
      layer = xmltag.layer;
      type = xmltag.tag;
      pointsData = xmltag.dpoints ?? '';
      paintdata = svgStyleToPaint(xmltag.style ?? '');
      //points = getPointsFromPolypoints(pointsData);

      if (layer == 'basePlanLayer_1') {
        if (xmltag.tag == 'polyline') {
          points = getPointsFromPolypoints(pointsData);
          touchyCanvas.drawPoints(shape.PointMode.polygon, points ?? <Offset>[], paintdata!);
        } else if (xmltag.tag == 'path') {
          path = parseSvgPathData(xmltag.dpoints ?? '');
          touchyCanvas.drawPath(path ?? Path(), paintdata ?? Paint());
        }
      } else if (layer == 'spacePolygonLayer_1') {
        //debugPrint('--------------spacePolygonLayer_1------ui--------');
        path = getPathFromPolypoints(xmltag.dpoints ?? '');
        touchyCanvas.drawPath(path ?? Path(), paintdata ?? Paint(), onTapDown: (details) {
          showDataBottomSheet(xmltag.id ?? '');
          //debugPrint('>>>>>>>>>${details.localPosition}');
          debugPrint('>>>>>>>>>${xmltag.id}');
        }, onPanUpdate: (detail) {
          debugPrint('----------Black line Swiped---------------------');
        });
      }
    }

    // canvas.drawOval(
    //     Rect.fromLTWH(100, 100, 300, 400),
    //     Paint()
    //       ..color = Colors.deepPurple
    //       ..style = PaintingStyle.stroke
    //       ..strokeWidth = 70, onTapDown: (_) {
    //   debugPrint("purple oval touched");
    // });

    // canvas.drawRect(
    //     Rect.fromLTWH(20, 300, 100, 300),
    //     Paint()
    //       ..color = Colors.deepOrange
    //       ..style = PaintingStyle.stroke
    //       ..strokeWidth = 50, onTapDown: (_) {
    //   debugPrint("orange rect touched");
    // });
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }

  showDataBottomSheet(String spaceid) {
    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      context: context,
      builder: (BuildContext context) {
        return SizedBox(
          height: 200,
          child: Container(
            padding: const EdgeInsets.only(top: 15),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Details',
                  style: TextStyle(color: ComponentUtils.primecolor, fontWeight: FontWeight.bold, fontSize: 18),
                ),
                const Divider(
                  color: Colors.grey,
                  thickness: 2,
                  height: 2,
                  indent: 25.0,
                  endIndent: 25.0,
                ),
                Container(
                  padding: const EdgeInsets.only(top: 15, left: 25),
                  child: Row(
                    children: <Widget>[
                      Text(
                        'Space id :',
                        style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 17),
                      ),
                      Text(
                        spaceid,
                        style: TextStyle(color: ComponentUtils.primary, fontSize: 15),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.only(top: 10, left: 25, right: 25),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Descrption :',
                        style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 17),
                      ),
                      Row(
                        children: <Widget>[
                          Flexible(
                            child: Text(
                              'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.',
                              style: TextStyle(color: ComponentUtils.primary, fontSize: 15),
                              maxLines: 4,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Offset> getPointsFromPolypoints(String pointsData) {
    var points = <Offset>[];
    var coords, x, y;
    int i = 0;
    try {
      for (var pair in pointsData.split(' ')) {
        i++;
        coords = pair.split(',');
        if (coords[0] != null && coords[0] != '') {
          x = getcoord(double.parse(coords[0]));
          y = getcoord(double.parse(coords[1]));
          points.add(Offset(x, y));
        }
      }
    } catch (e) {
      debugPrint('getPointsFromPolypoints>>>>>>$e');
    }
    return points;
  }

  double getcoord(double x) {
    try {
      String result = x.toStringAsFixed(2);
      x = double.parse(result);
    } catch (e) {
      debugPrint('getcoord>>>>>>$e');
    }
    return x;
  }

  Paint svgStyleToPaint(String svgStyleString) {
    Map<String, String> svgStyle = {};
    svgStyle = svgStyleStringToMap(svgStyleString);
    Paint paint = Paint();

    // Color
    if (svgStyle.containsKey('fill')) {
      paint.style = PaintingStyle.fill;
      paint.color = const Color(0xffc215d5).withOpacity(0.5);
    }

    // Stroke style
    if (svgStyle.containsKey('stroke')) {
      if (svgStyle.containsKey('fill')) {
        paint.style = PaintingStyle.fill;
      } else if (svgStyle.length == 1) {
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 0.5;
      } else {
        paint.style = PaintingStyle.stroke;
      }
    }
    return paint;
  }

  Map<String, String> svgStyleStringToMap(String svgStyleString) {
    Map<String, String> svgStyle = {};

    svgStyleString.split(';').forEach((styleDeclaration) {
      styleDeclaration = styleDeclaration.trim();

      if (styleDeclaration.contains(':')) {
        final indexOfColon = styleDeclaration.indexOf(':');
        final propertyName = styleDeclaration.substring(0, indexOfColon).trim();
        final propertyValue = styleDeclaration.substring(indexOfColon + 1).trim();

        svgStyle[propertyName] = propertyValue;
      }
    });

    return svgStyle;
  }

  Path getPathFromPolypoints(String pointsData) {
    Path path = Path();
    var coords, x, y;
    int i = 0;
    try {
      for (var pair in pointsData.split(' ')) {
        i++;
        coords = pair.split(',');
        if (coords[0] != null && coords[0] != '') {
          x = getcoord(double.parse(coords[0]));
          y = getcoord(double.parse(coords[1]));

          if (i == 1) {
            path.moveTo(x, y);
          } else {
            path.lineTo(x, y);
          }
        }
        // if (i > 0) {
        //   path.close();
        // }
      }
    } catch (e) {
      debugPrint('getPathFromPolypoints>>>>>>$e');
    }
    return path;
  }

  // @override
  // bool hitTest(Offset position) {
  //   return true;
  // }
}
