import 'dart:math';
import 'dart:ui' as shape;

import 'package:flutter/material.dart';
import 'package:path_drawing/path_drawing.dart';
import 'package:touchable/touchable.dart';

import 'package:get/get.dart';

import '../../../providers/agilequest/floorplan/aq_floor_plan_controller.dart';

class Aqtest2 extends StatelessWidget {
  final XmlTag? xmltag;

  Aqtest2({
    super.key,
    this.xmltag,
  });
  var points = <Offset>[];
  Path path = Path();
  var paint = Paint();

  double getcoord(double x) {
    String result = x.toStringAsFixed(2);
    x = double.parse(result);
    return x;
  }

  void fetchData() {
    try {
      var pointsData = xmltag?.dpoints ?? '';
      // paint = Paint()
      //   ..style = PaintingStyle.stroke
      //   ..strokeWidth = 0.5;
      paint = svgStyleToPaint(xmltag?.style ?? '');
      if (xmltag?.layer == 'basePlanLayer_1') {
        // debugPrint('--------------basePlanLayer_1--------------');
        if (xmltag?.tag == 'polyline') {
          points = getPointsFromPolypoints(pointsData);
        } else if (xmltag?.tag == 'path') {
          path = parseSvgPathData(xmltag?.dpoints ?? '');
        }
      } else if (xmltag?.layer == 'spacePolygonLayer_1') {
        // debugPrint('--------------spacePolygonLayer_1--------------');
        path = getPathFromPolypoints(xmltag?.dpoints ?? '');
      }
    } catch (e) {
      debugPrint('error in fetchdate>>>>> $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      key: GlobalKey(),
      child: Container(
        height: 500,
        width: 500,
        color: Colors.amber,
        child: CanvasTouchDetector(
          key: GlobalKey(),
          gesturesToOverride: const [GestureType.onTapDown],
          builder: (context) {
            return CustomPaint(
              isComplex: true,
              willChange: false,
              painter: DataPainter(
                context: context,
                key: GlobalKey(),
                points: points,
                type: xmltag?.tag,
                paintdata: paint,
                path: path,
                layer: xmltag?.layer,
              ),
            );
          },
        ),
      ),
    );
  }

  Path getPathFromPolypoints(String pointsData) {
    Path path = Path();
    var coords, x, y;
    int i = 0;
    for (var pair in pointsData.split(' ')) {
      i++;
      coords = pair.split(',');
      if (coords[0] != null && coords[0] != '') {
        x = getcoord(double.parse(coords[0]));
        y = getcoord(double.parse(coords[1]));

        if (i == 1) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      // if (i > 0) {
      //   path.close();
      // }
    }
    return path;
  }

  List<Offset> getPointsFromPolypoints(String pointsData) {
    var points = <Offset>[];
    var coords, x, y;
    int i = 0;
    for (var pair in pointsData.split(' ')) {
      i++;
      coords = pair.split(',');
      if (coords[0] != null && coords[0] != '') {
        x = getcoord(double.parse(coords[0]));
        y = getcoord(double.parse(coords[1]));

        points.add(Offset(x, y));
      }
    }
    return points;
  }

  Paint svgStyleToPaint(String svgStyleString) {
    Map<String, String> svgStyle = {};
    svgStyle = svgStyleStringToMap(svgStyleString);
    Paint paint = Paint();

    // Color
    if (svgStyle.containsKey('fill')) {
      paint.style = PaintingStyle.fill;
      paint.color = const Color(0xffc215d5).withOpacity(0.5);
    }

    // Stroke style
    if (svgStyle.containsKey('stroke')) {
      if (svgStyle.containsKey('fill')) {
        paint.style = PaintingStyle.fill;
      } else if (svgStyle.length == 1) {
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 0.5;
      } else {
        paint.style = PaintingStyle.stroke;
      }
    }
    //if (paint.color != Colors.black) paint.style = PaintingStyle.fill;
    // debugPrint('paint>>>>>$paint');

    return paint;
  }

  Map<String, String> svgStyleStringToMap(String svgStyleString) {
    Map<String, String> svgStyle = {};

    svgStyleString.split(';').forEach((styleDeclaration) {
      styleDeclaration = styleDeclaration.trim();

      if (styleDeclaration.contains(':')) {
        final indexOfColon = styleDeclaration.indexOf(':');
        final propertyName = styleDeclaration.substring(0, indexOfColon).trim();
        final propertyValue = styleDeclaration.substring(indexOfColon + 1).trim();

        svgStyle[propertyName] = propertyValue;
      }
    });

    return svgStyle;
  }
}

class DataPainter extends CustomPainter {
  final BuildContext context;
  final GlobalKey key;
  List<Offset>? points;
  String? type;
  Paint? paintdata;
  Path? path;
  String? layer;

  DataPainter({
    required this.key,
    required this.context,
    this.points,
    this.type,
    this.paintdata,
    this.path,
    this.layer,
  });

  @override
  void paint(Canvas baseCanvas, Size size) {
    TouchyCanvas touchyCanvas = TouchyCanvas(context, baseCanvas);

    touchyCanvas.drawOval(
        const Rect.fromLTWH(100, 100, 300, 400),
        Paint()
          ..color = Colors.deepPurple
          ..style = PaintingStyle.stroke
          ..strokeWidth = 70, onTapDown: (_) {
      debugPrint("purple oval touched");
    });

    touchyCanvas.drawRect(
        const Rect.fromLTWH(20, 300, 100, 300),
        Paint()
          ..color = Colors.deepOrange
          ..style = PaintingStyle.stroke
          ..strokeWidth = 50, onTapDown: (_) {
      debugPrint("orange rect touched");
    });

    // if (layer == 'basePlanLayer_1') {
    //   if (type == 'polyline') {
    //     touchyCanvas.drawPoints(shape.PointMode.polygon, points ?? <Offset>[], paintdata!);
    //   } else if (type == 'path') {
    //     touchyCanvas.drawPath(path ?? Path(), paintdata ?? Paint());
    //   }
    // } else if (layer == 'spacePolygonLayer_1') {
    //   //debugPrint('--------------spacePolygonLayer_1------ui--------');
    //   touchyCanvas.drawPath(path ?? Path(), paintdata ?? Paint(), onTapDown: (details) {
    //     debugPrint('>>>>>>>>>${details.localPosition}');
    //   }, onPanUpdate: (detail) {
    //     debugPrint('Black line Swiped');
    //   });
    // }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }

  // @override
  // bool hitTest(Offset position) {
  //   return true;
  // }
}
