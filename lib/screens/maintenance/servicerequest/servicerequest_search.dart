import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/servicerequest/service_request.dart';
import '../../../providers/maintenance/servicerequest_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';
import 'servicerequest_details.dart';
import 'servicerequest_search_widget.dart';

class ServiceRequests extends StatelessWidget {
  ServiceRequests({super.key});
  LabelController talabel = Get.find<LabelController>();
  ServiceRequestController srCtrl = Get.put(ServiceRequestController());
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_SERVICEREQUEST')!.value!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        actions: [
          IconButton(
              onPressed: () async {
                Get.to(() => ServicerequestSearchWidget());
                srCtrl.filterwidgetloading.value = true;
                await talabel.getlabels('TMCMOBILE_SITESEARCH', 'Site_search', filtertype: 'tab');
                Future.delayed(const Duration(seconds: 1), () async {
                  srCtrl.filterwidgetloading.value = false;
                });
              },
              icon: Icon(
                Icons.search,
                color: ComponentUtils.primecolor,
              )),
        ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            // Get.off(() => HomeScreen());
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      floatingActionButton: FloatingActionButton(
          mini: true,
          onPressed: () {
            srCtrl.sr_mode.value = 'create';
            srCtrl.servicerequest.value = Servicerequest();
            srCtrl.servicerequest.value.entityType = 'STORE';
            srCtrl.servicerequest.value.status = 'Draft';
            srCtrl.servicerequest.value.priority = 'Low';
            Get.to(() => ServiceRequestDetails());
          },
          child: const Icon(Icons.add)),
      body: Column(
        children: <Widget>[
          filterChipContainer(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshSrList,
              child: GetX<ServiceRequestController>(
                initState: (state) async {
                  srCtrl.labelsloading.value = true;
                  srCtrl.isListLoading.value = true;
                  Future.delayed(const Duration(seconds: 0), () async {
                    debugPrint('openstatus----    ${srCtrl.srstatus.value}');
                    await srCtrl.getlabels(talabel);
                    await srCtrl.getServicerequestData(action: 'onload');
                    srCtrl.labelsloading.value = false;
                  });
                },
                builder: (_) {
                  return _.isListLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.srList.isEmpty
                          ? Center(
                              child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.srList[index]);
                              },
                              itemCount: _.srList.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterChipContainer() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.only(left: 12, right: 10, top: 5, bottom: 2),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Wrap(
                alignment: WrapAlignment.start,
                spacing: 6.0,
                runSpacing: 6.0,
                children: List<Widget>.generate(srCtrl.filterList.length, (int index) {
                  return srCtrl.filterList[index];
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future _refreshSrList() async {
    await srCtrl.getServicerequestData();
  }

  Widget listitem(BuildContext context, Servicerequest sr) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          srCtrl.sr_mode.value = 'edit';
          srCtrl.servicerequest.value = Servicerequest();
          srCtrl.servicerequest.value = sr;
          Get.to(() => ServiceRequestDetails());
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            sr.serviceReqNumber ?? '',
                            style: TextStyle(
                                color: primary,
                                fontWeight: FontWeight.bold,
                                fontSize: DeviceUtils.taFontSize(1.8, context)),
                          ),
                          Text('${sr.status}',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.3, context),
                                fontWeight: FontWeight.bold,
                              )),
                        ]),
                    const SizedBox(height: 6.0, width: 0),
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Flexible(
                            child: Text(sr.storeName?.toString() ?? '',
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: primary,
                                  fontSize: DeviceUtils.taFontSize(1.5, context),
                                )),
                          ),
                          Text('${(sr.creationDate)}',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                              )),
                        ]),
                    const SizedBox(height: 4.0, width: 0),
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(sr.issueType ?? '',
                              style: TextStyle(
                                color: primary,
                                fontSize: DeviceUtils.taFontSize(1.5, context),
                              )),
                          // Text('${(sr.creationDate)}',
                          //     style: TextStyle(
                          //       fontSize: DeviceUtils.taFontSize(1.5, context),
                          //     )),
                        ]),
                    const SizedBox(
                      height: 0.0,
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
