import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/photos/photospg.dart';
import 'package:tangoworkplace/screens/maintenance/servicerequest/sr_datasearch.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';
import '../../../models/servicerequest/sr_assetinstance.dart';
import '../../../models/servicerequest/sr_floor.dart';
import '../../../models/servicerequest/sr_space.dart';
import '../../../providers/maintenance/servicerequest_controller.dart';
import '../../common/utils/dms/dmsfilepg.dart';
import '../../common/utils/qrcodeview.dart';

class ServiceRequestDetails extends StatelessWidget {
  ServiceRequestDetails({
    super.key,
  });
  LabelController talabel = Get.find<LabelController>();
  ServiceRequestController srCtrl = Get.find<ServiceRequestController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  @override
  Widget build(BuildContext context) {
    return GetX<LabelController>(
      initState: (state) {
        srCtrl.labelsloading.value = true;
        srCtrl.isdetailsloading.value = true;

        Future.delayed(const Duration(seconds: 1), () async {
          await srCtrl.getlabels(talabel);
          await srCtrl.setCurrentRow();
          srCtrl.labelsloading.value = false;
        });
      },
      builder: (_) {
        return (srCtrl.labelsloading.value || srCtrl.isdetailsloading.value)
            ? ComponentUtils.labelLoadScaffold()
            : Scaffold(
                appBar: AppBar(
                  iconTheme: Theme.of(context).appBarTheme.iconTheme,
                  title: Text(talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS')?.value ?? '',
                      style: ComponentUtils.appbartitlestyle),
                  backgroundColor: Colors.white,
                  elevation: 5,
                  leading: IconButton(
                    icon: ComponentUtils.backpageIcon,
                    color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                    onPressed: () {
                      debugPrint('------back-------');
                      Get.back();
                      srCtrl.getServicerequestData();
                    },
                  ),
                  actions: [
                    Obx(
                      () =>
                          (srCtrl.readonly.value || (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_QRSCANNER') == null))
                              ? Container()
                              : IconButton(
                                  onPressed: () async {
                                    await Permission.camera.request();
                                    String? res = await Get.to(() => const QrCodeView());
                                    debugPrint('qrcode data===============$res');

                                    if (res != null) {
                                      await getQRAssetData(res);
                                    }
                                  },
                                  icon: Icon(
                                    Icons.qr_code_scanner,
                                    color: ComponentUtils.primecolor,
                                  ),
                                ),
                    ),
                  ],
                ),
                body: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: srDetForm(),
                ),
                bottomNavigationBar: bottombuttonbar(_formKey),
              );
      },
    );
  }

  Widget srDetForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Obx(
        () => Column(
          children: <Widget>[
            //sr number
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SRNUMBER') != null && srCtrl.sr_mode.value != 'create')
              TaFormInputText(
                  label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SRNUMBER')!.value,
                  readOnly: true, //talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SRNUMBER').ro,
                  value: srCtrl.servicerequest.value.serviceReqNumber ?? ''),
            //sr status
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_STATUS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_STATUS')!.value,
                readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER').ro,
                value: srCtrl.servicerequest.value.status ?? '',
              ),
//Entity id
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ENTITYID') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ENTITYID')?.value,
                readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER').ro,
                // value: srCtrl.servicerequest.value.storeName ?? '',
                controller: srCtrl.locationCtrl,
                suffixbutton: IconButton(
                  onPressed: () async {
                    if (!srCtrl.readonly.value && !talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ENTITYID')!.ro!) {
                      var oldvalue = srCtrl.servicerequest.value.entityId;
                      await Get.to(
                        () => SrDatasearch(
                          hdrtext: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ENTITYID')!.value,
                          stype: 'location',
                        ),
                      );
                      if (oldvalue != srCtrl.servicerequest.value.entityId) {
                        srCtrl.locationCtrl.text = srCtrl.servicerequest.value.storeName!;
                        srCtrl.floorLov.value = [];
                        srCtrl.servicerequest.value.floorId = null;

                        srCtrl.servicerequest.value.spaceId = null;
                        srCtrl.servicerequest.value.spaceDisplayName = null;
                        srCtrl.spaceNameCtrl.text = '';

                        srCtrl.assetInstanceNameCtrl.text = '';
                        srCtrl.servicerequest.value.assetInstanceName = '';
                        srCtrl.servicerequest.value.assetInstanceId = null;

                        //  Future.delayed(const Duration(seconds: 1), () async {
                        await srCtrl.getFloorLovData();
                        srCtrl.servicerequest.value.floorId = null;
                        // });
                      }
                    } else {
                      return;
                    }
                  },
                  icon: const Icon(Icons.search),
                ),
                onChanged: (val) {
                  debugPrint('val--------$val');
                  srCtrl.locationCtrl.text = val;
                  srCtrl.servicerequest.value.storeName = val;
                },
                inputtype: (srCtrl.readonly.value || talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ENTITYID')!.ro!)
                    ? ''
                    : 'inputlov',
                validate: (String? value) {
                  if (value!.isEmpty) {
                    return 'Location is Required';
                  }
                  return null;
                },
              ),
//floor
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_FLOOR') != null)
              Row(
                children: [
                  SizedBox(
                    //height: 70.0,
                    width: Get.width - 80,
                    child: TaFormDropdown(
                      label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_FLOOR')!.value,
                      emptttext: 'Select',
                      readonly: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_FLOOR')!.ro! || srCtrl.readonly.value),
                      onChanged: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_FLOOR')!.ro! || srCtrl.readonly.value)
                          ? null
                          : (newValue) async {
                              debugPrint('newValue          $newValue');
                              srCtrl.servicerequest.value.floorId = newValue;
                              srCtrl.servicerequest.value.spaceId = null;
                              srCtrl.servicerequest.value.spaceDisplayName = null;
                              srCtrl.spaceNameCtrl.text = '';

                              srCtrl.assetInstanceNameCtrl.text = '';
                              srCtrl.servicerequest.value.assetInstanceName = '';
                              srCtrl.servicerequest.value.assetInstanceId = null;
                            },
                      value:
                          (srCtrl.servicerequest.value.floorId == '' || srCtrl.servicerequest.value.floorId == 'null')
                              ? null
                              : srCtrl.servicerequest.value.floorId,
                      listflag: (srCtrl.floorLov.value.isNotEmpty),
                      items: srCtrl.floorLov.value.map((SrFloors f) {
                        return DropdownMenuItem(
                          value: f.storeId,
                          child: Text(
                            f.storeName!,
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  IconButton(
                      alignment: Alignment.centerRight,
                      padding: EdgeInsets.zero,
                      tooltip: 'view floorplan',
                      icon: const Image(image: AssetImage('lib/icons/plan-icon.png')),
                      iconSize: 35,
                      color: CommonUtils.createMaterialColor(const Color(0XFF394251)),
                      //onPressed: () {},
                      onPressed: () {
                        if (srCtrl.servicerequest.value.floorId != null && srCtrl.servicerequest.value.floorId != 0) {
                          CommonUtils.cadviewer(Get.context!, srCtrl.servicerequest.value.entityId?.toString(),
                              srCtrl.servicerequest.value.floorId?.toString(), '',
                              action: 'sr');
                        } else {
                          return;
                        }
                      }),
                ],
              ),

            //Space Id
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')!.value,
                readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER').ro,
                // value: srCtrl.servicerequest.value.spaceDisplayName ?? '',
                controller: srCtrl.spaceNameCtrl,
                suffixbutton: IconButton(
                  onPressed: () async {
                    if (!srCtrl.readonly.value &&
                        !talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')!.ro! &&
                        !(srCtrl.servicerequest.value.floorId == null || srCtrl.servicerequest.value.floorId == 0)) {
                      await Get.to(
                        () => SrDatasearch(
                          hdrtext: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')!.value,
                          stype: 'space',
                          onselect: (SrSpace space) async {
                            // srCtrl.floorLov.clear();
                            //  await srCtrl.getFloorLovData();
                            //  srCtrl.servicerequest.value.floorId = space.floorId;
                          },
                        ),
                      );
                      srCtrl.spaceNameCtrl.text = srCtrl.servicerequest.value.spaceDisplayName ?? '';
                    }
                  },
                  icon: const Icon(Icons.search),
                ),
                onChanged: (val) {
                  debugPrint('space val--------$val');

                  srCtrl.servicerequest.value.assetInstanceName = null;
                  srCtrl.servicerequest.value.assetInstanceId = null;
                  srCtrl.assetInstanceNameCtrl.text = '';

                  srCtrl.spaceNameCtrl.text = val;
                  srCtrl.servicerequest.value.spaceDisplayName = val;
                },
                inputtype: (srCtrl.readonly.value || talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')!.ro!)
                    ? ''
                    : 'inputlov',
                // validate: (String value) {
                //   if (value.isEmpty) {
                //     return 'Space is Required';
                //   }
                //   return null;
                // },
              ),
            //asset name
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCEID') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCEID')!.value,
                readOnly: true,
                controller: srCtrl.assetInstanceNameCtrl,
                suffixbutton: IconButton(
                  onPressed: () async {
                    if (!srCtrl.readonly.value &&
                        !talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCEID')!.ro!) {
                      await Get.to(
                        () => SrDatasearch(
                          hdrtext: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCEID')!.value,
                          stype: 'asset_instance',
                        ),
                      );
                      srCtrl.assetInstanceNameCtrl.text = srCtrl.servicerequest.value.assetInstanceName!;
                    }
                  },
                  icon: const Icon(Icons.search),
                ),
                onChanged: (val) {
                  debugPrint('space val--------$val');
                  srCtrl.assetInstanceNameCtrl.text = val;
                  srCtrl.servicerequest.value.assetInstanceName = val;
                },
                inputtype:
                    (srCtrl.readonly.value || talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCEID')!.ro!)
                        ? ''
                        : 'inputlov',
                // validate: (String value) {
                //   if (value.isEmpty) {
                //     return 'Space is Required';
                //   }
                //   return null;
                // },
              ),
// issue type
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE') != null)
              TaFormDropdown(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE')!.value,
                emptttext: 'Select',
                readonly: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE')!.ro! || srCtrl.readonly.value),
                onChanged: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE')!.ro! || srCtrl.readonly.value)
                    ? null
                    : (newValue) async {
                        debugPrint('newValue          ' + newValue);
                        srCtrl.servicerequest.value.issueType = newValue;
                        await srCtrl.onIssueTypeVC(newValue);
                      },
                value: (srCtrl.servicerequest.value.issueType == '' || srCtrl.servicerequest.value.issueType == 'null')
                    ? null
                    : srCtrl.servicerequest.value.issueType,
                listflag: (srCtrl.issueTypeLov.value.isNotEmpty),
                items: srCtrl.issueTypeLov.value.map((LookupValues l) {
                  return DropdownMenuItem(
                    value: l.lookupCode,
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                  );
                }).toList(),
                validate: (value) {
                  if (value == null || value.isEmpty) {
                    String vmsg = '${talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE')!.value!} is Required';
                    return vmsg;
                  }
                  return null;
                },
              ),

            //impacted area
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_IMPACTEDAREA') != null)
              TaFormDropdown(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_IMPACTEDAREA')!.value,
                emptttext: 'Select',
                readonly: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_IMPACTEDAREA')!.ro! || srCtrl.readonly.value),
                onChanged: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_IMPACTEDAREA')!.ro! || srCtrl.readonly.value)
                    ? null
                    : (newValue) async {
                        debugPrint('newValue          ' + newValue);
                        srCtrl.servicerequest.value.impactedArea = newValue;
                        await srCtrl.onImpactedAreaVC(newValue);
                      },
                value: (srCtrl.servicerequest.value.impactedArea == '' ||
                        srCtrl.servicerequest.value.impactedArea == 'null')
                    ? null
                    : srCtrl.servicerequest.value.impactedArea,
                listflag: (srCtrl.impactAreaLov.value.isNotEmpty),
                items: srCtrl.impactAreaLov.value.map((LookupValues l) {
                  return DropdownMenuItem(
                    value: l.lookupCode,
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                  );
                }).toList(),
                validate: (value) {
                  if (value == null || value.isEmpty) {
                    String vmsg = '${talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_IMPACTEDAREA')!.value!} is Required';
                    return vmsg;
                  }
                  return null;
                },
              ),

            //problem code
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PROBLEMCODE') != null)
              TaFormDropdown(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PROBLEMCODE')!.value,
                emptttext: 'Select',
                readonly: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PROBLEMCODE')!.ro! || srCtrl.readonly.value),
                onChanged: (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PROBLEMCODE')!.ro! || srCtrl.readonly.value)
                    ? null
                    : (newValue) async {
                        debugPrint('newValue          ' + newValue);
                        srCtrl.servicerequest.value.problemCode = newValue;
                      },
                value:
                    (srCtrl.servicerequest.value.problemCode == '' || srCtrl.servicerequest.value.problemCode == 'null')
                        ? null
                        : srCtrl.servicerequest.value.problemCode,
                listflag: (srCtrl.problemCodeLov.value.isNotEmpty),
                items: srCtrl.problemCodeLov.value.map((LookupValues l) {
                  return DropdownMenuItem(
                    value: l.lookupCode,
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                  );
                }).toList(),
                validate: (value) {
                  if (value == null || value.isEmpty) {
                    String vmsg = '${talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PROBLEMCODE')!.value!} is Required';
                    return vmsg;
                  }
                  return null;
                },
              ),
            //description
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_DESCRIPTION') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_DESCRIPTION')!.value,
                readOnly: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_DESCRIPTION')!.ro! || srCtrl.readonly.value,
                value: srCtrl.servicerequest.value.description ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  srCtrl.servicerequest.value.description = val;
                },
                onSaved: (val) {
                  srCtrl.servicerequest.value.description = val;
                },
                validate: (val) {
                  if (val == null || val == '') {
                    String vmsg = '${talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_DESCRIPTION')!.value!} is Required';
                    return vmsg;
                  }
                  return null;
                },
              ),
            //requested for
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_REQUESTEDFOR') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_REQUESTEDFOR')!.value,
                readOnly: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_REQUESTEDFOR')!.ro! || srCtrl.readonly.value,
                value: srCtrl.servicerequest.value.requestedFor ?? '',
                onChanged: (val) {
                  srCtrl.servicerequest.value.requestedFor = val;
                },
                onSaved: (val) {
                  srCtrl.servicerequest.value.requestedFor = val;
                },
              ),
            //phone
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PHONENUM') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PHONENUM')!.value,
                readOnly: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PHONENUM')!.ro! || srCtrl.readonly.value,
                value: srCtrl.servicerequest.value.phone ?? '',
                onChanged: (val) {
                  srCtrl.servicerequest.value.phone = val;
                },
                onSaved: (val) {
                  srCtrl.servicerequest.value.phone = val;
                },
                keyboard: TextInputType.number,
              ),
            //notify Email
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_NOTIFYEMAIL') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_NOTIFYEMAIL')!.value,
                readOnly: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_NOTIFYEMAIL')!.ro! || srCtrl.readonly.value,
                value: srCtrl.servicerequest.value.notifyEmail ?? '',
                onChanged: (val) {
                  srCtrl.servicerequest.value.notifyEmail = val;
                },
                onSaved: (val) {
                  srCtrl.servicerequest.value.notifyEmail = val;
                },
              ),

            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PHOTOS') != null)
              photos(talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_PHOTOS')!.value!),
            if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_DOCUMENTS') != null)
              documents(talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_DOCUMENTS')!.value!),
          ],
        ),
      ),
    );
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      color: Colors.white,
      child: Obx(
        () => Container(
          margin: const EdgeInsets.only(left: 12.0, right: 12.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  Get.back();
                  srCtrl.getServicerequestData();
                },
              ),
              if (!srCtrl.readonly.value)
                const SizedBox(
                  height: 5.0,
                  width: 5.0,
                ),
              if (!srCtrl.readonly.value)
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SUBMIT')?.value ?? 'Submit',
                  onPressed: () async {
                    final isValid = fkey.currentState!.validate();
                    if (!isValid) {
                      return;
                    }

                    _formKey.currentState!.save();
                    debugPrint('------------------saved--------------------');
                    srCtrl.servicerequest.value.status = 'Submit Pending';
                    await srCtrl.onSaveServiceRequest();
                  },
                ),
              // if (srCtrl.servicerequest.value.status == 'Draft')
              //   SizedBox(
              //     height: 5.0,
              //     width: 5.0,
              //   ),
              // if (srCtrl.servicerequest.value.status == 'Draft')
              //   TaButton(
              //     type: 'elevate',
              //     buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
              //     onPressed: () async {
              //       final isValid = fkey.currentState!.validate();
              //       if (!isValid) {
              //         return;
              //       }

              //       _formKey.currentState!.save();
              //       debugPrint('------------------saved--------------------');
              //       await srCtrl.onSaveServiceRequest();
              //     },
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  Widget photos(String labelStr) {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => PhotosPG(
                    entityId: srCtrl.servicerequest.value.serviceReqId,
                    entityType: 'SERVICE_REQUEST',
                    mode: (srCtrl.readonly.value) ? 'view' : 'edit',
                  ));
            },
            icon: Icon(
              Icons.photo_library,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget documents(String labelStr) {
    return Container(
      padding: const EdgeInsets.only(bottom: 10),
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => DmsFilePG(
                    entityId: srCtrl.servicerequest.value.serviceReqId,
                    entityType: 'SERVICE_REQUEST',
                    subentityid: srCtrl.servicerequest.value.serviceReqId,
                    subentitytype: 'SERVICE_REQUEST',
                    file_mode: (srCtrl.readonly.value) ? 'view' : 'edit',
                  ));
            },
            icon: Icon(
              Icons.file_present,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Future getQRAssetData(String assetinstid) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    await srCtrl.getAssetInstanceData(assetinstid: assetinstid);
    ProgressUtil.closeLoaderDialog(Get.context!);

    if (srCtrl.assetinstList.value.isNotEmpty && srCtrl.assetinstList.value.isNotEmpty) {
      SrAssetInstance assetdata = srCtrl.assetinstList.value.first;
      return Get.defaultDialog(
        title: 'QR Asset Data',
        titleStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: ComponentUtils.primecolor),
        content: Container(
            padding: const EdgeInsets.fromLTRB(15.0, 0.0, 15.0, 0.0),
            child: Column(
              children: [
                if (assetdata.entityName != null)
                  assetAttribute(talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ENTITYID')?.value ?? 'Location',
                      assetdata.entityName),
                if (assetdata.floorName != null)
                  assetAttribute(
                      talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_FLOOR')?.value ?? 'Floor', assetdata.floorName),
                if (assetdata.spaceName != null)
                  assetAttribute(
                      talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SPACE')?.value ?? 'Space', assetdata.spaceName),
                if (assetdata.assetInstanceName != null)
                  assetAttribute(
                      talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ASSETINSTANCEID')?.value ?? 'Asset Instance',
                      assetdata.assetInstanceName),
              ],
            )),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CLOSE')?.value ?? 'Close',
                onPressed: () {
                  Get.back();
                },
              ),
              const SizedBox(
                width: 15.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SELECT')?.value ?? 'Select',
                onPressed: () async {
                  srCtrl.locationCtrl.text = assetdata.entityName ?? '';
                  srCtrl.servicerequest.value.storeName = assetdata.entityName ?? '';
                  srCtrl.servicerequest.value.entityId = assetdata.locationId;

                  await srCtrl.getFloorLovData();
                  srCtrl.servicerequest.value.floorId = assetdata.floorId;

                  srCtrl.spaceNameCtrl.text = assetdata.spaceName ?? '';
                  srCtrl.servicerequest.value.spaceDisplayName = assetdata.spaceName ?? '';
                  srCtrl.servicerequest.value.spaceId = assetdata.spaceId;

                  srCtrl.assetInstanceNameCtrl.text = assetdata.assetInstanceName ?? '';
                  srCtrl.servicerequest.value.assetInstanceName = assetdata.assetInstanceName ?? '';
                  srCtrl.servicerequest.value.assetInstanceId = assetdata.assetInstanceId;

                  Get.back();
                },
              ),
              const SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ],
      );
    } else {
      return ComponentUtils.showpopup(msg: 'No data available with this QR code...');
    }
  }

  Widget assetAttribute(String label, String? val) {
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              label ?? '',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            Text(
              val ?? '',
              style: const TextStyle(
                fontSize: 14,
                //fontWeight: FontWeight.bold
              ),
            ),
          ],
        ));
  }
}
