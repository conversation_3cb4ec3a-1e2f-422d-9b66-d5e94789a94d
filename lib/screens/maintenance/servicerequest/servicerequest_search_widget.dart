import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';

import '../../../../common/component_utils.dart';
import '../../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../../models/ta_admin/entitystatus.dart';
import '../../../providers/maintenance/servicerequest_controller.dart';

class ServicerequestSearchWidget extends GetView<ServiceRequestController> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  ServiceRequestController srCtrl = Get.find<ServiceRequestController>();
  LabelController talabel = Get.find<LabelController>();

  ServicerequestSearchWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Search', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
        actions: [
          IconButton(
              onPressed: () {
                Get.off(() => HomeScreen());
              },
              icon: Icon(
                Icons.home,
                color: ComponentUtils.primecolor,
              )),
        ],
      ),
      key: _scaffoldKey,
      body: Obx(
        () => srCtrl.filterwidgetloading.isTrue
            ? const ProgressIndicatorCust()
            : Container(
                // width: double.infinity,
                margin: const EdgeInsets.only(left: 10.0, right: 10.0, top: 5.0),
                constraints: const BoxConstraints(maxWidth: 500),
                padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
                // height: 200,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.0),
                    boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                // child: Column(
                //   children: [
                child: Column(
                  children: <Widget>[
                    TaSearchInputText(
                        autofocus: true,
                        makeSearch: (searchtext) async {
                          Get.back();
                          await srCtrl.getServicerequestData(searchText: srCtrl.searchController.text);
                        },
                        searchController: srCtrl.searchController,
                        hintSearch: 'Search '),
                    const SizedBox(
                      width: 10.0,
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                      const SizedBox(
                        width: 10.0,
                      ),

                      Radio(
                        value: 'Open',
                        groupValue: srCtrl.openCloseFilter.value,
                        onChanged: (dynamic value) {
                          srCtrl.openCloseFilter.value = value;
                        },
                      ),

                      const Text(
                        'Open',
                        style: TextStyle(fontSize: 12),
                      ),
                      const SizedBox(
                        width: 10.0,
                      ),

                      Radio(
                        value: 'Completed',
                        groupValue: srCtrl.openCloseFilter.value,
                        onChanged: (dynamic value) {
                          srCtrl.openCloseFilter.value = value;
                        },
                      ),

                      const Text(
                        'Completed',
                        style: TextStyle(fontSize: 12),
                      ),
                      // ),
                    ]),
                    const SizedBox(
                      width: 10.0,
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 10.0, top: 10.0),
                      child: TaDropDown(
                        width: double.infinity,
                        label: 'Filter By',
                        initvalue: srCtrl.srFilterBy.value,
                        value: srCtrl.srFilterBy.value,
                        items: srCtrl.filterByLov.value.map((EntityStatus e) {
                          return DropdownMenuItem(
                            value: e.statusCode,
                            child: Text(
                              e.status!,
                              style: const TextStyle(fontSize: 14, color: Colors.black),
                            ),
                          );
                        }).toList(),
                        onChanged: (val) async {
                          debugPrint('val -----' + val);
                          srCtrl.srFilterBy.value = val;
                        },
                      ),
                    ),
                    // if (talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE') != null)
                    Container(
                      margin: const EdgeInsets.only(left: 10.0, top: 10.0),
                      child: TaDropDown(
                        width: double.infinity,
                        label: 'Sort By',
                        initvalue: srCtrl.srSortBy.value,
                        value: srCtrl.srSortBy.value,
                        items: srCtrl.sortByLov.value.map((EntityStatus e) {
                          return DropdownMenuItem(
                            value: e.statusCode,
                            child: Text(
                              e.status!,
                              style: const TextStyle(fontSize: 14, color: Colors.black),
                            ),
                          );
                        }).toList(),
                        onChanged: (val) async {
                          debugPrint('val -----' + val);
                          srCtrl.srSortBy.value = val;
                        },
                      ),
                    ),
                    // if (talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE') != null)

                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          TaButton(
                            type: 'elevate',
                            buttonText: 'Back',
                            readOnly: false,
                            onPressed: () => Get.back(),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          TaButton(
                            type: 'elevate',
                            buttonText: 'Apply',
                            readOnly: false,
                            onPressed: () async {
                              Get.back();
                              await srCtrl.getServicerequestData(searchText: srCtrl.searchController.text);
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}
