import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/sitemanagement/target/targetbvo.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../models/sitemanagement/target/targetview.dart';
import '../../../../providers/sitemanagement/target/target_general_controller.dart';

class TargetGeneral extends GetView<TargetGeneralController> {
  Targetbvo? target;
  String? mode;

  TargetGeneral({super.key, this.target, this.mode});

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  TargetGeneralController genCtrl = Get.find<TargetGeneralController>();

  @override
  Widget build(BuildContext context) {
    genCtrl.labelsloading.value = true;

    return Stack(children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            try {
              genCtrl.isloading.value = true;
              Future.delayed(Duration.zero, () async {
                await genCtrl.getlabels(talabel);
              });
            } finally {
              genCtrl.isloading.value = false;
            }
          },
          builder: (_) {
            return (genCtrl.labelsloading.value) ? const ProgressIndicatorCust() : generalTab(target);
          },
        ),
      ),
    ]);
  }

  Widget generalTab(Targetbvo? t) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_TARGETNUM') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_TARGETNUM')!.value,
              value: t?.targetNumber,
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_TARGETNAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_TARGETNAME')!.value,
              value: t!.targetName,
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_STATUS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_STATUS')!.value,
              value: t!.statusDesc,
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_PROPOSEDSTARTDATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_PROPOSEDSTARTDATE')!.value,
              value: t!.proposedStartDate,
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_TIER') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_TIER')!.value,
              value: t!.teir,
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_ANNUALPLAN') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_ANNUALPLAN')!.value,
              value: t!.annualPlan?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_LATITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_LATITUDE')!.value,
              value: t!.latitude?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_LONGITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_LONGITUDE')!.value,
              value: t!.longitude?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_ADDRESS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_ADDRESS')!.value,
              value: t!.address?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_CITY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_CITY')!.value,
              value: t!.city?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_STATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_STATE')!.value,
              value: t!.state?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_ZIPCODE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_ZIPCODE')!.value,
              value: t!.zipCode?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_COUNTRY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_COUNTRY')!.value,
              value: t!.country?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_DMANAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_DMANAME')!.value,
              value: t!.dmaName?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_TARGET_GENERAL_COUNTY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_TARGET_GENERAL_COUNTY')!.value,
              value: t!.county?.toString(),
              readOnly: true,
            ),
        ],
      ),
    );
  }
}
