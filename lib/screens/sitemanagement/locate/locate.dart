import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter_mailer/flutter_mailer.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/component_widgets/custommapinfowindowctrl.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';

import '../../../common/widgets/email_sender.dart' as taemail;
import '../../../models/sitemanagement/locate/locatedata.dart';
import '../../../providers/sitemanagement/locate/locate_controller.dart';

import 'package:image_painter/image_painter.dart';

class Locate extends StatelessWidget {
  Locate({super.key});
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  LocateController locateState = Get.put(LocateController());

  LocateController locateCtrlr = Get.find<LocateController>();
  LabelController talabel = Get.find<LabelController>();
  final imageKey = GlobalKey<ImagePainterState>();
  final Completer<GoogleMapController> _controller = Completer();
  final ImagePainterController _imagePaintcontroller = ImagePainterController(
    color: ComponentUtils.primecolor,
    strokeWidth: 2,
    mode: PaintMode.freeStyle,
  );

  // Future _refreshTargets() async {
  //   targetSearchCtrlr.getTargetSearchData(searchText: targetSearchCtrlr.searchController.text);
  // }

  @override
  Widget build(BuildContext context) {
    debugPrint('----------build----------');
    bool isLargeScreen = CommonUtils.isTablet(context);
    return
        // SafeArea(
        //   child:
        Scaffold(
      body: Stack(
        children: <Widget>[
          Scaffold(
            endDrawer: entityListDrawer(),
            //extendBodyBehindAppBar: true,
            //backgroundColor: Colors.transparent,
            appBar: AppBar(
              iconTheme: Theme.of(context).appBarTheme.iconTheme,
              title: TaSearchInputText(
                  height: 30.0,
                  makeSearch: (searchtext) async {
                    if (locateCtrlr.entityfilterlist.value.isNotEmpty) {
                      locateCtrlr.dataloadflag.value = 'search';
                      await locateCtrlr.getEntityData();
                    } else {
                      locateCtrlr.searchController.text = '';
                      ComponentUtils.showpopup(msg: 'Please select entities..');
                    }
                  },
                  plachold: 'appbar',
                  searchController: locateCtrlr.searchController,
                  hintSearch: 'Search '),

              actions: [
                if (!isLargeScreen)
                  Obx(
                    () => IconButton(
                      color: ComponentUtils.primecolor,
                      icon: locateCtrlr.mapflag.value ? const Icon(Icons.format_list_bulleted) : const Icon(Icons.map),
                      onPressed: () async {
                        locateCtrlr.mapflag.value = !locateCtrlr.mapflag.value;
                      },
                    ),
                  ),
                if (isLargeScreen)
                  IconButton(
                    color: ComponentUtils.primecolor,
                    icon: locateCtrlr.mapflag.value ? const Icon(Icons.format_list_bulleted) : const Icon(Icons.map),
                    onPressed: () async {
                      _scaffoldKey.currentState!.openEndDrawer();
                    },
                  ),
              ],
              //backgroundColor: Colors.transparent,
              //elevation: 0.0,
              backgroundColor: Colors.white,
              elevation: 5,
              leading: IconButton(
                icon: ComponentUtils.backpageIcon,
                color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                onPressed: () {
                  Get.off(() => HomeScreen());
                },
              ),
            ),
            // floatingActionButtonLocation: FloatingActionButtonLocation.miniStartDocked,
            floatingActionButton: Obx(
              () => Padding(
                padding: const EdgeInsets.all(1.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: <Widget>[
                    // FloatingActionButton(
                    //   heroTag: 'bottommenu',
                    //   mini: true,
                    //   onPressed: () async {
                    //     showMenubottomsheet();
                    //   },
                    //   child: Icon(Icons.menu),
                    // ),
                    FloatingActionButton(
                      heroTag: 'entitymapfilter',
                      mini: true,
                      onPressed: () async {
                        await entitymapfilter();
                      },
                      child: const Icon(Icons.layers),
                      // Column(
                      //   mainAxisAlignment: MainAxisAlignment.center,
                      //   children: [
                      //     Icon(Icons.layers),
                      //     Text(
                      //       'Entities',
                      //       style: TextStyle(fontSize: 8),
                      //     ),
                      //   ],
                      // ),
                    ),
                    if (locateCtrlr.mapflag.value)
                      const SizedBox(
                        height: 10,
                      ),
                    if (locateCtrlr.mapflag.value)
                      FloatingActionButton(
                        heroTag: 'annotate',
                        mini: true,
                        onPressed: () async {
                          ProgressUtil.showLoaderDialog(Get.context!);
                          WidgetsBinding.instance.focusManager.primaryFocus?.unfocus();

                          Future.delayed(
                            const Duration(seconds: 1),
                            () async {
                              ProgressUtil.closeLoaderDialog(Get.context!);
                              await showmapscreenshot();
                            },
                          );
                        },
                        child: const Icon(Icons.document_scanner),
                      ),
                    if (locateCtrlr.mapflag.value)
                      const SizedBox(
                        height: 10,
                      ),
                    if (locateCtrlr.mapflag.value)
                      FloatingActionButton(
                        heroTag: 'maplayer',
                        mini: true,
                        onPressed: () async {
                          //locateCtrlr.initloading.value = true;

                          if (locateCtrlr.mapType.value == MapType.normal) {
                            locateCtrlr.mapType.value = MapType.satellite;
                          } else {
                            locateCtrlr.mapType.value = MapType.normal;
                          }
                          //locateCtrlr.initloading.value = false;
                        },
                        child: const Icon(Icons.public),
                      ),
                    const SizedBox(
                      height: 10,
                    ),
                    FloatingActionButton(
                      heroTag: 'currentloc',
                      mini: true,
                      onPressed: () async {
                        if (!locateCtrlr.locaccess.value) {
                          await locateCtrlr.determinePosition();
                        }
                        if (locateCtrlr.locaccess.value && locateCtrlr.dataloadflag.value == 'search') {
                          locateCtrlr.dataloadflag.value = 'mapcurrent';
                          await locateCtrlr.getEntityData();
                        } else if (locateCtrlr.locaccess.value) {
                          debugPrint('----------currentloc----------');
                          locateCtrlr.initloading.value = true;
                          Future.delayed(const Duration(seconds: 1), () {
                            locateCtrlr.lattitude.value = locateCtrlr.crntlattitude.value;
                            locateCtrlr.longitude.value = locateCtrlr.crntlongitude.value;
                            locateCtrlr.initloading.value = false;
                          });
                        }
                        locateCtrlr.mapzoom.value = 12;
                        locateCtrlr.mapType.value = MapType.normal;
                      },
                      child: const Icon(Icons.my_location),
                    )
                  ],
                ),
              ),
            ),

            key: _scaffoldKey,
            body: Obx(
              () => Stack(
                alignment: AlignmentDirectional.topCenter,
                children: <Widget>[
                  Positioned.fill(
                    top: locateCtrlr.mapflag.value ? 0 : 40,
                    child: GetX<LocateController>(
                      initState: (state) async {
                        debugPrint('----------onInit----------');
                        locateCtrlr.initloading.value = true;

                        await locateCtrlr.determinePosition();
                        Future.delayed(const Duration(seconds: 1), () {
                          locateCtrlr.initloading.value = false;
                        });
                        //await locateCtrlr.getEntityData();
                      },
                      builder: (_) {
                        return _.initloading.isTrue
                            ? const ProgressIndicatorCust()
                            : locateCtrlr.mapflag.value
                                ? googleMap()
                                : entityList(_);
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Column(children: <Widget>[
                            Container(
                              // color: Colors.amber,
                              child: Obx(
                                () => MultiSelectChipDisplay(
                                  items: locateCtrlr.entityfilterlist.value.map((e) => MultiSelectItem(e, e)).toList(),
                                  // chipColor: Colors.red[100],
                                  // icon: Icon(Icons.check),
                                  // onTap: (val) {
                                  //   locateCtrlr.entityfilterlist.value.remove(val);

                                  //   locateCtrlr.tempFilters.clear();
                                  //   locateCtrlr.tempFilters.addAll(locateCtrlr.entityfilterlist.value);

                                  //   locateCtrlr.targetsfilter.value = locateCtrlr.tempFilters.value.contains('Target') ? true : false;
                                  //   locateCtrlr.sitesfilter.value = locateCtrlr.tempFilters.value.contains('Site') ? true : false;
                                  //   locateCtrlr.storesfilter.value = locateCtrlr.tempFilters.value.contains('Store') ? true : false;
                                  // },
                                ),
                              ),
                            ),
                          ]),
                          // if (locateCtrlr.mapflag.value)
                          //   TaButton(
                          //     type: 'elevate',
                          //     buttonText: 'Annotate',
                          //     btnStyle: ButtonStyle(
                          //         shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                          //             borderRadius: BorderRadius.circular(18.0), side: BorderSide(color: ComponentUtils.primecolor)))),
                          //     onPressed: () {
                          //       ProgressUtil.showLoaderDialog(Get.context);
                          //       WidgetsBinding.instance.focusManager.primaryFocus?.unfocus();

                          //       Future.delayed(
                          //         const Duration(seconds: 1),
                          //         () async {
                          //           ProgressUtil.closeLoaderDialog(Get.context);
                          //           await showmapscreenshot();
                          //         },
                          //       );
                          //     },
                          //   ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      // ),
    );
  }

  Widget entityListDrawer() {
    return OrientationBuilder(builder: (context, orientation) {
      return SizedBox(
        width: orientation.name == Orientation.landscape.name ? Get.width / 2 : Get.width / 1.5,
        child: Drawer(
          child: Obx(() => entityList(locateCtrlr)),
        ),
      );
    });
  }

  Widget entityList(LocateController _) {
    return _.entityList.isEmpty
        ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
        : ListView.builder(
            itemBuilder: (context, index) {
              return listitem(context, _.entityList[index]);
            },
            itemCount: _.entityList.length,
          );
  }

  Widget listitem(BuildContext context, LocateData ld) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {
        bool isLargeScreen = CommonUtils.isTablet(context);
        if (isLargeScreen) Get.back();
        locateCtrlr.initloading.value = true;
        locateCtrlr.mapflag.value = true;

        locateCtrlr.mapType.value = MapType.satellite;
        locateCtrlr.lattitude.value = ld.latitude!;
        locateCtrlr.longitude.value = ld.longitude!;

        Future.delayed(Duration(seconds: isLargeScreen ? 1 : 0), () {
          locateCtrlr.mapzoom.value = 20.0;
          locateCtrlr.initloading.value = false;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,

                  //crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ld.entityType == 'TARGET'
                          ? 'Target'
                          : ld.entityType == 'SITE'
                              ? 'Site'
                              : 'Store',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      ld.entityNumber ?? '',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ]),
              const SizedBox(
                height: 5,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Flexible(
                    child: Text(
                      ld.entityName ?? '',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 5.0,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Flexible(
                    child: Text(
                      ld.address ?? '',
                      style: TextStyle(color: primary, fontSize: 10),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    ld.city ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  Text(
                    ld.state ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget googleMap() {
    return Obx(
      () => locateCtrlr.initloading.value
          ? const ProgressIndicatorCust()
          : Stack(
              children: <Widget>[
                GoogleMap(
                  gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
                    Factory<OneSequenceGestureRecognizer>(
                      () => EagerGestureRecognizer(),
                    ),
                  },
                  zoomGesturesEnabled: true,
                  //liteModeEnabled: true,
                  tiltGesturesEnabled: true,
                  zoomControlsEnabled: false,
                  mapToolbarEnabled: false,
                  buildingsEnabled: true,
                  myLocationButtonEnabled: false,
                  mapType: locateCtrlr.mapType.value,
                  initialCameraPosition: CameraPosition(
                    target: LatLng(locateCtrlr.lattitude.value ?? 0.0, locateCtrlr.longitude.value ?? 0.0),
                    zoom: locateCtrlr.mapzoom.value,
                  ),
                  onMapCreated: (GoogleMapController controller) {
                    locateCtrlr.gmapcontroller = controller;
                    locateCtrlr.customInfoWindowController.googleMapController = controller;
                    Completer<GoogleMapController> ctrl = Completer();

                    ctrl.complete(controller);
                  },
                  markers: locateCtrlr.markers.value,
                  onCameraMove: (Position) {
                    locateCtrlr.customInfoWindowController.onCameraMove!();
                  },
                  onTap: (Position) {
                    locateCtrlr.customInfoWindowController.hideInfoWindow!();
                  },
                ),
                CustomInfoWindow(
                  controller: locateCtrlr.customInfoWindowController,
                  height: 60,
                  width: 150,
                  offset: 20,
                ),
              ],
            ),
    );
  }

  Future entitymapfilter() {
    return Get.defaultDialog(
      barrierDismissible: false,
      title: 'Entities',
      titleStyle: const TextStyle(fontSize: 16),
      content: Obx(
        () => Container(
          // margin: EdgeInsets.only(left: 15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.only(left: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Targets',
                      style: TextStyle(
                          //color: primary,
                          fontSize: 12),
                    ),
                    Checkbox(
                      value: locateCtrlr.targetsfilter.value,
                      onChanged: (val) {
                        locateCtrlr.targetsfilter.value = val!;
                        setfilterlist(val, 'Target');
                      },
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsets.only(left: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Sites',
                      style: TextStyle(
                          //color: primary,
                          fontSize: 12),
                    ),
                    Checkbox(
                      value: locateCtrlr.sitesfilter.value,
                      onChanged: (val) {
                        locateCtrlr.sitesfilter.value = val!;
                        setfilterlist(val, 'Site');
                      },
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsets.only(left: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Stores',
                      style: TextStyle(
                          //color: primary,
                          fontSize: 12),
                    ),
                    Checkbox(
                      value: locateCtrlr.storesfilter.value,
                      onChanged: (val) {
                        locateCtrlr.storesfilter.value = val!;
                        setfilterlist(val, 'Store');
                        //  return null;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TaButton(
              type: 'elevate',
              buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CLOSE')?.value ?? 'Close',
              onPressed: () {
                locateCtrlr.entityfilterlist.clear();
                locateCtrlr.entityfilterlist.value = List.from(locateCtrlr.tempFilters.value);

                locateCtrlr.targetsfilter.value = locateCtrlr.tempFilters.value.contains('Target') ? true : false;
                locateCtrlr.sitesfilter.value = locateCtrlr.tempFilters.value.contains('Site') ? true : false;
                locateCtrlr.storesfilter.value = locateCtrlr.tempFilters.value.contains('Store') ? true : false;

                Get.back();
              },
            ),
            const SizedBox(
              width: 4.0,
            ),
            TaButton(
              type: 'elevate',
              buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_OK')?.value ?? 'Ok',
              onPressed: () async {
                debugPrint('filter list--------${locateCtrlr.entityfilterlist.value}');
                locateCtrlr.tempFilters.clear();
                locateCtrlr.tempFilters.addAll(locateCtrlr.entityfilterlist.value);
                Get.back();
                await locateCtrlr.getEntityData();
              },
            ),
            const SizedBox(
              width: 7.0,
            ),
          ],
        ),
      ],
    );
  }

  setfilterlist(bool val, String entity) {
    if (val) {
      if (locateCtrlr.entityfilterlist.isEmpty || !locateCtrlr.entityfilterlist.contains(entity))
        locateCtrlr.entityfilterlist.add(entity);
    } else {
      if (locateCtrlr.entityfilterlist.contains(entity)) locateCtrlr.entityfilterlist.remove(entity);
    }
  }

  Future showmapscreenshot() async {
    final uin8listsc = await (locateCtrlr.gmapcontroller.takeSnapshot());

    Get.defaultDialog(
      title: 'Annotate',
      titleStyle: TextStyle(color: ComponentUtils.primecolor),
      content: SizedBox(
        height: MediaQuery.of(Get.context!).size.height * 0.68,
        // width: 400,
        child: //Image.memory(uin8list),
            ImagePainter.memory(
          controller: _imagePaintcontroller,
          uin8listsc!,
          key: imageKey,
          scalable: true,

          //textDelegate: DutchTextDelegate(),
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TaButton(
              type: 'elevate',
              buttonText: 'Close',
              onPressed: () {
                Get.back();
              },
            ),
            const SizedBox(
              height: 2,
              width: 10,
            ),
            TaButton(
              type: 'elevate',
              buttonText: 'Send',
              onPressed: () async {
                final base64String = base64Encode(uin8listsc);
                String fp = "data:image/png;base64,$base64String";
                //String fp = await savePaintImage(uin8listsc);
                String platformResponse;
                bool flag = false;

                MailOptions mailOptions = MailOptions(
                  body: 'Map screenshot from Tango Workspace Mobile App.',
                  subject: 'Map Snapshot...',
                  attachments: [fp],
                  isHTML: false,
                );

                try {
                  await FlutterMailer.send(mailOptions);
                  //await taemail.TaEmailSender.send(email);
                  platformResponse = 'success';
                } catch (error) {
                  debugPrint('$error');
                  flag = false;
                  platformResponse = error.toString();
                }

                if (!flag) {
                  debugPrint('response   $platformResponse');
                  // ComponentUtils.showpopup(msg: platformResponse);
                } else {
                  Get.back();
                }
              },
            ),
            const SizedBox(
              height: 2,
              width: 10,
            ),
          ],
        ),
      ],
    );
  }

  Future<String> savePaintImage(Uint8List imgData) async {
    // final RenderRepaintBoundary boundary = imageKey.currentContext!.findRenderObject();
    // final ui.Image image = await boundary.toImage();
    // final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    // final Uint8List pngBytes = byteData!.buffer.asUint8List();
    final directory = (await getApplicationDocumentsDirectory()).path;
    await Directory('$directory/sample').create(recursive: true);
    final fullPath = '$directory/sample/${DateTime.now().millisecondsSinceEpoch}.png';
    final imgFile = File(fullPath);

    imgFile.writeAsBytesSync(imgData);

    // final image = await (imageKey.currentState!.exportImage() as Future<Uint8List>);
    // final directory = (await getApplicationDocumentsDirectory()).path;
    // await Directory('$directory/sample').create(recursive: true);
    // final fullPath = '$directory/sample/${DateTime.now().millisecondsSinceEpoch}.png';
    // final imgFile = File('$fullPath');
    // imgFile.writeAsBytesSync(image);
    return fullPath;
  }

  void showMenubottomsheet() {
    Get.bottomSheet(
      Container(height: 500), //entityDataBox(loc),
      //barrierColor: Colors.red[50],
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(35), topRight: Radius.circular(35)),
          side: BorderSide(
            width: 1,
            color: Colors.grey,
          )),
      enableDrag: false,
    );
  }
}
