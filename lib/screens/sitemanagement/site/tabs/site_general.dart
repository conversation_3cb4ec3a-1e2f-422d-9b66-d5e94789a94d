import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/models/ta_admin/entitystatus.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/sitemanagement/site/siteview.dart';
import '../../../../providers/sitemanagement/site/site_general_controller.dart';

class SiteGeneral extends GetView<SiteGeneralController> {
  final SiteView? site;
  String? mode;
  SiteGeneral({super.key, this.site, this.mode});

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  SiteGeneralController genCtrl = Get.find<SiteGeneralController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    genCtrl.labelsloading.value = true;
    genCtrl.isLoading.value = true;
    return Stack(alignment: AlignmentDirectional.bottomCenter, children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await genCtrl.getlabels(talabel);
              await genCtrl.onloadPageData(site!);
            });
            genCtrl.isLoading.value = false;
          },
          builder: (_) {
            return (genCtrl.labelsloading.value || genCtrl.isLoading.value)
                ? const ProgressIndicatorCust()
                : Form(
                    key: _formKey,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    child: generalTab(site),
                  );
          },
        ),
      ),
      bottombuttonbar(_formKey),
    ]);
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            const SizedBox(
              height: 5.0,
              width: 5.0,
            ),
            if (mode == 'edit')
              TaButton(
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                type: 'elevate',
                onPressed: () {
                  genCtrl.onSaveSiteRecord();
                },
              ),
            const SizedBox(
              width: 5.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget generalTab(SiteView? s) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_SITE_GENERAL_SITENUM') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_SITENUM')!.value,
              value: s!.siteNumber.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_SITENAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_SITENAME')!.value,
              value: s!.siteName.toString(),
              readOnly: true,
            ),

          // if (talabel.get('TMCMOBILE_SITE_GENERAL_STATUS') != null)
          //   TaInputText(
          //     title: talabel.get('TMCMOBILE_SITE_GENERAL_STATUS')!.value,
          //     value: s!.status?.toString(),
          //     readOnly: true,
          //   ),

          if (talabel.get('TMCMOBILE_SITE_GENERAL_STATUS') != null)
            Obx(
              () => TaFormDropdown(
                label: talabel.get('TMCMOBILE_SITE_GENERAL_STATUS')!.value,
                emptttext: 'Select',
                onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_SITE_GENERAL_STATUS')!.ro!)
                    ? null
                    : (newValue) {
                        debugPrint('newValue          ' + newValue);
                        genCtrl.siteview.value.cExtAttr19 = newValue;
                      },
                value: (genCtrl.siteview.value.cExtAttr19 == '' || genCtrl.siteview.value.cExtAttr19 == 'null')
                    ? null
                    : genCtrl.siteview.value.cExtAttr19,
                listflag: (genCtrl.statuslov.value.isNotEmpty),
                items: genCtrl.statuslov.value.map((EntityStatus e) {
                  return DropdownMenuItem(
                    value: e.statusCode,
                    enabled: e.displayFlag?.toUpperCase() == 'Y',
                    child: Text(
                      e.status!,
                      style: TextStyle(fontSize: 14, color: (e.displayFlag == 'Y') ? Colors.black : Colors.grey),
                    ),
                  );
                }).toList(),
              ),
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_LATITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_LATITUDE')!.value,
              value: s!.latitude?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_LONGITUDE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_LONGITUDE')!.value,
              value: s!.longitude.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_ADDRESS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_ADDRESS')!.value,
              value: s!.address?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_CITY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_CITY')!.value,
              value: s!.city?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_STATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_STATE')!.value,
              value: s!.state?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_ZIPCODE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_ZIPCODE')!.value,
              value: s!.zipCode?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_DMANAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_DMANAME')!.value,
              value: s!.dmaName?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_GENERAL_COUNTY') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_GENERAL_COUNTY')!.value,
              value: s!.county?.toString(),
              readOnly: true,
            ),

          const SizedBox(
            height: 80,
          ),
          // if (talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM') != null)
          // TaInputText(
          //   title: talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM').value,
          //   value:s.zipCode?.toString(),
          //   readOnly: true,
          // ),
          //  if (talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM') != null)
          // TaInputText(
          //   title: talabel.get('TMCMOBILE_SITE_GENERAL_PROGRAM').value,
          //   value:s.zipCode?.toString(),
          //   readOnly: true,
          // ),
        ],
      ),
    );
  }
}
