import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/sitemanagement/site/site_informantion_attributes_data.dart';
import '../../../../models/sitemanagement/site/siteview.dart';
import '../../../../providers/sitemanagement/site/site_general_controller.dart';
import '../../../../providers/sitemanagement/site/site_info_attributes_controller.dart';

class SiteInfoAttributes extends GetView<SiteInfoAttributesController> {
  final SiteView? site;
  SiteInfoAttributes({super.key, this.site});

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  SiteInfoAttributesController siteInfoAttrCtrl = Get.find<SiteInfoAttributesController>();

  @override
  Widget build(BuildContext context) {
    siteInfoAttrCtrl.labelsloading.value = true;

    return Stack(children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await siteInfoAttrCtrl.getlabels(talabel);
              await siteInfoAttrCtrl.getSiteInfoData(site?.siteId);
            });
          },
          builder: (_) {
            return (siteInfoAttrCtrl.labelsloading.value || siteInfoAttrCtrl.isLoading.value)
                ? const ProgressIndicatorCust()
                : siteInfoAttributes(siteInfoAttrCtrl.siteInfoRow.value);
          },
        ),
      ),
    ]);
  }

  Widget siteInfoAttributes(SiteInformationAttributes? s) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR19') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR19')!.value,
              value: s!.cExtAttr19Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR110') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR110')!.value,
              value: s!.nExtAttr110?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_DEXTATTR34') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_DEXTATTR34')!.value,
              value: s!.dExtAttr34?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR115') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR115')!.value,
              value: s!.nExtAttr115?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV27') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV27')!.value,
              value: s!.cExtLov27Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR111') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR111')!.value,
              value: s!.nExtAttr111?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR27') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR27')!.value,
              value: s!.cExtAttr27?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR112') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_NEXTATTR112')!.value,
              value: s!.nExtAttr112?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR62') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR62')!.value,
              value: s!.cExtAttr62?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV30') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV30')!.value,
              value: s!.cExtLov30Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV6') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV6')!.value,
              value: s!.cExtLov6Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV74') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV74')!.value,
              value: s!.cExtLov74Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR30') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR30')!.value,
              value: s!.cExtAttr30?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV2') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV2')!.value,
              value: s!.cExtLov2Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_TIMEZONE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_TIMEZONE')!.value,
              value: s!.timezone?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV22') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV22')!.value,
              value: s!.cExtLov22Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV16') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV16')!.value,
              value: s!.cExtLov16Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV72') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTLOV72')!.value,
              value: s!.cExtLov72Desc?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_PROPOSEDSTARTDATE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_PROPOSEDSTARTDATE')!.value,
              value: s!.proposedStartDate?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR29') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_CEXTATTR29')!.value,
              value: s!.cExtAttr29?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_DEXTATTR33') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES_DEXTATTR33')!.value,
              value: s!.dExtAttr33?.toString() ?? '',
              readOnly: true,
            ),
        ],
      ),
    );
  }
}
