import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/utils/richtexteditor.dart';

import '../../../../common/component_utils.dart';
import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/component_widgets/taforminputtext.dart';
import '../../../../common/widgets/component_widgets/tarichinputtext.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/sitemanagement/site/siteview.dart';
import '../../../../providers/sitemanagement/site/site_swot_controller.dart';

import 'package:flutter_html/flutter_html.dart';

import '../../../../utils/common_utils.dart';

class SiteSwot extends GetView<SiteSwotController> {
  final SiteView? site;
  String? mode;
  SiteSwot({super.key, this.site, this.mode});

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  SiteSwotController swtCtrl = Get.find<SiteSwotController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    debugPrint('mode>>>>>>>$mode');
    swtCtrl.labelsloading.value = true;
    //mode = 'view';

    return Stack(alignment: AlignmentDirectional.bottomCenter, children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await swtCtrl.getlabels(talabel);
              await swtCtrl.getSiteRecord(site?.siteId);
            });
          },
          builder: (_) {
            return (swtCtrl.labelsloading.value || swtCtrl.isloading.value)
                ? const ProgressIndicatorCust()
                : swotData();
          },
        ),
      ),
      bottombuttonbar(_formKey),
    ]);
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            const SizedBox(
              height: 5.0,
              width: 5.0,
            ),
            if (mode == 'edit')
              TaButton(
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                type: 'elevate',
                onPressed: () {
                  swtCtrl.onSaveSwotRecord();
                },
              ),
            const SizedBox(
              width: 5.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget swotData() {
    return Form(
      key: _formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: SingleChildScrollView(
        padding: const EdgeInsets.only(top: 10),
        child: Column(
          children: <Widget>[
            if (talabel.get('TMCMOBILE_SITE_SWOT_DEALSUMMARY') != null &&
                (mode == 'edit' && talabel.get('TMCMOBILE_SITE_SWOT_STRENGTH')!.ro == false))
              dealSummary(),
            if (talabel.get('TMCMOBILE_SITE_SWOT_DEALSUMMARY') != null &&
                (mode == 'view' || talabel.get('TMCMOBILE_SITE_SWOT_STRENGTH')!.ro == true))
              TaRichInputText(
                // height: 200.0,
                label: talabel.get('TMCMOBILE_SITE_SWOT_DEALSUMMARY')!.value,
                value: swtCtrl.siterec.value.dealSummary?.toString() ?? '',
              ),

            if (talabel.get('TMCMOBILE_SITE_SWOT_STRENGTH') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SITE_SWOT_STRENGTH')!.value,
                value: swtCtrl.siterec.value.strength?.toString() ?? '',
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_SITE_SWOT_STRENGTH')!.ro : true,
                maxLines: 5,
                minLines: 5,
                onChanged: (val) {
                  swtCtrl.siterec.value.strength = val;
                },
                onSaved: (val) {
                  swtCtrl.siterec.value.strength = val;
                },
              ),
            if (talabel.get('TMCMOBILE_SITE_SWOT_WEAKNESS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SITE_SWOT_WEAKNESS')!.value,
                value: swtCtrl.siterec.value.weakness?.toString() ?? '',
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_SITE_SWOT_WEAKNESS')!.ro : true,
                maxLines: 5,
                minLines: 5,
                onChanged: (val) {
                  swtCtrl.siterec.value.weakness = val;
                },
                onSaved: (val) {
                  swtCtrl.siterec.value.weakness = val;
                },
              ),
            if (talabel.get('TMCMOBILE_SITE_SWOT_OPPORTUNITY') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SITE_SWOT_OPPORTUNITY')!.value,
                value: swtCtrl.siterec.value.opportunity?.toString() ?? '',
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_SITE_SWOT_OPPORTUNITY')!.ro : true,
                maxLines: 5,
                minLines: 5,
                onChanged: (val) {
                  swtCtrl.siterec.value.opportunity = val;
                },
                onSaved: (val) {
                  swtCtrl.siterec.value.opportunity = val;
                },
              ),
            if (talabel.get('TMCMOBILE_SITE_SWOT_THREAT') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SITE_SWOT_THREAT')!.value,
                value: swtCtrl.siterec.value.threat?.toString() ?? '',
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_SITE_SWOT_THREAT')!.ro : true,
                maxLines: 5,
                minLines: 5,
                onChanged: (val) {
                  swtCtrl.siterec.value.threat = val;
                },
                onSaved: (val) {
                  swtCtrl.siterec.value.threat = val;
                },
              ),
            if (talabel.get('TMCMOBILE_SITE_SWOT_CONSTRUCTIONANALYSIS') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SITE_SWOT_CONSTRUCTIONANALYSIS')!.value,
                value: swtCtrl.siterec.value.constructionAnalysis?.toString() ?? '',
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_SITE_SWOT_CONSTRUCTIONANALYSIS')!.ro : true,
                maxLines: 5,
                minLines: 5,
                onChanged: (val) {
                  swtCtrl.siterec.value.constructionAnalysis = val;
                },
                onSaved: (val) {
                  swtCtrl.siterec.value.constructionAnalysis = val;
                },
              ),
            if (talabel.get('TMCMOBILE_SITE_SWOT_SWOTDESCRIPTION') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_SITE_SWOT_SWOTDESCRIPTION')!.value,
                value: swtCtrl.siterec.value.swotDescription?.toString() ?? '',
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_SITE_SWOT_SWOTDESCRIPTION')!.ro : true,
                maxLines: 5,
                minLines: 5,
                onChanged: (val) {
                  swtCtrl.siterec.value.swotDescription = val;
                },
                onSaved: (val) {
                  swtCtrl.siterec.value.swotDescription = val;
                },
              ),
            // const SizedBox(
            //   height: 80.0,
            // ),
          ],
        ),
      ),
    );
  }

  Widget dealSummary() {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(talabel.get('TMCMOBILE_SITE_SWOT_DEALSUMMARY')?.value ?? 'Deal Summary'),
        IconButton(
            onPressed: () {
              debugPrint('open>>>>>>>>');
              _openDialog(talabel.get('TMCMOBILE_SITE_SWOT_DEALSUMMARY')?.value ?? 'Deal Summary');
              //Get.to(() => DocAttachments());
            },
            icon: Icon(
              Icons.drive_file_rename_outline,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  void _openDialog(String hdr) async {
    await Navigator.of(Get.context!).push(MaterialPageRoute<String>(
        builder: (BuildContext context) {
          return Scaffold(
            appBar: AppBar(
              leading: IconButton(
                icon: const Icon(Icons.clear),
                color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                onPressed: () {
                  debugPrint('------back-------');
                  Get.back();
                },
              ),
              backgroundColor: Colors.white,
              elevation: 5,
              title: Text(
                hdr,
                style: ComponentUtils.appbartitlestyle,
              ),
              // actions: [
              //   TextButton(
              //     onPressed: () {
              //       Get.back();
              //     },
              //     child: Row(
              //       mainAxisSize: MainAxisSize.min,
              //       crossAxisAlignment: CrossAxisAlignment.center,
              //       children: [
              //         Text(
              //           'Done',
              //           style: ComponentUtils.appbartitlestyle,
              //         ),
              //       ],
              //     ),
              //   ),
              // ],
            ),
            body: RichTextEditorWidget(
                // content: swtCtrl.siterec.value.dealSummary,
                ),
          );
        },
        fullscreenDialog: true));
  }
}
