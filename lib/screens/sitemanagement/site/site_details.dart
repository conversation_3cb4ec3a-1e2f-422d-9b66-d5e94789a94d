import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/common/documents_controller.dart';
import 'package:tangoworkplace/providers/common/entitycomments_controller.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/documents/documents.dart';
import 'package:tangoworkplace/screens/common/comments/entity_comments.dart';
import 'package:tangoworkplace/screens/common/photos/photos.dart';

import '../../../common/component_utils.dart';
import '../../../models/sitemanagement/site/siteview.dart';
import '../../../providers/common/commoncontacts_controller.dart';
import '../../../providers/common/entitydemographics_controller.dart';
import '../../../providers/common/entitymilestones_controller.dart';
import '../../../providers/common/photosgrid_controller.dart';
import '../../../providers/sitemanagement/site/site_controller.dart';
import '../../../providers/sitemanagement/site/site_general_controller.dart';
import '../../../providers/sitemanagement/site/site_info_attributes_controller.dart';
import '../../../providers/sitemanagement/site/site_swot_controller.dart';
import '../../../providers/sitemanagement/site/sitedetails_controller.dart';
import '../../common/contacts/contacts_data.dart';
import '../../common/demographic/entitydemographics.dart';
import '../../common/milestone/entity_milestones.dart';
import '../../common/photos/photosgrid.dart';
import '../../common/questionnaire/entityquestionnaire.dart';
import 'tabs/site_general.dart';
import 'tabs/site_info_attributes.dart';
import 'tabs/site_swot.dart';

class SiteDetails extends StatelessWidget {
  SiteView? site;
  String? nav_from;
  int? siteid;
  String? sitename;
  Map? tabroles;
  String? parent_mode;
  SiteDetails({super.key, this.site, this.nav_from, this.siteid, this.sitename, this.tabroles, this.parent_mode});

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey _tabControllerKey = GlobalKey();
  final sitetabs = <Tab>[];
  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  SiteDetailsController detCtrl = Get.put(SiteDetailsController());
  SiteSearchController siteSearchState = Get.put(SiteSearchController());
  SiteSearchController siteSearchCtrlr = Get.find<SiteSearchController>();

  var generaltab;
  var siteinfoattributetab;
  var swottab;
  var scheduletab;
  var demographicstab;
  var questionnairetab;
  var photostab;
  var docstab;
  var commentstab;
  var contactstab;

  List<Tab> tabsdata = [];

  String? _setTablabels(Applabel? label, String role) {
    if (label != null && tabrolecheck(role)) {
      sitetabs.add(Tab(
        text: label.value,
      ));
      return label.value;
    }
    return null;
  }

  bool tabrolecheck(String tabrole) {
    return tabroles!.containsKey(tabrole) && tabroles![tabrole] != 'na';
  }

  Future _getlabels(LabelController labCtrl) async {
    try {
      await labCtrl.getlabels('TMCMOBILE_SITE', 'site_details', filtertype: 'page');

      generaltab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_GENERAL'), 'general');
      siteinfoattributetab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_SITEINFOATTRIBUTES'), 'siteinfo');
      swottab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_SWOT'), 'swot');
      scheduletab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_SCHEDULE'), 'milestones');
      demographicstab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_DEMOGRAPHICS'), 'demographics');
      //  questionnairetab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_QUESTIONNAIRE'));
      photostab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_PHOTOS'), 'photos');
      docstab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_DOCUMENTS'), 'documents');
      commentstab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_COMMENTS'), 'entitycomments');
      contactstab = _setTablabels(labCtrl.get('TMCMOBILE_SITE_CONTACTS'), 'contacts');
    } catch (e) {
      debugPrint('$e');
    }
    labCtrl.setdataloading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    //bool siteSearchState = Get.isRegistered<SiteSearchController>();
    Future.delayed(Duration.zero, () {
      debugPrint('sitename-------$sitename');
      debugPrint('tabroles-------$tabroles');
      talabel.setdataloading.value = true;

      SiteGeneralController siteGeneralController = Get.put(SiteGeneralController());
      SiteInfoAttributesController siteInfoAttributesController = Get.put(SiteInfoAttributesController());
      SiteSwotController siteSwotController = Get.put(SiteSwotController());
      EntityMilestonesController entityMilestonesController = Get.put(EntityMilestonesController());
      EntityCommentsController entityCommentsController = Get.put(EntityCommentsController());
      PhotosGridController photoController = Get.put(PhotosGridController());
      DocumentController documentController = Get.put(DocumentController());
      EntityDemographicsController demographicController = Get.put(EntityDemographicsController());
      CommonContactsController contactCtrl = Get.put(CommonContactsController());
    });
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title:
            Text(sitename ?? '', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
                ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () async {
            debugPrint('------back-------');
            if (nav_from == 'target_associted_sites')
              await talabel.getlabels('TMCMOBILE_TARGET', 'target_details', filtertype: 'page');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<LabelController>(initState: (state) {
        detCtrl.isloading.value = true;
        Future.delayed(const Duration(seconds: 1), () async {
          try {
            await _getlabels(talabel);
            if (nav_from == 'locate' || nav_from == 'approval' || nav_from == 'target_associted_sites') {
              await detCtrl.getSiteRecord(siteid?.toString());

              site = detCtrl.siterec.value;
              debugPrint('sitename>>>>>>${site?.siteName}');
            }
          } finally {
            detCtrl.isloading.value = false;
          }
        });
      }, builder: (_) {
        return (talabel.setdataloading.value || detCtrl.isloading.value)
            ? const ProgressIndicatorCust()
            : DefaultTabController(
                key: _tabControllerKey,
                initialIndex: 0,
                length: (sitetabs.isNotEmpty) ? sitetabs.length : 1,
                child: Column(children: <Widget>[
                  Material(
                    elevation: 5,
                    color: Colors.white,
                    child: TabBar(
                      //controller: _tabController,
                      isScrollable: true,
                      labelColor: ComponentUtils.tablabelcolor,
                      unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                      indicatorColor: ComponentUtils.tabindicatorColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.normal,
                          fontSize: 14,
                          color: ComponentUtils.tablabelcolor),
                      tabs: (sitetabs.isNotEmpty)
                          ? sitetabs
                          : [
                              const Tab(
                                text: 'General',
                              )
                            ],
                    ),
                  ),
                  Expanded(
                    child: (sitetabs.isEmpty)
                        ? TabBarView(children: [
                            SiteGeneral(
                              site: site,
                              mode: 'view',
                            )
                          ])
                        : TabBarView(
                            children: [
                              if (generaltab != null)
                                SiteGeneral(
                                  site: site,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['general'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (siteinfoattributetab != null)
                                SiteInfoAttributes(
                                  site: site,
                                ),
                              if (swottab != null)
                                SiteSwot(
                                  site: site,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['swot'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (scheduletab != null)
                                EntityMilestones(
                                  entityType: 'SITE',
                                  entityId: site!.siteId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['milestones'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (demographicstab != null)
                                EntityDemographics(
                                  entityType: 'SITE',
                                  entityId: site!.siteId,
                                  // mode:(this.parent_mode == 'edit' && tabroles != null && tabroles!['demographics'] == 'edit') ? 'edit' : 'view',
                                ),
                              if (questionnairetab != null)
                                EntityQuestionnaire(
                                  site: site,
                                  // mode:(this.parent_mode == 'edit' && tabroles != null && tabroles!['questionnaire'] == 'edit') ? 'edit' : 'view',
                                ),
                              if (photostab != null)
                                // Photos(
                                //   entityType: 'SITE',
                                //   entityId: site!.siteId,
                                //   photoMode: 'v',
                                // ),

                                PhotosGrid(
                                  //Photos(
                                  entityType: 'SITE',
                                  entityId: site!.siteId,

                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['photos'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (docstab != null)
                                Documents(
                                  rootfolderid: site!.rootFolderId,
                                  entityType: 'SITE',
                                  entityId: site!.siteId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['documents'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (commentstab != null)
                                EntityComments(
                                  entityType: 'SITE',
                                  entityId: site!.siteId,
                                ),
                              if (contactstab != null)
                                ContactsData(
                                  entityId: site!.siteId,
                                  entityType: 'SITE',
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['contacts'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                            ],
                          ),
                  ),
                ]),
              );
      }),
    );
  }
}
