import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/common/adhoctasks_controller.dart';
import 'package:tangoworkplace/providers/common/documents_controller.dart';
import 'package:tangoworkplace/providers/common/documents_tree_controller.dart';
import 'package:tangoworkplace/providers/common/entitycomments_controller.dart';
import 'package:tangoworkplace/providers/common/entitymilestones_controller.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/providers/common/punchlist_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/program/programprojects_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/budget_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/budget_summary_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectdeatils_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectgeneral_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/statusreport/statusreport_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/adhoctasks/entity_adhoctasks.dart';
import 'package:tangoworkplace/screens/common/documents/documents.dart';
import 'package:tangoworkplace/screens/common/milestone/entity_milestones.dart';
import 'package:tangoworkplace/screens/projectmanagement/program/tabs/program_projects_tab.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/tabs/project_general_tab.dart';

import '../../../common/component_utils.dart';
import '../../../models/program/programs_search_data.dart';
import '../../../providers/common/commoncontacts_controller.dart';
import '../../../providers/common/meetingminute/meetingminutes_controller.dart';
import '../../../providers/projectmanagement/program/programgeneral_controller.dart';
import 'tabs/program_general_tab.dart';

class ProgramDetails extends StatelessWidget {
  final ProgramsSearchData? prg;
  String? parent_mode;
  Map? tabroles;
  ProgramDetails({super.key, this.prg, this.parent_mode, this.tabroles});
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final programtabs = <Tab>[];
  Applabel? label;
  LabelController talabel = Get.find<LabelController>();

  var generaltab, projectstab, docstab, adhoctasktab;

  List<Tab> tabsdata = [];

  String? _setTablabels(Applabel? label, String role) {
    if (label != null
        //&& tabrolecheck(role)
        ) {
      programtabs.add(Tab(
        text: label.value,
      ));
      return label.value;
    }
    return null;
  }

  bool tabrolecheck(String tabrole) {
    return tabroles!.containsKey(tabrole) && tabroles![tabrole] != 'na';
  }

  Future _getlabels(LabelController labCtrl) async {
    try {
      // labCtrl.setdataloading.value = true;
      await labCtrl.getlabels('TMCMOBILE_PROGRAMS', 'program_details', filtertype: 'page');

      generaltab = _setTablabels(labCtrl.get('TMCMOBILE_PROGRAMS_GENERAL'), 'general');
      projectstab = _setTablabels(labCtrl.get('TMCMOBILE_PROGRAMS_PROJECTS'), 'projects');
      docstab = _setTablabels(labCtrl.get('TMCMOBILE_PROGRAMS_DOCUMENTS'), 'document');
      adhoctasktab = _setTablabels(labCtrl.get('TMCMOBILE_PROGRAMS_ADHOCTASKS'), 'adhoctasks');

      debugPrint('generaltab>>$generaltab   projectstab>>$projectstab  docstab>>$docstab  adhoctasktab>>$adhoctasktab');
    } catch (e) {
      debugPrint('$e');
    }
    labCtrl.setdataloading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('program name-------${prg!.programName}');
    debugPrint('tab roles-------$tabroles');
    talabel.setdataloading.value = true;

    //ProgramGeneralController programGeneralController =
    Get.put(ProgramGeneralController());
    //ProjectDetailsController projectDetailsState = Get.put(ProjectDetailsController());
    //DocumentController documentController =
    Get.put(DocumentController());
    //DocumentsTreeController documentTreeController =
    Get.put(DocumentsTreeController());
    //EntityAdhicTasksController adhoctaskController =
    Get.put(EntityAdhicTasksController());
    Get.put(ProgramProjectsController());

    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(prg!.programName!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<LabelController>(initState: (state) {
        Future.delayed(Duration.zero, () async {
          await _getlabels(talabel);
          //debugPrint('root folder id ------${proj.parentRootFolderId}');
        });
      }, builder: (_) {
        return (talabel.setdataloading.value)
            ? const ProgressIndicatorCust()
            : DefaultTabController(
                initialIndex: 0,
                length: (programtabs.isNotEmpty) ? programtabs.length : 1,
                child: Column(children: <Widget>[
                  Material(
                    elevation: 5,
                    color: Colors.white,
                    child: TabBar(
                      //controller: _tabController,
                      isScrollable: true,
                      labelColor: ComponentUtils.tablabelcolor,
                      unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                      indicatorColor: ComponentUtils.tabindicatorColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.normal,
                          fontSize: 14,
                          color: ComponentUtils.tablabelcolor),
                      tabs: (programtabs.isNotEmpty)
                          ? programtabs
                          : [
                              const Tab(
                                text: 'General',
                              )
                            ],
                    ),
                  ),
                  Expanded(
                    child: (programtabs.isEmpty)
                        ? TabBarView(children: [ProgramGeneral(prg: prg)])
                        : TabBarView(
                            children: [
                              if (generaltab != null) ProgramGeneral(prg: prg),
                              if (projectstab != null)
                                ProgramProjects(
                                  programId: prg!.programId,
                                  // mode:
                                  //     (this.parent_mode == 'edit' && tabroles != null && tabroles!['schedule'] == 'edit') ? 'edit' : 'view',
                                ),
                              if (docstab != null)
                                Documents(
                                  rootfolderid: prg?.rootFolderId ?? 0,
                                  entityType: 'PROGRAM',
                                  entityId: prg!.programId,
                                  //   mode: (parent_mode == 'edit' && tabroles != null && tabroles!['document'] == 'edit') ? 'edit' : 'view',
                                  mode: 'view',
                                ),
                              if (adhoctasktab != null)
                                EntityAdhocTasks(
                                  entityType: 'PROGRAM',
                                  entityId: prg!.programId,
                                  // mode: (this.parent_mode == 'edit' && tabroles != null && tabroles!['adhoctasks'] == 'edit')
                                  //     ? 'edit'
                                  //     : 'view',
                                  mode: 'view',
                                ),
                            ],
                          ),
                  ),
                ]),
              );
      }),
    );
  }
}
