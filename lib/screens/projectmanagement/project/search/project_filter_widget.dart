import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectsearch_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import '../../../../common/component_utils.dart';
import '../../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../../models/ta_admin/entitystatus.dart';

class ProjectFilterWidget extends GetView<ProjectSearchController> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String? _projectFlag;
  // TextEditingController _searchController = TextEditingController();

  ProjectSearchController projectSearchCtrlr = Get.find<ProjectSearchController>();
  LabelController talabel = Get.find<LabelController>();

  ProjectFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Search', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Get.back();
          },
        ),
        actions: [
          IconButton(
              onPressed: () {
                Get.off(() => HomeScreen());
              },
              icon: Icon(
                Icons.home,
                color: ComponentUtils.primecolor,
              )),
        ],
      ),
      key: _scaffoldKey,
      body: Obx(
        () => projectSearchCtrlr.filterwidgetloading.isTrue
            ? const ProgressIndicatorCust()
            : Container(
                // width: double.infinity,
                margin: const EdgeInsets.only(left: 10.0, right: 10.0, top: 5.0),
                constraints: const BoxConstraints(maxWidth: 500),
                padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
                // height: 200,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.0),
                    boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                // child: Column(
                //   children: [
                child: Column(
                  children: <Widget>[
                    TaSearchInputText(
                        autofocus: true,
                        makeSearch: (searchtext) {
                          Get.back();
                          projectSearchCtrlr.fetchProjects(searchText: searchtext.toString());
                        },
                        searchController: projectSearchCtrlr.searchCtrl,
                        hintSearch: talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS')?.value ?? 'Search Projects'),
                    const SizedBox(
                      width: 10.0,
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                      const SizedBox(
                        width: 10.0,
                      ),

                      Radio(
                        value: 'My',
                        groupValue: projectSearchCtrlr.allmyfilter.value,
                        onChanged: (dynamic value) {
                          projectSearchCtrlr.allmyfilter.value = value;
                        },
                      ),

                      const Text(
                        'My',
                        style: TextStyle(fontSize: 12),
                      ),
                      const SizedBox(
                        width: 10.0,
                      ),

                      Radio(
                        value: 'All',
                        groupValue: projectSearchCtrlr.allmyfilter.value,
                        onChanged: (dynamic value) {
                          projectSearchCtrlr.allmyfilter.value = value;
                        },
                      ),

                      const Text(
                        'All',
                        style: TextStyle(fontSize: 12),
                      ),
                      // ),
                    ]),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const SizedBox(
                          width: 10.0,
                        ),

                        Radio(
                          value: 'Ongoing',
                          groupValue: projectSearchCtrlr.ongngcmpltd.value,
                          onChanged: (dynamic value) {
                            projectSearchCtrlr.ongngcmpltd.value = value;
                          },
                        ),

                        const Text(
                          'Ongoing',
                          style: TextStyle(fontSize: 12),
                        ),
                        const SizedBox(
                          width: 10.0,
                        ),
                        Radio(
                          value: 'Completed',
                          groupValue: projectSearchCtrlr.ongngcmpltd.value,
                          onChanged: (dynamic value) {
                            projectSearchCtrlr.ongngcmpltd.value = value;
                          },
                        ),
                        // Expanded(
                        // child:
                        const Text(
                          'Completed',
                          style: TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                    if (talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE') != null)
                      Container(
                        margin: const EdgeInsets.only(left: 10.0, top: 10.0),
                        child: TaDropDown(
                          width: double.infinity,
                          label: talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_TYPE')?.value ?? 'Type',
                          initvalue: projectSearchCtrlr.projectTypeFilter.value,
                          value: projectSearchCtrlr.projectTypeFilter.value,
                          items: projectSearchCtrlr.projectTypeLov.value.map((EntityStatus e) {
                            return DropdownMenuItem(
                              value: e.statusCode,
                              child: Text(
                                e.status!,
                                style: const TextStyle(fontSize: 14, color: Colors.black),
                              ),
                            );
                          }).toList(),
                          onChanged: (val) async {
                            debugPrint('val -----' + val);
                            projectSearchCtrlr.projectTypeFilter.value = val;
                          },
                        ),
                      ),
                    if (talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_STATUS') != null)
                      Container(
                        margin: const EdgeInsets.only(left: 10.0, top: 10.0),
                        child: TaDropDown(
                          width: double.infinity,
                          label: talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_STATUS')?.value ?? 'Status',
                          initvalue: projectSearchCtrlr.status.value,
                          value: projectSearchCtrlr.status.value,
                          items: projectSearchCtrlr.statuslov.value.map((EntityStatus e) {
                            return DropdownMenuItem(
                              value: e.statusCode,
                              child: Text(
                                e.status!,
                                style: const TextStyle(fontSize: 14, color: Colors.black),
                              ),
                            );
                          }).toList(),
                          onChanged: (val) async {
                            debugPrint('val -----' + val);
                            projectSearchCtrlr.status.value = val;
                          },
                        ),
                      ),
                    if (talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY') != null)
                      Container(
                        margin: const EdgeInsets.only(left: 10.0, top: 10.0),
                        child: TaDropDown(
                          width: double.infinity,
                          label: talabel.get('TMCMOBILE_PROJECTSEARCH_SEARCHFILTERS_SORTBY')?.value ?? 'Sort By',
                          initvalue: projectSearchCtrlr.sortBy.value,
                          value: projectSearchCtrlr.sortBy.value,
                          items: projectSearchCtrlr.sortByLov.value.map((EntityStatus e) {
                            return DropdownMenuItem(
                              value: e.statusCode,
                              child: Text(
                                e.status!,
                                style: const TextStyle(fontSize: 14, color: Colors.black),
                              ),
                            );
                          }).toList(),
                          onChanged: (val) async {
                            debugPrint('val -----' + val);
                            projectSearchCtrlr.sortBy.value = val;
                          },
                        ),
                      ),
                    const SizedBox(
                      width: 30.0,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          TaButton(
                            type: 'elevate',
                            buttonText: 'Back',
                            readOnly: false,
                            onPressed: () => Get.back(),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          TaButton(
                            type: 'elevate',
                            buttonText: 'Apply',
                            readOnly: false,
                            onPressed: () {
                              Get.back();
                              projectSearchCtrlr.fetchProjects();
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}
