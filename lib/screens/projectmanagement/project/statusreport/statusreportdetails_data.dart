import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputdate.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/project/statusreport/statusreportdetails.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/statusreport/statusreport_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class StatusReportDetailsdata extends StatelessWidget {
  final statusid;
  final projectid;
  final appbartitle;
  String? mode;
  StatusReportDetailsdata({super.key, this.statusid, this.projectid, this.appbartitle, this.mode});
  LabelController talabel = Get.find<LabelController>();
  StatusReportController statusrepoCtrl = Get.find<StatusReportController>();

  Future _refreshStatusRepoDetData() async {
    await statusrepoCtrl.loadStatusreportDetailsData(projectid, statusid);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(appbartitle ?? '', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
            // statusrepoCtrl.loadStatusReportsData(projectid);
          },
        ),
        actions: [
          if (mode == 'edit')
            TextButton(
              onPressed: () async {
                await statusrepoCtrl.saveSrDetData(projectid, statusid);
                Get.back();
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                    style: ComponentUtils.appbartitlestyle,
                  ),
                ],
              ),
            ),
        ],
      ),
      body: Stack(
        children: <Widget>[
          Positioned.fill(
            //top: 15,
            child: RefreshIndicator(
              onRefresh: _refreshStatusRepoDetData,
              child: GetX<StatusReportController>(
                initState: (state) async {
                  Future.delayed(const Duration(seconds: 0), () async {
                    statusrepoCtrl.srdetchangeList.clear();
                    await statusrepoCtrl.loadStatusreportDetailsData(projectid, statusid);
                  });
                },
                builder: (_) {
                  return _.detdataloading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.srdetails.isEmpty
                          ? Center(
                              child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.srdetails[index], index);
                              },
                              itemCount: _.srdetails.length,
                              //  ),
                            );
                },
              ),
            ),
          ),
          // Positioned(
          //   bottom: 30,
          //   right: 30,
          //   child: FloatingActionButton(
          //     elevation: 5.0,
          //     child: const Icon(
          //       Icons.add,
          //       size: 30,
          //     ),
          //     onPressed: () {},
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, StatusReportDetailsData srd, int index) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          srd.milestoneName ?? '',
                          style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 2,
                    ),
                    if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_ORIGINALCMPLTEDATE') != null)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: <Widget>[
                          Text(
                            talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_ORIGINALCMPLTEDATE')!.value!,
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            srd.origianlCompletionDate ?? '',
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    const SizedBox(
                      height: 2,
                    ),
                    if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_PROJECTEDCMPLTEDATE') != null)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: <Widget>[
                          Text(
                            talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_PROJECTEDCMPLTEDATE')!.value!,
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            srd.projectedCompletionDate ?? '',
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    const SizedBox(
                      height: 2,
                    ),
                    if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_ACTUALCMPLTEDATE') != null)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: <Widget>[
                          Text(
                            talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_ACTUALCMPLTEDATE')!.value!,
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            srd.actualCompletionDate ?? '',
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    const SizedBox(
                      height: 2,
                    ),
                    if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_MILESTONERISK') != null)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: <Widget>[
                          Text(
                            talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_MILESTONERISK')!.value!,
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            srd.milestoneRisk ?? '',
                            style: TextStyle(color: primary, fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                  ],
                ),
                if (mode == 'edit')
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: <Widget>[
                      IconButton(
                          onPressed: () {
                            debugPrint('-----------------');
                            statusrepoCtrl.deleteDetItemfromlist(srd.detailStatusId);
                          },
                          icon: Icon(
                            Icons.delete,
                            color: secondary,
                          )),
                      IconButton(
                          onPressed: () async {
                            await updateSrDetailsItem(srd, index);
                          },
                          icon: Icon(
                            Icons.edit,
                            color: secondary,
                          )),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  updateSrDetailsItem(StatusReportDetailsData srd, int index) {
    statusrepoCtrl.updateDetflag.value = false;
    statusrepoCtrl.projectedCompliDateCtrl.text = ComponentUtils.dateToString(srd.projectedCompletionDate);
    statusrepoCtrl.actualCompliDateCtrl.text = ComponentUtils.dateToString(srd.actualCompletionDate);
    statusrepoCtrl.detRisk.value = srd.milestoneRisk!;

    return Get.defaultDialog(
        title: 'Update',
        titleStyle: const TextStyle(fontSize: 16),
        content: SizedBox(
          height: 250,
          child: SingleChildScrollView(
            child: Column(
              children: [
                if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_PROJECTEDCMPLTEDATE') != null)
                  TaFormInputDate(
                    label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_PROJECTEDCMPLTEDATE')!.value,
                    controller: statusrepoCtrl.projectedCompliDateCtrl,
                    onChanged: (dateval) {
                      statusrepoCtrl.updateDetflag.value = true;
                      statusrepoCtrl.projectedCompliDateCtrl.text = dateval.toString();
                    },
                    //readOnly: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_PROJECTEDCMPLTEDATE').ro,
                    // onSaved: (val) {
                    //   statusrepoCtrl.weekendingCtrl.text = val;
                    //   statusrepoCtrl.statusreport.value.weekEnding = val;
                    // },
                  ),
                if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_ACTUALCMPLTEDATE') != null)
                  TaFormInputDate(
                    label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_ACTUALCMPLTEDATE')!.value,
                    controller: statusrepoCtrl.actualCompliDateCtrl,
                    onChanged: (dateval) {
                      statusrepoCtrl.updateDetflag.value = true;
                      statusrepoCtrl.actualCompliDateCtrl.text = dateval.toString();
                    },
                    //readOnly: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_WEEKENDING').ro,
                    // onSaved: (val) {
                    //   statusrepoCtrl.weekendingCtrl.text = val;
                    //   statusrepoCtrl.statusreport.value.weekEnding = val;
                    // },
                  ),
                if (talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_MILESTONERISK') != null)
                  TaFormDropdown(
                    label: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET_MILESTONERISK')!.value,
                    emptttext: 'Select',
                    onChanged: talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_FINANCIALHEALTH')!.ro!
                        ? null
                        : (newValue) {
                            debugPrint('newValue          ' + newValue);
                            statusrepoCtrl.updateDetflag.value = true;
                            statusrepoCtrl.detRisk.value = newValue;
                          },
                    value: (statusrepoCtrl.detRisk.value == '' || statusrepoCtrl.detRisk.value == 'null')
                        ? null
                        : statusrepoCtrl.detRisk.value,
                    listflag: (statusrepoCtrl.statusreportrisklov.value.isNotEmpty),
                    items: statusrepoCtrl.statusreportrisklov.value.map((LookupValues l) {
                      return DropdownMenuItem(
                        value: l.lookupCode,
                        child: new Text(
                          l.lookupValue!,
                          style: TextStyle(fontSize: 14, color: Colors.black),
                        ),
                      );
                    }).toList(),
                  ),
              ],
            ),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  Get.back();
                },
              ),
              const SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_OK')?.value ?? 'Ok',
                onPressed: () async {
                  if (statusrepoCtrl.updateDetflag.value) await statusrepoCtrl.updateDetItemFromlist(srd, index);
                  Get.back();
                },
              ),
              const SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }
}
