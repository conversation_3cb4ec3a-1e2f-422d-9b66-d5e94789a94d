import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectgeneral_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

class ProjectGeneral extends GetView<ProjectGeneralController> {
  final ProjectsView? project;
  String? mode;
  ProjectGeneral({super.key, this.project, this.mode});

  Applabel? label;
  LabelController talabel = Get.find<LabelController>();
  ProjectGeneralController genCtrl = Get.find<ProjectGeneralController>();

  @override
  Widget build(BuildContext context) {
    debugPrint('${project!.toJson()}');
    genCtrl.labelsloading.value = true;

    return Stack(children: <Widget>[
      Positioned.fill(
        child: GetX<LabelController>(
          initState: (state) {
            Future.delayed(Duration.zero, () async {
              await genCtrl.getlabels(talabel);
            });
          },
          builder: (_) {
            return genCtrl.labelsloading.value ? const ProgressIndicatorCust() : generalTab(project);
          },
        ),
      ),
    ]);
  }

  Widget generalTab(ProjectsView? p) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTID') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTID')!.value,
              value: p!.projectId.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTNAME') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTNAME')!.value,
              value: p!.projectName.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTNUM') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTNUM')!.value,
              value: p!.projectNumber?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTTYPE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROJECTTYPE')!.value,
              value: p!.projectTypeDesc?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_ADDRESS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_ADDRESS')!.value,
              value: p!.entityAddress.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_ENTITYTYPE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_ENTITYTYPE')!.value,
              value: p!.entityType?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_OWNERSHIPTYPE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_OWNERSHIPTYPE')!.value,
              value: p!.ownershipType?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_STATUS') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_STATUS')!.value,
              value: p!.statusDesc?.toString(),
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROGRAM') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_PROJECTS_GENERAL_PROGRAM')!.value,
              value: p!.programName?.toString(),
              readOnly: true,
            ),
        ],
      ),
    );
  }
}
