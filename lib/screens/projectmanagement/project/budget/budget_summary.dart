import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/ta_sticky_ordered_table.dart';
import '../../../../common/component_utils.dart';
import '../../../../common/progess_indicator_cust.dart';
import '../../../../models/project/budget/budgettotal.dart';
import '../../../../providers/projectmanagement/project/budget/budget_summary_controller.dart';
import '../../../../providers/ta_admin/label_controller.dart';
import '../../../../utils/common_utils.dart';
import '../../../common/utils/tree/tree_node.dart';

class BudgetSummary extends GetView<BudgetSummaryController> {
  final int? entityid;

  BudgetSummary({super.key, this.entityid});
  LabelController talabel = Get.find<LabelController>();

  List<DataRow> getRows(List<TreeNode<BudgetTotal>> bData, BuildContext context) =>
      bData.map((TreeNode<BudgetTotal> bItem) {
        final approvedVsCommitment = (bItem.node.approvedBudget ?? 0) - (bItem.node.commitment ?? 0);
        final cells = [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_DIVISION') != null) bItem.node.division ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_CATEGORY') != null) bItem.node.category ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_FORECAST') != null)
            ComponentUtils.convertNumberFormat(bItem.node.initialBudget?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_APPROVEDBUDGET') != null)
            ComponentUtils.convertNumberFormat(bItem.node.approvedBudget?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_COMMITMENT') != null)
            ComponentUtils.convertNumberFormat(bItem.node.commitment?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_CHANGEORDER') != null)
            ComponentUtils.convertNumberFormat(bItem.node.changeOrder?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_ACTUALCOST') != null)
            ComponentUtils.convertNumberFormat(bItem.node.actualCost?.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_APPROVEDVSCOMMITTED') != null)
            ComponentUtils.convertNumberFormat(approvedVsCommitment.toString())
        ];

        List<DataCell> getCells(List<dynamic> cells) => cells
            .mapIndexed((index, data) => DataCell(
                  SizedBox(
                    width: 130,
                    child: Row(
                      children: [
                        bItem.level == 1 && index == 0
                            ? const SizedBox(
                                width: 10,
                              )
                            : bItem.level == 2 && index == 0
                                ? const SizedBox(
                                    width: 45,
                                  )
                                : Container(),
                        if (bItem.isFolder && index == 0 && bItem.isRefreshing == false)
                          bItem.isExpanded
                              ? GestureDetector(
                                  onTap: () {
                                    controller.expandNode(bItem, bItem.node.entityId);
                                  },
                                  child: const Icon(Icons.arrow_drop_down))
                              : GestureDetector(
                                  onTap: () {
                                    controller.expandNode(bItem, bItem.node.entityId);
                                  },
                                  child: const Icon(Icons.arrow_right),
                                ),
                        if (bItem.isFolder && index == 0 && bItem.isRefreshing == true)
                          const SizedBox(
                            height: 15.0,
                            width: 15.0,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                        if (bItem.isFolder && index == 0 && bItem.isRefreshing == true)
                          const SizedBox(
                            height: 15.0,
                            width: 15.0,
                          ),
                        Expanded(
                          child: Text(
                            '$data',
                            style: bItem.isBold == true
                                ? const TextStyle(fontWeight: FontWeight.bold)
                                : const TextStyle(fontWeight: FontWeight.normal),
                          ),
                        ),
                      ],
                    ),
                  ),
                ))
            .toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  List<String?> columnsData() {
    List<String?> cd = [];
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_DIVISION') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_DIVISION')!.value!);
    }
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_CATEGORY') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_CATEGORY')!.value!);
    }
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_FORECAST') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_FORECAST')!.value!);
    }
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_APPROVEDBUDGET') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_APPROVEDBUDGET')!.value!);
    }
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_COMMITMENT') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_COMMITMENT')!.value!);
    }
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_CHANGEORDER') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_CHANGEORDER')!.value!);
    }
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_ACTUALCOST') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_ACTUALCOST')!.value!);
    }
    if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_APPROVEDVSCOMMITTED') != null) {
      cd.add(talabel.get('TMCMOBILE_PROJECTS_BUDGET_BUDGETSUMMERY_APPROVEDVSCOMMITTED')!.value!);
    }

    return cd;
  }

  @override
  Widget build(BuildContext context) {
    controller.loadSummary(entityId: entityid);
    List<String?> columns = columnsData();
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Budget Summary', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: Obx(
        () => controller.isLoading.isTrue
            ? const ProgressIndicatorCust()
            : TaStickyOrderedTable(
                columns: columns,
                //  [
                //   'Division',
                //   'Category',
                //   'Forecast',
                //   'Approved Budget',
                //   'Commitment',
                //   'Change Order',
                //   'Actual Cost',
                //   'Approved vs Committed'
                // ],

                //  ordering: const {0, 3, 2, 1, 5, 7, 4, 6}, //for testing ordering
                rows: getRows(controller.budgetDataToShow, context),
              ),
      ),
    );
  }
}
