import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tatable.dart';
import 'package:tangoworkplace/models/project/budget/budget.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/budget_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/component_utils.dart';

class BudgetApprovalDetails extends GetView<BudgetController> {
  final String? entityType;
  final int? entityId;
  BudgetApprovalDetails({super.key, this.entityType, this.entityId});
  LabelController talabel = Get.find<LabelController>();
  BudgetController budgetCntrl = Get.find<BudgetController>();

  Future _refreshBudgetData() async {
    budgetCntrl.loadEntityBudget(entitytype: entityType, entityid: entityId);
  }

  @override
  Widget build(BuildContext context) {
    return
        // Obx(
        //   () =>
        // budgetCntrl.labelsloading.value
        //     ? ComponentUtils.labelLoadScaffold()
        //     :
        Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(
          'Details',
          //talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS')?.value ?? '',
          style: ComponentUtils.appbartitlestyle,
        ),
        // actions: <Widget>[
        //   IconButton(
        //     color: ComponentUtils.primecolor,
        //     icon: const Icon(Icons.swap_horizontal_circle),
        //     onPressed: () {
        //       budgetCntrl.tblflag.toggle();
        //     },
        //   ),
        // ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: Container(
        // color: Colors.white,
        child: budgetDataTable(),
      ),
      //),

      // },
    );
  }

  Widget budgetDataTable() {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: RefreshIndicator(
          onRefresh: _refreshBudgetData,
          child: GetX<BudgetController>(
            initState: (state) {
              budgetCntrl.loadEntityBudget(entitytype: entityType, entityid: entityId);
            },
            builder: (ctrl) {
              return ctrl.isload.isTrue
                  ? const ProgressIndicatorCust()
                  : ctrl.budgets.isEmpty
                      ? Center(
                          child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                      : (!budgetCntrl.tblflag.value
                          ? TaTable(
                              columns: const [
                                'Line Item', 'Initial budget', 'Approved Budget'
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT').value,
                                // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST') != null)
                                //   talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST').value
                              ],
                              rows: getRows(budgetCntrl.budgets),
                            )
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, ctrl.budgets[index]);
                              },
                              itemCount: ctrl.budgets.length,
                            ));
            },
          ),
        ),
      ),
    ]);
  }

  List<DataColumn> getColumns(List<String> columns) => columns
      .map((String column) => DataColumn(
            label: Text(column),
            // onSort: onSort,
          ))
      .toList();

  List<DataRow> getRows(List<Budget> budgetdata) => budgetdata.map((Budget bgt) {
        final cells = [
          bgt.lineItemSeq?.toString(),
          ComponentUtils.convertNumberFormat(bgt.initialBudget?.toString() ?? '0.0'),
          ComponentUtils.convertNumberFormat(bgt.approvedBudget?.toString() ?? '0.0'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM') != null) bgt.lineItemSeq,
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE') != null) bgt.taskTypeTemp ?? '',
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION') != null) bgt.divisionTemp ?? '',
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME') != null) bgt.subTaskName ?? '',
          // //bgt.taskName ?? '',
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET') != null)
          //   ComponentUtils.convertNumberFormat(bgt.initialBudget?.toString() ?? '0.0'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET') != null)
          //   ComponentUtils.convertNumberFormat(bgt.approvedBudget?.toString() ?? '0.0'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST') != null)
          //   ComponentUtils.convertNumberFormat(bgt.estimatedCost?.toString() ?? '0.0'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT') != null)
          //   ComponentUtils.convertNumberFormat(bgt.commitment?.toString() ?? '0.0'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST') != null)
          //   ComponentUtils.convertNumberFormat(bgt.actualCost?.toString() ?? '0.0')
        ];
        List<DataCell> getCells(List<dynamic> cells) => cells.map((data) => DataCell(Text('$data'))).toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  Widget listitem(BuildContext context, Budget bgt) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      padding: const EdgeInsets.only(right: 10, left: 10, top: 5),
      child: Column(
        children: [
          ComponentUtils.listVertRow('Line Item', bgt.lineItemSeq?.toString() ?? ''),
          ComponentUtils.listVertRow('Initial Budget', bgt.initialBudget?.toString() ?? '0.0', dtype: 'num'),
          ComponentUtils.listVertRow('Approved Budget', bgt.approvedBudget?.toString() ?? '0.0', dtype: 'num'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM') != null)
          //   ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKNUM').value, bgt.lineItemSeq.toString() ?? '0'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE') != null)
          //   ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_TASKTYPE').value, bgt.taskTypeTemp ?? ''),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION') != null)
          //   ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_DIVISION').value, bgt.divisionDesc ?? ''),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME') != null)
          //   ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_SUBTASKNAME').value, bgt.subTaskName ?? ''),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET') != null)
          //   ComponentUtils.listVertRow(
          //       talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_INITBUDGET').value, bgt.initialBudget?.toString() ?? '0.0',
          //       dtype: 'num'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET') != null)
          //   // ComponentUtils.listVertRow('Amount', bgt.initialApprovedBudget.toString ?? '0.0'),
          //   ComponentUtils.listVertRow(
          //       talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_APRVDBUDGET').value, bgt.approvedBudget?.toString() ?? '0.0',
          //       dtype: 'num'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST') != null)
          //   ComponentUtils.listVertRow(
          //       talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ESTIMATEDCOST').value, bgt.estimatedCost?.toString() ?? '0.0',
          //       dtype: 'num'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT') != null)
          //   ComponentUtils.listVertRow(
          //       talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_COMMITMENT').value, bgt.commitment?.toString() ?? '0.0',
          //       dtype: 'num'),
          // if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST') != null)
          //   ComponentUtils.listVertRow(
          //       talabel.get('TMCMOBILE_PROJECTS_BUDGET_DETAILS_ACTUALCOST').value, bgt.actualCost?.toString() ?? '0.0',
          //       dtype: 'num'),
        ],
      ),
    );
  }
}
