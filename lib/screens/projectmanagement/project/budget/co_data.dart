import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tatable.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/project/budget/cohdr.dart';
import 'package:tangoworkplace/models/project/budget/pohdr.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/co_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/po_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class Codata extends StatelessWidget {
  final int? entityid;
  Codata({super.key, this.entityid});
  LabelController talabel = Get.find<LabelController>();
  CoController coCtrl = Get.put(CoController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text(talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO')?.value ?? '', style: ComponentUtils.appbartitlestyle),
          actions: <Widget>[
            IconButton(
              color: ComponentUtils.primecolor,
              icon: const Icon(Icons.swap_horizontal_circle),
              onPressed: () {
                coCtrl.tblflag.toggle();
              },
            ),
          ],
          backgroundColor: Colors.white,
          elevation: 5,
          leading: IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
            onPressed: () {
              debugPrint('------back-------');
              Get.back();
            },
          ),
        ),
        body: cotable(),
      ),
    );
  }

  Future _refreshPoData() async {
    coCtrl.loadCOheaderData(projectid: entityid);
  }

  Widget cotable() {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: RefreshIndicator(
          onRefresh: _refreshPoData,
          child: GetX<CoController>(
            initState: (state) {
              coCtrl.loadCOheaderData(projectid: entityid);
            },
            builder: (ctrl) {
              return ctrl.ishdrloading.isTrue
                  ? const ProgressIndicatorCust()
                  : ctrl.codata.isEmpty
                      ? Center(
                          child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                      : (coCtrl.tblflag.value
                          ? TaTable(
                              columns: [
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_COID') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_COID')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_CONUM') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_CONUM')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_POID') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_POID')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_STATUS') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_STATUS')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_ISSUEDATE') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_ISSUEDATE')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_TOTAMTWITHTAX') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_TOTAMTWITHTAX')!.value,
                                if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_SUPPLIER') != null)
                                  talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_SUPPLIER')!.value,
                              ],
                              rows: getRows(coCtrl.codata),
                            )
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, ctrl.codata[index]);
                              },
                              itemCount: ctrl.codata.length,
                            ));
            },
          ),
        ),
      ),
    ]);
  }

  List<DataRow> getRows(List<CoHdr> codata) => codata.map((CoHdr co) {
        final cells = [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_COID') != null) co.chgOrderId.toString() ?? '0',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_CONUM') != null) co.chgOrderNumber ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_POID') != null) co.chgOrderId.toString() ?? '0',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_STATUS') != null) co.statusDesc ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_ISSUEDATE') != null) co.chgOrderDate ?? '',
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_TOTAMTWITHTAX') != null)
            ComponentUtils.convertNumberFormat(co.totalAmtWithTax.toString() ?? '0.0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_SUPPLIER') != null) co.supplierName ?? ''
        ];
        List<DataCell> getCells(List<dynamic> cells) => cells.map((data) => DataCell(Text('$data'))).toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  Widget listitem(BuildContext context, CoHdr co) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      padding: const EdgeInsets.only(right: 10, left: 10, top: 5),
      child: Column(
        children: [
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_COID') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_COID')!.value!, co.chgOrderId.toString() ?? '0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_CONUM') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_CONUM')!.value!, co.chgOrderNumber ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_POID') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_POID')!.value!, co.chgOrderId.toString() ?? '0'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_STATUS') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_STATUS')!.value!, co.statusDesc ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_ISSUEDATE') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_ISSUEDATE')!.value!, co.chgOrderDate ?? ''),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_TOTAMTWITHTAX') != null)
            ComponentUtils.listVertRow(talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_TOTAMTWITHTAX')!.value!,
                co.totalAmtWithTax.toString() ?? '0.0',
                dtype: 'num'),
          if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_SUPPLIER') != null)
            ComponentUtils.listVertRow(
                talabel.get('TMCMOBILE_PROJECTS_BUDGET_CO_SUPPLIER')!.value!, co.supplierName ?? ''),
        ],
      ),
    );
  }
}
