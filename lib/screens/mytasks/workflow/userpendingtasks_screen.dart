import 'package:flutter/material.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/utils.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/mytasks/workflow/user_pending_tasks.dart';
import 'package:tangoworkplace/providers/mytasks/workflow/userpendingtasks_controller.dart';
import 'package:tangoworkplace/screens/mytasks/workflow/usertaskslist_screen.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/themedata.dart';

import '../../../common/component_utils.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../home/<USER>';

class Myapprovals extends StatelessWidget {
  String? navFrom;
  Myapprovals({this.navFrom, super.key});
  static const routName = '/Myapprovals';
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  MyApprovalsController myApprovalsState = Get.put(MyApprovalsController());

  MyApprovalsController myApprovalsCntrlr = Get.find<MyApprovalsController>();
  LabelController talabel = Get.find<LabelController>();
  Future _refreshPendingtasks() async {
    ProgressUtil.showLoaderDialog(Get.context!);
    await myApprovalsCntrlr.loadtaskcount();
    ProgressUtil.closeLoaderDialog(Get.context!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_HOME_APPROVALS')?.value ?? 'My Approvals',
            style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            //  Get.off(() => HomeScreen());
            Get.back();
          },
        ),
        actions: [
          IconButton(
            onPressed: _refreshPendingtasks,
            icon: Icon(Icons.refresh, color: ComponentUtils.primecolor),
          ),
        ],
      ),
      key: _scaffoldKey,
      body: Column(
        children: [
          const SizedBox(
            height: 5.0,
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshPendingtasks,
              child: GetX<MyApprovalsController>(
                initState: (state) async {
                  Future.delayed(Duration.zero, () async {
                    await myApprovalsCntrlr.getlabels();

                    await myApprovalsCntrlr.loadtaskcount();
                  });
                },
                builder: (_) {
                  return _.entityTaskslabelsLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : SingleChildScrollView(
                          child: Column(
                            children: [
                              if (navFrom == null || navFrom == 'project')
                                Column(
                                  children: [
                                    if (talabel.get('TMCMOBILE_APPROVALS_PROJECTS') != null)
                                      taskEntityItem(
                                          entityType: 'PROJECT',
                                          label: talabel.get('TMCMOBILE_APPROVALS_PROJECTS')?.value,
                                          count: myApprovalsCntrlr.projecttaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_BUDGET') != null)
                                      taskEntityItem(
                                          entityType: 'BUDGET',
                                          label: talabel.get('TMCMOBILE_APPROVALS_BUDGET')?.value,
                                          count: myApprovalsCntrlr.budgettaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_BUDGETCHGREQ') != null)
                                      taskEntityItem(
                                          entityType: 'BUDGETCO',
                                          label: talabel.get('TMCMOBILE_APPROVALS_BUDGETCHGREQ')?.value,
                                          count: myApprovalsCntrlr.budgetcotaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_PO') != null)
                                      taskEntityItem(
                                          entityType: 'PO',
                                          label: talabel.get('TMCMOBILE_APPROVALS_PO')?.value,
                                          count: myApprovalsCntrlr.potaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_CO') != null)
                                      taskEntityItem(
                                          entityType: 'CO',
                                          label: talabel.get('TMCMOBILE_APPROVALS_CO')?.value,
                                          count: myApprovalsCntrlr.cotaskcnt.value),
                                    // if (talabel.get('TMCMOBILE_APPROVALS_EWA') != null)
                                    //   taskEntityItem(
                                    //       entityType: 'EWA',
                                    //       label: talabel.get('TMCMOBILE_APPROVALS_EWA')?.value,
                                    //       count: myApprovalsCntrlr.ewataskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_INVOICE') != null)
                                      taskEntityItem(
                                          entityType: 'INVOICE',
                                          label: talabel.get('TMCMOBILE_APPROVALS_INVOICE')?.value,
                                          count: myApprovalsCntrlr.invoicetaskcnt.value),
                                  ],
                                ),
                              if (navFrom == null || navFrom == '')
                                Column(
                                  children: [
                                    if (talabel.get('TMCMOBILE_APPROVALS_SITES') != null)
                                      taskEntityItem(
                                          entityType: 'SITE',
                                          label: talabel.get('TMCMOBILE_APPROVALS_SITES')?.value,
                                          count: myApprovalsCntrlr.sitetaskcnt.value),
                                  ],
                                ),
                              if (navFrom == null || navFrom == 'lease')
                                Column(
                                  children: [
                                    if (talabel.get('TMCMOBILE_APPROVALS_LEASE') != null)
                                      taskEntityItem(
                                          entityType: 'LEASE',
                                          label: talabel.get('TMCMOBILE_APPROVALS_LEASE')?.value,
                                          count: myApprovalsCntrlr.leasetaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_LEASEBATCHRENT') != null)
                                      taskEntityItem(
                                          entityType: 'LEASEBATCH',
                                          label: talabel.get('TMCMOBILE_APPROVALS_LEASEBATCHRENT')?.value,
                                          count: myApprovalsCntrlr.leasebatchrenttaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_LEASEOTP') != null)
                                      taskEntityItem(
                                          entityType: 'LEASEOTP',
                                          label: talabel.get('TMCMOBILE_APPROVALS_LEASEOTP')?.value,
                                          count: myApprovalsCntrlr.leaseotptaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_LEASERECURRINGCOST') != null)
                                      taskEntityItem(
                                          entityType: 'LEASEREC',
                                          label: talabel.get('TMCMOBILE_APPROVALS_LEASERECURRINGCOST')?.value,
                                          count: myApprovalsCntrlr.leaserecurcosttaskcnt.value),
                                  ],
                                ),
                              if (navFrom == null || navFrom == '')
                                Column(
                                  children: [
                                    // if (talabel.get('TMCMOBILE_APPROVALS_SITES') != null)
                                    //   taskEntityItem(
                                    //       entityType: 'SITE',
                                    //       label: talabel.get('TMCMOBILE_APPROVALS_SITES')?.value,
                                    //       count: myApprovalsCntrlr.sitetaskcnt.value),
                                    if (talabel.get('TMCMOBILE_APPROVALS_DOCUMENTS') != null)
                                      taskEntityItem(
                                          entityType: 'DOCUMENT',
                                          label: talabel.get('TMCMOBILE_APPROVALS_DOCUMENTS')?.value,
                                          count: myApprovalsCntrlr.doctaskcnt.value),
                                  ],
                                ),
                            ],
                          ),
                        );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget taskEntityItem({String? entityType, String? label, String? count}) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          myApprovalsCntrlr.taskListSearchController.text = '';
          Get.to(() => UsertaskList(
                entitytype: entityType,
                entitylabel: label,
              ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          height: 50,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                label ?? '${entityType!} Approvals',
                style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 15),
              ),
              Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Container(
                      //padding: EdgeInsets.all(1),
                      decoration: BoxDecoration(
                        color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 26,
                        minHeight: 14,
                      ),
                      child: Center(
                        child: Text(
                          count ?? '...',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 10.0,
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: primary,
                    ),
                  ]),
            ],
          ),
        ));
  }
}
