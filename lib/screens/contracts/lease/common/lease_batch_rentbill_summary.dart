import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../common/component_utils.dart';
import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/component_widgets/tatable.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/contracts/lease/LeaseBatchRentPaysummeryData.dart';
import '../../../../models/contracts/lease/leasebatchrentbillsummerydata.dart';
import '../../../../providers/contracts/lease/lease_batch_rentbill_summery_controller.dart';
import '../../../../providers/contracts/lease/lease_batch_rentpay_summery_controller.dart';
import '../../../../providers/ta_admin/label_controller.dart';
import '../../../../utils/device_util.dart';

class LeaseBatchRentBillSummery extends StatelessWidget {
  int? batchid;
  LeaseBatchRentBillSummery({super.key, this.batchid});
  LabelController talabel = Get.find<LabelController>();
  LeaseBatchRentBillSummeryController lbprsCtrl = Get.find<LeaseBatchRentBillSummeryController>();

  Future _refreshStatusReports() async {
    await lbprsCtrl.loadBatchRentBillSummery(batchid);
  }

  @override
  Widget build(BuildContext context) {
    return //SingleChildScrollView(
        //child:
        summerytable();
    //);
  }

  Widget summerytable() {
    return Stack(children: <Widget>[
      Positioned.fill(
        // child: RefreshIndicator(
        // onRefresh: _refreshPoData,
        child: GetX<LeaseBatchRentBillSummeryController>(
          initState: (state) {
            lbprsCtrl.loadBatchRentBillSummery(batchid);
          },
          builder: (ctrl) {
            return ctrl.isLoading.isTrue
                ? const ProgressIndicatorCust()
                : (ctrl.lbrpsList.isEmpty)
                    ? Center(
                        child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, Get.context!))))
                    : // (poCtrl.tblflag.value?
                    TaTable(
                        columns: const [
                          //'Lease Payment Batch Id',
                          'Currency Code',
                          'Lease Count',
                          'Total Payment Count',
                          'Total Net Amount',
                          'Total Gross Amount',
                          'Total Tax Amount'
                        ],
                        rows: getRows(ctrl.lbrpsList),
                      );
            // : ListView.builder(
            //     itemBuilder: (context, index) {
            //       return listitem(context, ctrl.podata[index]);
            //     },
            //     itemCount: ctrl.podata.length,
            //   ),
          },
        ),
      ),
    ]);
  }

  List<DataRow> getRows(List<LeaseBatchRentBillsummeryData> rpdata) => rpdata.map((LeaseBatchRentBillsummeryData rp) {
        final cells = [
          // rp.leasePaymentBatchId.toString(),
          rp.currencyCode ?? '',
          rp.distinctLeasesCount?.toString() ?? '0',
          rp.totalPaymentsCount?.toString() ?? '0',
          ComponentUtils.convertNumberFormat(rp.sumNetAmount?.toString() ?? '0.0'),
          ComponentUtils.convertNumberFormat(rp.sumGrossAmount?.toString() ?? '0.0'),
          ComponentUtils.convertNumberFormat(rp.sumTaxAmount?.toString() ?? '0.0'),
        ];
        List<DataCell> getCells(List<dynamic> cells) => cells.map((data) => DataCell(Text('$data'))).toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  Widget listitem(BuildContext context, LeaseBatchRentPaysummeryData lp) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return SingleChildScrollView(
      child: GestureDetector(
        // onTap: () {

        // },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
          padding: const EdgeInsets.only(right: 10, left: 10, top: 5),
          child: Column(
            children: [
              ComponentUtils.listVertRow('Lease Payment Batch Id', lp.leasePaymentBatchId.toString()),
              ComponentUtils.listVertRow('Lease Payment Batch Id', lp.currencyCode ?? ''),
              ComponentUtils.listVertRow('Lease Payment Batch Id', lp.distinctLeasesCount?.toString() ?? '0'),
              ComponentUtils.listVertRow('Lease Payment Batch Id', lp.totalPaymentsCount?.toString() ?? '0'),
              ComponentUtils.listVertRow('Lease Payment Batch Id', lp.sumNetAmount?.toString() ?? '0.0', dtype: 'num'),
              ComponentUtils.listVertRow('Lease Payment Batch Id', lp.sumGrossAmount?.toString() ?? '0.0',
                  dtype: 'num'),
              ComponentUtils.listVertRow('Lease Payment Batch Id', lp.sumTaxAmount?.toString() ?? '0.0', dtype: 'num'),
            ],
          ),
        ),
      ),
    );
  }
}
