import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/component_widgets/tatable.dart';
import '../../../models/common/demographic.dart';
import '../../../providers/common/entitydemographics_controller.dart';
import '../../../utils/device_util.dart';

class EntityDemographics extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  final String? mode;
  EntityDemographics({super.key, this.entityType, this.entityId, this.mode});
  LabelController talabel = Get.find<LabelController>();
  EntityDemographicsController dmgCtrl = Get.find<EntityDemographicsController>();

  // Future _refreshAdhocTasklist() async {
  //   adhocTaskCtrl.loadAdhocTasks(entityType: entityType, entityId: entityId);
  // }

  @override
  Widget build(BuildContext context) {
    return listStack(context);
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      Positioned.fill(
        child: GetX<EntityDemographicsController>(
          initState: (state) async {
            //  if (!dmgCtrl.dataloaded.value)
            await dmgCtrl.loadEntityDemographics(entityType!, entityId.toString());
          },
          builder: (_) {
            return _.isloading.isTrue
                ? const ProgressIndicatorCust()
                : _.dmglist.isEmpty
                    ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                    : Stack(
                        alignment: Alignment.topLeft,
                        children: [
                          Container(
                            margin: const EdgeInsets.fromLTRB(20, 15, 0, 10),
                            child: Table(
                              defaultColumnWidth: const FixedColumnWidth(120.0),
                              children: [
                                TableRow(children: [
                                  Row(
                                    children: [
                                      Text('Cbsa Class :',
                                          style: TextStyle(
                                              color: ComponentUtils.primary,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12)),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Text(dmgCtrl.cbsaclass.value,
                                          style: TextStyle(color: ComponentUtils.primary, fontSize: 12))
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Text('Store Class :',
                                          style: TextStyle(
                                              color: ComponentUtils.primary,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12)),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Text(dmgCtrl.storeclass.value,
                                          style: TextStyle(color: ComponentUtils.primary, fontSize: 12)),
                                    ],
                                  ),
                                ]),
                                // TableRow(children: [
                                //   Column(children: [
                                //     SizedBox(
                                //       height: 8,
                                //     ),
                                //     Text(dmgCtrl.cbsaclass.value, style: TextStyle(color: ComponentUtils.primary, fontSize: 12))
                                //   ]),
                                //   Column(children: [
                                //     SizedBox(
                                //       height: 8,
                                //     ),
                                //     Text(dmgCtrl.storeclass.value, style: TextStyle(color: ComponentUtils.primary, fontSize: 12))
                                //   ]),
                                // ]),
                              ],
                            ),
                          ),

                          // Container(
                          //   margin: EdgeInsets.all(10.0),
                          //   constraints: BoxConstraints(maxWidth: 500),
                          //   padding: EdgeInsets.all(10.0),
                          //   // height: 200,
                          //   decoration: BoxDecoration(
                          //       color: Colors.white,
                          //       borderRadius: BorderRadius.circular(10.0),
                          //       boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                          //   child: Column(children: [
                          //     ComponentUtils.listVertRow('Cbsa Class', dmgCtrl.cbsaclass.value),
                          //     ComponentUtils.listVertRow('Store Class', dmgCtrl.storeclass.value),
                          //   ]),
                          // ),
                          Positioned.fill(
                            top: 40,
                            child: TaTable(
                              columns: dmgCtrl.tColumns.value,
                              rows: getRows(dmgCtrl.dmglist),
                            ),
                          ),
                        ],
                      );

            //  ListView.builder(
            //     itemBuilder: (context, index) {
            //       return listitem(context, _.dmglist.value[index]);
            //     },
            //     itemCount: _.dmglist.length,
            //   );
          },
        ),
      ),
      // Positioned(
      //   bottom: 30,
      //   right: 30,
      //   child: FloatingActionButton(
      //     elevation: 5.0,
      //     child: const Icon(
      //       Icons.add,
      //       size: 30,
      //     ),
      //     onPressed: () {
      //       debugPrint('add button----------------------');
      //       Get.to(() => AdhocTaskDetails(
      //             entityId: entityId,
      //             entityType: entityType,
      //             at_mode: 'create',
      //           ));
      //     },
      //   ),
      // ),
    ]);
  }

  List<DataRow> getRows(List<Demographic> data) => data.map((Demographic dmg) {
        var d = dmg.rows!;
        var cells = [dmg.columnName ?? ''];
        for (var e in d) {
          if (e == 'null') e = '';
          cells.add(e ?? '');
        }
        List<DataCell> getCells(List<dynamic> cells) => cells.map((data) => DataCell(Text('$data'))).toList();
        return DataRow(cells: getCells(cells));
      }).toList();

  Widget listitem(BuildContext context, Demographic dmg) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 1, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Flexible(
                  child: Text(
                    dmg.columnName ?? '',
                    style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // Text(
                //   task.taskSeq?.toString() ?? '',
                //   style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                //   overflow: TextOverflow.ellipsis,
                // ),
              ]),
              const SizedBox(
                height: 5,
              ),
              // Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [

              //   Text(
              //     task.taskTypeDesc?.toString() ?? '',
              //     style: TextStyle(color: primary, fontSize: 10),
              //   ),
              //   Text(
              //     task.status?.toString() ?? '',
              //     style: TextStyle(color: primary, fontSize: 10),
              //     overflow: TextOverflow.ellipsis,
              //   ),
              // ]),
              // SizedBox(
              //   height: 5,
              // ),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.start,
              //   children: [
              //     Icon(
              //       Icons.mail,
              //       color: primary,
              //       size: 15,
              //     ),
              //     SizedBox(
              //       width: 5,
              //     ),
              //     Text(
              //       task.assignedTo?.toString() ?? '',
              //       style: TextStyle(color: primary, fontSize: 10),
              //       overflow: TextOverflow.ellipsis,
              //     ),
              //   ],
              // ),
              // if (task.actualFinsh != null)
              //   SizedBox(
              //     height: 5.0,
              //   ),
              // if (task.actualFinsh != null)
              //   Row(mainAxisAlignment: MainAxisAlignment.start, children: [
              //     Text(
              //       task.actualFinsh?.toString() ?? '',
              //       style: TextStyle(color: primary, fontSize: 10),
              //       overflow: TextOverflow.ellipsis,
              //     ),
              //   ]),
            ],
          ),
        ),
      ),
    );
  }
}
