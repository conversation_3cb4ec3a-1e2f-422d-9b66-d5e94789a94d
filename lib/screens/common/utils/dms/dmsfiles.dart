import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../common/component_utils.dart';
import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/components.dart';
import '../../../../models/common/utils/dmsfile.dart';
import '../../../../providers/common/utils/dmsfiles_controller.dart';
import '../../../../utils/device_util.dart';

class DmsFiles extends StatelessWidget {
  final String? entitytype;
  final int? entityid;
  final int? rootfolderid;
  final int? subentityid;
  final String? subentitytype;
  final hdrText;
  final file_mode;
  String? mode;

  DmsFiles(
      {super.key,
      this.entitytype,
      this.mode,
      this.entityid,
      this.rootfolderid,
      this.hdrText,
      this.file_mode,
      this.subentityid,
      this.subentitytype});

  DmsFilesController dmsfileCtrl = Get.find<DmsFilesController>();

  void _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );
    if (result != null) {
      List<File> files;
      files = result.paths.map((path) => File(path!)).toList();

      for (var element in files) {
        File f = element;
        debugPrint('file name    ${f.path}');
      }
      ProgressUtil.showLoaderDialog(Get.context!);

      dmsfileCtrl.uploadFiles(files,
          entityType: entitytype, entityId: entityid, subentityid: subentityid, subentitytype: subentitytype);
    } else {
      // User canceled the picker
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Positioned.fill(
          top: 5,
          child: GetX<DmsFilesController>(
            initState: (state) {
              dmsfileCtrl.tempfiles.clear();
              dmsfileCtrl.fetchDmsFiles(subentityid: subentityid, subentitytype: subentitytype);
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.filesdata.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.filesdata[index], index);
                          },
                          itemCount: _.filesdata.length,
                          //  ),
                        );
            },
          ),
        ),
        if (file_mode == 'edit' && mode != 'view')
          Positioned(
            bottom: 30,
            right: 30,
            child: FloatingActionButton(
              elevation: 5.0,
              child: const Icon(
                Icons.add,
                size: 30,
              ),
              onPressed: () {
                _pickFiles();
              },
            ),
          ),
      ],
    );
  }

  Widget listitem(BuildContext context, DmsFile d, int index) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                GestureDetector(
                  onTap: () {
                    ComponentUtils.documentviewer(context, d.fileId.toString());
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Icon(
                        Icons.description,
                        color: secondary,
                        size: 20.0,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      SizedBox(
                        width: 230,
                        child: Text(
                          d.fileName ?? '',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 5,
                          softWrap: true,
                          style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
                if (file_mode == 'edit' && mode != 'view')
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      IconButton(
                          onPressed: () {
                            dmsfileCtrl.filesdata.removeWhere((element) => element.fileId == d.fileId);
                            dmsfileCtrl.tempfiles.value.add(d.fileId);
                          },
                          icon: Icon(
                            Icons.delete,
                            color: secondary,
                          )),
                    ],
                  )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
