import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/utils/docattachments_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../../common/component_utils.dart';
import 'attachmentdata.dart';
import 'dmsentityuploadfiles.dart';

class DocAttachments extends StatelessWidget {
  final entitytype;
  final entityid;
  final subentityid;
  final subentitytype;
  final fileidlist;
  final hdrText;
  final attachmentTabText;
  final uploadedTabText;
  String? mode;
  DocAttachments(
      {super.key,
      this.entitytype,
      this.entityid,
      this.subentityid,
      this.subentitytype,
      this.fileidlist,
      this.hdrText,
      this.attachmentTabText,
      this.mode,
      this.uploadedTabText});
  LabelController talabel = Get.find<LabelController>();
  DocAttachmentController docAttachCtrl = Get.put(DocAttachmentController());
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrText ?? 'Documents', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
        actions: [
          if (mode != 'view')
            TextButton(
              onPressed: () async {
                await docAttachCtrl.saveDocuments();
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                    style: ComponentUtils.appbartitlestyle,
                  ),
                ],
              ),
            ),
        ],
      ),
      key: _scaffoldKey,
      body: DefaultTabController(
        initialIndex: 0,
        length: 2,
        child: Column(
          children: <Widget>[
            Material(
              elevation: 5,
              color: Colors.white,
              child: TabBar(
                // isScrollable: true,
                labelColor: ComponentUtils.tablabelcolor,
                unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                indicatorColor: ComponentUtils.tabindicatorColor,
                indicatorSize: TabBarIndicatorSize.tab,
                labelStyle: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.normal,
                    fontSize: 14,
                    color: ComponentUtils.tablabelcolor),
                tabs: [Tab(text: attachmentTabText ?? 'Attchments'), Tab(text: uploadedTabText ?? 'Uploaded')],
              ),
            ),
            Expanded(
              child: TabBarView(
                children: [
                  Attachementsdata(
                    fileidlist: fileidlist,
                    entityid: entityid,
                    entitytype: entitytype,
                    mode: mode,
                  ),
                  DmsEntityUploadFiles(
                    entityid: entityid,
                    entitytype: entitytype,
                    subentityid: subentityid,
                    subentitytype: subentitytype,
                    mode: mode,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
