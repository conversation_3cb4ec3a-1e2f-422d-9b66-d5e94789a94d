import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/screens/common/documents/documents.dart';
import 'package:tangoworkplace/screens/common/photos/photos.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../../common/component_utils.dart';
import '../../../../providers/common/utils/dmsfiles_controller.dart';
import '../../../../providers/ta_admin/label_controller.dart';
import 'dmsfiles.dart';

class DmsFilePG extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  final int? subentityid;
  final String? subentitytype;
  final int? rootfolderid;
  final hdrText;
  final file_mode;
  String? mode;

  DmsFilePG(
      {super.key,
      this.entityType,
      this.mode,
      this.entityId,
      this.rootfolderid,
      this.hdrText,
      this.file_mode,
      this.subentityid,
      this.subentitytype});
  DmsFilesController dmsfileCtrl = Get.put(DmsFilesController());
  LabelController talabel = Get.find<LabelController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrText ?? 'Documents',
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
        actions: [
          if (file_mode == 'edit' && mode != 'view')
            TextButton(
              onPressed: () async {
                await dmsfileCtrl.saveDocuments();
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                    style: ComponentUtils.appbartitlestyle,
                  ),
                ],
              ),
            ),
        ],
      ),
      body: DmsFiles(
        rootfolderid: rootfolderid,
        entityid: entityId,
        entitytype: entityType,
        subentityid: subentityid,
        subentitytype: subentitytype,
        file_mode: file_mode,
        mode: mode,
      ),
    );
  }
}
