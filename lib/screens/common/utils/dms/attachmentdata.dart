import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/utils/dmsfile.dart';
import 'package:tangoworkplace/providers/common/utils/docattachments_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';

class Attachementsdata extends StatelessWidget {
  final entitytype;
  final entityid;
  final fileidlist;
  String? mode;
  Attachementsdata({super.key, this.entitytype, this.entityid, this.fileidlist, this.mode});
  DocAttachmentController docAttachCtrl = Get.find<DocAttachmentController>();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Positioned.fill(
          top: 15,
          child: GetX<DocAttachmentController>(
            initState: (state) {
              if (fileidlist != null && fileidlist != '') {
                docAttachCtrl.tempfileids.value = fileidlist.split(',');
                docAttachCtrl.getattachments(fileidlist);
              } else {
                docAttachCtrl.tempfileids.value = [];
              }
            },
            builder: (_) {
              return _.isattachLoading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.attachmentDocs.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.attachmentDocs[index], index, 'attachments');
                          },
                          itemCount: _.attachmentDocs.length,
                          //  ),
                        );
            },
          ),
        ),
        if (mode != 'view')
          Positioned(
            bottom: 30,
            right: 30,
            child: FloatingActionButton(
              elevation: 5.0,
              child: const Icon(
                Icons.add,
                size: 30,
              ),
              onPressed: () {
                docAttachCtrl.tempfiles.value.clear();
                _openDialog();
              },
            ),
          ),
      ],
    );
  }

  void _openDialog() async {
    await Navigator.of(Get.context!).push(MaterialPageRoute<String>(
        builder: (BuildContext context) {
          return Scaffold(
            appBar: AppBar(
              leading: IconButton(
                icon: const Icon(Icons.clear),
                color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                onPressed: () {
                  debugPrint('------back-------');
                  Get.back();
                },
              ),
              backgroundColor: Colors.white,
              elevation: 5,
              title: Text(
                'Available Documents',
                style: ComponentUtils.appbartitlestyle,
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    docAttachCtrl.addfileIdsToAttachments();
                    Get.back();
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'Done',
                        style: ComponentUtils.appbartitlestyle,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            body: GetX<DocAttachmentController>(
              initState: (state) {
                docAttachCtrl.fetchEntityDmsFiles(entityid: entityid, entitytype: entitytype);
              },
              builder: (_) {
                return _.isLoading.isTrue
                    ? const ProgressIndicatorCust()
                    : _.entityDocs.isEmpty
                        ? Center(
                            child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return listitem(context, _.entityDocs[index], index, 'entitydms');
                            },
                            itemCount: _.entityDocs.length,
                            //  ),
                          );
              },
            ),
          );
        },
        fullscreenDialog: true));
  }

  Widget listitem(BuildContext context, DmsFile d, int index, String type) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: EdgeInsets.symmetric(vertical: type == 'attachments' ? 5 : 15, horizontal: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Icon(
                          Icons.description,
                          color: secondary,
                          size: 20.0,
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Text(
                          d.fileName ?? '',
                          style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ],
                ),
                if (mode == 'view')
                  const SizedBox(
                    height: 35,
                  )
                else
                  type == 'attachments'
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            IconButton(
                                onPressed: () {
                                  debugPrint('-----------------');
                                  docAttachCtrl.deletefilefromlist(d.fileId.toString());
                                  docAttachCtrl.attachmentDocs.removeWhere((element) => element.fileId == d.fileId);
                                },
                                icon: Icon(
                                  Icons.delete,
                                  color: secondary,
                                )),
                          ],
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            GestureDetector(
                                child: Obx(
                                  () => docAttachCtrl.entityDocs.value[index].selectrow!
                                      ? Icon(
                                          Icons.check,
                                          color: Theme.of(context).primaryColor,
                                        )
                                      : const Icon(
                                          Icons.check_box_outline_blank,
                                          color: Colors.grey,
                                        ),
                                ),
                                onTap: () {
                                  d = docAttachCtrl.entityDocs.value[index];
                                  docAttachCtrl.entityDocs[index].selectrow = !d.selectrow!;
                                  debugPrint('-----------${docAttachCtrl.entityDocs.value[index].fileName}');

                                  docAttachCtrl.entityDocs[index] = d;
                                  // docAttachCtrl.attachmentDocs
                                  //     .removeWhere((element) => element.fileId == docAttachCtrl.entityDocs.value[index].fileId);

                                  if (docAttachCtrl.entityDocs[index].selectrow!) {
                                    docAttachCtrl.tempfiles.value.add(docAttachCtrl.entityDocs[index]);
                                  } else {
                                    docAttachCtrl.tempfiles.value.remove(docAttachCtrl.entityDocs[index]);
                                  }
                                  debugPrint('temp files ----${docAttachCtrl.tempfiles.value}');
                                }),
                          ],
                        ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
