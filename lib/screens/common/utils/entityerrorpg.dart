import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../common/component_utils.dart';

class EntityErrorPg extends StatelessWidget {
  final entitytype;
  final entityname;
  final entityid;

  EntityErrorPg({super.key, this.entitytype, this.entityname, this.entityid});

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(entityname, style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Container(
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'You dont have access to this $entitytype.\n\n Please contact the administrator...',
                style: TextStyle(
                  color: ComponentUtils.primecolor,
                  fontSize: DeviceUtils.taFontSize(1.55, context),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 30,
              ),
              TaButton(
                type: 'elevate',
                buttonText: 'Go Back',
                onPressed: () async {
                  debugPrint('------back-------');
                  Get.back();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
