import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

import '../../../common/component_utils.dart';
import '../../../common/widgets/alert_screen.dart';
import '../../../common/widgets/component_widgets/taforminputtext.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/common/utils/mfalogin_controller.dart';
import '../../../utils/common_utils.dart';
import '../../../utils/connections.dart';
import '../../../utils/request_api_utils.dart';

class MfaLogin extends StatelessWidget {
  final String? username;
  Function? toHome;
  final int? mfaexpiry;
  MfaLogin({super.key, this.username, this.toHome, this.mfaexpiry});
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  MfaLoginController mfaCtrl = Get.find<MfaLoginController>();
  @override
  Widget build(BuildContext context) {
    mfaCtrl.initializeTimer(mfaexpiry: mfaexpiry ?? 2);
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.clear),
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.delete<MfaLoginController>();
            CommonUtils.logout(context);
            Get.back();
          },
        ),
        backgroundColor: Colors.white,
        elevation: 5,
        title: Text(
          'OTP verification',
          style: ComponentUtils.appbartitlestyle,
        ),
      ),
      body: Obx(
        () => Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Container(
            padding: const EdgeInsets.fromLTRB(0, 50, 0, 0),
            child: Column(
              children: [
                Container(
                  //decoration: BoxDecoration(color: ComponentUtils.bodybackground),
                  //height: 125,

                  child: TaFormInputText(
                    label: 'OTP Code',
                    readOnly: mfaCtrl.expiryflag.value,
                    minLines: 1,
                    maxLines: 1,
                    value: mfaCtrl.otpval.value ?? '',
                    keyboard: TextInputType.number,
                    onChanged: (val) {
                      mfaCtrl.otpval.value = val?.toString() ?? '';
                    },
                    onSaved: (val) {
                      mfaCtrl.otpval.value = val?.toString() ?? '';
                    },
                    validate: (value) {
                      if (value.isEmpty) {
                        return 'This attribute is Required';
                      }
                      return null;
                    },
                  ),
                ),
                Container(
                    padding: const EdgeInsets.only(left: 10, right: 12),
                    margin: const EdgeInsets.only(top: 4.0),
                    alignment: Alignment.bottomLeft,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Visibility(
                          visible: mfaCtrl.otperrflag.value,
                          child: Container(
                            padding: const EdgeInsets.only(left: 12.0),
                            margin: const EdgeInsets.only(top: 1.0),
                            alignment: Alignment.bottomLeft,
                            child: Text(mfaCtrl.errormsg.value,
                                style: TextStyle(
                                  fontSize: 14.0,
                                  color: Theme.of(context).primaryColor,
                                )),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text('otp expiry  :',
                                style: TextStyle(
                                  fontSize: 12.0,
                                  color: ComponentUtils.primary,
                                )),
                            const SizedBox(width: 2),
                            Text(mfaCtrl.remtime.value,
                                style: TextStyle(
                                  fontSize: 12.0,
                                  color: ComponentUtils.secondary,
                                )),
                          ],
                        ),
                      ],
                    )),
                const SizedBox(
                  height: 10.0,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TaButton(
                      type: 'elevate',
                      buttonText: 'Cancel',
                      onPressed: () {
                        debugPrint('------back-------');
                        Get.delete<MfaLoginController>();
                        CommonUtils.logout(context);
                        Get.back();
                      },
                    ),
                    const SizedBox(
                      width: 10.0,
                    ),
                    Visibility(
                      visible: !mfaCtrl.expiryflag.value,
                      child: TaButton(
                        type: 'elevate',
                        buttonText: 'Verify',
                        onPressed: () async {
                          final isValid = _formKey.currentState!.validate();
                          if (!isValid) {
                            return;
                          }

                          _formKey.currentState!.save();

                          String url =
                              ApiService.getServerurl() + validateotpurl + '/' + mfaCtrl.otpval.value + '/' + username;
                          debugPrint('url-----------$url');
                          var uri = Uri.parse(url);
                          await http
                              .get(
                            uri,
                          )
                              .then((response) async {
                            debugPrint('${response.statusCode}');
                            Map<String, dynamic>? c = {};
                            if (response.statusCode == 200) {
                              c = json.decode(response.body) as Map<String, dynamic>?;
                              debugPrint('----$c');

                              if (c != null && c['status'] == 0) {
                                return toHome!();
                              } else {
                                mfaCtrl.errormsg.value = 'OTP Code is invalid. ';
                                mfaCtrl.otperrflag.value = true;
                              }
                            } else {
                              mfaCtrl.errormsg.value = 'OTP Code is invalid.. ';
                              mfaCtrl.otperrflag.value = true;
                            }
                          }).catchError((err) {
                            mfaCtrl.errormsg.value = 'OTP Code is invalid... ';
                            mfaCtrl.otperrflag.value = true;
                          });
                        },
                      ),
                    ),
                    const SizedBox(
                      width: 12.0,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
