import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/tree_node_widget.dart';
import 'package:tangoworkplace/providers/common/tree_node_controller.dart';
import 'package:tangoworkplace/screens/common/utils/tree/tree_node.dart';

import '../../../../models/common/document.dart';

List<TreeNode> copyTreeNodes(List<TreeNode>? nodes) {
  return _copyNodesRecursively(
    nodes,
  )!;
}

List<TreeNode<Document>> parseDocumentsResponse(
    Map<String, dynamic>? documentsFetched,
    ) {
  if (documentsFetched != null) {
    var status = documentsFetched['status'] as int?;

    if (status == 0) {
      var tagObjJson = documentsFetched['records'] as List?;
      if (tagObjJson != null) {
        return tagObjJson
            .map((tagJson) => Document.fromJson(tagJson))
            .map((document) {
          final isFolder =
              document.docType == "FOLDER" || document.docType == "SUB";
          return TreeNode(
            document,
            Container(
              padding: const EdgeInsets.all(2.0),
              child: Text(
                  "${document.docName ?? ""} ${isFolder ? " (${document.childcount ?? ""})" : ""}"),
            ),
            isFolder: isFolder,
          );
        }).toList();
      }
    }
  }
  return [];
}

List<TreeNode>? _copyNodesRecursively(
  List<TreeNode>? nodes,
) {
  if (nodes == null) {
    return null;
  }
  return List.unmodifiable(
    nodes.map((n) {
      return TreeNode(
        n.node,
        n.content,
        children: _copyNodesRecursively(n.children),
        isFolder: n.isFolder,
      );
    }),
  );
}

Widget buildNodes(
  Iterable<TreeNode> nodes,
  double? indent,
  double? iconSize,
) {
  for (var node in nodes) {
    Get.put(TreeNodeController(node), tag: node.hashCode.toString());
  }
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      for (var node in nodes)
        TreeNodeWidget(
          indent: indent,
          iconSize: iconSize,
          tagField: node.hashCode.toString(),
        )
    ],
  );
}
