import 'package:flutter/material.dart';

class TreeNode<T> {
  List<TreeNode>? children;
  Widget content;
  T node;
  bool isFolder;
  bool isExpanded;
  bool? isRefreshing;
  bool? isBold;
  int? level;

  TreeNode(
    this.node,
    this.content, {
    this.children,
    this.isFolder = true,
    this.isExpanded = false,
    this.isBold = false,
    this.level,
    this.isRefreshing = false
  });
}
