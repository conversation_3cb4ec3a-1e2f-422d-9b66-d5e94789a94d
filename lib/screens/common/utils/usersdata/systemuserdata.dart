import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/utils/adhoctasknotifuser.dart';
import 'package:tangoworkplace/models/common/utils/systemuser.dart';
import 'package:tangoworkplace/providers/common/adhoctasks_controller.dart';
import 'package:tangoworkplace/providers/common/utils/usernotifications_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../../common/component_utils.dart';
import '../../../../providers/common/meetingminute/mmActionItems_controller.dart';

class SystemUsersData extends StatelessWidget {
  final source;
  final adhoctaskid;
  MmActionItemsController? mmadhoctaskCntrlr;
  SystemUsersData({super.key, this.adhoctaskid, this.source, this.mmadhoctaskCntrlr});
  UserNotificationsController usernotiCtrl = Get.put(UserNotificationsController());
  EntityAdhicTasksController adhoctaskCntrlr = Get.find<EntityAdhicTasksController>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        if (source == 'ADHOC_ASSIGNTO' || source == 'MM_ADHOC_ASSIGNTO')
          TaSearchInputText(
              makeSearch: (searchtext) {
                usernotiCtrl.fetchSytemUsers(searchText: searchtext.toString());
              },
              searchController: usernotiCtrl.searchController,
              hintSearch: 'Search '),
        Expanded(
          child: GetX<UserNotificationsController>(
            initState: (state) {
              if (source == 'ADHOC_ASSIGNTO' || source == 'MM_ADHOC_ASSIGNTO') usernotiCtrl.fetchSytemUsers();
            },
            builder: (_) {
              return _.isSystemUserLoading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.systemusers.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.systemusers[index], index);
                          },
                          itemCount: _.systemusers.length,
                          //  ),
                        );
            },
          ),
        ),
      ],
    );
  }

  Widget listitem(BuildContext context, SystemUser c, int index) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {
        if (source == 'ADHOC_ASSIGNTO') {
          //adhoctaskCntrlr.assignedto.value = c.userName;
          adhoctaskCntrlr.assignToCtrl.text = c.userName!;
          adhoctaskCntrlr.emailaddress.value = c.emailAddress!;
          Get.back();
        } else if (source == 'MM_ADHOC_ASSIGNTO') {
          mmadhoctaskCntrlr!.assignToCtrl.text = c.userName!;
          mmadhoctaskCntrlr!.emailaddress.value = c.emailAddress!;
          Get.back();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        c.userName ?? '',
                        style: TextStyle(
                            color: primary,
                            //fontWeight: FontWeight.bold,
                            fontSize: 14),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Row(children: [
                        Text('${c.firstName} ${c.lastName}',
                            style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                      ]),
                      const SizedBox(
                        height: 2,
                      ),
                      Row(children: [
                        Icon(
                          Icons.email,
                          color: secondary,
                          size: 15,
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Text(c.emailAddress ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                      ]),
                    ],
                  ),
                  if (source != 'ADHOC_ASSIGNTO' && source != 'MM_ADHOC_ASSIGNTO')
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                            child: Obx(
                              () => usernotiCtrl.systemusers.value[index].selectrow!
                                  ? Icon(
                                      Icons.check,
                                      color: Theme.of(context).primaryColor,
                                    )
                                  : const Icon(
                                      Icons.check_box_outline_blank,
                                      color: Colors.grey,
                                    ),
                            ),
                            onTap: () {
                              c = usernotiCtrl.systemusers.value[index];
                              usernotiCtrl.systemusers[index].selectrow = !c.selectrow!;
                              debugPrint('-----------${usernotiCtrl.systemusers.value[index].lastName}');

                              usernotiCtrl.systemusers[index] = c;
                              usernotiCtrl.adhocnotifyusers.removeWhere((element) =>
                                  element.emailAddress == usernotiCtrl.systemusers.value[index].emailAddress);
                              if (usernotiCtrl.systemusers[index].selectrow!) {
                                usernotiCtrl.adhocnotifyusers.add(AdhocTaskNotifUser(
                                    adhocTaskId: adhoctaskid,
                                    emailAddress: c.emailAddress,
                                    firstName: c.firstName,
                                    lastName: c.lastName,
                                    userName: c.userName));
                              }
                            }),
                      ],
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
