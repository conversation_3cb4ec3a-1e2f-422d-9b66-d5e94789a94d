import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../utils/common_utils.dart';

class QrCodeView extends StatefulWidget {
  const QrCodeView({super.key});

  @override
  State<StatefulWidget> createState() => _QrCodeViewState();
}

class _QrCodeViewState extends State<QrCodeView> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  Barcode? result;
  var qrcodedata;
  int scancount = 0;
  QRViewController? controller;
  bool qrloading = false;

  // In order to get hot reload to work we need to pause the camera if the platform
  // is android, or resume the camera if the platform is iOS.
  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller?.pauseCamera();
    } else if (Platform.isIOS) {
      controller?.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Scan QR Code', style: ComponentUtils.appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            Navigator.pop(context, null);
          },
        ),
      ),
      body: Column(
        children: <Widget>[
          Expanded(
            flex: 5,
            child: qrloading
                ? const ProgressIndicatorCust()
                : QRView(
                    key: qrKey,
                    onQRViewCreated: _onQRViewCreated,
                    cameraFacing: CameraFacing.back,
                    overlay: QrScannerOverlayShape(
                      borderWidth: 10,
                      borderColor: ComponentUtils.primecolor,
                      borderRadius: 10,
                      borderLength: 20,
                      cutOutSize: MediaQuery.of(context).size.width * 0.8,
                    ),
                  ),
          ),
          // Expanded(
          //   flex: 1,
          //  child: Center(
          // child: (result != null) ? Text('Barcode Type: ${describeEnum(result?.format)}   Data: ${result?.code}') : Text('Scan a code'),
          //    child: (qrcodedata != null) ? Text('data is : $qrcodedata') : Text('Scan code'),
          //  ),
          // )
        ],
      ),
    );
  }

  Future<void> _onQRViewCreated(QRViewController controller) async {
    this.controller = controller;

    controller.scannedDataStream.listen((scanData) {
      String? res = scanData.code?.toString();
      if (res != null) {
        setState(() {
          scancount++;
          qrloading = true;
          if (scancount == 1) Get.back(result: res);
        });
      }
    });
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
