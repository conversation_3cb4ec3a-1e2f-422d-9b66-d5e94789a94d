import 'dart:convert';
import 'package:get/get.dart';
import 'package:html/parser.dart' as htmlparser;

import 'package:flutter/material.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:svg_path_parser/svg_path_parser.dart';
import 'package:universal_html/html.dart';
import '../../../common/component_utils.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/sitemanagement/site/site_swot_controller.dart';

class RichTextEditorWidget extends StatelessWidget {
  final String? entitytype;
  final String? entityname;
  final String? source;
  final entityid;

  RichTextEditorWidget({super.key, this.entitytype, this.entityname, this.entityid, this.source});
  SiteSwotController swtCtrl = Get.find<SiteSwotController>();
  HtmlEditorController controller = HtmlEditorController();

  @override
  Widget build(BuildContext context) {
    swtCtrl.prevText.value = swtCtrl.siterec.value.dealSummary ?? '';
    return SingleChildScrollView(
      child: Container(
        height: Get.height - 100,
        color: Colors.white,
        padding: const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          //mainAxisAlignment: MainAxisAlignment.center,
          //crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Obx(
              () => HtmlEditor(
                controller: controller,
                htmlEditorOptions: HtmlEditorOptions(
                  hint: 'Your text here...',
                  shouldEnsureVisible: true,
                  initialText: swtCtrl.siterec.value.dealSummary,
                ),
                htmlToolbarOptions: HtmlToolbarOptions(
                  toolbarPosition: ToolbarPosition.aboveEditor,
                  // defaultToolbarButtons: [
                  //   // StyleButtons(),
                  //   FontSettingButtons(),
                  //   FontButtons(),
                  //   ColorButtons(),
                  //   ListButtons(),
                  //   // ParagraphButtons(),
                  // ],
                  toolbarType: ToolbarType.nativeGrid,
                  onButtonPressed: (ButtonType type, bool? status, Function? updateStatus) {
                    return true;
                  },
                  onDropdownChanged: (DropdownType type, dynamic changed, Function(dynamic)? updateSelectedItem) {
                    return true;
                  },
                ),
                otherOptions: const OtherOptions(height: 550),
                callbacks: Callbacks(
                  // onBeforeCommand: (String? currentHtml) {
                  //   debugPrint('html before change is $currentHtml');
                  //   swtCtrl.prevText.value = currentHtml ?? '';
                  // },
                  onChangeContent: (p0) {
                    debugPrint('change content>>>$p0');

                    swtCtrl.currText.value = p0 ?? '';
                    // controller.clearFocus();
                  },
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TaButton(
                  type: 'elevate',
                  buttonText: 'Back',
                  onPressed: () {
                    //debugPrint('prev text>>>>${swtCtrl.prevText.value}');
                    controller.setText(swtCtrl.prevText.value);
                    Get.back();
                  },
                ),
                const SizedBox(
                  width: 4.0,
                ),
                TaButton(
                  type: 'elevate',
                  buttonText: 'Reset',
                  onPressed: () {
                    //debugPrint('prev text>>>>${swtCtrl.prevText.value}');
                    controller.setText(swtCtrl.prevText.value);
                  },
                ),
                const SizedBox(
                  width: 4.0,
                ),
                TaButton(
                  type: 'elevate',
                  buttonText: 'Update',
                  onPressed: () {
                    //  debugPrint('curr text>>>>${swtCtrl.currText.value}');
                    swtCtrl.siterec.value.dealSummary = swtCtrl.currText.value;
                    Get.back();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
