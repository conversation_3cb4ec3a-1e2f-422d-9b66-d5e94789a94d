import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../models/project/budget/projectbaselineverions.dart';
import '../../../providers/common/budget/projbaseline_controller.dart';
import '../../../utils/device_util.dart';

class ProjBaselineVerions extends StatelessWidget {
  final entityid;
  ProjBaselineVerions({super.key, this.entityid});
  ProjectBaselineVersionsController pbvCtrlr = Get.find<ProjectBaselineVersionsController>();
  Future _baselineversions() async {
    await pbvCtrlr.loadProjBaselineVersionsData(entityid: entityid);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      Positioned.fill(
        //top: 45,
        child: RefreshIndicator(
          onRefresh: _baselineversions,
          child: GetX<ProjectBaselineVersionsController>(
            initState: (state) async {
              pbvCtrlr.loadProjBaselineVersionsData(
                entityid: entityid,
              );
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.baselineverdatadata.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.baselineverdatadata[index]);
                          },
                          itemCount: _.baselineverdatadata.length,
                        );
            },
          ),
        ),
      ),
    ]);
  }

  Widget listitem(BuildContext context, ProjBaselineVersions bv) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      // onTap: () {
      //   mmCtrl.meetingminute.value = mm;
      //   mmCtrl.mm_mode.value = 'edit';
      //   Get.to(() => MeetingMinuteDetails(
      //         entityid: entityid,
      //         entitytype: entitytype,
      //         projectview: projectview,
      //         mode: mode,
      //       ));
      // },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,

                  //crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bv.baselineId.toString(),
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Flexible(
                      child: Text(
                        bv.createdBy ?? '',
                        style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ]),
              const SizedBox(
                height: 5,
              ),
              Row(
                children: [
                  Icon(
                    Icons.calendar_month,
                    color: secondary,
                    size: 15,
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  Text(
                    bv.creationDate != null ? 'Creation Date ${bv.creationDate}' '' : '',
                    style: TextStyle(color: primary, fontSize: 10),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              const SizedBox(
                height: 5.0,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(children: [
                    Icon(
                      Icons.schedule,
                      color: secondary,
                      size: 15,
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    Text(
                      bv.approvedBudgetAmount?.toString() ?? '0',
                      style: TextStyle(color: primary, fontSize: 10),
                    ),
                  ]),
                  Text(
                    bv.totalAprvdBudgetAmt?.toString() ?? '0',
                    style: TextStyle(color: primary, fontSize: 10),
                  ),
                ],
              ),
              const SizedBox(
                height: 5,
              ),
              // Row(
              //   children: [
              //     TaButton(
              //       buttonText: 'History',
              //       onPressed: () {
              //         debugPrint('----------------');
              //       },
              //     ),
              //   ],
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
