import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputdate.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtime.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/photos/photospg.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';
import '../../../providers/common/meetingminute/meetingminutes_controller.dart';
import '../documents/documentspg.dart';
import '../photos/photosgrid.dart';
import 'mm_actionitems.dart';
import 'mm_attendees.dart';
import 'mm_followups.dart';

class MeetingMinuteDetails extends StatelessWidget {
  final entityid;
  final entitytype;
  final ProjectsView? projectview;
  String? mode;

  MeetingMinuteDetails({super.key, this.entityid, this.entitytype, this.projectview, this.mode});
  LabelController talabel = Get.find<LabelController>();
  MeetingMinutesController mmCtrl = Get.find<MeetingMinutesController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GetX<LabelController>(
      initState: (state) {
        mmCtrl.labelsloading.value = true;
        mmCtrl.isdetailsloading.value = true;

        Future.delayed(const Duration(seconds: 1), () async {
          await mmCtrl.getlabels(talabel);
          await mmCtrl.setCurrentRow();
          mmCtrl.labelsloading.value = false;
          mmCtrl.isdetailsloading.value = false;
        });
      },
      builder: (_) {
        return (mmCtrl.labelsloading.value || mmCtrl.isdetailsloading.value)
            ? ComponentUtils.labelLoadScaffold()
            : Scaffold(
                appBar: AppBar(
                  iconTheme: Theme.of(context).appBarTheme.iconTheme,
                  title: Text(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS')?.value ?? '',
                      style: ComponentUtils.appbartitlestyle),
                  backgroundColor: Colors.white,
                  elevation: 5,
                  leading: IconButton(
                    icon: ComponentUtils.backpageIcon,
                    color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                    onPressed: () {
                      debugPrint('------back-------');
                      Get.back();
                      mmCtrl.loadMeetingMinutesData(entityid: entityid, entitytype: entitytype);
                    },
                  ),
                ),
                body: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: mmDetForm(),
                ),
                bottomNavigationBar: bottombuttonbar(_formKey),
              );
      },
    );
  }

  Widget mmDetForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Obx(
        () => Column(
          children: <Widget>[
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGID') != null &&
                mmCtrl.mm_mode.value != 'create')
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGID')!.value,
                readOnly: true, // talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_STORENUMBER').ro,
                value: mmCtrl.meetingminute.value.meetingId?.toString() ?? '',
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_PROJNAME') != null && entitytype == 'PROJECT')
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_PROJNAME')!.value,
                readOnly: true,
                value: projectview?.projectName ?? '',
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_ORGANIZER') != null &&
                mmCtrl.mm_mode.value != 'create')
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_ORGANIZER')!.value,
                readOnly: true,
                value: mmCtrl.meetingminute.value.organizer?.toString(),
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGTITLE') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGTITLE')!.value,
                readOnly:
                    mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGTITLE')!.ro : true,
                value: mmCtrl.meetingminute.value.meetingTitle ?? '',
                maxLines: 3,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.meetingTitle = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.meetingTitle = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGTYPE') != null)
              TaFormDropdown(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGTYPE')!.value,
                emptttext: 'Select',
                onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGTYPE')!.ro!)
                    ? null
                    : (newValue) {
                        debugPrint('newValue          ' + newValue);
                        mmCtrl.meetingminute.value.meetingType = newValue;
                      },
                value:
                    (mmCtrl.meetingminute.value.meetingType == '' || mmCtrl.meetingminute.value.meetingType == 'null')
                        ? null
                        : mmCtrl.meetingminute.value.meetingType,
                listflag: (mmCtrl.meetingTypelov.value.isNotEmpty),
                items: mmCtrl.meetingTypelov.value.map((LookupValues l) {
                  return DropdownMenuItem(
                    value: l.lookupCode,
                    child: new Text(
                      l.lookupValue!,
                      style: TextStyle(fontSize: 14, color: Colors.black),
                    ),
                  );
                }).toList(),
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGDATE') != null)
              TaFormInputDate(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGDATE')!.value,
                controller: mmCtrl.meetingDateCtrl,
                onChanged: (dateval) {
                  debugPrint('meetingDate--------- $dateval');
                  mmCtrl.meetingDateCtrl.text = dateval.toString();
                  mmCtrl.meetingminute.value.meetingDate = dateval.toString();
                },
                readOnly:
                    mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_MEETINGDATE')!.ro : true,
                onSaved: (dateval) {
                  mmCtrl.meetingDateCtrl.text = dateval.toString();
                  mmCtrl.meetingminute.value.meetingDate = dateval.toString();
                },
                validate: (value) {
                  if (value == null) {
                    return 'This attribute is Required';
                  }
                  return null;
                },
              ),
            // Row(

            // children: <Widget>[
            // Expanded(
            //   child:
            TaFormInputTime(
              label: (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_STARTTIME') != null)
                  ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_STARTTIME')!.value
                  : 'Start Time',
              controller: mmCtrl.fromTimeCtrl,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_STARTTIME')!.ro : true,
              onChanged: (val) {
                debugPrint('from time--------- $val');
                mmCtrl.fromTimeCtrl.text = val ?? '';
                mmCtrl.meetingminute.value.startTime = val ?? '';
              },
              onSaved: (val) {
                mmCtrl.fromTimeCtrl.text = val ?? '';
                mmCtrl.meetingminute.value.startTime = val ?? '';
              },
              validate: (value) {
                if (value == null || value == '') {
                  return 'This attribute is Required';
                }
                return null;
              },
            ),
            // ),
            // Expanded(
            //   child:
            TaFormInputTime(
              label: (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_ENDTIME') != null)
                  ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_ENDTIME')!.value
                  : 'End Time',
              controller: mmCtrl.toTimeCtrl,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_ENDTIME')!.ro : true,
              onChanged: (val) {
                debugPrint('To time--------- $val');
                mmCtrl.toTimeCtrl.text = val ?? '';
                mmCtrl.meetingminute.value.endTime = val ?? '';
              },
              onSaved: (val) {
                mmCtrl.toTimeCtrl.text = val ?? '';
                mmCtrl.meetingminute.value.endTime = val ?? '';
              },
              validate: (value) {
                if (value == null || value == '') {
                  return 'This attribute is Required';
                } else if (value.isNotEmpty && mmCtrl.fromTimeCtrl.text.isNotEmpty) {
                  DateTime sdt = DateFormat("hh:mma").parse(mmCtrl.fromTimeCtrl.text.replaceAll(' ', ''));
                  DateTime edt = DateFormat("hh:mma").parse(value.replaceAll(' ', ''));
                  //String stime = mmCtrl.fromTimeCtrl?.text.replaceAll(' ', '');
                  //String etime = value.replaceAll(' ', '');
                  if (sdt.compareTo(edt) > 0) {
                    var etime = (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_ENDTIME') != null)
                        ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_ENDTIME')!.value!
                        : ' From Time';
                    var stime = (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_STARTTIME') != null)
                        ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_STARTTIME')!.value!
                        : ' Start Time';
                    return '$etime should be after $stime .';
                  }
                }
                return null;
              },
            ),
            // ),
            //   ],
            // ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_OBJECTIVE') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_OBJECTIVE')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_OBJECTIVE')!.ro : true,
                value: mmCtrl.meetingminute.value.objective ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.objective = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.objective = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_AGENDA') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_AGENDA')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_AGENDA')!.ro : true,
                value: mmCtrl.meetingminute.value.agenda ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.agenda = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.agenda = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NOTES') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NOTES')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NOTES')!.ro : true,
                value: mmCtrl.meetingminute.value.notes ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.notes = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.notes = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ATTENDEES') != null)
              if (mmCtrl.mm_mode.value != 'create')
                attendees(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ATTENDEES')!.value!),

            //attribute section--------------------------------
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_DISTRIBUTIONDATE') != null)
              TaFormInputDate(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_DISTRIBUTIONDATE')!.value,
                controller: mmCtrl.distributionDateCtrl,
                onChanged: (dateval) {
                  debugPrint('distribution date--------- $dateval');
                  mmCtrl.distributionDateCtrl.text = dateval.toString();
                  mmCtrl.meetingminute.value.distributionDate = dateval?.toString();
                },
                readOnly:
                    mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_DISTRIBUTIONDATE')!.ro : true,
                onSaved: (dateval) {
                  mmCtrl.distributionDateCtrl.text = dateval.toString();
                  mmCtrl.meetingminute.value.distributionDate = dateval?.toString();
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_LOCATION') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_LOCATION')!.value,
                readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_LOCATION')!.ro : true,
                value: mmCtrl.meetingminute.value.location ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.location = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.location = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_WEBEXCALLIN') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_WEBEXCALLIN')!.value,
                readOnly:
                    mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_WEBEXCALLIN')!.ro : true,
                value: mmCtrl.meetingminute.value.webexCallIn ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.webexCallIn = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.webexCallIn = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NEXTLOCATION') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NEXTLOCATION')!.value,
                readOnly:
                    mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NEXTLOCATION')!.ro : true,
                value: mmCtrl.meetingminute.value.nextLocation ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.nextLocation = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.nextLocation = val;
                },
              ),
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NEXTWEBEXCALLIN') != null)
              TaFormInputText(
                label: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NEXTWEBEXCALLIN')!.value,
                readOnly:
                    mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_NEXTWEBEXCALLIN')!.ro : true,
                value: mmCtrl.meetingminute.value.nextWebexCallIn ?? '',
                maxLines: 5,
                minLines: 1,
                keyboard: TextInputType.multiline,
                textInputAct: TextInputAction.newline,
                onChanged: (val) {
                  mmCtrl.meetingminute.value.nextWebexCallIn = val;
                },
                onSaved: (val) {
                  mmCtrl.meetingminute.value.nextWebexCallIn = val;
                },
              ),
            //------------------------------------------------------------
            //Follow up
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_FOLLOWUP') != null && mmCtrl.mm_mode.value != 'create')
              followups(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_FOLLOWUP')!.value!),
            //Action Items
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS') != null && mmCtrl.mm_mode.value != 'create')
              actionitems(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ACTIONITEMS')!.value!),
            //Photos
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_PHOTOS') != null && mmCtrl.mm_mode.value != 'create')
              photos(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_PHOTOS')!.value!),
            //Attachments
            if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ATTACHMENTS') != null &&
                mmCtrl.mm_mode.value != 'create' &&
                mmCtrl.meetingminute.value.docFolderId != null)
              documents(talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ATTACHMENTS')!.value!),
          ],
        ),
      ),
    );
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      color: Colors.white,
      child: Obx(
        () => Container(
          margin: EdgeInsets.only(left: 12.0, right: 12.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
                onPressed: () {
                  Get.back();
                  mmCtrl.loadMeetingMinutesData(entityid: entityid, entitytype: entitytype);
                },
              ),
              if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_SENDTOATTENDEES') != null &&
                  mmCtrl.mm_mode.value != 'create' &&
                  mode == 'edit')
                SizedBox(
                  height: 5.0,
                  width: 5.0,
                ),
              if (talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_SENDTOATTENDEES') != null &&
                  mmCtrl.mm_mode.value != 'create' &&
                  mode == 'edit')
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_DETAILS_SENDTOATTENDEES')?.value ?? 'Save',
                  onPressed: () async {
                    final isValid = fkey.currentState!.validate();
                    if (!isValid) {
                      return;
                    }

                    _formKey.currentState!.save();
                    debugPrint('------------------saved--------------------');
                    await mmCtrl.onSaveMeetingMinute(action: 'sendemail', projectname: projectview?.projectName);
                  },
                ),
              if (mode == 'edit')
                SizedBox(
                  height: 5.0,
                  width: 5.0,
                ),
              if (mode == 'edit')
                TaButton(
                  type: 'elevate',
                  buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                  onPressed: () async {
                    final isValid = fkey.currentState!.validate();
                    if (!isValid) {
                      return;
                    }

                    _formKey.currentState!.save();
                    debugPrint('------------------saved--------------------');
                    await mmCtrl.onSaveMeetingMinute();
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget attendees(String str) {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(str),
        IconButton(
            onPressed: () {
              Get.to(() => MeetingMinuteAttendees(
                    hdrText: str,
                    contactsText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ATTENDEES_CONTACTS')?.value,
                    sysUserText: talabel.get('TMCMOBILE_COMMON_MEETINGMINUTES_ATTENDEES_SUPPLIERSITECONTACTS')?.value,
                    entityid: entityid,
                    entitytype: entitytype,
                    meetingid: mmCtrl.meetingminute.value.meetingId,
                    mode: mode,
                  ));
            },
            icon: Icon(
              Icons.groups,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget attributes(String str) {
    //var txt = talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET')?.value ?? 'Details';
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(str),
        IconButton(
            onPressed: () {
              // Get.to(() => StatusReportDetailsdata(
              //       appbartitle: txt,
              //       projectid: statusrepoCtrl.statusreport.value.projectId,
              //       statusid: statusrepoCtrl.statusreport.value.statusId,
              //     ));
            },
            icon: Icon(
              Icons.interests,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget followups(String str) {
    //var txt = talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET')?.value ?? 'Details';
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(str),
        IconButton(
            onPressed: () {
              Get.to(() => MeetingMinuteFollowups(
                    hdrText: str ?? 'Follow ups',
                    meetingid: mmCtrl.meetingminute.value.meetingId,
                    mode: mode,
                  ));
            },
            icon: Icon(
              Icons.running_with_errors,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget actionitems(String str) {
    //var txt = talabel.get('TMCMOBILE_PROJECTS_STATUSREPORTS_DETAILS_DET')?.value ?? 'Details';
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(str),
        IconButton(
            onPressed: () {
              Get.to(() => MeetingMinuteActionItems(
                    hdrText: str,
                    entityId: mmCtrl.meetingminute.value.meetingId,
                    entityType: 'MEETINGMINUTE',
                    parentEntityId: entityid,
                    parentEntityType: entitytype,
                    mode: mode,
                  ));
            },
            icon: Icon(
              Icons.ads_click,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget photos(String labelStr) {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => PhotosPG(
                    entityId: mmCtrl.meetingminute.value.meetingId,
                    entityType: 'MEETINGMIN',
                    mode: mode,
                  ));
            },
            icon: Icon(
              Icons.photo_library,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }

  Widget documents(String str) {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(str),
        IconButton(
            onPressed: () {
              Get.to(() => DocumentsPG(
                    rootfolderid: mmCtrl.meetingminute.value.docFolderId,
                    entityType: 'Meetingmin',
                    entityId: mmCtrl.meetingminute.value.meetingId,
                    hdrText: str,
                    mode: mode,
                  ));
            },
            icon: Icon(
              Icons.file_present,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }
}
