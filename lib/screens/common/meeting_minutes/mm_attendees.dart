import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tacheckbox.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/meeting_minutes/meeting_attendee.dart';
import 'package:tangoworkplace/models/common/utils/adhoctasknotifuser.dart';
import 'package:tangoworkplace/models/common/utils/contact.dart';
import 'package:tangoworkplace/models/common/utils/suppliersitecontact.dart';
import 'package:tangoworkplace/models/common/utils/systemuser.dart';
import 'package:tangoworkplace/providers/common/utils/usernotifications_controller.dart';
import 'package:tangoworkplace/providers/common/utils/docattachments_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/utils/usersdata/systemuserdata.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../providers/common/meetingminute/meetingminutes_controller.dart';

class MeetingMinuteAttendees extends StatelessWidget {
  final meetingid;
  final entityid;
  final entitytype;
  final hdrText;
  final sysUserText;
  final contactsText;
  String? mode;
  MeetingMinuteAttendees(
      {super.key,
      this.meetingid,
      this.mode,
      this.entityid,
      this.entitytype,
      this.hdrText,
      this.sysUserText,
      this.contactsText});

  MeetingMinutesController mmCtrl = Get.find<MeetingMinutesController>();
  LabelController talabel = Get.find<LabelController>();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: mode == 'edit'
          ? bottombuttonbar()
          : Container(
              height: 1,
            ),
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrText ?? 'Attendees', style: ComponentUtils.appbartitlestyle),
        actions: [
          if (mode == 'edit')
            TextButton(
              onPressed: () async {
                await mmCtrl.saveAttendees(meetingid);
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                    style: ComponentUtils.appbartitlestyle,
                  ),
                ],
              ),
            ),
        ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: Stack(
        children: <Widget>[
          Positioned.fill(
            //child: RefreshIndicator(
            // onRefresh: _refreshComments,
            child: GetX<MeetingMinutesController>(
              initState: (state) {
                mmCtrl.loadMeetingAttendeesData(meetingid: meetingid);
              },
              builder: (_) {
                return _.attendeesloading.isTrue
                    ? const ProgressIndicatorCust()
                    : _.meetingattendeesdata.isEmpty
                        ? Center(
                            child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              return listitem(context, _.meetingattendeesdata[index]);
                            },
                            itemCount: _.meetingattendeesdata.length,
                          );
              },
            ),
          ),
          // Positioned(
          //   bottom: 30,
          //   right: 30,
          //   child: FloatingActionButton(
          //     elevation: 5.0,
          //     child: const Icon(
          //       Icons.add,
          //       size: 30,
          //     ),
          //     onPressed: () {
          //       _openDialog();
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget bottombuttonbar() {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            if (sysUserText != null)
              TaButton(
                type: 'elevate',
                buttonText: sysUserText ?? 'Supplier Site Contacts',
                onPressed: () {
                  _openDialog('Supplier_Site_Contacts');
                },
              ),
            SizedBox(
              height: 5.0,
              width: 5.0,
            ),
            if (contactsText != null)
              TaButton(
                type: 'elevate',
                //buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                buttonText: contactsText ?? 'Contacts',
                onPressed: () {
                  _openDialog('contacts');
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget listitem(BuildContext context, MeetingAttendee ma) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          debugPrint('');
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          '${ma.name ?? ''} ${ma.lastName ?? ''}',
                          style: TextStyle(
                              color: primary,
                              //fontWeight: FontWeight.bold,
                              fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(children: [
                          Text(ma.role ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ]),
                        const SizedBox(
                          height: 2,
                        ),
                        Row(children: [
                          Icon(
                            Icons.email,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(ma.emailAddress ?? '',
                              style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                        ]),
                      ],
                    ),
                    if (mode == 'edit')
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          IconButton(
                              onPressed: () {
                                debugPrint('-----------------');
                                mmCtrl.meetingattendeesdata
                                    .removeWhere((element) => element.emailAddress == ma.emailAddress);
                              },
                              icon: Icon(
                                Icons.delete,
                                color: secondary,
                              )),
                        ],
                      ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  void _openDialog(String usertype) async {
    usertype == 'contacts' ? mmCtrl.fetchContacts(entityid, entitytype) : mmCtrl.fetchSupplierSiteContacts();
    await Navigator.of(Get.context!).push(MaterialPageRoute<String>(
        builder: (BuildContext context) {
          return Scaffold(
            appBar: AppBar(
              leading: IconButton(
                icon: const Icon(Icons.clear),
                color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                onPressed: () {
                  debugPrint('------back-------');
                  Get.back();
                },
              ),
              backgroundColor: Colors.white,
              elevation: 5,
              title: Text(
                'Users',
                style: ComponentUtils.appbartitlestyle,
              ),
              // actions: [
              //   TextButton(
              //     onPressed: () {
              //       Get.back();
              //     },
              //     child: Row(
              //       mainAxisSize: MainAxisSize.min,
              //       crossAxisAlignment: CrossAxisAlignment.center,
              //       children: [
              //         Text(
              //           'Add',
              //           style: ComponentUtils.appbartitlestyle,
              //         ),
              //       ],
              //     ),
              //   ),
              // ],
            ),
            body: usertype == 'contacts'
                ? ContactsData(
                    entityid: entityid,
                    entitytype: entitytype,
                    meetingid: meetingid,
                  )
                : SupplierSiteContacts(
                    meetingid: meetingid,
                  ),
          );
        },
        fullscreenDialog: true));
  }
}

class ContactsData extends StatelessWidget {
  final entitytype;
  final entityid;
  final meetingid;
  ContactsData({
    super.key,
    this.entitytype,
    this.entityid,
    this.meetingid,
  });
  MeetingMinutesController mmCtrl = Get.find<MeetingMinutesController>();

  @override
  Widget build(BuildContext context) {
    return GetX<MeetingMinutesController>(
      initState: (state) async {},
      builder: (_) {
        return _.isContactsLoading.isTrue
            ? const ProgressIndicatorCust()
            : _.contacts.isEmpty
                ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                // : Obx(
                //     () =>
                : ListView.builder(
                    itemBuilder: (context, index) {
                      return listitem(context, _.contacts[index], index);
                    },
                    itemCount: _.contacts.length,
                    //  ),
                  );
      },
    );
  }

  Widget listitem(BuildContext context, Contact c, int index) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return
        // GestureDetector(
        //   onTap: () {
        //     debugPrint('');
        //   },
        // child:
        Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      c.contactUserId ?? '',
                      style: TextStyle(
                          color: primary,
                          //fontWeight: FontWeight.bold,
                          fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Row(children: [
                      Text('${c.name} ${c.lastName}',
                          style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                    const SizedBox(
                      height: 2,
                    ),
                    Row(children: [
                      Icon(
                        Icons.email,
                        color: secondary,
                        size: 15,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Text(c.email ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    GestureDetector(
                        child: Obx(
                          () => mmCtrl.contacts.value[index].selectrow!
                              ? Icon(
                                  Icons.check,
                                  color: Theme.of(context).primaryColor,
                                )
                              : const Icon(
                                  Icons.check_box_outline_blank,
                                  color: Colors.grey,
                                ),
                        ),
                        onTap: () {
                          c = mmCtrl.contacts.value[index];
                          mmCtrl.contacts[index].selectrow = !c.selectrow!;
                          debugPrint('-----------${mmCtrl.contacts.value[index].lastName}');

                          mmCtrl.contacts[index] = c;
                          mmCtrl.meetingattendeesdata
                              .removeWhere((element) => element.emailAddress == mmCtrl.contacts.value[index].email);

                          if (mmCtrl.contacts[index].selectrow!) {
                            mmCtrl.meetingattendeesdata.value.add(MeetingAttendee(
                                attendanceFlag: 'TRUE',
                                meetingId: meetingid,
                                name: c.name,
                                role: c.role,
                                lastName: c.lastName,
                                emailAddress: c.email));
                          }
                        }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SupplierSiteContacts extends StatelessWidget {
  final meetingid;
  SupplierSiteContacts({super.key, this.meetingid});
  MeetingMinutesController mmCtrl = Get.find<MeetingMinutesController>();
  @override
  Widget build(BuildContext context) {
    return GetX<MeetingMinutesController>(
      initState: (state) {},
      builder: (_) {
        return _.isSupplierSiteContactLoading.isTrue
            ? const ProgressIndicatorCust()
            : _.suppliersitecontacts.isEmpty
                ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                : ListView.builder(
                    itemBuilder: (context, index) {
                      return listitem(context, _.suppliersitecontacts[index], index);
                    },
                    itemCount: _.suppliersitecontacts.length,
                    //  ),
                  );
      },
    );
  }

  Widget listitem(BuildContext context, SupplierSiteContact c, int index) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      c.contactUserId ?? '',
                      style: TextStyle(
                          color: primary,
                          //fontWeight: FontWeight.bold,
                          fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Row(children: [
                      Text('${c.name ?? ''} ${c.lastName ?? ''}',
                          style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                    const SizedBox(
                      height: 2,
                    ),
                    Row(children: [
                      Icon(
                        Icons.email,
                        color: secondary,
                        size: 15,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      // Text(c.emailAddress ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    GestureDetector(
                        child: Obx(
                          () => mmCtrl.suppliersitecontacts.value[index].selectrow!
                              ? Icon(
                                  Icons.check,
                                  color: Theme.of(context).primaryColor,
                                )
                              : const Icon(
                                  Icons.check_box_outline_blank,
                                  color: Colors.grey,
                                ),
                        ),
                        onTap: () {
                          c = mmCtrl.suppliersitecontacts.value[index];
                          mmCtrl.suppliersitecontacts[index].selectrow = !c.selectrow!;
                          debugPrint('-----------${mmCtrl.suppliersitecontacts.value[index].lastName}');

                          mmCtrl.suppliersitecontacts[index] = c;
                          mmCtrl.meetingattendeesdata.removeWhere(
                              (element) => element.emailAddress == mmCtrl.suppliersitecontacts.value[index].email);

                          if (mmCtrl.suppliersitecontacts[index].selectrow!) {
                            mmCtrl.meetingattendeesdata.value.add(MeetingAttendee(
                                attendanceFlag: 'TRUE',
                                meetingId: meetingid,
                                name: c.name,
                                role: c.role,
                                lastName: c.lastName,
                                emailAddress: c.email));
                          }
                        }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
