import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/photo.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';
import 'package:file_picker/file_picker.dart';

import '../../../common/component_utils.dart';

class Photos extends GetView<PhotosController> {
  final String? entityType;
  final int? entityId;
  final String? mode;
  final String? photoMode;

  Photos({super.key, this.entityType, this.entityId, this.photoMode = 'view', this.mode});

  Future _refreshPhotos() async {
    controller.templist.clear();
    controller.loadPhotos(entityType!, entityId);
  }

  final ImagePicker _picker = ImagePicker();
  Future<void> _openCamera() async {
    var picture = await _picker.pickImage(source: ImageSource.camera);
    if (picture != null) {
      ProgressUtil.showLoaderDialog(Get.context!);
      controller.uploadPhotos(picture.path, entityType!, entityId);
    }
  }

  void _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.image,
    );
    if (result != null) {
      List<File> files;
      files = result.paths.map((path) => File(path!)).toList();

      for (var element in files) {
        File f = element;
        debugPrint('file name    ${f.path}');
      }
      ProgressUtil.showLoaderDialog(Get.context!);
      controller.uploadmultiplePhotos(files, entityType!, entityId);
    } else {
      // User canceled the picker
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: RefreshIndicator(
          onRefresh: _refreshPhotos,
          child: GetX<PhotosController>(
            initState: (state) {
              controller.loadPhotos(entityType!, entityId);
            },
            builder: (_) {
              return _.isloading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.photos.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.photos[index]);
                          },
                          itemCount: _.photos.length,
                        );
            },
          ),
        ),
      ),
      if (photoMode != 'view' && mode != 'view')
        Positioned(
          bottom: 30,
          right: 30,
          child: SpeedDialFloatingButton(icon: Icons.expand_less, iconButton: [
            FloatingIconButton(
                icon: Icons.camera_alt,
                onPressed: () {
                  _openCamera();
                }),
            FloatingIconButton(
                icon: Icons.image,
                onPressed: () {
                  _pickFiles();
                }),
          ]),
        ),
    ]);
  }

  Widget listitem(BuildContext context, Photo p) {
    const primary = Color(0xff696b9e);
    const secondary = Color(0xfff29a94);

    return GestureDetector(
        // onTap: () {
        //   Get.to(
        //       () => ProjectDetails(
        //             proj: p,
        //           ),
        //       arguments: [
        //         {"projectId": p.projectId},
        //         {"porojectview": p}
        //       ]);
        // },
        child: Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      width: double.infinity,
      //height: 110,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  width: double.infinity,
                  //margin: EdgeInsets.only(right: 10),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    //border: Border.all(width: 1, color: Colors.grey[300]),
                  ),
                  //height: 250,
                  child: ComponentUtils.apiImage(p.picId, height: 200.0, width: 200.0),
                ),
                if (photoMode == 'view')
                  const SizedBox(
                    height: 10,
                  ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.fromLTRB(5.0, 0, 0, 0),
                        child: Text(p.picName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: primary,
                              fontSize: 12,
                              letterSpacing: .1,
                            )),
                      ),
                    ),
                    if (photoMode == 'edit' && mode != 'view')
                      IconButton(
                        onPressed: () {
                          debugPrint('---------delete--------');
                          controller.photos.removeWhere((element) => element.picId == p.picId);
                          controller.templist.value.add(p.picId);
                        },
                        icon: Icon(
                          Icons.delete,
                          color: secondary,
                        ),
                      ),
                  ],
                ),
                const SizedBox(
                  height: 5,
                ),
                Row(children: [
                  Icon(
                    Icons.person,
                    color: secondary,
                    size: 15,
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  Text(p.createdBy ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                ]),
                const SizedBox(
                  height: 2,
                ),
                Row(children: [
                  Icon(
                    Icons.date_range,
                    color: secondary,
                    size: 15,
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  Text(p.creationDate ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                ]),
              ],
            ),
          )
        ],
      ),
    ));
  }
}
