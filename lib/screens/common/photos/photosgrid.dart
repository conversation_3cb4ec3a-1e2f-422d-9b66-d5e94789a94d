import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/models/common/photo.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../providers/common/photosgrid_controller.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';
import '../../../utils/preferences_utils.dart';

class PhotosGrid extends GetView<PhotosGridController> {
  final String? entityType;
  final int? entityId;
  final String? mode;
  final ImagePicker _picker = ImagePicker();

  final LabelController talabel = Get.find<LabelController>();

  PhotosGrid({super.key, this.entityId, this.entityType, this.mode});

  @override
  Widget build(BuildContext context) {
    controller.loadPhotos(entityType: entityType ?? '', entityId: entityId);

    return Stack(children: <Widget>[
      Positioned.fill(
        top: 50,
        child: Obx(
          () => controller.isLoading.value
              ? const ProgressIndicatorCust()
              : Container(margin: const EdgeInsets.all(8.0), child: controller.isGrid.value ? _buildGrid(context) : _buildList(context)),
        ),
      ),
      Positioned(
        top: 10,
        right: 10,
        child: Obx(
          () => IconButton(
            icon: Icon(
              controller.isGrid.value ? Icons.format_list_bulleted : Icons.grid_view,
            ),
            color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
            onPressed: () {
              controller.switchUI();
            },
          ),
        ),
      ),
      Positioned(
        top: 10,
        left: 20,
        child: Obx(
          () => controller.templist.isNotEmpty
              ? TaButton(
                  type: 'elevate',
                  buttonText: 'Save',
                  onPressed: () async {
                    await controller.deletePhotos();
                  })
              : Container(),
        ),
      ),
      if (mode == 'edit')
        // Obx(
        //  () =>
        Positioned(
          bottom: 30,
          right: 30,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SpeedDialFloatingButton(icon: Icons.expand_less, iconButton: [
                FloatingIconButton(
                    icon: Icons.camera_alt,
                    onPressed: () {
                      openCamera(context);
                    }),
                FloatingIconButton(
                    icon: Icons.image,
                    onPressed: () {
                      openGallery(context);
                    }),
              ]),
              //const SizedBox(
              //   height: 16.0,
              // ),
              // if (mode == 'edit' && mode != 'view' && controller.templist.isNotEmpty)
              //  FloatingActionButton(child: const Icon(Icons.save), onPressed: () => controller.deletePhotos()),
            ],
          ),
        ),
      // ),
    ]);
  }

  Widget _buildList(BuildContext context) {
    return ListView.builder(
      itemBuilder: (context, index) {
        return listItem(context, controller.photos[index]);
      },
      itemCount: controller.photos.length,
    );
  }

  Widget _buildGrid(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: StaggeredGrid.count(
        crossAxisCount: 3,
        mainAxisSpacing: 4,
        crossAxisSpacing: 4,
        children: controller.photos
            .mapIndexed(
              (index, photo) => StaggeredGridTile.count(
                crossAxisCellCount: (controller.indexesBig.contains(index)) ? 2 : 1,
                mainAxisCellCount: (controller.indexesBig.contains(index)) ? 2 : 1,
                child: GestureDetector(
                    onTap: () {
                      showPhotoEditDialog(context, photoToEdit: photo);
                    },
                    child: imageGridCell(photo.picId)),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget imageGridCell(var picId) {
    const secondary = Color(0xfff29a94);
    try {
      return CachedNetworkImage(
        imageUrl: controller.getPicUrl(picId),
        httpHeaders: SharedPrefUtils.getHeaders(),
        imageBuilder: (context, imageProvider) => Stack(children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.elliptical(15, 20)),
              color: Colors.white,
              border: Border.all(color: Colors.grey, width: 0.1, style: BorderStyle.solid),
              image: DecorationImage(
                fit: BoxFit.cover,
                image: imageProvider,
              ),
            ),
          ),
          if (mode == 'edit' && mode != 'view')
            Positioned(
              top: 4.0,
              right: 4.0,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.elliptical(10, 10)),
                  color: Colors.white.withAlpha(120),
                  border: Border.all(color: Colors.grey, width: 0.1, style: BorderStyle.solid),
                ),
                child: IconButton(
                  onPressed: () {
                    controller.markToDelete(picId);
                  },
                  icon: const Icon(
                    Icons.delete,
                    color: secondary,
                  ),
                ),
              ),
            )
        ]),
        placeholder: (context, url) => placeholderPhoto(),
        errorWidget: (context, url, error) => errorPhoto(),
      );
    } catch (e) {
      debugPrint(e.toString());
      return const AssetImage('lib/icons/no_image_icon.gif') as Widget;
    }
  }

  Widget listItem(BuildContext context, Photo p) {
    const primary = Color(0xff696b9e);
    const secondary = Color(0xfff29a94);
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: GestureDetector(
          onTap: () {
            showPhotoEditDialog(context, photoToEdit: p);
          },
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(15)),
            child: Container(
              color: Colors.white,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(
                          width: double.infinity,
                          child: CachedNetworkImage(
                            imageUrl: controller.getPicUrl(p.picId),
                            httpHeaders: SharedPrefUtils.getHeaders(),
                            height: 200,
                            errorWidget: (context, url, error) => errorPhoto(),
                            placeholder: (context, url) => placeholderPhoto(),
                            fit: BoxFit.cover,
                          ),
                        ),
                        if (mode == 'view')
                          const SizedBox(
                            height: 10,
                          ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Expanded(
                              child: Container(
                                margin: const EdgeInsets.only(left: 15.0, right: 15.0),
                                child: Text(p.picName ?? '',
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      color: primary,
                                      fontSize: 12,
                                      letterSpacing: .1,
                                    )),
                              ),
                            ),
                            if (mode == 'edit')
                              IconButton(
                                onPressed: () {
                                  controller.markToDelete(p.picId);
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: secondary,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Container(
                          margin: const EdgeInsets.only(left: 15.0, right: 15.0),
                          child: Row(children: [
                            const Icon(
                              Icons.person,
                              color: secondary,
                              size: 15,
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            Text(p.createdBy ?? '', style: const TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                          ]),
                        ),
                        const SizedBox(
                          height: 2,
                        ),
                        Container(
                          margin: const EdgeInsets.only(left: 15.0, right: 15.0, bottom: 8.0),
                          child: Row(children: [
                            const Icon(
                              Icons.date_range,
                              color: secondary,
                              size: 15,
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            Text(p.creationDate ?? '', style: const TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                          ]),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          )),
    );
  }

  Widget errorPhoto() {
    return const Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            color: Colors.white,
          ),
          Text(
            'Image not found',
            style: TextStyle(color: Colors.white, fontSize: 20),
          )
        ],
      ),
    );
  }

  Widget placeholderPhoto() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.elliptical(15, 20)),
        color: Colors.white,
        border: Border.all(color: Colors.grey, width: 0.1, style: BorderStyle.solid),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  void openCamera(BuildContext context) {
    _picker.pickImage(source: ImageSource.camera).then((picture) {
      if (picture != null) {
        showPhotoEditDialog(
          context,
          photos: [picture],
        );
      }
    });
  }

  void openGallery(BuildContext context) {
    _picker.pickMultiImage().then((pictures) {
      if (pictures.isNotEmpty) {
        showPhotoEditDialog(
          context,
          photos: pictures,
        );
      }
    });
  }

  Widget _photoCarousel(BuildContext contextPage, List<String> paths) {
    if (paths.length == 1) {
      return Image.file(
        File(paths.first),
        fit: BoxFit.cover,
        width: MediaQuery.of(contextPage).size.width,
      );
    } else {
      return CarouselSlider(
          options: CarouselOptions(
              height: (MediaQuery.of(contextPage).size.height / 5) * 3,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              pauseAutoPlayOnTouch: true),
          items: paths.map((path) {
            return Builder(builder: (BuildContext context) {
              return Image.file(
                File(path),
                fit: BoxFit.cover,
                width: MediaQuery.of(context).size.width,
              );
            });
          }).toList());
    }
  }

  Future<void> showPhotoEditDialog(BuildContext contextParent, {List<XFile>? photos, Photo? photoToEdit}) async {
    controller.openDetails(photoToEdit: photoToEdit);

    showModalBottomSheet(
      context: contextParent,
      isScrollControlled: true,
      barrierColor: Colors.black.withAlpha(220),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30.0)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                LimitedBox(
                  maxHeight: ((MediaQuery.of(context).size.height / 5) * 3) - MediaQuery.of(context).padding.bottom,
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    child: photos != null
                        ? _photoCarousel(context, photos.map((e) => e.path).toList())
                        : CachedNetworkImage(
                            imageUrl: controller.getPicUrl(photoToEdit?.picId),
                            httpHeaders: SharedPrefUtils.getHeaders(),
                            placeholder: (context, url) => placeholderPhoto(),
                            errorWidget: (context, url, error) => errorPhoto(),
                            width: MediaQuery.of(context).size.width,
                            fit: BoxFit.cover,
                          ),
                  ),
                ),
                if (photos == null || photos.length == 1)
                  TaInputText(
                    title: 'Name',
                    maxLines: 1,
                    controller: controller.namePhotoTextController,
                    onChanged: (value) => {},
                  ),
                TaInputTextField(
                  title: 'Description',
                  minLines: 3,
                  maxLines: 3,
                  controller: controller.descriptionPhotoTextController,
                  onChanged: (value) => {},
                ),
                if (photoToEdit != null)
                  TaInputText(
                    title: "Date",
                    controller: controller.dateController,
                    readOnly: true,
                  ),
                if (photoToEdit != null)
                  TaInputText(
                    title: "Created By",
                    controller: controller.createdByController,
                    readOnly: true,
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TaButton(
                      type: 'elevate',
                      buttonText: 'Cancel',
                      onPressed: () {
                        Get.back();
                      },
                    ),
                    const SizedBox(
                      width: 8.0,
                    ),
                    TaButton(
                      type: 'elevate',
                      buttonText: 'Save',
                      onPressed: () async {
                        if (photos != null) {
                          await controller.uploadPhotos(
                            photosPath: photos.map((e) => e.path).toList(),
                            entityId: entityId,
                            entityType: entityType ?? '',
                          );
                        } else if (photoToEdit != null) {
                          await controller.updatePhoto(
                            picId: photoToEdit.picId ?? 0,
                            entityId: entityId,
                            entityType: entityType ?? '',
                          );
                        }
                        Get.back();
                      },
                    ),
                    const SizedBox(
                      width: 10.0,
                    ),
                  ],
                ),
                SizedBox(
                  height: MediaQuery.of(context).padding.bottom,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
