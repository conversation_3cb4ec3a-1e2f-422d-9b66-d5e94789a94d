import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/entitycomments_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/contacts/contacts_data_details.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../models/common/contacts/commoncontactsdata.dart';
import '../../../models/common/suppliers/ProjectSuppliersData.dart';
import '../../../providers/common/commoncontacts_controller.dart';
import '../../../providers/common/projectsuppliers_controller.dart';

class ProjectSuppliers extends GetView<EntityCommentsController> {
  final String? entityType;
  final int? entityId;
  String? mode;
  ProjectSuppliers({super.key, this.entityType, this.entityId, this.mode});

  LabelController talabel = Get.find<LabelController>();
  ProjectSuppliersController cnctCntrl = Get.find<ProjectSuppliersController>();
  final DateFormat formatter = DateFormat('MM-dd-yyyy');
  Future _refreshComments() async {
    cnctCntrl.getProjectSuppliersData(entityid: entityId);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: RefreshIndicator(
          onRefresh: _refreshComments,
          child: GetX<ProjectSuppliersController>(
            initState: (state) {
              Future.delayed(const Duration(milliseconds: 100), () async {
                await cnctCntrl.getProjectSuppliersData(entityid: entityId);
              });
            },
            builder: (_) {
              return _.isLoading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.suppliers.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.suppliers[index]);
                          },
                          itemCount: _.suppliers.length,
                        );
            },
          ),
        ),
      ),
    ]);
  }

  Widget listitem(BuildContext context, ProjectSuppliersData s) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
        onTap: () {
          debugPrint('-----------');
          Get.to(ContactDataDetails(
            projsupplier: s,
            source: 'supplier',
          ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${s.supplierName}',
                      style: TextStyle(
                          color: primary,
                          //fontWeight: FontWeight.bold,
                          fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(children: [
                      Icon(
                        Icons.badge,
                        color: secondary,
                        size: 15,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Text(s.trades ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                    const SizedBox(
                      height: 2,
                    ),
                    Row(children: [
                      Icon(
                        Icons.email,
                        color: secondary,
                        size: 15,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Text(s.email ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    ]),
                    // SizedBox(
                    //   height: 2,
                    // ),
                    // Row(children: [
                    //   Icon(
                    //     Icons.badge,
                    //     color: secondary,
                    //     size: 15,
                    //   ),
                    //   SizedBox(
                    //     width: 5,
                    //   ),
                    //   Text(s.supplierNumber1 ?? '', style: TextStyle(color: primary, fontSize: 10, letterSpacing: .1)),
                    // ]),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
