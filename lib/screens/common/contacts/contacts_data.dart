import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/entitycomments_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import '../../../common/component_utils.dart';
import '../../../providers/common/projectsuppliers_controller.dart';
import '../suppliers/projectsuppliers.dart';
import 'common_contacts.dart';

class ContactsData extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  String? mode;
  ContactsData({super.key, this.entityType, this.entityId, this.mode});

  LabelController talabel = Get.find<LabelController>();
  final GlobalKey<State> _tabKey = GlobalKey<State>();
  ProjectSuppliersController projectSuppliersController = Get.put(ProjectSuppliersController());
  final DateFormat formatter = DateFormat('MM-dd-yyyy');

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      Positioned.fill(
        child: DefaultTabController(
          initialIndex: 0,
          key: _tabKey,
          length: 2,
          child: Column(
            children: <Widget>[
              Material(
                elevation: 5,
                color: Colors.white,
                child: TabBar(
                  // isScrollable: true,
                  labelColor: ComponentUtils.tablabelcolor,
                  unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                  indicatorColor: ComponentUtils.tabindicatorColor,
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelStyle: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontStyle: FontStyle.normal,
                      fontSize: 14,
                      color: ComponentUtils.tablabelcolor),
                  tabs: const [Tab(text: 'Contacts'), Tab(text: 'Suppliers')],
                ),
              ),
              Expanded(
                child: TabBarView(
                  children: [
                    CommonContacts(entityId: entityId, entityType: entityType, mode: mode),
                    ProjectSuppliers(entityId: entityId, entityType: entityType, mode: mode),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ]);
  }
}
