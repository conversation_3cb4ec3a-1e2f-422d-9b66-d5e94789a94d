import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/contacts/commoncontactsdata.dart';
import 'package:tangoworkplace/models/common/suppliers/ProjectSuppliersData.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/component_utils.dart';

class ContactDataDetails extends StatelessWidget {
  CommonContactsData? contact;
  final ProjectSuppliersData? projsupplier;
  String? source;

  ContactDataDetails({super.key, this.contact, this.projsupplier, this.source});
  LabelController talabel = Get.find<LabelController>();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    //return
    //GetX<LabelController>(
    // initState: (state) {

    // },
    // builder: (_) {
    return
        //  (adhoctaskCntrlr.labelsloading.value || adhoctaskCntrlr.isdetailsloading.value)
        //     ? ComponentUtils.labelLoadScaffold()    :
        Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text('Details',
            // Text(talabel.get('TMCMOBILE_COMMON_ADHOCTASKS_DETAILS')?.value ?? '',
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.all(10.0),
                constraints: const BoxConstraints(maxWidth: 500),
                padding: const EdgeInsets.fromLTRB(10.0, 20.0, 10.0, 20.0),
                // height: 200,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.0),
                    boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                child: source == 'contact' ? contactsData() : supplierData(),
              ),
            ],
          ),
        ),
      ),
      //bottomNavigationBar: bottombuttonbar(_formKey),
      //  );
      //  },
    );
  }

  Widget contactsData() {
    CommonContactsData c = contact!;
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: [
            Icon(
              Icons.person,
              color: secondary,
              size: 20,
            ),
            const SizedBox(
              width: 8,
            ),
            Text(
              c.name ?? '',
              style: const TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              c.lastName ?? '',
              style: const TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Row(
          children: [
            Icon(
              Icons.work,
              color: secondary,
              size: 20,
            ),
            const SizedBox(
              width: 8,
            ),
            Text(
              c.roleVal ?? '',
              style: const TextStyle(
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Row(children: [
          Icon(
            Icons.apartment,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          Text(
            c.company ?? '',
            style: const TextStyle(
              fontSize: 14,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        if (c.isVendor != null &&
            (c.isVendor?.toUpperCase() == 'TRUE' ||
                c.isVendor?.toUpperCase() == 'YES' ||
                c.isVendor?.toUpperCase() == 'Y'))
          const SizedBox(
            height: 10,
          ),
        if (c.isVendor != null &&
            (c.isVendor?.toUpperCase() == 'TRUE' ||
                c.isVendor?.toUpperCase() == 'YES' ||
                c.isVendor?.toUpperCase() == 'Y'))
          Row(
            children: [
              Icon(
                Icons.engineering,
                color: secondary,
                size: 20,
              ),
              const SizedBox(
                width: 5,
              ),
              const Text(
                'Vendor',
                style: TextStyle(
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        const SizedBox(
          height: 10,
        ),
        Row(children: [
          Icon(
            Icons.call,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          const Text(
            'Cell Phone:',
            style: TextStyle(
              fontSize: 12,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        if (c.cellphone != null && c.cellphone != '')
          GestureDetector(
            onTap: () async {
              debugPrint('-----------');
              final Uri launchUri = Uri(
                scheme: 'tel',
                path: c.cellphone,
              );
              await url_launcher.launchUrl(launchUri);
            },
            child: Container(
              padding: const EdgeInsets.only(left: 27.0),
              child: Text(
                c.cellphone ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        const SizedBox(
          height: 10,
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Row(children: [
                  Icon(
                    Icons.call,
                    color: secondary,
                    size: 20,
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  const Text(
                    'Business Phone:',
                    style: TextStyle(
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ]),
                if (c.businessPhone != null && c.businessPhone != '')
                  GestureDetector(
                    onTap: () async {
                      debugPrint('-----------');
                      final Uri launchUri = Uri(
                        scheme: 'tel',
                        path: c.businessPhone,
                      );
                      await url_launcher.launchUrl(launchUri);
                    },
                    child: Container(
                      padding: const EdgeInsets.only(left: 27.0),
                      child: Text(
                        c.businessPhone ?? '',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(
              width: 100,
            ),
            Column(
              children: [
                const Text(
                  'Ext:',
                  style: TextStyle(
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  c.extension ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.blue,
                    decoration: TextDecoration.underline,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Row(children: [
          Icon(
            Icons.mail,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          const Text(
            'Email:',
            style: TextStyle(
              fontSize: 12,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        if (c.email != null && c.email != '')
          GestureDetector(
            onTap: () async {
              debugPrint('-----------');
              final Uri launchUri = Uri(
                scheme: 'mailto',
                path: c.email,
              );
              await url_launcher.launchUrl(launchUri);
            },
            child: Container(
              padding: const EdgeInsets.only(left: 27.0),
              child: Text(
                c.email ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        const SizedBox(
          height: 10,
        ),
        Row(children: [
          Icon(
            Icons.chat,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          const Text(
            'Comments:',
            style: TextStyle(
              fontSize: 12,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        Container(
          padding: const EdgeInsets.only(left: 27.0),
          child: Text(
            c.comments ?? '',
            style: const TextStyle(
              fontSize: 14,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget supplierData() {
    ProjectSuppliersData c = projsupplier!;
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: [
            Icon(
              Icons.apartment,
              color: secondary,
              size: 20,
            ),
            const SizedBox(
              width: 8,
            ),
            Text(
              c.supplierName ?? '',
              style: const TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Row(children: [
          Icon(
            Icons.stacked_bar_chart,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          Text(
            ' ${c.trades}',
            style: const TextStyle(
              fontSize: 14,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        const SizedBox(
          height: 8,
        ),
        Row(children: [
          Icon(
            Icons.person,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          Text(
            'contact : ${c.contactPerson}',
            style: const TextStyle(
              fontSize: 14,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        const SizedBox(
          height: 8,
        ),
        Row(children: [
          Icon(
            Icons.call,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          const Text(
            'Phone:',
            style: TextStyle(
              fontSize: 12,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        if (c.phoneNumber != null && c.phoneNumber != '')
          GestureDetector(
            onTap: () async {
              debugPrint('-----------');
              final Uri launchUri = Uri(
                scheme: 'tel',
                path: c.phoneNumber,
              );
              await url_launcher.launchUrl(launchUri);
            },
            child: Container(
              padding: const EdgeInsets.only(left: 27.0),
              child: Text(
                c.phoneNumber ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        const SizedBox(
          height: 10,
        ),
        Row(children: [
          Icon(
            Icons.mail,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          const Text(
            'Email:',
            style: TextStyle(
              fontSize: 12,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        if (c.email != null && c.email != '')
          GestureDetector(
            onTap: () async {
              debugPrint('-----------');
              final Uri launchUri = Uri(
                scheme: 'mailto',
                path: c.email,
              );
              await url_launcher.launchUrl(launchUri);
            },
            child: Container(
              padding: const EdgeInsets.only(left: 27.0),
              child: Text(
                c.email ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        const SizedBox(
          height: 10,
        ),
        Row(children: [
          Icon(
            Icons.location_on,
            color: secondary,
            size: 20,
          ),
          const SizedBox(
            width: 8,
          ),
          const Text(
            'Address:',
            style: TextStyle(
              fontSize: 12,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ]),
        Container(
          padding: const EdgeInsets.only(left: 27.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                c.address1 ?? '',
                style: const TextStyle(
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(
                width: 2,
              ),
              Text(
                c.city ?? '',
                style: const TextStyle(
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(
                width: 2,
              ),
              Text(
                c.state ?? '',
                style: const TextStyle(
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(
                width: 2,
              ),
              Text(
                c.zip ?? '',
                style: const TextStyle(
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
