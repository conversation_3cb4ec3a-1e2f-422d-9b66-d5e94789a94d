import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/milestone/milestonechecklistdata.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';

class MilestoneChecklistPG extends StatelessWidget {
  final int? milestoneid;
  String? mode;

  MilestoneChecklistPG({super.key, this.milestoneid, this.mode});
  LabelController talabel = Get.find<LabelController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(talabel.get('TMCMOBILE_PROJECTS_SCHEDULE_CHECKLIST')?.value ?? '',
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: MilestoneChecklistData(
        milestoneid: milestoneid,
        mode: mode,
      ),
    );
  }
}
