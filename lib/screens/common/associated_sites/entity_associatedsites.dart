import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/models/sitemanagement/site/sitebvo.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/project_search_screen.dart';
import 'package:tangoworkplace/screens/sitemanagement/site/site_details.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../providers/sitemanagement/site/entityassociatedsites_controller.dart';
import '../../../providers/sitemanagement/site/site_controller.dart';
import '../utils/entityerrorpg.dart';

class EntityAssociatedSites extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  final String? mode;
  EntityAssociatedSites({super.key, this.entityType, this.entityId, this.mode});
  LabelController talabel = Get.find<LabelController>();
  EntityAssociatedSitesController easitesCtrl = Get.find<EntityAssociatedSitesController>();
  SiteSearchController siteSearchState = Get.put(SiteSearchController());
  SiteSearchController siteSearchCtrlr = Get.find<SiteSearchController>();
  Future _refreshSitelist() async {
    await easitesCtrl.getSitesBvo(entityId);
  }

  @override
  Widget build(BuildContext context) {
    return listStack(context);
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      Positioned.fill(
        //top: adhocTaskCtrl.isContractor.value == 'N' ? 45 : 1,
        child: RefreshIndicator(
          onRefresh: _refreshSitelist,
          child: GetX<EntityAssociatedSitesController>(
            initState: (state) async {
              easitesCtrl.isloading.value = true;
              await easitesCtrl.getlabels(talabel);
              await easitesCtrl.getSitesBvo(entityId);
            },
            builder: (_) {
              return _.isloading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.sitebvoList.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.sitebvoList[index]);
                          },
                          itemCount: _.sitebvoList.length,
                        );
            },
          ),
        ),
      ),
    ]);
  }

  Widget listitem(BuildContext context, SiteBvo s) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () async {
        // Get.to(() => SiteDetails(
        //       nav_from: 'target_associted_sites',
        //       siteid: s.siteId,
        //       sitename: s.siteName,
        //     ));

        String mode = await siteSearchCtrlr.getUserEntityEditAccess(
            entityid: s.siteId.toString(), entitytype: 'SITE', roFlag: s.readOnlyFlag);

        if (mode == 'error') {
          Get.to(
            () => EntityErrorPg(
              entitytype: 'Site',
              entityid: s.siteId,
              entityname: s.siteName,
            ),
          );
        } else {
          Get.to(() => SiteDetails(
                nav_from: 'target_associted_sites',
                siteid: s.siteId,
                sitename: s.siteName,
                tabroles: siteSearchCtrlr.tabroles.value,
                parent_mode: mode,
              ));
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Flexible(
                  child: Text(
                    s.siteName ?? '',
                    style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ]),
              const SizedBox(
                height: 5,
              ),
              if (talabel.get('TMCMOBILE_SITE_GENERAL_SITENUM') != null)
                ComponentUtils.listVertRow('${talabel.get('TMCMOBILE_SITE_GENERAL_SITENUM')?.value ?? 'Site Number'}:',
                    s.siteNumber?.toString() ?? '',
                    labelStyle: TextStyle(
                      color: primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    )),
              if (talabel.get('TMCMOBILE_SITE_GENERAL_STATUS') != null)
                ComponentUtils.listVertRow('${talabel.get('TMCMOBILE_SITE_GENERAL_STATUS')?.value ?? 'Site Status'}:',
                    s.statusDesc?.toString() ?? '',
                    labelStyle: TextStyle(
                      color: primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    )),
              if (talabel.get('TMCMOBILE_SITE_GENERAL_CATEGORY') != null)
                ComponentUtils.listVertRow('${talabel.get('TMCMOBILE_SITE_GENERAL_CATEGORY')?.value ?? 'Category:'}:',
                    s.category?.toString() ?? '',
                    labelStyle: TextStyle(
                      color: primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    )),
              const SizedBox(
                height: 5,
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Icon(
                  Icons.location_on,
                  color: secondary,
                  size: 15,
                ),
                const SizedBox(
                  width: 5,
                ),
                Expanded(
                  child: Text((s.address ?? '') + ' ' + (s.city ?? '') + ' ' + (s.state ?? ' '),
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: primary,
                        fontSize: 12,
                        letterSpacing: .1,
                      )),
                ),
              ]),
            ],
          ),
        ),
      ),
    );
  }
}
