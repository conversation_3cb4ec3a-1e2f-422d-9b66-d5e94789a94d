import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/tree_view.dart';
import 'package:tangoworkplace/providers/common/documents_tree_controller.dart';

import '../../../utils/device_util.dart';

class DocumentsTree extends GetView<DocumentsTreeController> {
  final String? entityType;
  final int? entityId;
  final int? rootFolderId;
  final String? mode;

  const DocumentsTree({
    super.key,
    this.entityId,
    this.mode,
    this.rootFolderId,
    this.entityType,
  });

  @override
  Widget build(BuildContext context) {
    controller.loadInitial(rootFolderId);

    return rootFolderId == null || rootFolderId == 0
        ? Center(
            child: Text(
              'No Data',
              style: TextStyle(
                fontSize: DeviceUtils.taFontSize(
                  1.5,
                  context,
                ),
              ),
            ),
          )
        : Obx(
            () => controller.isLoading.isTrue
                ? const ProgressIndicatorCust()
                : SingleChildScrollView(
                    child: TreeView(
                      nodes: controller.documentsList,
                    ),
                  ),
          );
  }
}
