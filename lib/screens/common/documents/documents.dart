import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/models/common/document.dart';
import 'package:tangoworkplace/providers/common/documents_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';

class Documents extends GetView<DocumentController> {
  final String? entityType;
  final int? entityId;
  final int? rootfolderid;
  String? mode;
  Documents({super.key, this.entityType, this.entityId, this.rootfolderid, this.mode});
  DocumentController docState = Get.find<DocumentController>();

  void _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );
    if (result != null) {
      List<File> files;
      files = result.paths.map((path) => File(path!)).toList();

      for (var element in files) {
        File f = element;
        debugPrint('file name    ${f.path}');
      }
      ProgressUtil.showLoaderDialog(Get.context!);
      docState.uploadFiles(files, entityType, entityId);
    } else {
      // User canceled the picker
    }
  }

  @override
  Widget build(BuildContext context) {
    return rootfolderid == null || rootfolderid == 0
        ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
        : Stack(children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(
                  width: 15,
                ),
                Row(
                  children: [
                    Obx(
                      () => IconButton(
                        icon: Icon(docState.listviewflag.isFalse ? Icons.format_list_bulleted : Icons.grid_view),
                        color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                        onPressed: () {
                          docState.listviewflag.value = !docState.listviewflag.value;
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Positioned.fill(
              top: 45,
              child: GetX<DocumentController>(initState: (state) {
                Get.find<DocumentController>().loadDocuments(folderid: rootfolderid);
              }, builder: (_) {
                return _.isLoading.isTrue
                    ? const ProgressIndicatorCust()
                    : _.doclist.isEmpty || _.doclist.isEmpty
                        ? Center(
                            child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                        : docState.listviewflag.isFalse
                            ? GridView.builder(
                                //physics:  ,
                                itemCount: _.doclist.length,
                                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: docState.listviewflag.isFalse ? 3 : 1,
                                  crossAxisSpacing: 2,
                                  mainAxisSpacing: 8,
                                  childAspectRatio: (2 / 1),
                                ),
                                itemBuilder: (
                                  context,
                                  index,
                                ) {
                                  return GestureDetector(
                                    onTap: () {
                                      onDocTap(context, _.doclist[index]);
                                    },
                                    child: Container(
                                        child: Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      children: [
                                        getfileicon(_.doclist[index]),
                                        getDocdetails(_.doclist[index]),
                                      ],
                                    )),
                                  );
                                },
                              )
                            : Container(
                                margin: const EdgeInsets.only(bottom: 20),
                                child: ListView.builder(
                                  itemBuilder: (context, index) {
                                    return GestureDetector(
                                      onTap: () {
                                        onDocTap(context, _.doclist[index]);
                                      },
                                      child: Container(
                                          margin: const EdgeInsets.only(bottom: 6, left: 28, right: 5, top: 5),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            children: [
                                              getfileicon(_.doclist[index]),
                                              getDocdetails(_.doclist[index]),
                                            ],
                                          )),
                                    );
                                  },
                                  itemCount: _.doclist.length,
                                  controller: _.docScrollCtrl,
                                ),
                              );
              }),
            ),
            if (rootfolderid != null && mode != 'view')
              Positioned(
                bottom: 30,
                right: 30,
                child: FloatingActionButton(
                  elevation: 5.0,
                  child: const Icon(
                    Icons.add,
                    size: 30,
                  ),
                  onPressed: () {
                    _pickFiles();
                  },
                ),
              ),
          ]);
  }

  onDocTap(BuildContext context, Document d) {
    if (d.docType == 'FOLDER' || d.docType == 'SUB') {
      docState.loadDocuments(doc: d);
    } else if (d.docType == 'FILE') {
      ComponentUtils.documentviewer(context, d.id.toString());
    } else {
      return null;
    }
  }

  Widget getDocdetails(Document d) {
    if (d.docType == 'FOLDER') {
      return Text('${d.docName ?? ' '} (${d.docCount?.toString() ?? '0'})',
          style: TextStyle(fontSize: 12, color: ComponentUtils.primary), textAlign: TextAlign.center);
    } else if (d.docType == 'SUB') {
      return Text('...', style: TextStyle(fontSize: 12, color: ComponentUtils.primary), textAlign: TextAlign.center);
    } else {
      return Flexible(
        child: Text(d.docName ?? '',
            style: TextStyle(fontSize: 12, color: ComponentUtils.primary),
            softWrap: false,
            maxLines: docState.listviewflag.isFalse ? 2 : 5,
            overflow: TextOverflow.ellipsis,
            textAlign: docState.listviewflag.isFalse ? TextAlign.center : TextAlign.start),
      );
    }
  }

  Widget getfileicon(Document d) {
    if (d.docType == 'FOLDER' || d.docType == 'SUB') {
      return Icon(
        Icons.folder,
        size: 30,
        color: CommonUtils.createMaterialColor(const Color(0XFFe6b800)),
      );
    } else {
      return Icon(
        Icons.description,
        size: 30,
        color: CommonUtils.createMaterialColor(const Color(0XFF193366)),
      );
    }
  }
}
