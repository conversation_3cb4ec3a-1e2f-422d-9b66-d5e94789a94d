import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/screens/common/documents/documents.dart';
import 'package:tangoworkplace/screens/common/photos/photos.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';

class DocumentsPG extends GetView<PhotosController> {
  final String? entityType;
  final int? entityId;
  final int? rootfolderid;
  final hdrText;
  String? mode;

  DocumentsPG({super.key, this.entityType, this.mode, this.entityId, this.rootfolderid, this.hdrText});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(hdrText, style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: SizedBox.expand(
        child: Documents(
          rootfolderid: rootfolderid,
          entityId: entityId,
          entityType: entityType,
          mode: mode,
        ),
      ),
    );
  }
}
