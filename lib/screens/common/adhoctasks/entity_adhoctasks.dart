import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/adhoctask.dart';
import 'package:tangoworkplace/models/common/adhoctemplate.dart';
import 'package:tangoworkplace/models/common/utils/contact.dart';
import 'package:tangoworkplace/providers/common/adhoctasks_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/device_util.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import '../../../common/component_utils.dart';
import 'adhoctasks_details.dart';

class EntityAdhocTasks extends StatelessWidget {
  final String? entityType;
  final int? entityId;
  String? mode;

  EntityAdhocTasks({super.key, this.entityType, this.entityId, this.mode});
  LabelController talabel = Get.find<LabelController>();
  EntityAdhicTasksController adhocTaskCtrl = Get.find<EntityAdhicTasksController>();

  Future _refreshAdhocTasklist() async {
    adhocTaskCtrl.loadAdhocTasks(entityType: entityType, entityId: entityId);
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('isContractor--------${adhocTaskCtrl.isContractor.value}');

    if (adhocTaskCtrl.isContractor.value == 'N') {
      return Obx(() => adhocTaskCtrl.listflag.isTrue ? listStack(context) : templateSatck(context));
    } else {
      return listStack(context);
    }
  }

  Widget addTasks() {
    return PopupMenuButton(
      icon: const Icon(Icons.more_vert),
      //color: ComponentUtils.primecolor,
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 1,
          onTap: () {
            Future.delayed(const Duration(seconds: 0), () async {
              adhocTaskCtrl.at_mode.value = 'create';

              adhocTaskCtrl.adhoctask.value = AdhocTask();
              Get.to(() => AdhocTaskDetails(
                    entityId: entityId,
                    entityType: entityType,
                    mode: mode,

                    //task: new AdhocTask(),
                  ));
            });
          },
          child: Text("Add Item", style: TextStyle(fontSize: 12, color: ComponentUtils.primecolor)),
        ),
        PopupMenuItem(
          value: 2,
          onTap: () async {
            await adhocTaskCtrl.gettemplateslov(entityType);
            await _showtemplates();
          },
          child: Text("Add from Template", style: TextStyle(fontSize: 12, color: ComponentUtils.primecolor)),
        ),
      ],
    );
  }

  _showtemplates() async {
    return Get.defaultDialog(
      title: 'Add from Template',
      titleStyle: const TextStyle(fontSize: 16),
      content: Column(
        children: [
          await templateform(Get.context),
        ],
      ),
    );
  }

  Widget templateSatck(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: <Widget>[
        templateform(context),
      ],
    );
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      if (adhocTaskCtrl.isContractor.value == 'N')
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                const SizedBox(
                  width: 15,
                ),
                // TaButton(
                //   type: 'elevate',
                //   buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_ADD')?.value ?? 'Add',
                //   onPressed: () {
                //     adhocTaskCtrl.at_mode.value = 'create';
                //     adhocTaskCtrl.adhoctask.value = AdhocTask();
                //     Get.to(() => AdhocTaskDetails(
                //           entityId: entityId,
                //           entityType: entityType,

                //           //task: new AdhocTask(),
                //         ));
                //   },
                // ),
                mode == 'edit' ? addTasks() : const SizedBox(width: 5),
              ],
            ),
            const SizedBox(
              width: 5,
            ),
            Row(
              children: [
                filterlov(),
              ],
            ),
          ],
        ),
      Positioned.fill(
        top: adhocTaskCtrl.isContractor.value == 'N' ? 45 : 1,
        child: RefreshIndicator(
          onRefresh: _refreshAdhocTasklist,
          child: GetX<EntityAdhicTasksController>(
            initState: (state) {
              adhocTaskCtrl.loadAdhocTasks(
                  entityType: entityType, entityId: entityId, isContractor: adhocTaskCtrl.isContractor.value);
            },
            builder: (_) {
              return _.isloading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.adhoctaskdata.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.adhoctaskdata[index]);
                          },
                          itemCount: _.adhoctaskdata.length,
                        );
            },
          ),
        ),
      ),
      // Positioned(
      //   bottom: 30,
      //   right: 30,
      //   child: FloatingActionButton(
      //     elevation: 5.0,
      //     child: const Icon(
      //       Icons.add,
      //       size: 30,
      //     ),
      //     onPressed: () {
      //       debugPrint('add button----------------------');
      //       Get.to(() => AdhocTaskDetails(
      //             entityId: entityId,
      //             entityType: entityType,
      //             at_mode: 'create',
      //           ));
      //     },
      //   ),
      // ),
    ]);
  }

  Widget filterlov() {
    return Container(
      margin: const EdgeInsets.only(top: 5, right: 7),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Obx(
            () => TaDropSown(
              label: 'Select',
              initvalue: adhocTaskCtrl.taskfilter.value,
              items: <DropdownMenuItem<String>>[
                const DropdownMenuItem(
                    value: "All", child: Text("All Tasks", style: TextStyle(fontSize: 12, color: Colors.black))),
                const DropdownMenuItem(
                    value: "My", child: Text("My Tasks", style: TextStyle(fontSize: 12, color: Colors.black))),
              ].map<DropdownMenuItem<String>>((DropdownMenuItem<String> value) {
                return DropdownMenuItem<String>(
                  value: value.value,
                  child: value.child,
                );
              }).toList(),
              onChanged: (val) async {
                adhocTaskCtrl.taskfilter.value = val;
                adhocTaskCtrl.loadAdhocTasks(entityType: entityType, entityId: entityId, filter: val);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, AdhocTask task) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;

    return GestureDetector(
      onTap: () {
        //adhocTaskCtrl.labelsloading.value = true;
        adhocTaskCtrl.adhoctask.value = task;
        adhocTaskCtrl.at_mode.value = 'edit';
        Get.to(() => AdhocTaskDetails(
              entityId: entityId,
              entityType: entityType,
              mode: mode,
            ));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Flexible(
                  child: Text(
                    task.taskName ?? '',
                    style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  task.taskSeq?.toString() ?? '',
                  style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ]),
              const SizedBox(
                height: 5,
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                // Text(
                //   'task Type:',
                //   style: TextStyle(color: primary, fontSize: 10),
                // ),
                // SizedBox(
                //   width: 10.0,
                // ),
                Text(
                  task.taskTypeDesc?.toString() ?? '',
                  style: TextStyle(color: primary, fontSize: 10),
                ),
                Text(
                  task.status?.toString() ?? '',
                  style: TextStyle(color: primary, fontSize: 10),
                  overflow: TextOverflow.ellipsis,
                ),
              ]),
              const SizedBox(
                height: 5,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Icon(
                    Icons.mail,
                    color: primary,
                    size: 15,
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  Text(
                    task.assignedTo?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              if (task.actualFinsh != null)
                const SizedBox(
                  height: 5.0,
                ),
              if (task.actualFinsh != null)
                Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  Text(
                    task.actualFinsh?.toString() ?? '',
                    style: TextStyle(color: primary, fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                ]),
            ],
          ),
        ),
      ),
    );
  }

  templateform(BuildContext? context) async {
    return Obx(
      () => adhocTaskCtrl.templateloading.isTrue
          ? const ProgressIndicatorCust()
          : Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(top: 5),
                  margin: const EdgeInsets.fromLTRB(10, 20, 10, 5),
                  width: Get.width,
                  height: 190,
                  // decoration: BoxDecoration(
                  //     color: Colors.white,
                  //     borderRadius: BorderRadius.circular(10.0),
                  //     boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 15, offset: Offset(0, 10))]),
                  // decoration: BoxDecoration(
                  //     border: Border.all(color: HexColor('#4C4B5D').withOpacity(0.2), width: 1), borderRadius: BorderRadius.circular(15)),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      // adhocTaskCtrl.templateloading.value ? ProgressIndicatorCust() :
                      templatesLov(context),
                      const SizedBox(
                        height: 40,
                      ),
                      TaButton(
                        type: 'elevate',
                        buttonText: 'Generate AdhocTasks',
                        onPressed: () async {
                          if (adhocTaskCtrl.generatebtnbinding.value) {
                            await adhocTaskCtrl.generateItemsFromTemplate(
                              entityType: entityType,
                              entityId: entityId,
                            );
                            Get.back();
                          } else {
                            ComponentUtils.showsnackbar(text: "Please Select Template...");
                            return null;
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget templatesLov(BuildContext? context) {
    return (adhocTaskCtrl.templatelov.isNotEmpty)
        ? DropdownButton(
            hint: const Text(
              'Select Template',
              style: TextStyle(fontSize: 14, color: Colors.black),
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
              adhocTaskCtrl.selectedtemplate.value = newValue;
              adhocTaskCtrl.generatebtnbinding.value = (newValue != null && newValue != '') ? true : false;
            },
            value: adhocTaskCtrl.selectedtemplate.value == '' ? null : adhocTaskCtrl.selectedtemplate.value,
            items: adhocTaskCtrl.templatelov.map((AdhocTemplate t) {
              return DropdownMenuItem(
                value: t.adhocTemplateId.toString(),
                child: new Text(
                  t.templateName!,
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
              );
            }).toList(),
          )
        : DropdownButton(
            hint: const Text(
              'Select Template',
            ),
            onChanged: (dynamic newValue) {
              debugPrint('newValue          ' + newValue);
            },
            value: null,
            items: const [
              DropdownMenuItem(
                  value: null,
                  child: Text(
                    "Select",
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  )),
            ],
          );
  }

  //addPunchlistItem(BuildContext ctx) {
  //   return Get.defaultDialog(
  //       title: 'Add Punochlist',
  //       titleStyle: TextStyle(fontSize: 16),
  //       content: Container(
  //         height: 250,
  //         child: SingleChildScrollView(
  //           child: Column(
  //             children: [
  //               TaInputTextField(
  //                 title: 'Item sequence',
  //                 controller: punchlistCntrl.addItemSeqCtrl,
  //                 keyboard: TextInputType.number,
  //                 minLines: 1,
  //                 maxLines: 1,
  //               ),
  //               TaInputTextField(
  //                 title: 'Name',
  //                 controller: punchlistCntrl.addItemNameCtrl,
  //                 minLines: 1,
  //                 maxLines: 1,
  //               ),
  //               TaInputTextField(
  //                 title: 'Division',
  //                 controller: punchlistCntrl.addDivisionCtrl,
  //                 minLines: 1,
  //                 maxLines: 1,
  //               ),
  //               TaInputTextField(
  //                 title: 'Item Type',
  //                 controller: punchlistCntrl.addItemTypeCtrl,
  //                 minLines: 1,
  //                 maxLines: 1,
  //               ),
  //               TaInputTextField(
  //                 title: 'Descrption',
  //                 controller: punchlistCntrl.addDescrpitionCtrl,
  //                 minLines: 3,
  //                 maxLines: 5,
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //       actions: [
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.end,
  //           children: [
  //             TaButton(
  //               type: 'elevate',
  //               buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_CANCEL')?.value ?? 'Cancel',
  //               onPressed: () {
  //                 punchlistCntrl.clearaddfieldsvalue();
  //                 Get.back();
  //               },
  //             ),
  //             SizedBox(
  //               width: 4.0,
  //             ),
  //             TaButton(
  //               type: 'elevate',
  //               buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
  //               onPressed: () {
  //                 punchlistCntrl.addPunchlistItem(entityid: entityId, entitytype: entityType);
  //               },
  //             ),
  //             SizedBox(
  //               width: 7.0,
  //             ),
  //           ],
  //         ),
  //       ]);
  // }

  // showactions() {
  //   return Get.defaultDialog(
  //       title: 'Actions',
  //       titleStyle: TextStyle(fontSize: 16),
  //       content: Container(
  //           margin: EdgeInsets.only(left: 15),
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: [
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: [
  //                   Text(
  //                     'All Not Applicable',
  //                     style: TextStyle(
  //                         //color: primary,
  //                         fontSize: 12),
  //                   ),
  //                   Obx(
  //                     () => Checkbox(
  //                       value: punchlistCntrl.allnotapplicableflag.value,
  //                       onChanged: (val) {
  //                         punchlistCntrl.allnotapplicableflag.value = val;
  //                       },
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: [
  //                   Text(
  //                     'All Complted',
  //                     style: TextStyle(
  //                         //color: primary,
  //                         fontSize: 12),
  //                   ),
  //                   Obx(
  //                     () => Checkbox(
  //                       value: punchlistCntrl.allcextattr1.value,
  //                       onChanged: (val) {
  //                         punchlistCntrl.allcextattr1.value = val;
  //                       },
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ],
  //           )),
  //       actions: [
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.end,
  //           children: [
  //             TaButton(
  //               type: 'elevate',
  //               buttonText: 'Cancel',
  //               onPressed: () {
  //                 punchlistCntrl.allnotapplicableflag.value = false;
  //                 punchlistCntrl.allcextattr1.value = false;
  //                 Get.back();
  //               },
  //             ),
  //             SizedBox(
  //               width: 4.0,
  //             ),
  //             TaButton(
  //               type: 'elevate',
  //               buttonText: 'Save',
  //               onPressed: () {
  //                 punchlistCntrl.applyAllFlagActions(entityid: entityId, entitytype: entityType);
  //               },
  //             ),
  //             SizedBox(
  //               width: 7.0,
  //             ),
  //           ],
  //         ),
  //       ]);
  // }
}
