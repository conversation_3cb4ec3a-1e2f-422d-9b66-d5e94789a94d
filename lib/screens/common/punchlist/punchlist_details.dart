import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tacheckbox.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputdate.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/taforminputtext.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/photo.dart';
import 'package:tangoworkplace/models/common/punchlist.dart';
import 'package:tangoworkplace/providers/common/punchlist_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/photos/photospg.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../models/lookup_values.dart';
import '../photos/photosgrid.dart';

class PunchlistDetails extends StatelessWidget {
  final Punchlist? punch;
  final entityId;
  final entityType;
  String? mode;

  PunchlistDetails({super.key, this.punch, this.entityId, this.entityType, this.mode});
  LabelController talabel = Get.find<LabelController>();
  EntityPunchlistController punchlistCntrlr = Get.find<EntityPunchlistController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    punchlistCntrlr.setCurrentRow(punch!);
    punchlistCntrlr.duedateCtrl.text = ComponentUtils.dateToString(punch!.dueDate);
    return GetX<LabelController>(
      initState: (state) {
        // Future.delayed(Duration.zero, () async {
        //   await punchlistCntrlr.getlabels(talabel);
        // });
      },
      builder: (_) {
        return punchlistCntrlr.labelsloading.value
            ? ComponentUtils.labelLoadScaffold()
            : Scaffold(
                appBar: AppBar(
                  iconTheme: Theme.of(context).appBarTheme.iconTheme,
                  title: Text(talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS')?.value ?? '',
                      style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
                      ),
                  backgroundColor: Colors.white,
                  elevation: 5,
                  leading: IconButton(
                    icon: ComponentUtils.backpageIcon,
                    color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                    onPressed: () {
                      debugPrint('------back-------');
                      Get.back();

                      punchlistCntrlr.loadEntityPunchlist(punch!.entityType, punch!.entityId);
                    },
                  ),
                ),
                body: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: punchlistDetForm(),
                ),
                bottomNavigationBar: bottombuttonbar(_formKey),
              );
      },
    );
  }

  Widget bottombuttonbar(GlobalKey<FormState> fkey) {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            if (mode == 'edit')
              TaButton(
                type: 'elevate',
                buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_DELETE')?.value ?? 'Delete',
                onPressed: () {
                  ComponentUtils.showwarnpopup(confirmfunction: () {
                    punchlistCntrlr.onDeleteDetRecord(
                        id: punch!.scopePunchListId, entitytype: punch!.entityType, entityid: punch!.entityId);
                    //Get.back();
                    //ProgressUtil.showLoaderDialog(Get.context);
                  });
                },
              ),
            mode == 'edit'
                ? TaButton(
                    type: 'elevate',
                    buttonText: talabel.get('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
                    onPressed: () {
                      final isValid = fkey.currentState!.validate();
                      if (!isValid) {
                        return;
                      }

                      _formKey.currentState!.save();
                      debugPrint('------------------saved--------------------');
                      punchlistCntrlr.onDetFormsave(punch!);
                    },
                  )
                : SizedBox(width: 5),
          ],
        ),
      ),
    );
  }

  Widget punchlistDetForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        children: <Widget>[
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ITEMSEQ') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ITEMSEQ')!.value,
              value: punch!.lineItemSeq?.toString() ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DIVISION') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DIVISION')!.value,
              value: punch!.division ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ITEMTYPE') != null)
            TaInputText(
              title: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ITEMTYPE')!.value,
              value: punch!.itemType ?? '',
              readOnly: true,
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_SCOPEPUNCHNAME') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_SCOPEPUNCHNAME')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_SCOPEPUNCHNAME')!.ro : true,
              value: punchlistCntrlr.scopepunchName.value ?? '',
              onSaved: (val) {
                punchlistCntrlr.scopepunchName.value = val;
              },
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DESC') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DESC')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DESC')!.ro : true,
              value: punch!.description ?? '',
              maxLines: 5,
              onSaved: (val) {
                punchlistCntrlr.description.value = val;
              },
              // ),
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_COMMENTS') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_COMMENTS')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_COMMENTS')!.ro : true,
              value: punch!.comments,
              minLines: 5,
              maxLines: 5,
              keyboard: TextInputType.multiline,
              textInputAct: TextInputAction.newline,
              onSaved: (val) {
                punchlistCntrlr.comments.value = val;
              },
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ASSIGNEE') != null)
            TaFormInputText(
              label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ASSIGNEE')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_ASSIGNEE')!.ro : true,
              value: punch!.assignee ?? '',
              // keyboard: TextInputType.emailAddress,
              onSaved: (val) {
                punchlistCntrlr.assignee.value = val;
              },
              // validate: (val) {
              //   if (!GetUtils.isEmail(val)) {
              //     return 'Provide valid Email';
              //   }
              //   return null;
              // },
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DUEDATE') != null)
            TaFormInputDate(
              label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DUEDATE')!.value,
              readOnly: mode == 'edit' ? talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_DUEDATE')!.ro : true,
              //value: punch.dueDate ?? '',
              controller: punchlistCntrlr.duedateCtrl,
              onChanged: (dateval) {
                debugPrint('dateval--------- $dateval');
                punchlistCntrlr.duedateCtrl.text = dateval.toString();
              },
              onSaved: (val) {
                punchlistCntrlr.duedate.value = val;
              },
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_CEXTLOV1') != null)
            TaFormDropdown(
              label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_CEXTLOV1')!.value,
              emptttext: 'Select',
              onChanged: (mode != 'edit' || talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_CEXTLOV1')!.ro!)
                  ? null
                  : (newValue) {
                      debugPrint('newValue          ' + newValue);
                      punch!.cExtLov1 = newValue;
                    },
              value: (punch!.cExtLov1 == '' || punch!.cExtLov1 == 'null') ? null : punch!.cExtLov1,
              listflag: (punchlistCntrlr.cextlov1lov.value.isNotEmpty),
              items: punchlistCntrlr.cextlov1lov.value.map((LookupValues l) {
                return DropdownMenuItem(
                  value: l.lookupCode,
                  child: new Text(
                    l.lookupValue!,
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                );
              }).toList(),
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_NOTAPPLIFLAG') != null)
            TaFormCheckBoxicon(
              label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_NOTAPPLIFLAG')!.value,
              enabled: mode == 'view' ? false : !talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_NOTAPPLIFLAG')!.ro!,
              value: punchlistCntrlr.notapplicableflag.value,
              onSaved: (val) {
                if (val == true) {
                  punchlistCntrlr.cextattr1.value = false;
                }
                punchlistCntrlr.notapplicableflag.value = val!;
              },
              onChanged: (value) {
                if (value!) {
                  punchlistCntrlr.notapplicableflag.value = value;
                  punchlistCntrlr.cextattr1.value = !value;
                } else {
                  punchlistCntrlr.notapplicableflag.value = value;
                  punchlistCntrlr.cextattr1.value = !value;
                }
              },
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_CEXTATTR1') != null)
            Obx(
              () => TaFormCheckBoxicon(
                label: talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_CEXTATTR1')!.value,
                enabled: mode == 'view'
                    ? false
                    : !talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_CEXTATTR1')!.ro! &&
                        !punchlistCntrlr.notapplicableflag.value,
                value: punchlistCntrlr.cextattr1.value,
                onSaved: (val) {
                  punchlistCntrlr.cextattr1.value = val!;
                },
              ),
            ),
          if (talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_PHOTOS') != null)
            photos(talabel.get('TMCMOBILE_COMMON_PUNCHLIST_DETAILS_PHOTOS')!.value!),
        ],
      ),
    );
  }

  Widget photos(String labelStr) {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 2, 12, 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(labelStr),
        IconButton(
            onPressed: () {
              Get.to(() => PhotosPG(
                    entityId: punch!.scopePunchListId,
                    entityType: entityType,
                    mode: mode,
                  ));

              // Get.defaultDialog(
              //   title: 'Photos',
              //   titleStyle: TextStyle(fontSize: 16),
              //   content: Container(
              //     height: 500,
              //     width: 400,
              //     //margin: EdgeInsets.fromLTRB(15, 5, 15, 10),
              //     child: Photos(
              //       entityType: 'PUNCHLIST',
              //       entityId: punch.scopePunchListId,
              //     ),
              //   ),
              // );
              //debugPrint('clicked photos----------');
            },
            icon: Icon(
              Icons.photo_library,
              color: ComponentUtils.primecolor,
            ))
      ]),
    );
  }
}
