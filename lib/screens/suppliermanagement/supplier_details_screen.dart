import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/screens/suppliermanagement/suppliertabs/supplier_general.dart';

import '../../../common/component_utils.dart';
import '../../models/suppliermanagement/supplier_data.dart';
import '../../models/ta_admin/app_label.dart';
import '../../providers/suppliermangement/supplier_general_controller.dart';
import '../../providers/suppliermangement/supplierdetails_controller.dart';
import '../../providers/ta_admin/label_controller.dart';

class SupplierDetails extends StatelessWidget {
  final int? supplierid;
  final String? navFrom;
  final String? supplierName;
  final Map<String, String>? tabRoles;
  final String? parentMode;

  SupplierDetails({
    super.key,
    this.supplierid,
    this.navFrom,
    this.supplierName,
    this.tabRoles,
    this.parentMode,
  });
  SupplierDetailsController detCtrl = Get.put(SupplierDetailsController());

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey _targettabControllerKey = GlobalKey();
  final targettabs = <Tab>[];
  Applabel? label;
  LabelController talabel = Get.find<LabelController>();

  var generaltab;
  var demographicstab;
  var fieldvalidtab;
  //var photostab;
  var docstab;
  var asssocsitestab;
  var attributestab;

  List<Tab> tabsdata = [];

  String? _setTablabels(Applabel? label) {
    if (label != null) {
      targettabs.add(Tab(
        text: label.value,
      ));
      return label.value;
    }
    return null;
  }

  Future _getlabels(LabelController labCtrl) async {
    try {
      // labCtrl.setdataloading.value = true;
      await labCtrl.getlabels('TMCMOBILE_SUPPLIER', 'supplier_details', filtertype: 'page');
      //if (detCtrl.tabroles.value!['general'] != 'na')
      generaltab = _setTablabels(labCtrl.get('TMCMOBILE_SUPPLIER_GENERAL'));
      // if (detCtrl.tabroles.value!['attributes'] != 'na')
      //  attributestab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_ATTRIBUTES'));
      // if (detCtrl.tabroles.value!['demog'] != 'na')
      // demographicstab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_DEMOGRAPHICS'));
      // if (detCtrl.tabroles.value!['fv'] != 'na')
      // fieldvalidtab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_FIELDVALIDATION'));

      // // photostab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_PHOTOS'));
      // if (detCtrl.tabroles.value!['document'] != 'na')
      //  docstab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_DOCUMENTS'));
      // if (detCtrl.tabroles.value!['as'] != 'na')
      // asssocsitestab = _setTablabels(labCtrl.get('TMCMOBILE_TARGET_ASSOCITEDSITES'));
    } catch (e) {
      debugPrint('$e');
    }
    labCtrl.setdataloading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    Future.delayed(Duration.zero, () {
      debugPrint('supplier name>>> $supplierName');
      talabel.setdataloading.value = true;

      SupplierGeneralController supplierGeneralController = Get.put(SupplierGeneralController());
      //TargetAttributesController targetAttributesController = Get.put(TargetAttributesController());
      //EntityCommentsController entityCommentsController = Get.put(EntityCommentsController());
      //PhotosGridController photoController = Get.put(PhotosGridController());
      //DocumentController documentController = Get.put(DocumentController());
      //EntityAssociatedSitesController entityAssociatedSitesController = Get.put(EntityAssociatedSitesController());
      //TargetFvController targetFvController = Get.put(TargetFvController());
      //EntityDemographicsController demographicController = Get.put(EntityDemographicsController());
    });
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(supplierName ?? '',
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<LabelController>(initState: (state) {
        detCtrl.isloading.value = true;
        Future.delayed(const Duration(seconds: 1), () async {
          try {
            await _getlabels(talabel);
            await detCtrl.getSupplierRecord(supplierid?.toString());
          } finally {
            detCtrl.isloading.value = false;
          }
        });
      }, builder: (_) {
        return (talabel.setdataloading.value || detCtrl.isloading.value)
            ? const ProgressIndicatorCust()
            : DefaultTabController(
                key: _targettabControllerKey,
                initialIndex: 0,
                length: (targettabs.isNotEmpty) ? targettabs.length : 1,
                child: Column(children: <Widget>[
                  Material(
                    elevation: 5,
                    color: Colors.white,
                    child: TabBar(
                      //controller: _tabController,
                      isScrollable: false,
                      labelColor: ComponentUtils.tablabelcolor,
                      unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                      indicatorColor: ComponentUtils.tabindicatorColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.normal,
                          fontSize: 14,
                          color: ComponentUtils.tablabelcolor),
                      tabs: (targettabs.isNotEmpty)
                          ? targettabs
                          : [
                              const Tab(
                                text: 'General',
                              )
                            ],
                    ),
                  ),
                  Expanded(
                    child: (targettabs.isEmpty)
                        ? TabBarView(children: [
                            SupplierGeneral(
                              supplier: detCtrl.supplierrec.value,
                              mode: detCtrl.mode.value,
                            ),
                          ])
                        : TabBarView(
                            children: [
                              if (generaltab != null)
                                SupplierGeneral(
                                  supplier: detCtrl.supplierrec.value,
                                  mode: detCtrl.mode.value,
                                ),
                            ],
                          ),
                  ),
                ]),
              );
      }),
    );
  }
}
