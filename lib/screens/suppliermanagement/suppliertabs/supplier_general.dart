import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/suppliermanagement/supplier_data.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../common/progess_indicator_cust.dart';
import '../../../../common/widgets/component_widgets/tadropdown.dart';
import '../../../../common/widgets/components.dart';
import '../../../common/component_utils.dart';
import '../../../providers/suppliermangement/supplier_general_controller.dart';

class SupplierGeneral extends GetView<SupplierGeneralController> {
  final SupplierData? supplier;
  final String? mode;

  SupplierGeneral({super.key, this.supplier, this.mode}) {
    Get.put(SupplierGeneralController());
  }

  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: AlignmentDirectional.bottomCenter,
        children: [
          _buildMainContent(),
          //_buildBottomAppBar(),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return GetX<SupplierGeneralController>(
      initState: (_) {
        Future.delayed(Duration.zero, () async {
          await controller.onloadPageData(supplier!);
          controller.isLoading.value = false;
        });
      },
      builder: (ctrl) {
        if (ctrl.isLoading.value || ctrl.labelsloading.value) {
          return const ProgressIndicatorCust();
        }

        return Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.only(top: 10, bottom: 80),
            child: _buildFormFields(),
          ),
        );
      },
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_SUPPLIERTYPE',
          controller.supplier.value.supplierType,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_SUPPLIERNAME',
          controller.supplier.value.supplierName,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_SUPPLIERFIRSTNAME',
          controller.supplier.value.supplierFirstName,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_SUPPLIERLASTNAME',
          controller.supplier.value.supplierLastName,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_SUPPLIERNUM',
          controller.supplier.value.supplierNumber,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_SUPPLIERID',
          controller.supplier.value.supplierId?.toString(),
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_STATUS',
          controller.supplier.value.statusDesc,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_DESC',
          controller.supplier.value.description,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_LEGATLENTITYTYPE',
          controller.supplier.value.legalEntityType,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_RECORDTYPE',
          controller.supplier.value.recordType,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_ADDRESS1',
          controller.supplier.value.address1,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_ADDRESS2',
          controller.supplier.value.address2,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_ADDRESS3',
          controller.supplier.value.address3,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_CITY',
          controller.supplier.value.city,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_STATE',
          controller.supplier.value.state,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_ZIP',
          controller.supplier.value.zip,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_COUNTRY',
          controller.supplier.value.countryName,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_CONTACTPERSON',
          controller.supplier.value.contactPerson,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_EMAIL',
          controller.supplier.value.email,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_PHONENUM',
          controller.supplier.value.phoneNumber,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_CELLNUM',
          controller.supplier.value.contactCellPhone,
        ),

        _buildReadOnlyField('TMCMOBILE_SUPPLIER_GENERAL_SUPPLIERSELECT', 'N'),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_PAYMENTTERM',
          controller.supplier.value.paymentTerm,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_TRADES',
          controller.supplier.value.trades,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_NEXTATTR01',
          controller.supplier.value.nExtAttr01?.toString(),
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_NEXTATTR02',
          controller.supplier.value.nExtAttr02?.toString(),
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_CEXTLOV1',
          controller.supplier.value.cExtAttr01,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_CEXTLOV2',
          controller.supplier.value.cExtAttr02,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_TAXID',
          controller.supplier.value.taxId,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_PARTNER',
          controller.supplier.value.partner,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_PREQUALIFIED',
          controller.supplier.value.prequalified,
        ),

        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_STARTDATE',
          controller.supplier.value.startDate,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_ENDDATE',
          controller.supplier.value.endDate,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_BANKROUTING',
          controller.supplier.value.bankRouting,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_BANKACCOUNT',
          controller.supplier.value.bankAccount,
        ),
        _buildReadOnlyField(
          'TMCMOBILE_SUPPLIER_GENERAL_PMTMETHOD',
          controller.supplier.value.paymentMethod,
        ),

        //_buildStatusDropdown(),
      ],
    );
  }

  Widget _buildReadOnlyField(String labelKey, String? value) {
    final label = _getLabel(labelKey);
    if (label == null) return const SizedBox.shrink();

    return TaInputText(
      title: label.value,
      value: value ?? '',
      readOnly: true,
    );
  }

  Widget _buildStatusDropdown() {
    final label = _getLabel('TMCMOBILE_SITE_GENERAL_STATUS');
    if (label == null) return const SizedBox.shrink();

    return Obx(() {
      final ctrl = controller;
      return TaFormDropdown(
        label: label.value,
        emptttext: 'Select',
        onChanged: _shouldEnableEditing ? _handleStatusChange : null,
        value: ctrl.supplier.value.status?.nullIfEmpty,
        listflag: ctrl.statuslov.isNotEmpty,
        items: ctrl.statuslov.map(ComponentUtils.buildDropdownItem).toList(),
      );
    });
  }

  Widget _buildBottomAppBar() {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            if (mode == 'edit') _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return TaButton(
      buttonText: _getLabel('TMCMOBILE_COMMON_BUTTONS_SAVE')?.value ?? 'Save',
      type: 'elevate',
      onPressed: controller.onSaveSupplierRecord(),
    );
  }

  void _handleStatusChange(String? newValue) {
    controller.supplier.update((val) {
      val?.status = newValue;
    });
  }

  bool get _shouldEnableEditing {
    final label = _getLabel('TMCMOBILE_SITE_GENERAL_STATUS');
    return mode == 'edit' && (label?.ro ?? true);
  }

  Applabel? _getLabel(String key) => Get.find<LabelController>().get(key);
}

extension StringExtensions on String? {
  bool equalsIgnoreCase(String? other) => this?.toLowerCase() == other?.toLowerCase();

  String? nullIfEmpty() => this?.trim().isEmpty ?? true ? null : this;
}
