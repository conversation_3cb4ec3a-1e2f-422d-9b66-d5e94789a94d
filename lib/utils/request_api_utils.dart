import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

import 'common_utils.dart';
import 'connections.dart';
import 'constvariables.dart';
import 'preferences_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
//import 'package:ssl_pinning_plugin/ssl_pinning_plugin.dart';

class ApiService {
  static Map<String, dynamic> recordMap = {};
  static getServerurl() {
    String hostVar = '${SharedPrefUtils.readPrefStr(ConstHelper.hostNameVar)}';
    String serverurl = 'https://$hostVar.tangoanalytics.com';
    if (hostVar == 'uspstest' || hostVar == 'usps') {
      serverurl = 'https://$hostVar.tangoanalytics.us';
    } else if (hostVar == 'local') {
      serverurl = "http://localhost:7001";
    }
    debugPrint('Fetching $serverurl');

    return serverurl;
  }

  static getApiServerurl() {
    String? apiserverurl = getServerurl() + apiurl;
    //print('getApiServerurl  $apiserverurl');
    return apiserverurl;
  }

  static getRestPathurl(String? path) {
    return getServerurl() + apiurl + path;
  }

//agile quest api

  static Future<String> getAqServerurl() async {
    String serverurl = await SharedPrefUtils.readPrefStr(ConstHelper.aqbaseurl);
    // debugPrint('Fetching   $serverurl');
    return serverurl;
  }

  static Future<String> getAqResturl(String path) async {
    return '${await getAqServerurl()}$path';
  }

  static Future<String> getAqRestPathurl(String path) async {
    debugPrint("getAqRestPathurl>>>>>>${await getAqServerurl()}aq-api$path");

    return '${await getAqServerurl()}aq-api$path';
  }

//------------------------------
  //GET
  static Future<dynamic> get(String? apival) async {
    String? resMap;
    Map<String, String> headers = SharedPrefUtils.getHeaders();
    debugPrint('$headers');
    String url = getRestPathurl(apival);
    debugPrint(url);
    var uri = Uri.parse(url);
    try {
      //checkSSL(url);
      // check(url, pslData.allowedSHAFingerprint, pslData.sha, headers, TIME_OUT_DURATION);
      //recordMap = new Map();
      debugPrint('-calling Api-');
      var response = await http.get(uri, headers: headers).timeout(Duration(seconds: ConstHelper.TIME_OUT_DURATION));
      // _processResponse(response);
      debugPrint('-response code- ${response.statusCode}');
      if (response.statusCode == 200) {
        //debugPrint(response.body);
        resMap = utf8.decode(response.bodyBytes);
      }
      return resMap;
    } on SocketException {
      throw FetchDataException('No Internet connection', uri.toString());
    } on TimeoutException {
      throw ApiNotRespondingException('API not responded in time', uri.toString());
    } on Exception {
      throw Exception();
    }
  }

  //POST
  static Future<dynamic> post(String? apival,
      {dynamic payloadObj, String? type, String? queryparams, Map<String, String>? data}) async {
    String? resMap;
    Map<String, String> headers = SharedPrefUtils.getHeaders();

    String url = getRestPathurl(apival);
    debugPrint(url);
    var uri = Uri.parse(url);
    //var payload = json.encode(payloadObj);
    try {
      if (type != null && type == 'form') headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';
      debugPrint('$headers');
      debugPrint('$payloadObj');
      debugPrint('-calling Api-');
      var response = await http
          .post(
            uri,
            headers: headers,
            body: payloadObj,
          )
          .timeout(Duration(seconds: ConstHelper.TIME_OUT_DURATION));
      //_processResponse(response);
      debugPrint('-response code- ${response.statusCode}');
      if (response.statusCode == 200) {
        // debugPrint(response.body);
        resMap = response.body;
      }
      return resMap;
    } on SocketException {
      throw FetchDataException('No Internet connection', uri.toString());
      // } on TimeoutException {
      //   throw ApiNotRespondingException('API not responded in time', uri.toString());
    } on Exception {
      throw Exception();
    }
  }

  static Future<dynamic> authpost(String apival,
      {dynamic payloadObj, String? type, String? queryparams, Map<String, String>? data}) async {
    String? resMap;
    Map<String, String> headers = SharedPrefUtils.getHeaders();

    String url = getServerurl() + apival;
    debugPrint(url);
    var uri = Uri.parse(url);
    //var payload = json.encode(payloadObj);
    try {
      if (type != null && type == 'form') headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';
      debugPrint('$headers');
      debugPrint('$payloadObj');
      debugPrint('-calling Api-');
      var response = await http
          .post(
            uri,
            headers: headers,
            body: payloadObj,
          )
          .timeout(Duration(seconds: ConstHelper.TIME_OUT_DURATION));
      //_processResponse(response);
      debugPrint('-response code- ${response.statusCode}');
      if (response.statusCode == 200) {
        debugPrint(response.body);
        resMap = response.body;
      }
      return resMap;
    } on SocketException {
      throw FetchDataException('No Internet connection', uri.toString());
    } on TimeoutException {
      throw ApiNotRespondingException('API not responded in time', uri.toString());
    } on Exception {
      throw Exception();
    }
  }

  static Future<dynamic> postuploaddata({
    String? apival,
    List<String>? paths,
    String? imgfrom,
    var camFile,
  }) async {
    Map<String, String> headers = SharedPrefUtils.getHeaders();
    String url = getRestPathurl(apival);
    debugPrint(url);
    var uri = Uri.parse(url);

    http.MultipartRequest request = http.MultipartRequest('POST', uri);
    request.headers.addAll(headers);
    if (imgfrom == 'camera') {
      request.files.add(await http.MultipartFile.fromPath('files', camFile));
    } else {
      for (String path in paths!) {
        request.files.add(await http.MultipartFile.fromPath('files', path));
      }
    }

    try {
      final response = await request.send();
      return "success";
    } catch (e) {
      debugPrint("Caught $e");
      return 'error';
    }
  }

  static Future<dynamic> postuploadfiles({String? apival, List<String>? paths, String? imgfrom, var camFile}) async {
    Map<String, String> headers = SharedPrefUtils.getHeaders();
    String url = getRestPathurl(apival);
    debugPrint(url);
    var uri = Uri.parse(url);

    http.MultipartRequest request = http.MultipartRequest('POST', uri);
    request.headers.addAll(headers);
    if (imgfrom == 'camera') {
      request.files.add(await http.MultipartFile.fromPath('files', camFile));
    } else {
      for (String path in paths!) {
        request.files.add(await http.MultipartFile.fromPath('files', path));
      }
    }

    Future<http.StreamedResponse> response = request.send();
    debugPrint("Subscribed!");
    try {
      response.asStream().listen((streamedResponse) {
        debugPrint("Received streamedResponse.statusCode:${streamedResponse.statusCode}");
        streamedResponse.stream.listen((data) {
          debugPrint("Received data:$data");
        });
      });
    } catch (e) {
      debugPrint("Caught $e");
    }

    return "success";
  }

  static Future<void> logout() async {
    try {
      Map<String, String> headers = SharedPrefUtils.getHeaders();
      String url = ApiService.getServerurl() + logouturl;
      debugPrint('url-----------$url');
      var uri = Uri.parse(url);
      await http.get(uri, headers: headers).then((response) async {
        debugPrint('${response.statusCode}');

        if (response.statusCode == 200) {
          debugPrint('---------success logout----------');
        }
      }).catchError((err) {});
    } finally {
      debugPrint('---------logout----------');
    }
  }

  static dynamic _processResponse(response) {
    switch (response.statusCode) {
      case 200:
      case 201:
        return response.data;
      case 400:
        throw BadRequestException(utf8.decode(response.bodyBytes));
      case 401:
      case 403:
        throw UnAuthorizedException(utf8.decode(response.bodyBytes));
      case 500:
      default:
        throw FetchDataException('Error occured with code : ${response.statusCode}');
    }
  }

  static Future<String?> mapiconapi(String? entitytype, String brandid) async {
    return getRestPathurl('/icon/$entitytype?$brandid');
  }
}

class AppException implements Exception {
  final String? message;
  final String? prefix;
  final String? url;

  AppException([this.message, this.prefix, this.url]);
}

class BadRequestException extends AppException {
  BadRequestException([String? message, String? url]) : super(message, 'Bad Request', url);
}

class FetchDataException extends AppException {
  FetchDataException([String? message, String? url]) : super(message, 'Unable to process', url);
}

class ApiNotRespondingException extends AppException {
  ApiNotRespondingException([String? message, String? url]) : super(message, 'Api not responded in time', url);
}

class UnAuthorizedException extends AppException {
  UnAuthorizedException([String? message, String? url]) : super(message, 'UnAuthorized request', url);
}

// class _PinSSL {
//   String serverURL = '';
//   HttpMethod httpMethod = HttpMethod.Get;
//   Map<String, String> headerHttp = new Map();
//   String allowedSHAFingerprint = '';
//   int timeout = 0;
//   SHA sha;
// }

// _PinSSL _data = new _PinSSL();
// Future checkSSL(String requestURL, {BuildContext context}) async {
//   bool checked = false;

//   List<String> allowedShA1FingerprintList = [];
//   allowedShA1FingerprintList.add('067118873d81d884ee6e22ba6bf230418baa801a');
//   try {
//     await SslPinningPlugin.check(
//       serverURL: requestURL,
//       headerHttp: _data.headerHttp,
//       httpMethod: HttpMethod.Get,
//       sha: SHA.SHA1,
//       allowedSHAFingerprints: allowedShA1FingerprintList,
//       timeout: 60,
//     ).then((value) {
//       print(value);
//       if (value == "CONNECTION_SECURE") {
//         checked = true;
//       } else if (value == "CONNECTION_NOT_SECURE") {
//         checked = false;
//       } else {
//         checked = false;
//       }
//     });
//     //print("başarılı");
//     return checked;
//   } catch (e) {
//     print("error SSL");
//   }
// }

// class _PiningSslData {
//   String serverURL = '';
//   Map<String, String> headerHttp = new Map();
//   String allowedSHAFingerprint = ConstHelper.fingerprint;
//   int timeout = 0;
//   SHA sha;
// }

// _PiningSslData pslData = new _PiningSslData();

// Future check(String url, String fingerprint, SHA sha, Map<String, String> headerHttp, int timeout) async {
//   List<String> allowedShA1FingerprintList = [];
//   allowedShA1FingerprintList.add(fingerprint);

//   try {
//     // Platform messages may fail, so we use a try/catch PlatformException.
//     String checkMsg = await HttpCertificatePinning.check(
//         serverURL: url, headerHttp: headerHttp, sha: sha, allowedSHAFingerprints: allowedShA1FingerprintList, timeout: timeout);

//     debugPrint(checkMsg);
//   } catch (e) {
//     debugPrint(e);
//   }
// }
