import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/models/ta_admin/page_rules.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../request_api_utils.dart';
import 'dbdata_utils.dart';

class AppLabels {
  String labelkey;
  AppLabels(this.labelkey);

  static Future<Map<String, dynamic>?> loadlabels(String key, {String? filtertype, int? size}) async {
    Map<String, dynamic>? resMap;
    //List<Applabel> labels;
    String apival = '$labelsurl?page=$key';
    if (!(filtertype != null && filtertype == 'page')) apival = '$apival&type=tab';
    try {
      debugPrint('apival---------$apival');

      dynamic res = await ApiService.get(apival);

      resMap = jsonDecode(res);

      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          // var tagObjsJson = resMap['labels'] as List;
          // if (tagObjsJson != null) {
          //   labels = tagObjsJson.map((tagJson) => Applabel.fromJson(tagJson)).toList();
          //   debugPrint('labels  Count------ ${labels.length}');
          return resMap;
          // }
        }
      }
    } catch (e) {
      debugPrint('$e');
    }
    return null;
  }

  static Future loadmetada(String key, String refr, {String? filtertype, int? size, String? ruletype}) async {
    Map<String, dynamic>? resMap;
    List<Applabel>? labels;
    int count = 0;
    if (refr != 'home') {
      count = await (DBDataProvider.db.refrRowCount(refr));
      debugPrint('$refr labels count     $count');
    }
    if (count == 0) {
      resMap = await loadlabels(key, filtertype: filtertype, size: size);
      if (resMap != null) {
        var labelObj = resMap['labels'] as List?;
        if (labelObj != null) {
          labels = labelObj.map((tagJson) => Applabel.fromJson(tagJson)).toList();
        }

        if (labels != null) {
          await DBDataProvider.db.loadlabelstodb(labels, refr);
        }
        if (ruletype != null) await getrules(resMap, ruletype);
      }
    }
  }

  static Future getrules(Map<String, dynamic> resMap, String ruletype) async {
    try {
      var rulesObj = resMap['rules'][ruletype] as dynamic;

      if (rulesObj != null) {
        ReservationRules rr = ReservationRules.fromJson(rulesObj);
        debugPrint('rulesObj     $rulesObj');
        await DBDataProvider.db.setreservationRules(rr);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  static clearolddata() async {
    await DBDataProvider.db.clearlabeltabeldata('label');
  }

  static Future<List<Applabel>?> loadlabelsfromdb(String parentkey, {String? filtertype}) async {
    try {
      var res = <Map<String, dynamic>>[];
      res = await DBDataProvider.db.getLabels(parentkey, filtertype: filtertype) ?? [];
      debugPrint('loadlabelsfromdb >>>>>>');
      debugPrint('count   ${res.length}');
      return res.map((tagJson) => Applabel.forJson(tagJson)).toList() ?? [];
    } catch (e) {
      debugPrint('loadlabelsfromdb >>>>>> $e');
    }
    return null;
  }

  static Future<Applabel?> getlabelfromdb(String key) async {
    var res = await DBDataProvider.db.getItem(key);
    if (res != null) {
      debugPrint('count   ${res.length}');
      Applabel al = Applabel(
          key: res['key'], value: res['value'], edit: res['edit'], req: res['req'], parentkey: res['parentkey']);
      return al;
    }
    return null;
  }
}
