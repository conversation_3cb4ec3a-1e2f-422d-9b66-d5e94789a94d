import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';

import '../../models/agilequest/auth/aq_languagecodes_data.dart';
import '../../models/ta_admin/app_label.dart';
import '../../models/ta_admin/page_rules.dart';
import '../../providers/ta_admin/label_controller.dart';

class DBDataProvider {
  DBDataProvider._();
  static final DBDataProvider db = DBDataProvider._();
  LabelController talabel = Get.find<LabelController>();

  Box? label_box;
  Box? label_ind_box;
  Box? rule_box;
  Box? aq_langcode_box;

  Future<void> get database async {
    if (!label_box!.isOpen) await initDB();
  }

  initDB() async {
    // var path = Directory.current.path;

    label_box = await Hive.openBox('LABEL');
    label_ind_box = await Hive.openBox('LABEL_INDEX');
    rule_box = await Hive.openBox('RULE_BOX');
    aq_langcode_box = await Hive.openBox('AQ_LANGCODE_BOX');
  }

  Future<void> clearlabeltabeldata(String box) async {
    if (box == 'label' || box == 'aqlangcodes') {
      await label_box?.clear();
      await label_ind_box?.clear();
      await aq_langcode_box?.clear();
    }
  }

  loadlabelstodb(List<Applabel> labels, String refr) async {
    if (!kIsWeb && !Hive.isBoxOpen('LABEL')) {
      Hive.init((await getApplicationDocumentsDirectory()).path);
    }
    label_box = await Hive.openBox('LABEL');
    label_ind_box = await Hive.openBox('LABEL_INDEX');
    rule_box = await Hive.openBox('RULE_BOX');
    Map<String, String?> data;
    labels.forEach((Applabel label) async {
      data = {
        'key': label.key,
        'value': label.value,
        'edit': label.edit ?? 'FALSE',
        'req': label.req ?? 'FALSE',
        'parentkey': label.parentkey,
        'refr': refr
      };

      await label_box?.put(label.key, data);
      await label_ind_box?.put(refr, label.key);
    });
  }

  Future<Map<dynamic, dynamic>?> getItem(String key) async {
    await database;
    var res = await label_box?.get(key);
    return res;
  }

  Future<List<Map<String, dynamic>>> getLabels(String key, {String? filtertype}) async {
    try {
      //await database;
      List? allKeys = label_box?.keys.toList();

      List skeys = allKeys!.where((w) {
        w as String;
        if (w.startsWith(key)) {
          return true;
        } else {
          return false;
        }
      }).toList();

      List<Map<String, dynamic>>? labels = <Map<String, dynamic>>[];
      Map<String, dynamic> v;
      var t;
      for (String k in skeys) {
        if (k.startsWith(key)) {
          // debugPrint('k-------$k');
          t = label_box?.get(k);
          v = Map.castFrom(t);
          //  debugPrint('v-------$v');
          labels.add(v);
        }
      }
      return labels;
    } catch (e) {
      debugPrint('loadlabelsfromdb >>>>>> $e');
      return <Map<String, dynamic>>[];
    }
  }

  Future<int> refrRowCount(String refr) async {
    await database;
    if (label_ind_box!.isNotEmpty && label_ind_box?.get(refr) != null) {
      return 2;
    }
    return 0;
  }

  Future<int?> delete(int id) async {
    await database;
    return await label_box?.clear();
  }

  Future<void> setreservationRules(ReservationRules rr) async {
    talabel.reservationrules.value = rr;
    debugPrint('setreservationRules---    ${talabel.reservationrules.value.toJson()}');
  }

  loadaqlangcodestodb(List<AqLanguageCodesData> codes) async {
    try {
      if (!kIsWeb && !Hive.isBoxOpen('AQ_LANGCODE_BOX')) {
        Hive.init((await getApplicationDocumentsDirectory()).path);
      }
      aq_langcode_box = await Hive.openBox('AQ_LANGCODE_BOX');

      codes.forEach((AqLanguageCodesData cd) async {
        await aq_langcode_box?.put(cd.key, cd.tag);
      });
    } catch (e) {
      debugPrint('loadlangcodestodb>>>>>>>$e');
    }
  }

  Future<String> getaqlangcode(String key) async {
    await database;
    var res = await aq_langcode_box?.get(key);
    return res ?? '';
  }
}
