import 'dart:async';

import 'package:get/get.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/models/ta_admin/page_rules.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

class DBProvider {
  DBProvider._();
  static final DBProvider db = DBProvider._();
  LabelController talabel = Get.find<LabelController>();

  sqflite.Database? _database;
  static var LABEL_TABLE = 'labels';
  static var RULES_TABLE = 'rules';

  Future<sqflite.Database?> get database async {
    if (_database != null) return _database;

    _database = await initDB();
    return _database;
  }

  initDB() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, "twslabels.db");
    return await sqflite.openDatabase(path, version: 1, onOpen: (db) {},
        onCreate: (sqflite.Database db, int version) async {
      await db.execute("CREATE TABLE $LABEL_TABLE ("
          "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
          "key TEXT,"
          "value TEXT,"
          "edit TEXT,"
          "req TEXT,"
          "parentkey TEXT,"
          "refr TEXT"
          ")");
    });
  }

  Future<int?> clearlabeltabeldata() async {
    sqflite.Database? db = await (database);
    return await db?.delete(LABEL_TABLE);
  }

  loadlabelstodb(List<Applabel> labels, String refr) async {
    final db = await database;
    Map<String, String?> data;
    labels.forEach((Applabel label) async {
      data = {
        'key': label.key,
        'value': label.value,
        'edit': label.edit ?? 'FALSE',
        'req': label.req ?? 'FALSE',
        'parentkey': label.parentkey,
        'refr': refr
      };
      await db!.insert(LABEL_TABLE, data);
    });
  }

  Future<List<Map<String, dynamic>>?> getItem(String key) async {
    final db = await (database);
    var res = await db?.query(LABEL_TABLE, where: "key = ?", whereArgs: [key], limit: 1);
    return res;
  }

  Future<List<Map<String, dynamic>>> getLabels(String key, {String? filtertype}) async {
    final db = await database;
    if (filtertype != null && filtertype == 'page') {
      return db!.query(LABEL_TABLE, where: "parentkey = ?", whereArgs: [key]);
    }
    return db!.rawQuery('SELECT * FROM $LABEL_TABLE WHERE key LIKE "$key%"');
  }

  Future<int?> refrRowCount(String refr) async {
    sqflite.Database? db = await (database);
    return sqflite.Sqflite.firstIntValue(await db!.rawQuery('SELECT COUNT(*) FROM $LABEL_TABLE WHERE refr ="$refr"'));
  }

  Future<int?> delete(int id) async {
    sqflite.Database? db = await (database);
    return await db?.delete(LABEL_TABLE);
  }

  Future<void> setreservationRules(ReservationRules rr) async {
    talabel.reservationrules.value = rr;
    debugPrint('setreservationRules---    ${talabel.reservationrules.value.toJson()}');
  }
}
