import 'package:flutter/material.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

ThemeData TaLightTheme() {
  return ThemeData(
    useMaterial3: false,
    primarySwatch: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
    primaryColor: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
    appBarTheme: AppBarTheme(
      backgroundColor: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
      titleTextStyle: TextStyle(color: CommonUtils.createMaterialColor(const Color(0XFFffffff))),
    ),
    scaffoldBackgroundColor: CommonUtils.createMaterialColor(const Color(0XFFf2f2f2)),
    // textTheme: TextTheme(

    // ),
  );

  // CheckboxTheme _tacheckBox(CheckboxTheme base){
  //   return base.c;
  // }

  // final ThemeData base = ThemeData.light();

  // return base.copyWith(
  //   // textTheme: _basicTextTheme(base.textTheme),
  //   //textTheme: Typography().white,

  //   primaryColor: HexColor('#B10C00'),
  //   appBarTheme: AppBarTheme(
  //     backgroundColor: HexColor('#B10C00'),
  //   ),

  //   //primaryColor: Color(0xff4829b2),
  //   indicatorColor: HexColor('#B10C00'),
  //   scaffoldBackgroundColor: HexColor('#f2f2f2'),

  //   //accentColor: Color(0xFFFFF8E1),
  //   // iconTheme: IconThemeData(
  //   //   color: Colors.white,
  //   //   size: 20.0,
  //   // ),
  //   // buttonColor: Colors.white,
  //   // backgroundColor: Colors.white,
  //   // tabBarTheme: base.tabBarTheme.copyWith(
  //   //   labelColor: Color(0xffce107c),
  //   //   unselectedLabelColor: Colors.grey,
  //   // )
  // );
}

// TextTheme taTextTheme(TextTheme base) {
//     return base.copyWith(
//         headline1: base.headline1.copyWith(
//           fontFamily: 'Roboto',
//           fontSize: 22.0,
//           color: Colors.black,
//         ),
//         title: base.title.copyWith(
//           fontFamily: 'Merriweather',
//           fontSize: 15.0,
//           color: Colors.green
//         ),
//         display1: base.headline.copyWith(
//           fontFamily: 'Roboto',
//           fontSize: 24.0,
//           color: Colors.white,
//         ),
//         display2: base.headline.copyWith(
//           fontFamily: 'Merriweather',
//           fontSize: 22.0,
//           color: Colors.grey,
//         ),
//         caption: base.caption.copyWith(
//           color: Color(0xFFCCC5AF),
//         ),
//         body1: base.body1.copyWith(color: Color(0xFF807A6B)));
//   }

class HexColor extends Color {
  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor";
    }
    return int.parse(hexColor, radix: 16);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));
}
