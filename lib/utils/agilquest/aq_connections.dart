class AqConn {
  static String appLoginUrl = 'auth/application/login';
  static String userLoginUrl = 'auth/login';
  static String appPropertiesUrl = 'aq-api/properties/application-properties';
  static String languageCodesUrl = 'aq-api/languages/bundles/';
  static String currencyTypeUrl = 'aq-api/forums/currency-types';

  //RESERVATION
  static String reserveViewsUrl = 'aq-api/reservations/views';

  //LOCATION
  static String locationsSearchUrl = 'aq-api/locations';

  //CATEGORIES
  static String resourceCategoriesUrl = 'aq-api/resources/categories';

  //DELEGATES
  static String delegatesUsersUrl = 'aq-api/users/profiles/delegate-clients';

  //Resrvation status change
  static String reservationChangeStatusUrl = 'aq-api/reservations/states';

  //Cancel Resrvation date range
  static String reservationGapsUrl = 'aq-api/reservations/gaps';

  //delete Draft Reservation
  static String deleteDraftReserveUrl = 'aq-api/reservations/drafts/';

  //RESOURCES
  static String resorceUrl = 'aq-api/resources/';

  //EVENTS
  static String getEventUrl = 'aq-api/reservations/events/';

  //REVIEW
  static String getReviews = 'aq-api/reviews/views';
  static String deleteReview = 'aq-api/reviews/';
  static String createReview = 'aq-api/reviews';
  static String updateReview = 'aq-api/reviews';

  // USER
  static String searchByName = 'aq-api/users/views/search/by-name';
}
