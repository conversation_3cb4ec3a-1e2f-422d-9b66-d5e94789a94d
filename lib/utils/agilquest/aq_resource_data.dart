class AqText {
  static String app_name = "Reserve";
  static String thanks_title = "Thank You!";
  static String password_reset_result_title = "A password reset email will be sent to the email address entered.";
  static String btn_title_check_in = "Check In";
  static String btn_delete = "Delete";
  static String btn_title_check_out = "Check Out";
  static String btn_title_end = "End";
  static String btn_title_cancel = "Cancel";
  static String btn_title_copy = "Copy";
  static String loc_at = "at";
  static String save = "Save";
  static String loc_multiple = "multiple";
  static String btn_title_view_event = "View Event";
  static String state_awaiting_decision = "Request is Awaiting Decision";
  static String state_awaiting_check_in = "Awaiting Check In";
  static String state_pending = "Pending";
  static String state_active = "Active";
  static String state_checked_in = "Checked In";
  static String state_request_denied = "Request Denied";
  static String state_expired = "Reservation Expired";
  static String state_bumped = "Reservation Canceled by Administrator";
  static String state_canceled = "Reservation Canceled";
  static String state_checked_out = "Reservation is Checked Out";
  static String state_ended = "Reservation Ended";
  static String state_canceled_within_lead = "Reservation Canceled Within Lead Time";
  static String state_multiasset_reserve_awaiting_decision = "Request with Multiple Assets is Awaiting Reservation Decision";
  static String state_draft_reservation = "Draft Reservation";
  static String state_recurring_reservation = "Recurring Reservation";
  static String dialog_check_in_confirmation = "Would you like to Start the Reservation Now?";
  static String label_search_where = "WHERE";
  static String search_form_title = "Search for Assets";
  static String label_search_what = "WHAT";
  static String label_search_start = "START";
  static String label_search_end = "END";
  static String label_search_when = "WHEN";
  static String btn_title_done = "Done";
  static String btn_title_search = "Search";
  static String estimated_price_details_title = "HOW IS THE ESTIMATED PRICE CALCULATED?";
  static String select_time_zone = "Select Time Zone";
  static String select_btn = "Select";
  static String reservations = "Reservations";
  static String presence = "Presence";
  static String no_results = "No Results Were Found";
  static String reservation_title_name = "Reservation Name";
  static String reservation_title_description = "Description";
  static String reservation_title_owner = "Reservation Owner";
  static String reservation_title_numberOfAttendee = "Number of People Expected to Attend";
  static String reservation_title_quantity = "Quantity";
  static String reservation_title_delivery_location = "Delivery Location";
  static String related_assets_title = "Related Assets";
  static String reservation_private = "Private";
  static String reservation_not_private = "Not Private";
  static String log_out_message = "Are you sure you want to Log Out?";
  static String log_out = "Log out";
  static String about = "About";
  static String home_page = "Home";
  static String status_inactive = "Inactive";
  static String status_draft = "Draft";
  static String status_active = "Active";
  static String dialog_cancel_reservation_confirmation = "Do you want to Cancel this Reservation";
  static String dialog_check_in_reservation_confirmation = "Would you like to Start the Reservation Now?";
  static String dialog_draft_delete_confirmation = "Do you want to Delete this Draft Reservation?";
  static String image = "Image";
  static String hello_blank_fragment = "Hello blank fragment";
  static String saml_url_title = "IdP URL";
  static String saml_url_description = "SAML identity provider URL";
  static String button_discolsure = "Expand";
  static String button_delete = "Delete";
  static String button_close = "Close";
  static String button_menu = "Menu Button";
  static String button_left = "Menu Button";
  static String button_right = "Menu Button";
  static String button_info = "Info Button";
  static String button_back = "Back Button";
  static String button_switch = "Switch Button";
  static String button_add = "Add Button";
  static String locations_txt = "Location(s)";
  static String actions_can_taken = "Actions that can be Taken";
  static String activity = "Activity";
  static String start_end_date_time = "Start and End Date/Time";
  static String comments = "Comments";
  static String shortDateTimeFormat = "MM/dd/yyyy hh:mm a";
  static String fullDateTimeFormat = "MMM dd, yyyy hh:mm a";
  static String timeFormat = "hh:mm a";
  static String fullDateFormat = "MMM dd, yyyy";
  static String shortDateFormat = "MM/dd/yyyy";
  static String noEndDate = "No End Date";
  static String invitess = "Invitees";


  //ERROR
  static String errorDateTimeEndBeforeStart = 'The End Date/Time must be after the Start Date/Time.';
  static String errorSearchInvitessEmptyText = 'Please enter user\'s first and/or last name.';
}
