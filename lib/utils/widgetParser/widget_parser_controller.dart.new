import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import 'dart:convert';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/widgetParser/json_form_response.dart';
import 'package:tangoworkplace/utils/widgetParser/json_section_widget.dart';
import 'package:tangoworkplace/utils/widgetParser/json_tab_widget.dart';
import 'package:tangoworkplace/utils/widgetParser/json_widget.dart';
import 'package:tangoworkplace/utils/widgetParser/json_widget_types.dart';

class WidgetParserController extends GetxController {
  RxMap<String, Object> jsonData = <String, Object>{}.obs;
  Rx<JsonFormResponse?> formResponse = Rx<JsonFormResponse?>(null);
  RxInt currentTabIndex = 0.obs;
  RxList<JsonWidget> jwidgetList = <JsonWidget>[].obs;
  var isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    debugPrint('Initializing WidgetParserController');
    // Add a small delay to ensure all dependencies are initialized
    Future.delayed(const Duration(milliseconds: 100), () {
      getWidgetJsonData();
    });
  }

  final List<TextEditingController> textEditControllers = [];

  Widget parseJson() {
    debugPrint('=====================================================');
    debugPrint('PARSING JSON TO WIDGETS - ULTRA SIMPLIFIED');
    debugPrint('ONLY handling tabs and columns for stability');

    // Handle the case when we have a form response with tabs
    if (formResponse.value != null &&
        formResponse.value?.componentList?.tabs != null &&
        formResponse.value!.componentList!.tabs!.isNotEmpty) {
      final tabs = formResponse.value!.componentList!.tabs!;
      debugPrint('Using tab-based format with ${tabs.length} tabs');
      
      return DefaultTabController(
        length: tabs.length,
        child: Scaffold(
          appBar: AppBar(
            bottom: TabBar(
              onTap: (index) {
                currentTabIndex.value = index;
              },
              isScrollable: true,
              tabs: tabs.map((tab) => Tab(text: tab.label ?? 'Tab')).toList(),
            ),
            title: const Text('Form'),
          ),
          body: TabBarView(
            children: tabs.map((tab) {
              // Ultra simplified tab content - just columns of text
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Tab header
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Text(
                        'Tab: ${tab.label ?? "Unnamed"}',
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ),
                    
                    // Process each section as a simple card with fields
                    ...?tab.sections?.map((section) {
                      // Skip empty sections
                      if (section.fields == null || section.fields!.isEmpty) {
                        return const SizedBox.shrink();
                      }
                      
                      return Card(
                        margin: const EdgeInsets.only(bottom: 16.0),
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Section header
                              if (section.label != null && section.label!.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 12.0),
                                  child: Text(
                                    section.label!,
                                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                  ),
                                ),
                              // Ultra simplified field rendering - basic containers
                              ...?section.fields?.map((field) {
                                return Container(
                                  margin: const EdgeInsets.symmetric(vertical: 4.0),
                                  padding: const EdgeInsets.all(8.0),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey.shade300),
                                    borderRadius: BorderRadius.circular(4.0),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Label
                                      if (field.text != null && field.text!.isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Text(
                                            field.text!,
                                            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                                          ),
                                        ),
                                      // Value - ultra simple toString
                                      Text(
                                        field.value?.toString() ?? '',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    ],
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      );
    }
    // Handle the legacy case (flat list of widgets)
    else if (jwidgetList.isNotEmpty) {
      debugPrint('Using legacy flat list format with ${jwidgetList.length} widgets - simplified');

      return Scaffold(
        appBar: AppBar(title: const Text('Form')),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: jwidgetList.map((widget) {
              // Ultra simplified field rendering
              return Container(
                margin: const EdgeInsets.symmetric(vertical: 4.0),
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Label
                    if (widget.text != null && widget.text!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 4.0),
                        child: Text(
                          widget.text!,
                          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                        ),
                      ),
                    // Value
                    Text(
                      widget.value?.toString() ?? '',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      );
    }
    // No data case
    else {
      debugPrint('No form data available to display');
      return const Center(child: Text('No form data available'));
    }
  }

  Widget buildWidget(JsonWidget jsonWidget) {
    debugPrint('=====================================================');
    debugPrint('BUILDING WIDGET - ULTRA SIMPLIFIED:');
    debugPrint('Type: ${jsonWidget.widgetType}');
    final widgetType = JsonWidgetTypeHelper.getType(jsonWidget.widgetType ?? 'text');
    
    // Ultra simplified widget building - only tabs and columns
    Widget resultWidget = const SizedBox.shrink(); // Default empty widget
    
    // Only support these specific core structural widget types
    switch (widgetType) {
      case JsonWidgetType.tabView:
        if (jsonWidget.children != null && jsonWidget.children!.isNotEmpty) {
          debugPrint('Building tab view with ${jsonWidget.children!.length} tabs');
          resultWidget = _buildTabViewWidget(jsonWidget);
        } else {
          debugPrint('Empty tab view - rendering placeholder');
          resultWidget = const Center(child: Text('Empty Tab View'));
        }
        break;
        
      case JsonWidgetType.tabBar:
        if (jsonWidget.children != null && jsonWidget.children!.isNotEmpty) {
          debugPrint('Building tab bar with ${jsonWidget.children!.length} tabs');
          resultWidget = _buildTabBarWidget(jsonWidget);
        } else {
          debugPrint('Empty tab bar - rendering placeholder');
          resultWidget = const SizedBox.shrink();
        }
        break;
        
      case JsonWidgetType.column:
        if (jsonWidget.children != null && jsonWidget.children!.isNotEmpty) {
          debugPrint('Building column with ${jsonWidget.children!.length} children');
          resultWidget = _buildColumnWidget(jsonWidget);
        } else {
          debugPrint('Empty column - rendering placeholder');
          resultWidget = const SizedBox.shrink();
        }
        break;
        
      // For text content - ultra simple
      case JsonWidgetType.textView:
        debugPrint('Building text: "${jsonWidget.text?.substring(0, math.min(jsonWidget.text?.length ?? 0, 30)) ?? ''}"${(jsonWidget.text?.length ?? 0) > 30 ? "..." : ""}');
        resultWidget = Text(
          jsonWidget.text ?? '',
          style: TextStyle(fontSize: jsonWidget.fontSize?.toDouble()),
        );
        break;
        
      // For all other widget types, just display text
      default:
        String typeText = jsonWidget.widgetType ?? 'unknown';
        debugPrint('Unsupported widget type: $typeText - rendering as placeholder');
        
        if (jsonWidget.text != null && jsonWidget.text!.isNotEmpty) {
          resultWidget = Text(jsonWidget.text ?? '');
        } else {
          resultWidget = Text('Unsupported: $typeText');
        }
        break;
    }
    
    debugPrint('=====================================================');
    return resultWidget;
  }

  // Simple widget for rendering any child
  Widget _buildSimpleWidget(JsonWidget jsonWidget) {
    // Simple rendering for any child widget type
    if (jsonWidget.text != null && jsonWidget.text!.isNotEmpty) {
      return Text(jsonWidget.text ?? '');
    } else if (jsonWidget.value != null) {
      return Text(jsonWidget.value.toString());
    } else {
      return const SizedBox.shrink();
    }
  }

  // SIMPLIFIED COLUMN WIDGET - the only structural widget we're fully supporting
  Widget _buildColumnWidget(JsonWidget jsonWidget) {
    debugPrint('=====================================================');
    debugPrint('BUILDING COLUMN WIDGET (ULTRA SIMPLIFIED):');
    
    // Default alignments - skipping complex parsing
    const mainAxisAlignment = MainAxisAlignment.start;
    const crossAxisAlignment = CrossAxisAlignment.start;
    
    // Log children information
    if (jsonWidget.children != null) {
      debugPrint('Child count: ${jsonWidget.children!.length}');
      // Only log first few children to avoid verbosity
      int logLimit = math.min(jsonWidget.children!.length, 3);
      for (int i = 0; i < logLimit; i++) {
        JsonWidget child = jsonWidget.children![i];
        debugPrint('Child $i: type=${child.widgetType}, text="${child.text ?? ''}"');
      }
      if (jsonWidget.children!.length > logLimit) {
        debugPrint('... and ${jsonWidget.children!.length - logLimit} more children');
      }
    } else {
      debugPrint('No children - returning empty container');
    }

    debugPrint('=====================================================');

    // For simplicity, we'll handle all children as generic widgets with minimal styling
    return jsonWidget.children != null
        ? Column(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            mainAxisSize: MainAxisSize.min,
            children: jsonWidget.children!.map((child) {
              // Just build each child directly
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: _buildSimpleWidget(child),
              );
            }).toList(),
          )
        : Container(
            padding: const EdgeInsets.all(8.0),
            child: const Text('Empty column', style: TextStyle(color: Colors.grey)),
          );
  }

  Widget _buildTabViewWidget(JsonWidget jsonWidget) {
    debugPrint('=====================================================');
    debugPrint('BUILDING TAB VIEW WIDGET (SIMPLIFIED):');
    
    if (jsonWidget.children == null || jsonWidget.children!.isEmpty) {
      debugPrint('No tabs found - returning empty container');
      debugPrint('=====================================================');
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: const Text('No tabs', style: TextStyle(color: Colors.grey)),
      );
    }
    
    debugPrint('Building tab view with ${jsonWidget.children!.length} tabs');
    debugPrint('=====================================================');

    return DefaultTabController(
      length: jsonWidget.children!.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Simple tab bar
          TabBar(
            isScrollable: true,
            tabs: jsonWidget.children!.map((tab) {
              return Tab(text: tab.text ?? 'Tab');
            }).toList(),
          ),
          
          // Tab content area
          Expanded(
            child: TabBarView(
              children: jsonWidget.children!.map((tab) {
                // For each tab, render a simple column of content
                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Tab: ${tab.text ?? "Unnamed"}', 
                           style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 16),
                      
                      // If tab has children, display them simply
                      if (tab.children != null && tab.children!.isNotEmpty)
                        ...tab.children!.map((child) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: _buildSimpleWidget(child),
                        )),
                      
                      // Otherwise show a placeholder
                      if (tab.children == null || tab.children!.isEmpty)
                        const Text('No content in this tab', style: TextStyle(color: Colors.grey)),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBarWidget(JsonWidget jsonWidget) {
    debugPrint('=====================================================');
    debugPrint('BUILDING TAB BAR WIDGET (SIMPLIFIED):');
    
    if (jsonWidget.children == null || jsonWidget.children!.isEmpty) {
      debugPrint('No tabs found - returning empty container');
      debugPrint('=====================================================');
      return Container();
    }
    
    debugPrint('Building tab bar with ${jsonWidget.children!.length} tabs');
    debugPrint('=====================================================');

    return TabBar(
      isScrollable: true,
      tabs: jsonWidget.children!.map((tab) => Tab(text: tab.text ?? 'Tab')).toList(),
    );
  }

  // Ultra simplified widget type determination
  String _determineWidgetType(Map<String, dynamic> item) {
    // In the ultra simplified version, we only log basic info
    String label = item['label']?.toString().toLowerCase() ?? '';
    
    debugPrint('=====================================================');
    debugPrint('WIDGET TYPE DETERMINATION (ULTRA SIMPLIFIED):');
    debugPrint('Field: $label');
    debugPrint('Always using simple text display for all field types');
    debugPrint('=====================================================');
    
    return 'taInput'; // Everything is just a simple input in the simplified version
  }

  // Fetch widget data from API
  Future<void> getWidgetJsonData() async {
    isLoading.value = true;
    debugPrint('=====================================================');
    debugPrint('FETCHING WIDGET JSON DATA:');
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['site_id', 'EXACT', '196819'],
      };
      var filter = CommonServices.getFilter(dynamicMap);
      var url = '$dynamicattributesurl/SITE/DYNAMIC_ATTRIBUTES';
      debugPrint('Request URL: $url');

      var resMap = await ApiService.post(url, payloadObj: filter);
      debugPrint('API response received: ${resMap != null ? 'success' : 'null'}');

      if (resMap != null) {
        try {
          debugPrint('PARSING JSON RESPONSE:');
          // Check if resMap is already a Map or if it's a String that needs to be decoded
          if (resMap is String) {
            debugPrint('Response is a String, decoding to JSON...');
            resMap = jsonDecode(resMap);
          }

          var status = resMap['status'] as int?;
          debugPrint('Response status: $status');
          if (status == 0) {
            // Handle the case when componentList is directly a Map (tab-based format)
            if (resMap['componentList'] is Map) {
              debugPrint('CASE: componentList is a Map - using tab-based format');
              // Create a temporary JsonFormResponse
              Map<String, dynamic> tempResponse = {
                'status': resMap['status'],
                'statusmessage': resMap['statusmessage'],
                'id': resMap['id'],
                'componentList': resMap['componentList']
              };

              // Process the response into our model
              formResponse.value = _processFormResponse(tempResponse);
              debugPrint('Form response processed with ${formResponse.value?.componentList?.tabs?.length ?? 0} tabs');
            }
            // Handle the legacy case (list of fields)
            else if (resMap['componentList'] is List) {
              var tagObjsJson = resMap['componentList'] as List;
              debugPrint('CASE: componentList is a List - using legacy flat list format with ${tagObjsJson.length} fields');

              jwidgetList.value = tagObjsJson.map((e) {
                Map<String, dynamic> item = e as Map<String, dynamic>;
                
                // Simplified transformation to JsonWidget structure
                return JsonWidget.fromJson({
                  'widgetType': 'taInput',
                  'text': item['label'] ?? '',
                  'value': item['val'],
                  'readOnly': true,
                });
              }).toList();
              debugPrint('Legacy format: ${jwidgetList.length} widgets processed');
            } else {
              debugPrint('ERROR: Unexpected componentList format: ${resMap['componentList']?.runtimeType}');
            }
          } else {
            debugPrint('ERROR: API returned non-zero status: $status');
          }
        } catch (e, stackTrace) {
          debugPrint('ERROR: Processing JSON response: $e');
          debugPrint('Stack trace: $stackTrace');
        }
      } else {
        debugPrint('ERROR: API response is null');
      }
    } catch (e, stackTrace) {
      debugPrint('ERROR: API request failed: $e');
      debugPrint('Stack trace: $stackTrace');
    } finally {
      isLoading.value = false;
      debugPrint('=====================================================');
    }
  }

  // Process the form response data
  JsonFormResponse _processFormResponse(Map<String, dynamic> jsonData) {
    debugPrint('=====================================================');
    debugPrint('PROCESSING FORM RESPONSE:');
    try {
      // Create top-level response
      JsonFormResponse response = JsonFormResponse(
        status: jsonData['status'],
        statusmessage: jsonData['statusmessage'],
        id: jsonData['id'],
      );

      // Process componentList
      var componentListData = jsonData['componentList'];
      if (componentListData is Map) {
        // Convert to the expected Map<String, dynamic> type
        Map<String, dynamic> componentListMap = {};
        componentListData.forEach((key, value) {
          componentListMap[key.toString()] = value;
        });

        ComponentList componentList = ComponentList(
          tabs: [],
        );

        // Process tabs
        List<dynamic> tabsData = componentListMap['tabs'] ?? [];
        debugPrint('Processing ${tabsData.length} tabs');
        List<JsonTabWidget> tabs = [];

        for (var tabData in tabsData) {
          JsonTabWidget tab = JsonTabWidget(
            label: tabData['label'],
            labelKey: tabData['labelKey'],
            sections: [],
          );

          // Process sections
          List<dynamic> sectionsData = tabData['sections'] ?? [];
          debugPrint('Processing ${sectionsData.length} sections in tab ${tabData['label'] ?? 'unnamed tab'}');
          List<JsonSectionWidget> sections = [];

          for (var sectionData in sectionsData) {
            JsonSectionWidget section = JsonSectionWidget(
              label: sectionData['label'],
              labelKey: sectionData['labelKey'],
              fields: [],
            );

            // Process fields - ultra simplified
            List<dynamic> fieldsData = sectionData['fields'] ?? [];
            debugPrint('Processing ${fieldsData.length} fields in section ${sectionData['label'] ?? 'unnamed section'}');
            List<JsonWidget> fields = [];

            for (var fieldData in fieldsData) {
              // All fields become simple taInput in our ultra simplified version
              fields.add(JsonWidget.fromJson({
                'widgetType': 'taInput',
                'text': fieldData['label'] ?? '',
                'value': fieldData['val'],
                'readOnly': true,
              }));
            }

            section.fields = fields;
            sections.add(section);
          }

          tab.sections = sections;
          tabs.add(tab);
        }

        componentList.tabs = tabs;
        response.componentList = componentList;
        debugPrint('Processed ${tabs.length} tabs with sections and fields');
      } else {
        debugPrint('ERROR: Component list is not a Map: ${componentListData.runtimeType}');
      }

      debugPrint('=====================================================');
      return response;
    } catch (e, stackTrace) {
      debugPrint('ERROR: Processing form response: $e');
      debugPrint('Stack trace: $stackTrace');
      debugPrint('=====================================================');
      return JsonFormResponse(
        status: jsonData['status'],
        statusmessage: 'Error: $e',
        id: jsonData['id'],
        componentList: ComponentList(tabs: []),
      );
    }
  }

  // Method that will be called to generate the observation for value change
  void generateValueObserver() {
    // This method is intentionally empty in the simplified version
  }
}
