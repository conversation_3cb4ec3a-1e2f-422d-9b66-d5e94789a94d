
import 'package:json_annotation/json_annotation.dart';

import 'json_widget.dart';

part 'json_drop_down_widget.g.dart';

@JsonSerializable()
class JsonDropDownWidget {
  JsonWidget widget;
  dynamic value; // ta_check_box, ta_drop_down


  JsonDropDownWidget(
      {required this.widget,
      required this.value});

  factory JsonDropDownWidget.fromJson(Map<String, dynamic> json) =>
      _$JsonDropDownWidgetFromJson(json);

  Map<String, dynamic> toJson() => _$JsonDropDownWidgetToJson(this);
}
