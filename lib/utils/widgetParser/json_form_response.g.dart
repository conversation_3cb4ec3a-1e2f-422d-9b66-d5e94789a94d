// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'json_form_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JsonFormResponse _$JsonFormResponseFromJson(Map<String, dynamic> json) => JsonFormResponse(
      status: json['status'] as int?,
      statusmessage: json['statusmessage'] as String?,
      id: json['id'] as int?,
      componentList:
          json['componentList'] == null ? null : ComponentList.fromJson(json['componentList'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$JsonFormResponseToJson(JsonFormResponse instance) => <String, dynamic>{
      'status': instance.status,
      'statusmessage': instance.statusmessage,
      'id': instance.id,
      'componentList': instance.componentList,
    };

ComponentList _$ComponentListFromJson(Map<String, dynamic> json) => ComponentList(
      tabs: (json['tabs'] as List<dynamic>?)?.map((e) => JsonTabWidget.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$ComponentListToJson(ComponentList instance) => <String, dynamic>{
      'tabs': instance.tabs,
    };
