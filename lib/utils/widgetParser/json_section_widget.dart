import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/utils/widgetParser/json_widget.dart';

part 'json_section_widget.g.dart';

@JsonSerializable()
class JsonSectionWidget {
  String? label;
  String? labelKey;
  List<JsonWidget>? fields;

  JsonSectionWidget({
    this.label,
    this.labelKey,
    this.fields,
  });

  factory JsonSectionWidget.fromJson(Map<String, dynamic> json) => _$JsonSectionWidgetFromJson(json);

  Map<String, dynamic> toJson() => _$JsonSectionWidgetToJson(this);
}
