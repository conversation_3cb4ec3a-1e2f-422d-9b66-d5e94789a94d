import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/utils/widgetParser/widget_parser_controller.dart';

import '../../common/progess_indicator_cust.dart';

class WidgetParserEngine extends GetView<WidgetParserController> {
  WidgetParserEngine({
    super.key,
  });

  WidgetParserController wpcState = Get.put(WidgetParserController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: ProgressIndicatorCust());
      }

      final widget = controller.parseJson();
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: widget,
      );
    });
  }
}
