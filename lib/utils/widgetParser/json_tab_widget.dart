import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/utils/widgetParser/json_section_widget.dart';

part 'json_tab_widget.g.dart';

@JsonSerializable()
class JsonTabWidget {
  String? label;
  String? labelKey;
  List<JsonSectionWidget>? sections;

  JsonTabWidget({
    this.label,
    this.labelKey,
    this.sections,
  });

  factory JsonTabWidget.fromJson(Map<String, dynamic> json) => _$JsonTabWidgetFromJson(json);

  Map<String, dynamic> toJson() => _$JsonTabWidgetToJson(this);
}
