import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/utils/widgetParser/json_tab_widget.dart';

part 'json_form_response.g.dart';

@JsonSerializable()
class JsonFormResponse {
  int? status;
  String? statusmessage;
  int? id;
  ComponentList? componentList;

  JsonFormResponse({
    this.status,
    this.statusmessage,
    this.id,
    this.componentList,
  });

  factory JsonFormResponse.fromJson(Map<String, dynamic> json) => _$JsonFormResponseFromJson(json);

  Map<String, dynamic> toJson() => _$JsonFormResponseToJson(this);
}

@JsonSerializable()
class ComponentList {
  List<JsonTabWidget>? tabs;

  ComponentList({
    this.tabs,
  });

  factory ComponentList.fromJson(Map<String, dynamic> json) => _$ComponentListFromJson(json);

  Map<String, dynamic> toJson() => _$ComponentListToJson(this);
}
