import 'package:get/get.dart';

enum JsonWidgetType {
  scaffold('scaffold'),
  textView('text'),
  row('row'),
  column('column'),
  appBar('appBar'),
  taInput('taInput'),
  taDropDown('taDropDown'),
  taCheckBox('taCheckBox');

  final String type;
  const JsonWidgetType(this.type);
}

extension JsonWidgetTypeHelper on JsonWidgetType {
  static JsonWidgetType getType(String type) {
    return JsonWidgetType.values.firstWhereOrNull((element) => element.type.toLowerCase() == type.toLowerCase()) ??
        JsonWidgetType.scaffold;
  }
}
