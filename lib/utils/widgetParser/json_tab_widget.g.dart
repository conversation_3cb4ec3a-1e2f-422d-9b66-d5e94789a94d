// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'json_tab_widget.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JsonTabWidget _$JsonTabWidgetFromJson(Map<String, dynamic> json) => JsonTabWidget(
      label: json['label'] as String?,
      labelKey: json['labelKey'] as String?,
      sections: (json['sections'] as List<dynamic>?)
          ?.map((e) => JsonSectionWidget.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$JsonTabWidgetToJson(JsonTabWidget instance) => <String, dynamic>{
      'label': instance.label,
      'labelKey': instance.labelKey,
      'sections': instance.sections,
    };
