import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/ta_web_view.dart';
import 'package:tangoworkplace/screens/suppliermanagement/suppliertabs/supplier_general.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

import '../models/ta_admin/entitystatus.dart';
import '../providers/ta_admin/home_controller.dart';
import '../utils/common_utils.dart';
import '../utils/connections.dart';
import '../utils/constvariables.dart';
import '../utils/device_util.dart';
import '../utils/preferences_utils.dart';
import '../utils/user_secure_storage.dart';
import 'progess_indicator_cust.dart';
import 'widgets/components.dart';
import 'widgets/map_web_view.dart';
import 'widgets/web_view_screen.dart';

class ComponentUtils {
  static bool equalsIgnoreCase(String? a, String b) =>
      (a == null && b == null) || (a != null && a.toLowerCase() == b.toLowerCase());
  static String platform = GetPlatform.isIOS ? 'ios' : 'android';
  static Color primecolor = HexColor('#b10c00');
  static Color tablabelcolor = HexColor('#3953A4');
  static Color tabunselectedLabelColor = HexColor('#4C4B5D').withOpacity(0.85);
  static Color tabindicatorColor = HexColor('#b10c00'); //  .withOpacity(0.85);
  static Color primary = const Color(0xff696b9e);
  static Color secondary = const Color(0xfff29a94);
  static Color bodybackground = CommonUtils.createMaterialColor(const Color(0XFFf2f2f2));
  static Color darkBackground = CommonUtils.createMaterialColor(const Color.fromARGB(255, 41, 41, 41));
  static Icon backpageIcon = GetPlatform.isIOS ? const Icon(Icons.arrow_back_ios) : const Icon(Icons.arrow_back);

  static TextStyle appbartitlestyle = TextStyle(
      color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), fontWeight: FontWeight.bold, fontSize: 16);

  static double getFontSize(double multiplier) {
    double unitHeightValue = MediaQuery.of(Get.context!).size.height * 0.01;
    //  print(multiplier * unitHeightValue);
    return multiplier * unitHeightValue;
  }

  static Widget? apiImage(var picId, {double? height, double? width}) {
    try {
      Map<String, String> headers = SharedPrefUtils.getHeaders();
      String pId = picId != null ? picId.toString() : '0';
      String url = '$imageapiurl?picId=$pId';
      String imageurl = ApiService.getRestPathurl(url);
      debugPrint('imageurl>>>>>$imageurl');

      return FadeInImage(
          height: height ?? 30.0,
          width: width ?? 30.0,
          image: NetworkImage(imageurl, headers: headers),
          placeholder: const AssetImage('lib/icons/no_image_icon.gif'),
          imageErrorBuilder: (context, error, stackTrace) {
            return Image.asset('lib/icons/no_image_icon.gif',
                height: height ?? 30.0, width: height ?? 30.0, fit: BoxFit.fitWidth);
          });
    } catch (e) {
      debugPrint(e.toString());
    }
    return null;
    //placeholder: AssetImage('lib/icons/no_image_icon.gif');
  }

  static Widget labelLoadScaffold() {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(Get.context!).appBarTheme.iconTheme,
        title: Text('.....', style: appbartitlestyle),
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: const ProgressIndicatorCust(),
    );
  }

  static bool strbool(String str) {
    if ((str.toUpperCase() == 'TRUE')) {
      return true;
    } else {
      return false;
    }
  }

  static String dateToString(String? dt) {
    String formatted = '';
    if (dt != null && dt.isNotEmpty) {
      try {
        DateFormat formatter = DateFormat('MM/dd/yyyy');
        formatted = formatter.format(DateTime.parse(dt));
      } catch (e) {
        debugPrint('exception  $e');
      }
    }
    return formatted;
  }

  static int? compareDates(String dt1, String dt2) {
    //0 --unknown
    //1--- dt1>dt2
    //2--- dt2>dt1

    int res = 0;

    var d1 = dateToString(dt1);
    var d2 = dateToString(dt2);
    if (d1 != '' && d2 != '') {
      DateTime t1 = DateTime.parse(d1);
      DateTime t2 = DateTime.parse(d2);
      if (t1.isAfter(t2) || t1.isAtSameMomentAs(t2)) {
        res = 1;
      } else {
        res = 2;
      }
      return res;
    }
    return null;
  }

  static double? StrToDouble(String val) {
    double? db;
    if (val != '') db = int.tryParse(val) == null ? double.tryParse(val) : int.tryParse(val)! + .0;
    return db;
  }

  static scrollToIndex(index, ScrollController ctrl) {
    const double height = 92;
    ctrl.animateTo(height * index, duration: const Duration(seconds: 2), curve: Curves.easeIn);
  }

  static void documentviewer(BuildContext context, String documentid) {
    var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
    var host = ApiService.getServerurl();
    var url = host + '$documentviewerurl?documentId=$documentid'; // &JSESSIONID=$sessionid';
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => WebViewContainer1(
                  url,
                  'View Document',
                  host,
                  source: 'DOCVIEW',
                )));
  }

  static showsnackbar({String? heading, required String text, int? seconds}) {
    Get.snackbar(
      heading ?? 'info:',
      text,
      icon: const Icon(Icons.info, color: Colors.white),
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: seconds ?? 2),
      isDismissible: true,
      //dismissDirection: SnackDismissDirection.HORIZONTAL,
      forwardAnimationCurve: Curves.easeOutBack,
    );
  }

  static showpopup({String? type, required String msg}) {
    Get.defaultDialog(
        title: type ?? 'Info',
        titleStyle: type == 'Error'
            ? TextStyle(fontSize: 16, color: primecolor)
            : const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
        barrierDismissible: true,
        content: Container(
          padding: const EdgeInsets.fromLTRB(15.0, 0.0, 15.0, 0.0),
          child: Text(
            msg,
            style: const TextStyle(fontSize: 12),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: 'Ok',
                onPressed: () {
                  Get.back();
                },
              ),
              const SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  static showwarnpopup(
      {String? title, String? contenttext, String? confirmBtn, String? cancelBtn, Function? confirmfunction}) {
    Get.defaultDialog(
        title: title ?? 'Delete',
        titleStyle: TextStyle(color: ComponentUtils.primecolor, fontSize: 18, fontWeight: FontWeight.bold),
        content: Container(
          // height: 250,
          margin: const EdgeInsets.only(left: 15, right: 15),
          child: Text(
            contenttext ?? 'Are you sure?',
            style: TextStyle(
              color: ComponentUtils.primary,
              fontSize: 12,
            ),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TaButton(
                type: 'elevate',
                buttonText: cancelBtn ?? 'Cancel',
                onPressed: () {
                  Get.back();
                },
              ),
              const SizedBox(
                width: 4.0,
              ),
              TaButton(
                type: 'elevate',
                buttonText: confirmBtn ?? 'Yes',
                onPressed: () {
                  confirmfunction!();
                },
              ),
              const SizedBox(
                width: 7.0,
              ),
            ],
          ),
        ]);
  }

  static String convertNumberFormat(String val) {
    String? locale = Get.find<HomeController>().userinfo.value.userPrefLocale;
    locale = (locale != null && locale != '') ? locale.replaceAll('-', '_') : 'en_US';

    final nf = NumberFormat.currency(
        //customPattern: '#,###.##',
        decimalDigits: 2,
        locale: locale,
        symbol: '');
    return nf.format(double.tryParse(val));
  }

  static Widget listVertRow(String label, String val, {String? dtype, TextStyle? labelStyle, TextStyle? valueStyle}) {
    final primary = ComponentUtils.primary;
    final nf = NumberFormat.currency(
      customPattern: '#,###.##',
      //locale: 'en_US',
    );
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 3, horizontal: 1),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(right: 15),
              child: Text(
                label,
                style: labelStyle ??
                    TextStyle(
                        color: primary, //fontWeight: FontWeight.bold,
                        fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Flexible(
              child: Text(
                  //dtype == 'num' ? nf.format(double.tryParse(val)) : val,
                  dtype == 'num' ? convertNumberFormat(val) : val,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: valueStyle ??
                      TextStyle(
                        color: primary,
                        fontSize: 12,
                        letterSpacing: .1,
                      )),
            ),
            // Text(
            //   //dtype == 'num' ? nf.format(double.tryParse(val)) : val,
            //   dtype == 'num' ? convertNumberFormat(val) : val,
            //   style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 12),
            // ),
          ],
        ));
  }

  static Future<void> mapviewer(BuildContext context) async {
    var sessionid = SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
    var user = UserContext.info()!.userName;
    var host = await ApiService.getServerurl();
    var url = host + '$mappingurl&username=$user';

    //var url = 'http://localhost:7001/mapv4services/?context=true&map_locale=en&isApp=true&username=$user';
    //url = 'https://dev.tangoanalytics.com/spacemgmt/?floorId=579769&spaceId=160&buildingId=579744&action=MAC';
    //url = 'https://filesampleshub.com/format/image/jpg';

    //url = 'https://examplefile.com/document/pdf/1-mb-pdf';
    //url = 'https://www.learningcontainer.com/sample-pdf-files-for-testing/';
    Get.to(
      () => MapWebView(
        url,
        'Maps',
        host,
        source: 'MAPVIEW',
      ),
    );

    //Get.to(() => InappWebView('Maps', url));
  }

  static Future<void> biddingviewer(BuildContext context, String pgname) async {
    var sessionid = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
    var biddinghosturl = await SharedPrefUtils.readPrefStr(ConstHelper.biddingurl);
    var user = UserContext.info()!.userName;
    var host = ApiService.getServerurl();
    var url = '$biddinghosturl?username=$user&jsessionid=$sessionid';
    Get.to(
      () => WebViewContainer1(
        url,
        pgname,
        host,
        source: 'BIDVIEW',
      ),
    );
  }

  static DropdownMenuItem<String> buildDropdownItem(EntityStatus status) {
    bool disFlag = status.displayFlag?.equalsIgnoreCase('Y') ?? false;
    return DropdownMenuItem(
      value: status.statusCode,
      enabled: disFlag,
      child: Text(
        status.status ?? '',
        style: TextStyle(
          fontSize: 14,
          color: disFlag ? Colors.black : Colors.grey,
        ),
      ),
    );
  }
}
