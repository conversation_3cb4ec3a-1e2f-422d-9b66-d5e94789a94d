import 'package:flutter/material.dart';

class Indicator extends StatelessWidget {
  const Indicator({
    super.key,
    required this.controller,
    required this.itemCount,
  });

  /// PageView Controller
  final PageController controller;

  /// Number of indicators
  final int itemCount;

  /// Ordinary colours
  final Color normalColor = Colors.grey;

  /// Selected color
  final Color selectedColor = Colors.black;

  /// Size of points
  final double size = 8.0;

  /// Spacing of points
  final double spacing = 4.0;

  /// Point Widget
  Widget _buildIndicator(int index, int pageCount, double dotSize, double spacing) {
    // Is the current page selected?
    bool isCurrentPageSelected = index == (controller.page != null ? controller.page!.round() % pageCount : 0);
    debugPrint('$isCurrentPageSelected');
    return MaterialButton(
        onPressed: () {
          debugPrint('Inside onTap');
          debugPrint('index $index $pageCount');
          controller.jumpToPage(index);
        },
        height: 8,
        minWidth: 10,
        color: isCurrentPageSelected ? selectedColor : normalColor,
        shape: const CircleBorder(),
        child: SizedBox(
          width: dotSize,
          height: dotSize,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: List<Widget>.generate(itemCount, (int index) {
        return _buildIndicator(index, itemCount, size, spacing);
      }),
    );
  }
}
