import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import '../../component_utils.dart';

class TaStickyOrderedTable extends StatelessWidget {
  final List<String?> columns;
  final Set<int>? ordering;
  final List<DataRow> rows;
  final ScrollController vericalScrollContreoller = ScrollController();

  TaStickyOrderedTable(
      {required this.columns, required this.rows, this.ordering, super.key});

  List<DataColumn> getColumns(List<String?> columns) => columns
      .map((String? column) => DataColumn(
            label: Text(column!),
          ))
      .toList();

  List<String?> _changeOrderingColumns() {
    final validData = ordering != null && ordering?.length == columns.length;
    final validOrderingData =
        ordering?.min == 0 && ordering?.max == (columns.length - 1);

    if (validOrderingData && validData) {
      List<String?> orderedList = [];
      for (final positionInList in ordering ?? {}) {
        final itemToAdd = columns
            .firstWhereIndexedOrNull((index, _) => index == positionInList);
        if (itemToAdd != null) {
          orderedList.add(itemToAdd);
        }
      }
      return orderedList;
    } else {
      debugPrint(
          'TaStickyOrder: ordering is not possible, because of incorrect order set');
      return columns;
    }
  }

  List<DataRow> _changeOrderingRows() {
    final validData = ordering != null && ordering?.length == columns.length;
    final validOrderingData =
        ordering?.min == 0 && ordering?.max == (columns.length - 1);

    if (validOrderingData && validData) {
      List<DataRow> orderedRows = [];
      for (var row in rows) {
        List<DataCell> orderedRowItems = [];
        for (final positionInList in ordering ?? {}) {
          final itemToAdd = row.cells
              .firstWhereIndexedOrNull((index, _) => index == positionInList);
          if (itemToAdd != null) {
            orderedRowItems.add(itemToAdd);
          }
        }
        orderedRows.add(DataRow(cells: orderedRowItems));
      }

      return orderedRows;
    } else {
      debugPrint(
          'TaStickyOrder: ordering is not possible, because of incorrect order set');
      return rows;
    }
  }

  Widget _getTableView(
      {required bool isForHeader, required List<String?> columnsOrdered, required List<DataRow> rowsOrdered}) {
    return DataTable(
      columns: getColumns(columnsOrdered),
      headingRowColor: WidgetStateProperty.all(const Color(0xFFe0e1eb)),
      rows: rowsOrdered,
      dataRowMinHeight: isForHeader ? 0 : 25,
      dataRowMaxHeight: isForHeader ? 0 : 60,
      dataTextStyle: TextStyle(color: ComponentUtils.primary, fontSize: 12),
      dataRowColor: WidgetStateProperty.all<Color>(Colors.white),
      dividerThickness: 1,
      headingRowHeight: isForHeader ? 25 : 0,
      columnSpacing: 25,
      headingTextStyle: TextStyle(
        color: ComponentUtils.primary,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
      showBottomBorder: !isForHeader,
    );
  }

  @override
  Widget build(BuildContext context) {
    final columnsOrdered = _changeOrderingColumns();
    final rowsOrdered = _changeOrderingRows();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        padding: const EdgeInsets.only(right: 1, left: 1, top: 1),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            _getTableView(isForHeader: true, columnsOrdered: columnsOrdered, rowsOrdered: rowsOrdered),
            Expanded(
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                scrollDirection: Axis.vertical,
                child: _getTableView(
                    isForHeader: false, columnsOrdered: columnsOrdered, rowsOrdered: rowsOrdered),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
