import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../component_utils.dart';

class TaFormInputTime extends StatelessWidget {
  final String? label;
  final String? value;
  final icon;
  final timeSelect;
  final int? maxLines;
  final int? minLines;
  final textInputAct;
  bool? readOnly;
  final TextEditingController? controller;
  final String? errortext;
  final TextInputType? textInputType;
  final String? hintText;
  final Icon? prefixIcon;
  final Icon? suffixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  String? Function(String? val)? validate;
  final String? validateMsg;
  final TextInputType? keyboard;
  final TextInputAction? actionKeyboard;
  final Function? onChanged;
  final Function? onSubmitField;
  final Function? onFieldTap;
  final Function? onSaved;
  final EdgeInsetsGeometry? padding;
  final String? valueToPassOnTap;

  TaFormInputTime(
      {super.key,
      this.label,
      this.value,
      this.icon,
      this.timeSelect,
      this.maxLines,
      this.controller,
      this.textInputAct,
      this.readOnly,
      this.keyboard,
      this.minLines,
      this.onChanged,
      this.actionKeyboard,
      this.defaultText,
      this.errortext,
      this.focusNode,
      this.hintText,
      this.obscureText,
      this.onFieldTap,
      this.onSaved,
      this.onSubmitField,
      this.prefixIcon,
      this.suffixIcon,
      this.textInputType,
      this.validate,
      this.validateMsg,
      this.padding,
      this.valueToPassOnTap});

  @override
  Widget build(BuildContext context) {
    readOnly = readOnly ?? false;
    return Padding(
      padding: padding ??
          const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 5,
          ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextFormField(
          controller: controller,
          initialValue: value,
          minLines: minLines ?? 1,
          maxLines: maxLines,
          readOnly: true,
          cursorColor: Theme.of(context).primaryColor,
          textInputAction: textInputAct ?? TextInputAction.done,
          style: const TextStyle(color: Colors.black, fontSize: 14),
          keyboardType: keyboard,
          onSaved: onSaved as void Function(String?)?,
          onChanged: onChanged as void Function(String)?,
          validator: validate,
          decoration: InputDecoration(
            errorText: errortext,

            suffixIcon: IconButton(
              onPressed: () {
                !readOnly!
                    ? _selectTime(Get.context!, timeSelect, onChanged, valueToPassOnTap ?? controller?.text)
                    : null;
              },
              icon: Icon(
                Icons.access_time,
                color: ComponentUtils.primecolor,
              ),
            ),

            labelText: label,
            filled: true,
            fillColor: readOnly! ? Colors.grey.shade200 : Colors.white70,
            floatingLabelBehavior: FloatingLabelBehavior.always,

            enabledBorder: readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),
            focusedBorder: readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),

            contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            isDense: true,
            errorStyle: const TextStyle(
              //color: Colors.red,
              // fontSize: 12.0,
              //fontWeight: FontWeight.w300,
              fontStyle: FontStyle.normal,
              //letterSpacing: 1.2,
            ),
            // errorBorder: OutlineInputBorder(
            //   borderSide: BorderSide.none,
            // ),
            errorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
          ),
        ),
      ),
    );
  }
}

Future<void> _selectTime(BuildContext context, TimeOfDay? selectedTime, Function? onChangeTime, String? value) async {
  TimeOfDay cTime = TimeOfDay.now();
  if (value != null && value.isNotEmpty) {
    value = value.replaceAll(' ', '');
    DateTime dt = DateFormat("hh:mma").parse(value);
    cTime = TimeOfDay.fromDateTime(dt);
  }

  selectedTime = selectedTime ?? cTime;
  final TimeOfDay? timeOfDay = await showTimePicker(
      context: context,
      initialTime: selectedTime,
      initialEntryMode: TimePickerEntryMode.dial,
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
          child: child!,
        );
      }
      //  confirmText: "CONFIRM",
      //     cancelText: "NOT NOW",
      //     helpText: "BOOKING TIME"
      );
  if (timeOfDay != null && timeOfDay != selectedTime) {
    onChangeTime!(timeOfDay.format(context));
  } else {
    onChangeTime!(cTime.format(context));
  }
}
