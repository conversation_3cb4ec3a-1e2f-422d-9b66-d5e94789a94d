import 'package:flutter/material.dart';

import '../../../utils/common_utils.dart';
import '../../component_utils.dart';
import '../components.dart';

class TaLabel extends StatelessWidget {
  final String? label;
  final IconData? icon;
  final TextEditingController? controller;
  final VoidCallback onFieldTap;

  const TaLabel({super.key, this.label, this.controller, this.icon, required this.onFieldTap});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextFormField(
          minLines: 1,
          maxLines: 1,
          readOnly: true,
          controller: controller,
          style: const TextStyle(color: Colors.black, fontSize: 14),
          decoration: InputDecoration(
            suffixIcon: IconButton(
              onPressed: () {
                onFieldTap();
              },
              icon: Icon(
                icon ?? Icons.search,
                color: ComponentUtils.primecolor,
              ),
            ),
            labelText: label,
            filled: true,
            fillColor: Colors.white70,
            floatingLabelBehavior: FloatingLabelBehavior.always,
            enabledBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            isDense: true,
          ),
        ),
      ),
    );
  }
}
