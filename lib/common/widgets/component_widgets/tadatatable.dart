// import 'package:flutter/material.dart';
// import 'package:horizontal_data_table/horizontal_data_table.dart';
// import 'package:linked_scroll_controller/linked_scroll_controller.dart';

// const int maxNumber = 3;
// const double cellWidth = 50;

// class MultiplicationTable extends StatefulWidget {
//   @override
//   _MultiplicationTableState createState() => _MultiplicationTableState();
// }

// class _MultiplicationTableState extends State<MultiplicationTable> {
//   LinkedScrollControllerGroup _controllers;
//   ScrollController _headController;
//   ScrollController _bodyController;
//   @override
//   void initState() {
//     super.initState();
//     _controllers = LinkedScrollControllerGroup();
//     _headController = _controllers.addAndGet();
//     _bodyController = _controllers.addAndGet();
//   }

//   @override
//   void dispose() {
//     _headController.dispose();
//     _bodyController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         TableHead(
//           scrollController: _headController,
//         ),
//         Expanded(
//           child: TableBody(
//             scrollController: _bodyController,
//           ),
//         ),
//       ],
//     );
//   }
// }

// class TableBody extends StatefulWidget {
//   final ScrollController scrollController;
//   TableBody({
//     @required this.scrollController,
//   });
//   @override
//   _TableBodyState createState() => _TableBodyState();
// }

// class _TableBodyState extends State<TableBody> {
//   LinkedScrollControllerGroup _controllers;
//   ScrollController _firstColumnController;
//   ScrollController _restColumnsController;
//   @override
//   void initState() {
//     super.initState();
//     _controllers = LinkedScrollControllerGroup();
//     _firstColumnController = _controllers.addAndGet();
//     _restColumnsController = _controllers.addAndGet();
//   }

//   @override
//   void dispose() {
//     _firstColumnController.dispose();
//     _restColumnsController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         SizedBox(
//           width: cellWidth,
//           child: ListView(
//             controller: _firstColumnController,
//             physics: ClampingScrollPhysics(),
//             children: List.generate(maxNumber - 1, (index) {
//               return TableCell(
//                 color: Colors.yellow.withOpacity(0.3),
//                 value: index + 2,
//               );
//             }),
//           ),
//         ),
//         Expanded(
//           child: SingleChildScrollView(
//             controller: widget.scrollController,
//             scrollDirection: Axis.horizontal,
//             physics: const ClampingScrollPhysics(),
//             child: SizedBox(
//               width: (maxNumber - 1) * cellWidth,
//               child: ListView(
//                 controller: _restColumnsController,
//                 physics: const ClampingScrollPhysics(),
//                 children: List.generate(maxNumber - 1, (y) {
//                   return Row(
//                     children: List.generate(maxNumber - 1, (x) {
//                       return TableCell(
//                         value: (x + 2) * (y + 2),
//                       );
//                     }),
//                   );
//                 }),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }

// class TableHead extends StatelessWidget {
//   final ScrollController scrollController;

//   TableHead({
//     @required this.scrollController,
//   });
//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       height: cellWidth,
//       child: Row(
//         children: [
//           TableCell(
//             color: Colors.yellow.withOpacity(0.3),
//             value: 1,
//           ),
//           Expanded(
//             child: ListView(
//               controller: scrollController,
//               physics: ClampingScrollPhysics(),
//               scrollDirection: Axis.horizontal,
//               children: List.generate(maxNumber - 1, (index) {
//                 return TableCell(
//                   color: Colors.yellow.withOpacity(0.3),
//                   value: index + 2,
//                 );
//               }),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class TableCell extends StatelessWidget {
//   final int value;
//   final Color color;
//   final cwidth;
//   final cheight;
//   final Widget alignment;
//   TableCell({
//     this.value,
//     this.color,
//     this.cheight,
//     this.cwidth,
//     this.alignment,
//   });
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       width: cwidth ?? cellWidth,
//       height: cheight ?? cellWidth,
//       decoration: BoxDecoration(
//         color: color,
//         border: Border.all(
//           color: Colors.black12,
//           width: 1.0,
//         ),
//       ),
//       alignment: alignment ?? Alignment.center,
//       child: Text(
//         ' ${value ?? ''}',
//         style: TextStyle(fontSize: 14.0),
//       ),
//     );
//   }
// }

// //-------------------------------------------------------------------------------------
// class MyHomePage extends StatefulWidget {
//   MyHomePage({Key key, this.title}) : super(key: key);
//   final String title;

//   @override
//   _MyHomePageState createState() => _MyHomePageState();
// }

// class _MyHomePageState extends State<MyHomePage> {
//   HDTRefreshController _hdtRefreshController = HDTRefreshController();

//   static const int sortName = 0;
//   static const int sortStatus = 1;
//   bool isAscending = true;
//   int sortType = sortName;

//   @override
//   void initState() {
//     user.initData(100);
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(widget.title),
//       ),
//       body: _getBodyWidget(),
//     );
//   }

//   Widget _getBodyWidget() {
//     return Container(
//       child: HorizontalDataTable(
//         leftHandSideColumnWidth: 100,
//         rightHandSideColumnWidth: 600,
//         isFixedHeader: true,
//         headerWidgets: _getTitleWidget(),
//         leftSideItemBuilder: _generateFirstColumnRow,
//         rightSideItemBuilder: _generateRightHandSideColumnRow,
//         itemCount: user.userInfo.length,
//         rowSeparatorWidget: const Divider(
//           color: Colors.black54,
//           height: 1.0,
//           thickness: 0.0,
//         ),
//         leftHandSideColBackgroundColor: Color(0xFFFFFFFF),
//         rightHandSideColBackgroundColor: Color(0xFFFFFFFF),
//         verticalScrollbarStyle: const ScrollbarStyle(
//           thumbColor: Colors.yellow,
//           isAlwaysShown: true,
//           thickness: 4.0,
//           radius: Radius.circular(5.0),
//         ),
//         horizontalScrollbarStyle: const ScrollbarStyle(
//           thumbColor: Colors.red,
//           isAlwaysShown: true,
//           thickness: 4.0,
//           radius: Radius.circular(5.0),
//         ),
//         enablePullToRefresh: true,
//         refreshIndicator: const WaterDropHeader(),
//         refreshIndicatorHeight: 60,
//         onRefresh: () async {
//           //Do sth
//           await Future.delayed(const Duration(milliseconds: 500));
//           _hdtRefreshController.refreshCompleted();
//         },
//         enablePullToLoadNewData: true,
//         loadIndicator: const ClassicFooter(),
//         onLoad: () async {
//           //Do sth
//           await Future.delayed(const Duration(milliseconds: 500));
//           _hdtRefreshController.loadComplete();
//         },
//         htdRefreshController: _hdtRefreshController,
//       ),
//       height: MediaQuery.of(context).size.height,
//     );
//   }

//   List<Widget> _getTitleWidget() {
//     return [
//       TextButton(
//         style: TextButton.styleFrom(
//           padding: EdgeInsets.zero,
//         ),
//         child: _getTitleItemWidget('Name' + (sortType == sortName ? (isAscending ? '↓' : '↑') : ''), 100),
//         onPressed: () {
//           sortType = sortName;
//           isAscending = !isAscending;
//           user.sortName(isAscending);
//           setState(() {});
//         },
//       ),
//       TextButton(
//         style: TextButton.styleFrom(
//           padding: EdgeInsets.zero,
//         ),
//         child: _getTitleItemWidget('Status' + (sortType == sortStatus ? (isAscending ? '↓' : '↑') : ''), 100),
//         onPressed: () {
//           sortType = sortStatus;
//           isAscending = !isAscending;
//           user.sortStatus(isAscending);
//           setState(() {});
//         },
//       ),
//       _getTitleItemWidget('Phone', 200),
//       _getTitleItemWidget('Register', 100),
//       _getTitleItemWidget('Termination', 200),
//     ];
//   }

//   Widget _getTitleItemWidget(String label, double width) {
//     return Container(
//       child: Text(label, style: TextStyle(fontWeight: FontWeight.bold)),
//       width: width,
//       height: 56,
//       padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
//       alignment: Alignment.centerLeft,
//     );
//   }

//   Widget _generateFirstColumnRow(BuildContext context, int index) {
//     return Container(
//       child: Text(user.userInfo[index].name),
//       width: 100,
//       height: 52,
//       padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
//       alignment: Alignment.centerLeft,
//     );
//   }

//   Widget _generateRightHandSideColumnRow(BuildContext context, int index) {
//     return Row(
//       children: <Widget>[
//         Container(
//           child: Row(
//             children: <Widget>[
//               Icon(user.userInfo[index].status ? Icons.notifications_off : Icons.notifications_active,
//                   color: user.userInfo[index].status ? Colors.red : Colors.green),
//               Text(user.userInfo[index].status ? 'Disabled' : 'Active')
//             ],
//           ),
//           width: 100,
//           height: 52,
//           padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
//           alignment: Alignment.centerLeft,
//         ),
//         Container(
//           child: Text(user.userInfo[index].phone),
//           width: 200,
//           height: 52,
//           padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
//           alignment: Alignment.centerLeft,
//         ),
//         Container(
//           child: Text(user.userInfo[index].registerDate),
//           width: 100,
//           height: 52,
//           padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
//           alignment: Alignment.centerLeft,
//         ),
//         Container(
//           child: Text(user.userInfo[index].terminationDate),
//           width: 200,
//           height: 52,
//           padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
//           alignment: Alignment.centerLeft,
//         ),
//       ],
//     );
//   }
// }

// User user = User();

// class User {
//   List<UserInfo1> userInfo = [];

//   void initData(int size) {
//     for (int i = 0; i < size; i++) {
//       userInfo.add(UserInfo1("User_$i", i % 3 == 0, '+001 9999 9999', '2019-01-01', 'N/A'));
//     }
//   }

//   ///
//   /// Single sort, sort Name's id
//   void sortName(bool isAscending) {
//     userInfo.sort((a, b) {
//       int aId = int.tryParse(a.name.replaceFirst('User_', '')) ?? 0;
//       int bId = int.tryParse(b.name.replaceFirst('User_', '')) ?? 0;
//       return (aId - bId) * (isAscending ? 1 : -1);
//     });
//   }

//   ///
//   /// sort with Status and Name as the 2nd Sort
//   void sortStatus(bool isAscending) {
//     userInfo.sort((a, b) {
//       if (a.status == b.status) {
//         int aId = int.tryParse(a.name.replaceFirst('User_', '')) ?? 0;
//         int bId = int.tryParse(b.name.replaceFirst('User_', '')) ?? 0;
//         return (aId - bId);
//       } else if (a.status) {
//         return isAscending ? 1 : -1;
//       } else {
//         return isAscending ? -1 : 1;
//       }
//     });
//   }
// }

// class UserInfo1 {
//   String name;
//   bool status;
//   String phone;
//   String registerDate;
//   String terminationDate;

//   UserInfo1(this.name, this.status, this.phone, this.registerDate, this.terminationDate);
// }
