import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'dart:math' as math;
import 'package:flutter/services.dart';

class TaRichInputText extends StatelessWidget {
  final label;
  final value;
  final icon;
  final height;
  final int? maxLines;
  final int? minLines;
  final textInputAct;
  bool? readOnly;
  final TextEditingController? controller;
  final String? errortext;
  final TextInputType? textInputType;
  final String? hintText;
  final Icon? prefixIcon;
  final Icon? suffixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  final validate;
  final String? validateMsg;
  final TextInputType? keyboard;
  final TextInputAction? actionKeyboard;
  final Function? onChanged;
  final Function? onSubmitField;
  final Function? onFieldTap;
  final Function? onSaved;
  final inputFormatters;
  final suffixbutton;
  final inputtype;
  TaRichInputText({
    super.key,
    this.label,
    this.value,
    this.icon,
    this.maxLines,
    this.controller,
    this.textInputAct,
    this.readOnly,
    this.keyboard,
    this.minLines,
    this.onChanged,
    this.actionKeyboard,
    this.defaultText,
    this.errortext,
    this.focusNode,
    this.hintText,
    this.obscureText,
    this.onFieldTap,
    this.onSaved,
    this.onSubmitField,
    this.prefixIcon,
    this.suffixIcon,
    this.textInputType,
    this.validate,
    this.validateMsg,
    this.inputFormatters,
    this.suffixbutton,
    this.inputtype,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    readOnly = readOnly ?? false;
    return Container(
      // height: height ?? 200,
      constraints: const BoxConstraints(
        // minHeight: 20.0,
        maxHeight: 120.0,
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),

      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          fillColor: Colors.grey.shade200,
          filled: true,
          enabledBorder: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(10.0)),
            borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(10.0)),
            borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
          ),
        ),
        child: SingleChildScrollView(
          child: Html(
            data: value,
          ),
        ),
      ),
    );
  }
}
