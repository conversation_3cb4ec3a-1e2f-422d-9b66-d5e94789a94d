import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

class TaFormDropdown extends StatelessWidget {
  final label;
  final emptttext;
  final value;
  final validationmsg;
  final items;
  final validate;
  final listflag;
  final onChanged;
  final onSaved;
  bool? readonly;
  TaFormDropdown({
    super.key,
    this.label,
    this.emptttext,
    this.value,
    this.validationmsg,
    this.items,
    this.validate,
    this.listflag,
    this.onChanged,
    this.onSaved,
    this.readonly,
  });
  @override
  Widget build(BuildContext context) {
    readonly = readonly ?? false;
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 5,
      ),
      child: Material(
        child: listflag
            ? DropdownButtonFormField(
                hint: Text(emptttext, style: const TextStyle(fontSize: 14, color: Colors.black)),
                value: value,
                onChanged: readonly == true ? null : onChanged,
                items: items,
                validator: validate,
                onSaved: readonly == true ? null : onSaved,
                decoration: InputDecoration(
                  labelStyle: const TextStyle(fontSize: 12),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: label,
                  //icon: Icon(Icons.build),
                  filled: true,
                  fillColor: readonly! ? Colors.grey.shade200 : Colors.white70,
                  enabledBorder: readonly!
                      ? OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                          borderSide:
                              BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                        )
                      : OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(12.0)),
                          borderSide:
                              BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                        ),
                  focusedBorder: readonly!
                      ? OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                          borderSide:
                              BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                        )
                      : OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                          borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0))),
                        ),
                  errorStyle: const TextStyle(
                    fontStyle: FontStyle.normal,
                  ),

                  errorBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
                  ),
                ),
              )
            : DropdownButtonFormField(
                hint: Text(emptttext, style: const TextStyle(fontSize: 14, color: Colors.black)),
                onChanged: onChanged,
                value: null,
                validator: validate,
                items: [
                  DropdownMenuItem(
                      value: null, child: Text(emptttext, style: const TextStyle(fontSize: 14, color: Colors.black))),
                ],
                decoration: InputDecoration(
                  labelStyle: const TextStyle(fontSize: 12),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: label,
                  //icon: Icon(Icons.build),
                  filled: true,
                  fillColor: readonly! ? Colors.grey.shade200 : Colors.white70,
                  enabledBorder: readonly!
                      ? OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                          borderSide:
                              BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                        )
                      : OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(12.0)),
                          borderSide:
                              BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                        ),
                  focusedBorder: readonly!
                      ? OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                          borderSide:
                              BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                        )
                      : OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                          borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0))),
                        ),
                  errorStyle: const TextStyle(
                    fontStyle: FontStyle.normal,
                  ),

                  errorBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
                  ),
                ),
              ),
      ),
    );
  }
}

class TaDropSown extends StatelessWidget {
  final label;
  final value;
  final initvalue;
  final List<DropdownMenuItem<dynamic>>? items;
  final icon;
  final int? maxLines;
  final int? minLines;
  final textInputAct;
  bool? readOnly;
  final TextEditingController? controller;
  final String? errortext;
  final TextInputType? textInputType;
  final String? hintText;
  final Icon? prefixIcon;
  final Icon? suffixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  final Function? validate;
  final String? validateMsg;
  final TextInputType? keyboard;
  final TextInputAction? actionKeyboard;
  final onChanged;
  final onSubmitField;
  final onFieldTap;
  final onSaved;
  TaDropSown({
    super.key,
    this.label,
    this.value,
    this.initvalue,
    this.items,
    this.icon,
    this.maxLines,
    this.controller,
    this.textInputAct,
    this.readOnly,
    this.keyboard,
    this.minLines,
    this.onChanged,
    this.actionKeyboard,
    this.defaultText,
    this.errortext,
    this.focusNode,
    this.hintText,
    this.obscureText,
    this.onFieldTap,
    this.onSaved,
    this.onSubmitField,
    this.prefixIcon,
    this.suffixIcon,
    this.textInputType,
    this.validate,
    this.validateMsg,
  });

  @override
  Widget build(BuildContext context) {
    readOnly = readOnly ?? false;
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        // vertical: 5,
      ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: Container(
          //color: Colors.transparent,
          padding: const EdgeInsets.only(left: 10, right: 2),
          height: 40,
          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.all(Radius.circular(12.0)),
          //   border: Border.all(color: CommonUtils.createMaterialColor(Color(0XFFC0C0C0)), width: 2),
          // ),

          child: DropdownButtonHideUnderline(
            child: (items != null && items!.isNotEmpty)
                ? DropdownButton(
                    hint: Text(
                      label,
                      style: const TextStyle(fontSize: 12, color: Colors.black),
                    ),
                    onChanged: onChanged,
                    value: initvalue,
                    items: items,
                  )
                : DropdownButton(
                    hint: Text(
                      label,
                    ),
                    onChanged: onChanged,
                    value: null,
                    items: const [
                      DropdownMenuItem(
                          value: null,
                          child: Text(
                            "Select",
                            style: TextStyle(fontSize: 12, color: Colors.black),
                          )),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}

class TaDropDown extends StatelessWidget {
  final label;
  final value;
  final initvalue;
  final List<DropdownMenuItem<dynamic>>? items;
  final icon;
  final int? maxLines;
  final int? minLines;
  final textInputAct;
  bool? readOnly;
  final TextEditingController? controller;
  final String? errortext;
  final TextInputType? textInputType;
  final String? hintText;
  final Icon? prefixIcon;
  final Icon? suffixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  final Function? validate;
  final String? validateMsg;
  final TextInputType? keyboard;
  final TextInputAction? actionKeyboard;
  final width;
  final onChanged;
  final onSubmitField;
  final onFieldTap;
  final onSaved;
  TaDropDown({
    super.key,
    this.label,
    this.value,
    this.initvalue,
    this.items,
    this.icon,
    this.maxLines,
    this.controller,
    this.textInputAct,
    this.readOnly,
    this.keyboard,
    this.minLines,
    this.onChanged,
    this.actionKeyboard,
    this.defaultText,
    this.errortext,
    this.focusNode,
    this.hintText,
    this.obscureText,
    this.onFieldTap,
    this.onSaved,
    this.onSubmitField,
    this.prefixIcon,
    this.suffixIcon,
    this.textInputType,
    this.validate,
    this.validateMsg,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    readOnly = readOnly ?? false;
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        // vertical: 5,
      ),
      child: Material(
          // elevation: 0.0,
          // borderRadius: BorderRadius.all(Radius.circular(25)),
          child: Column(children: [
        Stack(
          children: <Widget>[
            Container(
              width: width,
              height: 50,
              margin: const EdgeInsets.fromLTRB(0, 10, 10, 0),
              padding: const EdgeInsets.only(bottom: 1, top: 0, left: 8),
              decoration: BoxDecoration(
                border: Border.all(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                borderRadius: BorderRadius.circular(12.0),
                shape: BoxShape.rectangle,
              ),
              child: DropdownButtonHideUnderline(
                child: (items != null && items!.isNotEmpty)
                    ? DropdownButton(
                        hint: const Text(
                          "Select",
                          style: TextStyle(fontSize: 12, color: Colors.black),
                        ),
                        onChanged: onChanged,
                        value: initvalue,
                        items: items,
                      )
                    : DropdownButton(
                        onChanged: onChanged,
                        value: null,
                        items: const [
                          DropdownMenuItem(
                              value: null,
                              child: Text(
                                "Select",
                                style: TextStyle(fontSize: 12, color: Colors.black),
                              )),
                        ],
                      ),
              ),
            ),
            Positioned(
                left: 13,
                top: 4,
                child: Container(
                  //padding: EdgeInsets.only(bottom: 1, left: 1, right: 1),
                  color: Colors.white,
                  child: Text(
                    label,
                    style: TextStyle(color: CommonUtils.createMaterialColor(const Color(0XFF737373)), fontSize: 11),
                  ),
                )),
          ],
        ),
      ])),
    );
  }
}
