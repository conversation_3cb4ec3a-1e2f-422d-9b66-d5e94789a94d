import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/utils/common_utils.dart';

import '../../component_utils.dart';

class TaFormInputDate extends StatelessWidget {
  final label;
  final value;
  final icon;
  final dateSelect;
  final int? maxLines;
  final int? minLines;
  final textInputAct;
  bool? readOnly;
  final TextEditingController? controller;
  final String? errortext;
  final TextInputType? textInputType;
  final String? hintText;
  final Icon? prefixIcon;
  final Icon? suffixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  String? Function(String? val)? validate;
  final String? validateMsg;
  final TextInputType? keyboard;
  final TextInputAction? actionKeyboard;
  final Function? onChanged;
  final Function? onSubmitField;
  final Function? onFieldTap;
  final Function? onSaved;
  final padding;
  final firstDate;
  final lastDate;
  TaFormInputDate({
    super.key,
    this.label,
    this.value,
    this.icon,
    this.dateSelect,
    this.maxLines,
    this.controller,
    this.textInputAct,
    this.readOnly,
    this.keyboard,
    this.minLines,
    this.onChanged,
    this.actionKeyboard,
    this.defaultText,
    this.errortext,
    this.focusNode,
    this.hintText,
    this.obscureText,
    this.onFieldTap,
    this.onSaved,
    this.onSubmitField,
    this.prefixIcon,
    this.suffixIcon,
    this.textInputType,
    this.validate,
    this.validateMsg,
    this.padding,
    this.firstDate,
    this.lastDate,
  });

  @override
  Widget build(BuildContext context) {
    readOnly = readOnly ?? false;
    return Padding(
      padding: padding ??
          const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 5,
          ),
      child: Material(
        // elevation: 0.0,
        // borderRadius: BorderRadius.all(Radius.circular(25)),
        child: TextFormField(
          controller: controller,
          initialValue: value,
          minLines: minLines ?? 1,
          maxLines: maxLines,
          readOnly: true,
          cursorColor: Theme.of(context).primaryColor,
          textInputAction: textInputAct ?? TextInputAction.done,
          style: const TextStyle(color: Colors.black, fontSize: 14),
          keyboardType: keyboard,
          onSaved: onSaved as void Function(String?)?,
          onChanged: onChanged as void Function(String)?,
          validator: validate,
          decoration: InputDecoration(
            errorText: errortext,

            suffixIcon: IconButton(
              onPressed: () {
                !readOnly!
                    ? _selectDate(Get.context!, onChanged, dateSelect, firstDate, lastDate ?? DateTime(2101))
                    : null;
              },
              icon: Icon(
                Icons.date_range,
                color: ComponentUtils.primecolor,
              ),
            ),

            labelText: label,
            filled: true,
            fillColor: readOnly! ? Colors.grey.shade200 : Colors.white70,
            floatingLabelBehavior: FloatingLabelBehavior.always,

            enabledBorder: readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),
            focusedBorder: readOnly!
                ? OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 0.5),
                  )
                : OutlineInputBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                    borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFC0C0C0)), width: 2),
                  ),

            contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            isDense: true,
            errorStyle: const TextStyle(
              //color: Colors.red,
              // fontSize: 12.0,
              //fontWeight: FontWeight.w300,
              fontStyle: FontStyle.normal,
              //letterSpacing: 1.2,
            ),
            // errorBorder: OutlineInputBorder(
            //   borderSide: BorderSide.none,
            // ),
            errorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              borderSide: BorderSide(color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)), width: 2),
            ),
          ),
        ),
      ),
    );
  }
}

Future<void> _selectDate(BuildContext context, Function? onChangeDate, DateTime? selectedDate, DateTime? firstDate,
    DateTime? lastDate) async {
  DateTime selected = DateTime.now();
  selectedDate = selectedDate ?? selected;
  final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: firstDate ?? DateTime(2021),
      lastDate: lastDate ?? DateTime(2101));
  if (picked != null && picked != selectedDate) onChangeDate!('${picked.month}/${picked.day}/${picked.year}');
  // setState(() {
  //   selectedDate = picked;
  // });
}
