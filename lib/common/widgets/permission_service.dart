import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  // Check and request storage permission
  Future<bool> requestStoragePermission() async {
    // For Android < 13, use storage permission
    PermissionStatus status = await Permission.storage.status;

    if (!status.isGranted) {
      status = await Permission.storage.request();
    }

    // For Android 13+, use specific media permissions
    // if (await Permission.photos.isRequired ||
    //     await Permission.videos.isRequired ||
    //     await Permission.audio.isRequired) {
    //   status = await Permission.photos.request();
    //   if (!status.isGranted) {
    //     status = await Permission.videos.request();
    //   }
    //   if (!status.isGranted) {
    //     status = await Permission.audio.request();
    //   }
    // }

    // Handle permission results
    if (status.isGranted) {
      debugPrint('Storage permission granted');
      return true;
    } else if (status.isDenied) {
      debugPrint('Storage permission denied');
      return false;
    } else if (status.isPermanentlyDenied) {
      debugPrint('Storage permission permanently denied');
      // Open app settings so the user can manually enable permissions
      await openAppSettings();
      return false;
    }
    return false;
  }

  // Check permission status
  Future<bool> checkStoragePermission() async {
    bool storageGranted = await Permission.storage.isGranted;
    bool photosGranted = await Permission.photos.isGranted;
    // bool videosGranted = await Permission.videos.isGranted;
    // bool audioGranted = await Permission.audio.isGranted;

    return storageGranted || photosGranted;
    //|| videosGranted || audioGranted;
  }
}
