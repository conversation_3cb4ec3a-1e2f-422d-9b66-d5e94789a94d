import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/common/tree_node_controller.dart';

import '../../screens/common/utils/tree/tree_building.dart';
import '../progess_indicator_cust.dart';

class TreeNodeWidget extends StatelessWidget {
  final double? indent;
  final double? iconSize;
  final String? tagField;

  const TreeNodeWidget({super.key, this.indent, this.iconSize, this.tagField});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TreeNodeController>(
      tag: tagField,
      builder: (controller) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                iconSize: iconSize ?? 16.0,
                icon: !controller.node.isFolder
                    ? const Icon(Icons.description)
                    : controller.isLoading.value
                        ? const ProgressIndicatorCust()
                        : controller.isExpanded.value
                            ? const Icon(Icons.folder_open)
                            : const Icon(Icons.folder),
                onPressed: () => controller.node.isFolder ? controller.toggleNodeExpanded() : null,
              ),
              Expanded(
                child: controller.node.content,
              ),
            ],
          ),
          controller.node.isFolder
              ? Padding(
                  padding: EdgeInsets.only(left: indent!),
                  child: controller.isExpanded.value
                      ? buildNodes(
                          controller.children,
                          indent,
                          iconSize,
                        )
                      : Container(),
                )
              : Container(),
        ],
      ),
    );
  }
}
