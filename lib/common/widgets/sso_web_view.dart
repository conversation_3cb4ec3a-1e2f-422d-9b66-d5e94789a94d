import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'package:tangoworkplace/common/widgets/login_session.dart';
import 'package:tangoworkplace/screens/login_screen.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

class SSOWebView extends StatefulWidget {
  final url;
  final source;
  final username;

  const SSOWebView(this.url, this.source, this.username, {super.key});
  @override
  createState() => _SSOWebViewState(url, source, username);
}

class _SSOWebViewState extends State<SSOWebView> {
  final _url;
  final _source;
  final _username;
  final _key = UniqueKey();
  late WebViewController _controller;
  _SSOWebViewState(this._url, this._source, this._username);

  final cookiemngr = WebviewCookieManager();

  @override
  void initState() {
    super.initState();

    // Initialize the WebViewController
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) async {
            debugPrint(request.url);
            debugPrint("WebView url ${request.url}");
            if (_source == "login") {
              if (request.url.contains("tangoworkplace://")) {
                debugPrint("tangoworkplace:// found");
                String url = request.url;
                String sessionId = url.split("sessionId=")[1];
                debugPrint("sessionId $sessionId");
                await SharedPrefUtils.saveStr(ConstHelper.sessionIdvar, sessionId);
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => LoginSessionImpl(_username, seid: sessionId)),
                );
                return NavigationDecision.prevent;
              } else if (request.url.contains("tmcadfv01/faces/TMCHomePG.jspx")) {
                debugPrint("Webview done Inside!!!!!!!!!!!!!!!!");
                String? seid = await getcookies(request.url);
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => LoginSessionImpl(_username, seid: seid),
                  ),
                );
                return NavigationDecision.prevent;
              }
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) async {
            debugPrint("WebView url $url");
            if (_source == "logout") {
              SharedPrefUtils.saveStr("SSOEnabled", null);
              SharedPrefUtils.saveStr("SSOLogoutURL", "");
              SharedPrefUtils.clear();
              Navigator.pushNamedAndRemoveUntil(context, LoginPage.routName, (Route<dynamic> route) => false);
            }
          },
        ),
      );

    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    await _controller.loadRequest(Uri.parse(_url));
  }

  @override
  void dispose() async {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        appBar: AppBar(
          title: Text("SSO Login",
              style: const TextStyle(
                fontSize: 15.0,
                color: Colors.white,
              )),
        ),
        body: Container(
          child: WebViewWidget(
            controller: _controller,
            gestureRecognizers: const <Factory<OneSequenceGestureRecognizer>>{},
          ),
        ));
  }

  Future<String?> getcookies(String url) async {
    //debugPrint('finish url   ' + url);
    String? seid;
    final gotCookies = await cookiemngr.getCookies(url);
    debugPrint('$gotCookies');
    for (var item in gotCookies) {
      if (item.name.startsWith('JSESSIONID')) seid = item.toString();
    }
    //cookiemngr.removeCookie(url);
    return seid;
  }

  void clearCookies(WebViewController controller, BuildContext context) async {
    await cookiemngr.clearCookies();
  }
}
