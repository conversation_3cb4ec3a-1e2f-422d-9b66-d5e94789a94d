import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/widgets/aq_widgets/aq_space.dart';

import '../../component_utils.dart';
import '../components.dart';

class AqInfoLabel extends StatelessWidget {
  final String title;
  final String text;
  final VoidCallback? onClick;
  final Icon? traillingIcon;

  const AqInfoLabel({super.key, this.title = "", this.text = "", this.onClick, this.traillingIcon});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(title, style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 18)),
                const AqSpace(),
                Text(text),
                const AqSpace(),
              ]),
        ),
        if (traillingIcon != null)
          SizedBox(
            width: 45.0,
            child: IconButton(
              icon: traillingIcon!,
              onPressed: onClick ?? () {},
            ),
          )
      ],
    );
  }
}
