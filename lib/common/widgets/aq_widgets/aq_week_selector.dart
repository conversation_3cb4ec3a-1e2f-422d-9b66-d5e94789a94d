import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/types/dates/aq_weekday.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_utils.dart';

import '../../../providers/ta_admin/label_controller.dart';

class AqWeekSelector extends StatelessWidget {
  final List<int> selectedPosition;
  final Function(int) onClick;
  final LabelController talabel = Get.find<LabelController>();

  AqWeekSelector({
    super.key,
    required this.selectedPosition,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Row(
        children: [
          SizedBox(
            height: 40.0,
            child: ListView.builder(
              itemCount: AQDateUtils.allWeekDays.length,
              scrollDirection: Axis.horizontal,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (BuildContext context, int index) {
                return weekDayButton(AQDateUtils.allWeekDays[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget weekDayButton(AqWeekday day) {
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: SizedBox(
        width: 40.0,
        child: RawMaterialButton(
          fillColor: selectedPosition.contains(day.id) == true ? const Color(0XFFb10c00) : const Color(0xFFFFFFFF),
          focusColor: const Color(0XFFb10c00),
          hoverColor: const Color.fromARGB(255, 119, 5, 5),
          splashColor: const Color.fromARGB(255, 119, 5, 5),
          onPressed: () {
            onClick(day.id);
          },
          shape: const CircleBorder(side: BorderSide(color: Color(0XFFb10c00), width: 0.5)),
          child: Text(
            talabel.getAq(key: day.getWeekDaysShortenTagKey, defaultTxt: day.displayName)!.tag,
            style: TextStyle(
                color: selectedPosition.contains(day.id) == true ? const Color(0xFFFFFFFF) : const Color(0XFFb10c00)),
          ),
        ),
      ),
    );
  }
}
