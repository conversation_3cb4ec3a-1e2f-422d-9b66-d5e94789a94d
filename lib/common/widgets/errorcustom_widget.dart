import 'package:flutter/material.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/login_screen.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

class ErrorcustomWidget extends StatelessWidget {
  const ErrorcustomWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      appBar: AppBar(
        automaticallyImplyLeading: true,
        title: Text(
          'Welcome, ${SharedPrefUtils.readPrefStr(ConstHelper.userNamevar)}',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        backgroundColor: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
        elevation: 0,
        toolbarHeight: 70,
      ),
      body: Stack(children: <Widget>[
        Align(
          alignment: Alignment.topCenter,
          child: Container(
            decoration: BoxDecoration(
                borderRadius:
                    const BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
                color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
                shape: BoxShape.rectangle),
            height: 115,
          ),
        ),
        Positioned(
            top: 00,
            right: 15,
            left: 15,
            bottom: 10,
            child: ClipRRect(
                borderRadius: BorderRadius.circular(9),
                child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.red,
                    child: Container(
                        color: Colors.white,
                        child: const Center(child: Text("Error has Occured. Unable to load the page"))))))
      ]),
      bottomNavigationBar: BottomAppBar(
        color: Colors.white,
        child: Container(
          margin: EdgeInsets.only(left: 12.0, right: 12.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              IconButton(
                //update the bottom app bar view each time an item is clicked
                onPressed: () => Navigator.pushNamed(context, HomeScreen.routName),
                iconSize: 27.0,
                icon: Icon(
                  Icons.home,
                  color: CommonUtils.createMaterialColor(Color(0XFF394251)),
                  //darken the icon if it is selected or else give it a different color
                ),
              ),
              //to leave space in between the bottom app bar items and below the FAB
              SizedBox(
                width: 50.0,
              ),
              IconButton(
                onPressed: () {
                  // AppResourceBundleImpl().clear();
                  SharedPrefUtils.saveStr(ConstHelper.sessionIdvar, "");
                  SharedPrefUtils.saveStr(ConstHelper.userNamevar, "");
                  SharedPrefUtils.saveStr(ConstHelper.authVar, "");
                  Navigator.pushNamedAndRemoveUntil(context, LoginPage.routName, (Route<dynamic> route) => false);
                },
                iconSize: 27.0,
                icon: Icon(
                  Icons.logout,
                  color: CommonUtils.createMaterialColor(Color(0XFF394251)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
