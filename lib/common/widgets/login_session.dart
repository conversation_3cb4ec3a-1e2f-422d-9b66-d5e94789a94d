import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/alert_screen.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/utils/mfalogin.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/login_screen.dart';
import 'package:tangoworkplace/screens/ta_admin/user_terms_screen.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';
import '../../providers/common/utils/mfalogin_controller.dart';
import '../common_import.dart';
import 'component_widgets/taforminputtext.dart';

class LoginSessionImpl extends StatefulWidget {
  final username;
  final seid;
  const LoginSessionImpl(this.username, {super.key, this.seid});
  static const routName = '/loginimpl';
  @override
  _LoginSessionImplState createState() => _LoginSessionImplState(username, seid);
}

class _LoginSessionImplState extends State<LoginSessionImpl> {
  final _username;
  final _seid;
  final _key = UniqueKey();
  _LoginSessionImplState(this._username, this._seid);

  @override
  void initState() {
    super.initState();
    login(_username, _seid, context);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
    );
  }
}

void login(String user, String seid, BuildContext context) async {
  Map<String, String> headers = {};
  //debugPrint('seid      $seid');

  headers['Content-Type'] = 'application/json;charset=UTF-8';
  headers['Charset'] = 'utf-8';
  headers['Cookie'] = seid;
  debugPrint('-------------login-------------');
  //debugPrint('$seid');
  debugPrint('$headers');
  String url = ApiService.getServerurl() + loginssourl;
  debugPrint('Updated url $url');
  var uri = Uri.parse(url);
  await http.get(uri, headers: headers).then((response) async {
    debugPrint('${response.statusCode}');
    if (response.statusCode == 200) {
      //debugPrint('headers    ${response.headers}');
      //debugPrint('body    ${response.body}'););
      if (seid.isNotEmpty) {
        getSessionId(seid);
        DateTime expiryDate = DateTime.now().add(
          const Duration(seconds: 3600),
        );
        final timeToExpiry = expiryDate.difference(DateTime.now()).inSeconds;

        SharedPrefUtils.saveStr(ConstHelper.userNamevar, user);

        Timer? authTimer;
        if (authTimer != null) {
          authTimer.cancel();
        }

        debugPrint(' timeyto Expiry $timeToExpiry');

        authTimer = Timer(Duration(seconds: timeToExpiry), () {
          debugPrint('Timer Expired!!!!');
          SharedPrefUtils.clear();
        });

        getuserpage(response, context);
      } else {
        CommonUtils.logout(context);
      }
    } else {
      CommonUtils.logout(context);

      showDialog(
          context: context,
          builder: (BuildContext context) => getAlertDialog("Login failed", '${response.reasonPhrase}', context));
    }
  }).catchError((err) {
    CommonUtils.logout(context);
    showDialog(
        context: context, builder: (BuildContext context) => getAlertDialog("Login failed", err.toString(), context));
  });
}

Future<void> directlogin(String user, String pwd, BuildContext? context, {String? otptype, int? mfaexpiry}) async {
  Map<String, String> headers = Map();
  var sessionId = '';
  var temp = base64Encode('$user:$pwd'.codeUnits);

  headers['authorization'] = 'Basic $temp';
  headers['Content-Type'] = 'application/json;charset=UTF-8';
  headers['Charset'] = 'utf-8';

  debugPrint("before post");
  String? url = ApiService.getServerurl() + loginurl + '/';
  if (otptype != null && otptype != 'no-otp') {
    url = url! + otptype;
  } else {
    url = '${url!}no-otp';
  }
  debugPrint('Updated url $url');
  var uri = Uri.parse(url);
  await http.get(uri, headers: headers).then((response) async {
    debugPrint('${response.statusCode}');
    if (response.statusCode == 200) {
      String? sessionId = jsonDecode(response.body)['sessionId'] as String;
      debugPrint('seid>>>>    $sessionId');
      SharedPrefUtils.saveStr(ConstHelper.sessionIdvar, sessionId);
      SharedPrefUtils.saveStr(ConstHelper.userNamevar, user);
      getuserpage(response, context, otptype: otptype, username: user, mfaexpiry: mfaexpiry);
      //  }
    } else {
      SharedPrefUtils.clear();
      showDialog(
          context: context!,
          builder: (BuildContext context) => getAlertDialog("Login failed", errormsg(response.statusCode), context));
    }
  }).catchError((err) {
    SharedPrefUtils.clear();
    showDialog(
        context: context!, builder: (BuildContext context) => getAlertDialog("Login failed", err.toString(), context));
  });
  //return false;
}

void getuserpage(http.Response response, BuildContext? context,
    {String? otptype, String? username, int? mfaexpiry}) async {
  Map<String, dynamic>? userdata = {};
  Map<String, dynamic>? c = {};
  String? token;
  userdata = jsonDecode(response.body)['user'] as Map<String, dynamic>?;
  if (userdata == null) {
    showDialog(
        context: context!, builder: (BuildContext context) => getAlertDialog("Login failed", 'Login failed', context));
    CommonUtils.logout(context);
    //Get.off(LoginPage());
  } else {
    token = jsonDecode(response.body)['token'] as String?;
    SharedPrefUtils.saveStr(ConstHelper.tokenVal, token);
    SharedPrefUtils.saveStr('UserInfo', jsonEncode(userdata));
    debugPrint('userinfo    ${jsonEncode(userdata)}');
    c = jsonDecode(response.body)['context'] as Map<String, dynamic>?;
    debugPrint('$c');
    getContext(c);
    setResponseAditionalinfo(response);
    if (userdata['userTermsStatus'] != null && userdata['userTermsStatus'] == 'NOTACCEPTED') {
      //debugPrint(c['userTerms']);
      Navigator.pushReplacement(
        context!,
        MaterialPageRoute(
          builder: (context) => UserTerms(
            terms: userdata!['userTerms'],
            version: userdata['userTermsVersion'],
          ),
        ),
      );
    } else if (otptype != null && otptype != 'no-otp') {
      MfaLoginController mfaloginController = Get.put(MfaLoginController());
      mfaloginController.cleardata();
      otpDialog(context, username, mfaexpiry);
    } else {
      navigateToHome(context!);
    }
  }
}

void otpDialog(BuildContext? context, String? username, int? mfaexpiry) async {
  await Navigator.of(Get.context!).push(
    MaterialPageRoute<String>(
      builder: (BuildContext context) {
        return MfaLogin(
          toHome: () => navigateToHome(context),
          username: username,
          mfaexpiry: mfaexpiry,
        );
      },
      fullscreenDialog: true,
    ),
  );
}

void otperrormsg(BuildContext context, String message) {
  showDialog(
      context: context, builder: (BuildContext context) => getAlertDialog("Otp Error", message.toString(), context));
}

void navigateToHome(BuildContext context) {
  LabelController talabel = Get.put(LabelController());
  talabel.init.value = true;
  Navigator.pushReplacement(
    context,
    MaterialPageRoute(
      builder: (context) => HomeScreen(),
    ),
  );
}

void setResponseAditionalinfo(http.Response response) {
  Map<String, dynamic>? additionalinfo = {};
  var biddingurl;
  additionalinfo = jsonDecode(response.body)['additionalInfo'] as Map<String, dynamic>?;
  debugPrint('additionalinfo-----$additionalinfo');
  if (additionalinfo != null && additionalinfo['biddingurl'] != null) {
    biddingurl = additionalinfo['biddingurl'];
    debugPrint('biddingurl-----$biddingurl');
    SharedPrefUtils.saveStr(ConstHelper.biddingurl, biddingurl);
  }
}

void getContext(Map<String, dynamic>? c) {
  if (c != null) {
    SharedPrefUtils.setContextUserInfo(c);
  }
}

void setCookieList(String aSetCookie) {
  getSeid(aSetCookie);
  var setCookies = aSetCookie.split(',');
  var cookieStr = "";
  var cItr = setCookies.iterator;
  while (cItr.moveNext()) {
    var c = cItr.current.toString();
    if (c.startsWith("JSESSIONID") || c.startsWith("_WL_AUTHCOOKIE_JSESSIONID")) {
      cookieStr += "${cleanCookie(c)};";
      debugPrint(cookieStr);
    }
  }
  SharedPrefUtils.saveStr('cookieVal', cookieStr);
}

String cleanCookie(String hdrCookie) {
  return hdrCookie.substring(0, hdrCookie.indexOf(';'));
}

getSeid(String cookieVal) {
  String seid = '';
  var setCookies = cookieVal.split(';');
  var keyValue = setCookies[0].split('=');
  var key = keyValue[0].trim();
  if (key == 'JSESSIONID') {
    String value = keyValue[1].trim();
    //SharedPrefUtils.saveStr(sessionIdvar, value);
    int exaloc = value.indexOf("!");
    if (exaloc != -1) {
      seid = value.substring(0, exaloc);
    }
    debugPrint('getSeid-----------$seid');
    SharedPrefUtils.saveStr(ConstHelper.sessionIdvar, seid);
  }
}

getSessionId(String seid) {
  debugPrint('getSeid-----------$seid');
  String str = seid.replaceAll("JSESSIONID=", "");
  String ssid = '';
  int exaloc = str.indexOf("!");
  if (exaloc != -1) {
    ssid = str.substring(0, exaloc);
    debugPrint('getSeid-----------$ssid');
  }
  SharedPrefUtils.saveStr(ConstHelper.sessionIdvar, ssid);
}

String? errormsg(int code) {
  if (code == 400) return 'Bad Request';
  if (code == 401) return 'Unauthorized User';
  if (code == 404) return 'Not Found';
  return null;
}
