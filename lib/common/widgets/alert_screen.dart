import 'package:flutter/material.dart';

class AlertScreen extends StatelessWidget {
  const AlertScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}

AlertDialog getAlertDialog(title, content, context) {
  return AlertDialog(
    title: const Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
      Icon(
        Icons.highlight_off,
        color: Color(0XFFb10c00),
      ),
      SizedBox(
        width: 5.0,
      ),
      Text(
        "Error ",
        style: TextStyle(color: Color(0XFFb10c00), fontSize: 20),
      ),
    ]),
    content: Text(
      '$content',
    ),
    actions: <Widget>[
      TextButton(
        child: const Text('Close'),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    ],
  );
}
