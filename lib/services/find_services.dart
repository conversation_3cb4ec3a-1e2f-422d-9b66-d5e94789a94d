import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/models/find.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';
import '../utils/preferences_utils.dart';
import '../utils/connections.dart';

//calling rest api for FindPersons
Future<List<FindPerson>?> fetchPersons({required String queryparams}) async {
  String apival = '$personurl/search$queryparams';
  List<FindPerson>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['FindPersons'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => FindPerson.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchPersons>>>>>>>$e');
  }
  return tagObjs;
}

//calling rest api for FindSpaces
Future<List<FindSpace>?> fetchSpaces({required String queryparams}) async {
  String apival = '$spaceurl/search$queryparams';
  List<FindSpace>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['FindSpaces'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => FindSpace.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchSpaces>>>>>>>$e');
  }
  return tagObjs;
}

Future<int?> fetchFindCount(String findtype, {required String queryparams}) async {
  String apival;
  apival = findtype == 'persons' ? personurl : spaceurl;
  apival = '$apival/count$queryparams';
  List<FindSpace> tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['${findtype}Count'] as int?;
    return tagObjsJson;
    // }
    //}
  } catch (e) {
    debugPrint('fetchFindCount>>>>>>>$e');
  }
  return null;
}

Future<FindPerson> fetchPersonDetails(int? personId) async {
  String apival = '$personurl/$personId';
  FindPerson fp = FindPerson();
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    if (resMap != null) fp = FindPerson.fromJson(jsonDecode(resMap));
    //var tagObjsJson = jsonDecode(resMap) as FindPerson;
    //tagObjs = tagObjsJson.map((tagJson) => FindPerson.fromJson(tagJson)).toList();
    //}
  } catch (e) {
    debugPrint('fetchPersonDetails>>>>>>>$e');
  }
  return fp;
}

Future<List<FindSpace?>?> fetchSpaceDetails(int? spaceId) async {
  String apival = '$spaceurl/$spaceId';
  FindSpace fs = FindSpace();
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['SpaceDetails'] as List;
    List<FindSpace> tagObjs = tagObjsJson.map((tagJson) => FindSpace.fromJson(tagJson)).toList();
    return tagObjs;

    // }
    //}
  } catch (e) {
    debugPrint('fetchPersonDetails>>>>>>>$e');
  }
  return null;
}
