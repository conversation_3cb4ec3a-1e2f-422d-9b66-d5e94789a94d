import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/common_import.dart';

class ProjectSearchServices {
  static Future<Map<String, dynamic>?> fetchProjectsView({dynamic payloadObj}) async {
    debugPrint('--------fetchProjectsView------------');

    Map<String, dynamic>? tagObjs;
    try {
      tagObjs = await CommonServices.fetchDynamicApi(projectsviewurl, payloadObj: payloadObj);
    } catch (e) {
      debugPrint('fetchProjects>>>>>>>$e');
    }
    return tagObjs;
  }
}
