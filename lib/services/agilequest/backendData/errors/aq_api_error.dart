// val errorCode: Int,
//
// val subErrorCode: Int,
//
// val message: String,

import 'package:json_annotation/json_annotation.dart';

part 'aq_api_error.g.dart';

@JsonSerializable()
class AqApiError {
  int errorCode;
  int subErrorCode;
  String message;

  AqApiError({
    required this.errorCode,
    required this.subErrorCode,
    required this.message,
  });


  factory AqApiError.fromJson(Map<String, dynamic> json) => _$AqApiErrorFromJson(json);

  Map<String, dynamic> toJson() => _$AqApiErrorToJson(this);
}
