
import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/reserve/aq_reserve_view_data.dart';

part 'aq_reserve_views_response.g.dart';

@JsonSerializable()
class AqReserveViewsResponse {
  List<AqReserveViewData> views;

  AqReserveViewsResponse({
    required this.views,
  });

  factory AqReserveViewsResponse.fromJson(Map<String, dynamic> json) =>
      _$AqReserveViewsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqReserveViewsResponseToJson(this);
}
