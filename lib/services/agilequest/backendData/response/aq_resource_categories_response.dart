

import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/resource/aq_resource_category.dart';

part 'aq_resource_categories_response.g.dart';

@JsonSerializable()
class AqResourceCategoriesResponse {
  List<AqResourceCategory> categories;

  AqResourceCategoriesResponse({
    required this.categories,
  });

  factory AqResourceCategoriesResponse.fromJson(Map<String, dynamic> json) =>
      _$AqResourceCategoriesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqResourceCategoriesResponseToJson(this);
}
