
import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/user/aq_user_view_data.dart';

part 'aq_search_user_view_response.g.dart';

@JsonSerializable()
class AqSearchUserViewResponse {
  List<AqUserViewData>? views;
  bool? hasAdditional;
  int? nextStartRow;

  AqSearchUserViewResponse({
    this.views,
    this.hasAdditional,
    this.nextStartRow,
  });

  factory AqSearchUserViewResponse.fromJson(Map<String, dynamic> json) =>
      _$AqSearchUserViewResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqSearchUserViewResponseToJson(this);
}