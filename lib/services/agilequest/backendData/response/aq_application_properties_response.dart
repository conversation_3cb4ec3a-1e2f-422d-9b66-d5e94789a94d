

import 'package:json_annotation/json_annotation.dart';

import '../../../../models/agilequest/auth/aq_app_properties_data.dart';

part 'aq_application_properties_response.g.dart';

@JsonSerializable()
class AqApplicationPropertiesResponse {
  List<AqAppPropertiesData> mappings;

  AqApplicationPropertiesResponse({
    required this.mappings,
  });

  factory AqApplicationPropertiesResponse.fromJson(Map<String, dynamic> json) =>
      _$AqApplicationPropertiesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqApplicationPropertiesResponseToJson(this);
}
