import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/auth/aq_languagecodes_data.dart';

part 'aq_language_tags_response.g.dart';

@JsonSerializable()
class AqLanguageTagsResponse {
  List<AqLanguageCodesData> languageTags;

  AqLanguageTagsResponse({
    required this.languageTags,
  });

  factory AqLanguageTagsResponse.fromJson(Map<String, dynamic> json) => _$AqLanguageTagsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqLanguageTagsResponseToJson(this);
}
