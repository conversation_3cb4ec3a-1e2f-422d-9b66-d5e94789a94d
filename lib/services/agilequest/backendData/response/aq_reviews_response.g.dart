// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_reviews_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReviewsResponse _$AqReviewsResponseFromJson(Map<String, dynamic> json) =>
    AqReviewsResponse(
      hasAdditional: json['hasAdditional'] as bool,
      count: json['count'] as int,
      nextStartRow: json['nextStartRow'] as int,
      totalReviews: json['totalReviews'] as int,
      averageRating: (json['averageRating'] as num?)?.toDouble(),
      views: (json['views'] as List<dynamic>)
          .map((e) => AqReviewView.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AqReviewsResponseToJson(AqReviewsResponse instance) =>
    <String, dynamic>{
      'hasAdditional': instance.hasAdditional,
      'count': instance.count,
      'nextStartRow': instance.nextStartRow,
      'totalReviews': instance.totalReviews,
      'averageRating': instance.averageRating,
      'views': instance.views,
    };
