import 'package:json_annotation/json_annotation.dart';

import '../../../../models/agilequest/resource/aq_resource_data.dart';

part 'aq_resource_views_response.g.dart';

@JsonSerializable()
class AqResourceViewsResponse {
  List<AqResourceData>? views;

  AqResourceViewsResponse({
    this.views,
  });

  factory AqResourceViewsResponse.fromJson(Map<String, dynamic> json) => _$AqResourceViewsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqResourceViewsResponseToJson(this);
}
