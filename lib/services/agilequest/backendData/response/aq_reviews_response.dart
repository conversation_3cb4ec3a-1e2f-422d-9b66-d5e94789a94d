import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/reviews/aq_review_view.dart';


part 'aq_reviews_response.g.dart';

@JsonSerializable()
class AqReviewsResponse {
  bool hasAdditional;
  int count;
  int nextStartRow;
  int totalReviews;
  double? averageRating;
  List<AqReviewView> views;


  AqReviewsResponse({
    required this.hasAdditional,
    required this.count,
    required this.nextStartRow,
    required this.totalReviews,
    required this.averageRating,
    required this.views
  });

  factory AqReviewsResponse.fromJson(Map<String, dynamic> json) => _$AqReviewsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqReviewsResponseToJson(this);
}
