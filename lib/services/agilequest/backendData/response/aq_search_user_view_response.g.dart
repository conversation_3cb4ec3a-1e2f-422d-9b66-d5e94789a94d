// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_search_user_view_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqSearchUserViewResponse _$AqSearchUserViewResponseFromJson(
        Map<String, dynamic> json) =>
    AqSearchUserViewResponse(
      views: (json['views'] as List<dynamic>?)
          ?.map((e) => AqUserViewData.fromJson(e as Map<String, dynamic>))
          .toList(),
      hasAdditional: json['hasAdditional'] as bool?,
      nextStartRow: json['nextStartRow'] as int?,
    );

Map<String, dynamic> _$AqSearchUserViewResponseToJson(
        AqSearchUserViewResponse instance) =>
    <String, dynamic>{
      'views': instance.views,
      'hasAdditional': instance.hasAdditional,
      'nextStartRow': instance.nextStartRow,
    };
