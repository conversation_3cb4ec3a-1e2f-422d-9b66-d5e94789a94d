import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/auth/aq_currency_type_data.dart';

part 'aq_currency_types_response.g.dart';

@JsonSerializable()
class AqCurrencyTypesResponse {
  List<AqCurrencyTypeData> types;

  AqCurrencyTypesResponse(
    this.types,
  );

  factory AqCurrencyTypesResponse.fromJson(Map<String, dynamic> json) => _$AqCurrencyTypesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AqCurrencyTypesResponseToJson(this);
}
