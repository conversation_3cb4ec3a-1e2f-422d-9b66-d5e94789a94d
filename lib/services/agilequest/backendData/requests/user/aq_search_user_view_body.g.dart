// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_search_user_view_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqSearchUserViewBody _$AqSearchUserViewBodyFromJson(
        Map<String, dynamic> json) =>
    AqSearchUserViewBody(
      maxRows: json['maxRows'] as int,
      startRow: json['startRow'] as int,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      forManagement: json['forManagement'] as bool?,
      sortAscending: json['sortAscending'] as bool?,
      sortColumn: json['sortColumn'] as String?,
    );

Map<String, dynamic> _$AqSearchUserViewBodyToJson(
        AqSearchUserViewBody instance) =>
    <String, dynamic>{
      'maxRows': instance.maxRows,
      'startRow': instance.startRow,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'forManagement': instance.forManagement,
      'sortAscending': instance.sortAscending,
      'sortColumn': instance.sortColumn,
    };
