import 'package:json_annotation/json_annotation.dart';

part 'aq_search_user_view_body.g.dart';

@JsonSerializable()
class AqSearchUserViewBody {
  int maxRows;
  int startRow;
  String? firstName;
  String? lastName;
  bool? forManagement;
  bool? sortAscending;
  String? sortColumn;

  AqSearchUserViewBody({
    required this.maxRows,
    required this.startRow,
    this.firstName,
    this.lastName,
    this.forManagement,
    this.sortAscending,
    this.sortColumn
  });

  factory AqSearchUserViewBody.fromJson(Map<String, dynamic> json) => _$AqSearchUserViewBodyFromJson(json);

  Map<String, dynamic> toJson() => _$AqSearchUserViewBodyToJson(this);
}
