import 'package:json_annotation/json_annotation.dart';

part 'aq_user_credentials_request.g.dart';

@JsonSerializable()
class AqUserCredentialsRequest {
  String emailAddress;
  String password;

  AqUserCredentialsRequest(
    this.emailAddress,
    this.password,
  );

  factory AqUserCredentialsRequest.fromJson(Map<String, dynamic> json) =>
      _$AqUserCredentialsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AqUserCredentialsRequestToJson(this);
}
