import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';

part 'aq_app_credentials_request.g.dart';

@JsonSerializable()
class AqAppCredentialsRequest {
  String appLogin;
  String appPassword;

  AqAppCredentialsRequest(
    this.appLogin,
    this.appPassword,
  );

  factory AqAppCredentialsRequest.fromJson(Map<String, dynamic> json) =>
      _$AqAppCredentialsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AqAppCredentialsRequestToJson(this);

  factory AqAppCredentialsRequest.mobileAppCredentials() =>
      AqAppCredentialsRequest(
        GetPlatform.isIOS ? "iosMobile" : "androidMobile",
        "AgilquestApplication",
      );
}
