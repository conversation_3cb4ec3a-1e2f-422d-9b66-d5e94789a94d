import 'package:json_annotation/json_annotation.dart';


part 'aq_search_locations_request.g.dart';

@JsonSerializable(includeIfNull: false)
class AqSearchLocationsRequest {
  int sysidApplicationUser;
  List<int> sysidAllocRelationshipTypes;
  int? sysidLocatorNode;
  int locationDataTypeValue;

  AqSearchLocationsRequest({
    required this.sysidApplicationUser,
    required this.sysidAllocRelationshipTypes,
    this.sysidLocatorNode,
    required this.locationDataTypeValue,
  });

  factory AqSearchLocationsRequest.fromJson(Map<String, dynamic> json) =>
      _$AqSearchLocationsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AqSearchLocationsRequestToJson(this);
}
