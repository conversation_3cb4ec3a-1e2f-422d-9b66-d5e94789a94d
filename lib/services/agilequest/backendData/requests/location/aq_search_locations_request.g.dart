// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_search_locations_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqSearchLocationsRequest _$AqSearchLocationsRequestFromJson(
        Map<String, dynamic> json) =>
    AqSearchLocationsRequest(
      sysidApplicationUser: json['sysidApplicationUser'] as int,
      sysidAllocRelationshipTypes:
          (json['sysidAllocRelationshipTypes'] as List<dynamic>)
              .map((e) => e as int)
              .toList(),
      sysidLocatorNode: json['sysidLocatorNode'] as int?,
      locationDataTypeValue: json['locationDataTypeValue'] as int,
    );

Map<String, dynamic> _$AqSearchLocationsRequestToJson(
    AqSearchLocationsRequest instance) {
  final val = <String, dynamic>{
    'sysidApplicationUser': instance.sysidApplicationUser,
    'sysidAllocRelationshipTypes': instance.sysidAllocRelationshipTypes,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('sysidLocatorNode', instance.sysidLocatorNode);
  val['locationDataTypeValue'] = instance.locationDataTypeValue;
  return val;
}
