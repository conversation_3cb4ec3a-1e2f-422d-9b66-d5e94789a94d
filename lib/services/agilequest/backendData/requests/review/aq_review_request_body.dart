import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';

part 'aq_review_request_body.g.dart';

@JsonSerializable()
class AqReviewRequestBody {
  int? sysidReview;
  int rating;
  int sysidResource;
  String? comment;
  AqDate? aqStartTime;

  AqReviewRequestBody({
    this.sysidReview,
    required this.rating,
    required this.sysidResource,
    this.comment,
    this.aqStartTime
  });

  factory AqReviewRequestBody.fromJson(Map<String, dynamic> json) => _$AqReviewRequestBodyFromJson(json);

  Map<String, dynamic> toJson() => _$AqReviewRequestBodyToJson(this);
}
