// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_review_request_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReviewRequestBody _$AqReviewRequestBodyFromJson(Map<String, dynamic> json) =>
    AqReviewRequestBody(
      sysidReview: json['sysidReview'] as int?,
      rating: json['rating'] as int,
      sysidResource: json['sysidResource'] as int,
      comment: json['comment'] as String?,
      aqStartTime: json['aqStartTime'] == null
          ? null
          : AqDate.fromJson(json['aqStartTime'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AqReviewRequestBodyToJson(
        AqReviewRequestBody instance) =>
    <String, dynamic>{
      'sysidReview': instance.sysidReview,
      'rating': instance.rating,
      'sysidResource': instance.sysidResource,
      'comment': instance.comment,
      'aqStartTime': instance.aqStartTime,
    };
