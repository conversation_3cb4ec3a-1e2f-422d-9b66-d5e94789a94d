import 'package:json_annotation/json_annotation.dart';

part 'aq_reviews_request_body.g.dart';

@JsonSerializable()
class AqReviewsRequestBody {
  int startRow;
  int maxRows;
  int sysidResource;

  AqReviewsRequestBody({
    required this.startRow,
    required this.maxRows,
    required this.sysidResource
  });

  factory AqReviewsRequestBody.fromJson(Map<String, dynamic> json) => _$AqReviewsRequestBodyFromJson(json);

  Map<String, dynamic> toJson() => _$AqReviewsRequestBodyToJson(this);
}
