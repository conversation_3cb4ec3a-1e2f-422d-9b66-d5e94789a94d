import 'package:json_annotation/json_annotation.dart';

part 'aq_reservation_action_request.g.dart';

@JsonSerializable(includeIfNull: false)
class AqReservationAction {
  int? sysidReservation;
  int? sysidReservationActionType;
  bool? applyToRemaining;
  bool? suppressEmails;
  bool? approveChildReservations;
  bool? forQRCode;
  bool? succeeded;

  AqReservationAction({
    this.sysidReservation,
    this.sysidReservationActionType,
    this.applyToRemaining,
    this.suppressEmails,
    this.approveChildReservations,
    this.forQRCode,
    this.succeeded,
  });
  factory AqReservationAction.fromJson(Map<String, dynamic> json) => _$AqReservationActionFromJson(json);

  Map<String, dynamic> toJson() => _$AqReservationActionToJson(this);
}

class AqReservationActionList {
  List<AqReservationAction>? changes;
  AqReservationActionList({this.changes});

  Map toJson() => {'changes': changes};
}
