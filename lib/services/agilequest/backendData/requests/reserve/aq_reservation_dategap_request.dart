import 'package:json_annotation/json_annotation.dart';

import '../../../../../models/agilequest/date/aq_date.dart';

part 'aq_reservation_dategap_request.g.dart';

@JsonSerializable(includeIfNull: false)
class AqReservationDateGapReq {
  AqDate? stopTime;
  AqDate? reStartTime;
  int? sysidReservation;
  int? sysidReservationActionType;
  bool? suppressEmail;

  AqReservationDateGapReq({
    this.stopTime,
    this.reStartTime,
    this.sysidReservation,
    this.sysidReservationActionType,
    this.suppressEmail,
  });

  factory AqReservationDateGapReq.fromJson(Map<String, dynamic> json) => _$AqReservationDateGapReqFromJson(json);

  Map<String, dynamic> toJson() => _$AqReservationDateGapReqToJson(this);
}
