// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_reservation_dategap_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReservationDateGapReq _$AqReservationDateGapReqFromJson(
        Map<String, dynamic> json) =>
    AqReservationDateGapReq(
      stopTime: json['stopTime'] == null
          ? null
          : AqDate.fromJson(json['stopTime'] as Map<String, dynamic>),
      reStartTime: json['reStartTime'] == null
          ? null
          : AqDate.fromJson(json['reStartTime'] as Map<String, dynamic>),
      sysidReservation: json['sysidReservation'] as int?,
      sysidReservationActionType: json['sysidReservationActionType'] as int?,
      suppressEmail: json['suppressEmail'] as bool?,
    );

Map<String, dynamic> _$AqReservationDateGapReqToJson(
    AqReservationDateGapReq instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('stopTime', instance.stopTime);
  writeNotNull('reStartTime', instance.reStartTime);
  writeNotNull('sysidReservation', instance.sysidReservation);
  writeNotNull(
      'sysidReservationActionType', instance.sysidReservationActionType);
  writeNotNull('suppressEmail', instance.suppressEmail);
  return val;
}
