import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date_range.dart';

part 'aq_reserve_view_criteria_request.g.dart';

@JsonSerializable()
class AqReserveViewCriteriaRequest {
  int? startRow;
  int? maxRows;
  List<int>? sysidUserOwners;
  List<int>? sysidUserCreators;
  List<AqDateRange>? dates;
  String? sortColumn;
  bool? onlyParents;
  List<int>? reservationStates;
  List<int>? sysidCategories;
  List<int>? sysidLocatorNodes;
  String? purposeType;
  bool? sortAscending;
  bool? usersArgsOred;

  AqReserveViewCriteriaRequest({
    this.startRow,
    this.maxRows,
    this.sysidUserOwners,
    this.dates,
    this.sortColumn,
    this.onlyParents,
    this.reservationStates,
    this.purposeType,
    this.sortAscending,
    this.sysidCategories,
    this.sysidLocatorNodes,
    this.sysidUserCreators,
    this.usersArgsOred,
  });

  factory AqReserveViewCriteriaRequest.fromJson(Map<String, dynamic> json) => _$AqReserveViewCriteriaRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AqReserveViewCriteriaRequestToJson(this);
}
