// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_reservation_action_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReservationAction _$AqReservationActionFromJson(Map<String, dynamic> json) =>
    AqReservationAction(
      sysidReservation: json['sysidReservation'] as int?,
      sysidReservationActionType: json['sysidReservationActionType'] as int?,
      applyToRemaining: json['applyToRemaining'] as bool?,
      suppressEmails: json['suppressEmails'] as bool?,
      approveChildReservations: json['approveChildReservations'] as bool?,
      forQRCode: json['forQRCode'] as bool?,
      succeeded: json['succeeded'] as bool?,
    );

Map<String, dynamic> _$AqReservationActionToJson(AqReservationAction instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('sysidReservation', instance.sysidReservation);
  writeNotNull(
      'sysidReservationActionType', instance.sysidReservationActionType);
  writeNotNull('applyToRemaining', instance.applyToRemaining);
  writeNotNull('suppressEmails', instance.suppressEmails);
  writeNotNull('approveChildReservations', instance.approveChildReservations);
  writeNotNull('forQRCode', instance.forQRCode);
  writeNotNull('succeeded', instance.succeeded);
  return val;
}
