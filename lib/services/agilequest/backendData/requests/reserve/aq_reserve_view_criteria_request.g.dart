// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_reserve_view_criteria_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReserveViewCriteriaRequest _$AqReserveViewCriteriaRequestFromJson(
        Map<String, dynamic> json) =>
    AqReserveViewCriteriaRequest(
      startRow: json['startRow'] as int?,
      maxRows: json['maxRows'] as int?,
      sysidUserOwners: (json['sysidUserOwners'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      dates: (json['dates'] as List<dynamic>?)
          ?.map((e) => AqDateRange.fromJson(e as Map<String, dynamic>))
          .toList(),
      sortColumn: json['sortColumn'] as String?,
      onlyParents: json['onlyParents'] as bool?,
      reservationStates: (json['reservationStates'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      purposeType: json['purposeType'] as String?,
      sortAscending: json['sortAscending'] as bool?,
      sysidCategories: (json['sysidCategories'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      sysidLocatorNodes: (json['sysidLocatorNodes'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      sysidUserCreators: (json['sysidUserCreators'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      usersArgsOred: json['usersArgsOred'] as bool?,
    );

Map<String, dynamic> _$AqReserveViewCriteriaRequestToJson(
        AqReserveViewCriteriaRequest instance) =>
    <String, dynamic>{
      'startRow': instance.startRow,
      'maxRows': instance.maxRows,
      'sysidUserOwners': instance.sysidUserOwners,
      'sysidUserCreators': instance.sysidUserCreators,
      'dates': instance.dates,
      'sortColumn': instance.sortColumn,
      'onlyParents': instance.onlyParents,
      'reservationStates': instance.reservationStates,
      'sysidCategories': instance.sysidCategories,
      'sysidLocatorNodes': instance.sysidLocatorNodes,
      'purposeType': instance.purposeType,
      'sortAscending': instance.sortAscending,
      'usersArgsOred': instance.usersArgsOred,
    };
