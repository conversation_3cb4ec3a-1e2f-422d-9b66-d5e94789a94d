import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/ta_admin/change_context.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

import '../../providers/ta_admin/home_controller.dart';

//calling rest api for clients
Future<List<Client>?> fetchClients({String? queryparams}) async {
  String apival = clienturl;

  List<Client>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Clients'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Client.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchClients>>>>>>>$e');
  }
  return tagObjs;
}

//calling rest api for brands
Future<List<Brand>?> fetchBrands({required String queryparams}) async {
  String apival = brandsurl + queryparams;

  List<Brand>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Brands'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Brand.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchBrands>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<Bu>?> fetchBu({required String queryparams}) async {
  String apival = buurl + queryparams;

  List<Bu>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Bu'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Bu.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchBu>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<Country>?> fetchCountry({required String queryparams}) async {
  String apival = countriesurl + queryparams;

  List<Country>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Countries'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Country.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchCountry>>>>>>>$e');
  }
  return tagObjs;
}

Future<List<Language>?> fetchLanguage({required String queryparams}) async {
  String apival = localeurl + queryparams;

  List<Language>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['Languages'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Language.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchLanguage>>>>>>>$e');
  }
  return tagObjs;
}

Future<String> updateContext({String? data, String? userPrefLocale}) async {
  String result = 'error';
  String? token;
  Map<String, dynamic>? c = {};
  try {
    String apival = changecontexturl;

    var resMap = await ApiService.authpost(apival, payloadObj: data);
    var tagObjsJson = jsonDecode(resMap) as Map<String, dynamic>;

    if (tagObjsJson['status'] == 0) {
      // debugPrint('tagObjsJson-----${tagObjsJson}');
      token = tagObjsJson['token'] as String?;
      SharedPrefUtils.saveStr(ConstHelper.tokenVal, token);
      c = tagObjsJson['context'] as Map<String, dynamic>?;
      debugPrint('context-----$c');
      if (c != null) {
        SharedPrefUtils.setContextUserInfo(c, sfrom: 'change_context', userPrefLocale: userPrefLocale);
      }
      result = 'success';
    }
  } catch (error) {
    print(error);
    rethrow;
  }
  return result;
}

// String getSeid() {
//   String seid = '';
//   String cookieVal = SharedPrefUtils.readPrefStr('cookieVal');
//   var setCookies = cookieVal.split(';');
//   var keyValue = setCookies[0].split('=');
//   var key = keyValue[0].trim();
//   if (key == 'JSESSIONID') {
//     String value = keyValue[1].trim();
//     int exaloc = value.indexOf("!");
//     if (exaloc != -1) {
//       seid = value.substring(0, exaloc);
//     }
//     debugPrint(seid);
//   }
//   return seid;
// }
