import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

class DocAttachmentServices {
  static Future<String> uploadFiles(
      {String? entityType,
      int? entityId,
      int? subentityid,
      String? subentitytype,
      int? folderId,
      List<String>? paths}) async {
    String result = 'error';
    folderId = folderId ?? 0;
    String apival = '$adddmsurl/$entityType/$entityId/$folderId?subentityid=$subentityid&subentitytype=$subentitytype';
    try {
      await ApiService.postuploaddata(apival: apival, paths: paths);
      result = 'success';
    } catch (error) {
      debugPrint('$error');
    }
    return result;
  }

  static Future<String> deleteDmsFiles(Map m) async {
    String result = 'error';
    try {
      var apival = deletefilesurl;

      String bodyjson = json.encode(m);

      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    }
    debugPrint(result);
    return result;
  }
}
