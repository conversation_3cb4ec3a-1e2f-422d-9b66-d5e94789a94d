import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

import '../common_services.dart';

class PunchlistServices {
  static Future<Map<String, dynamic>?> fetchDynamicApi({dynamic payloadObj, String? url}) async {
    debugPrint('--------fetchPunchlist------------');

    Map<String, dynamic>? resMap;
    try {
      resMap = await CommonServices.fetchDynamicApi(url ?? punchlisturl, payloadObj: payloadObj);
      debugPrint('$resMap');
    } catch (e) {
      debugPrint('fetchPunchlist>>>>>>>$e');
    }
    return resMap;
  }

  static Future<Map<String, dynamic>> addPunchlistItem(Map m, {String? url}) async {
    int? result = 0;
    Map<String, dynamic> rMap = {
      'msg': 'Unknown Error Occurred',
      'res': 'fail',
    };

    try {
      var apival = url ?? addpunchlisturl;
      String bodyjson = jsonEncode(m);

      debugPrint('bodyjson    $bodyjson');

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);

      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = resMap['id'] as int?;
          rMap['msg'] = 'New Item added successfully...';
          rMap['id'] = result;
          rMap['res'] = 'success';
        } else {
          rMap['msg'] = resMap['statusmessage'];
          rMap['res'] = 'error';
        }
      }
    } catch (e) {
      debugPrint('$e');
    }
    return rMap;
  }

  static Future<String> applyAllFlagActions(Map m) async {
    String result = 'error';
    try {
      var apival = applyFlagFilterspunchlisturl;
      String bodyjson = jsonEncode(m);

      debugPrint('bodyjson    $bodyjson');

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);

      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (e) {
      debugPrint('$e');
    }
    return result;
  }

  static Future<String> updatePunchlistDetDataImpl(Map m) async {
    String result = 'error';
    try {
      String bodyjson = json.encode(m);
      var apival = updatepunchlisturl;
      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      print(error);
    }
    print(result);
    return result;
  }

  static Future<String> deletePunchlistDetDataImpl(int? id, int? entityid) async {
    String result = 'error';
    try {
      var apival = deletepunchlisturl;

      String bodyjson = json.encode({
        'scopePunchListId': id,
        'entityId': entityid,
      });

      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      print(error);
    }
    print(result);
    return result;
  }
}
