import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/common/entity_comment.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

class EntityCommentsServices {
  static Future<List<EntityComment>?> fetchEntityComments(String entityType, int entityId, {int? subentityid}) async {
    String apival = '$commentsurl/$entityId/$entityType';
    apival = subentityid != null ? '$apival?subentityid=$subentityid' : apival;
    List<EntityComment>? tagObjs;
    try {
      var resMap = await ApiService.get(apival);
      //var tagObjsJson;
      //if (resMap.isNotEmpty) {
      //if (resMap['status'] == 0) {
      debugPrint(resMap);
      if (resMap != null) {
        var tagObjsJson = jsonDecode(resMap)['Comments'] as List?;
        if (tagObjsJson != null) {
          tagObjs = tagObjsJson.map((tagJson) => EntityComment.fromJson(tagJson)).toList();
        }
      }
      // }
      //}
    } catch (e) {
      debugPrint('fetchEntityComments>>>>>>>$e');
    }
    return tagObjs;
  }

  static Future<String> addComments(Map m) async {
    String result = 'error';
    try {
      var apival = addcommentsurl;
      String bodyjson = jsonEncode(m);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);

      if (resMap != null) {
        resMap = jsonDecode(resMap);
        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (e) {
      debugPrint('$e');
    }
    return result;
  }
}
