import 'package:flutter/material.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../common_services.dart';

class MilestoneServices {
  static Future<Map<String, dynamic>?> fetchMilestones({dynamic payloadObj, String? url}) async {
    debugPrint('--------fetchMilestones------------');

    Map<String, dynamic>? tagObjs;
    try {
      tagObjs = await CommonServices.fetchDynamicApi(url ?? milestoneurl, payloadObj: payloadObj);
      debugPrint('$tagObjs');
    } catch (e) {
      debugPrint('fetchMilestones>>>>>>>$e');
    }
    return tagObjs;
  }
}
