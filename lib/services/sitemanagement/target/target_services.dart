import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:tangoworkplace/utils/request_api_utils.dart';

import '../../../models/sitemanagement/target/targetfv.dart';

class TargetServices {
  static Future<dynamic> gettargetfvdate(int? targetid, String lookupcodes) async {
    String apival = '$fieldvalidationdataurl?targetid=$targetid&lookupcode=$lookupcodes';
    var resMap;
    try {
      resMap = await ApiService.get(apival);

      resMap = jsonDecode(resMap);
    } catch (e) {
      debugPrint('gettargetfvdate>>>>>>>$e');
    }
    return resMap;
  }

  static Future<dynamic> saveFieldValidationData(TargetFV fv) async {
    var resMap;
    try {
      String bodyjson = json.encode(fv);
      var apival = updatefieldvalidationurl;
      debugPrint(bodyjson);

      resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          return 'success';
        }
      }
    } catch (error) {
      debugPrint('$error');
    }

    return null;
  }
}
