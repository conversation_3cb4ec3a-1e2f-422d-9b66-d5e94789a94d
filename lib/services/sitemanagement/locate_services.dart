import 'package:flutter/material.dart';
import 'package:tangoworkplace/common/common_import.dart';

class LocateServices {
  static Future getentitiesservice(Map m) async {
    var resMap;
    try {
      String payloadObj = json.encode(m);
      var apival = locateentitiesurl;
      debugPrint(payloadObj);

      resMap = await ApiService.post(apival, payloadObj: payloadObj);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          return resMap;
        }
      }
    } catch (error) {
      debugPrint('$error');
    }

    return null;
  }
}
