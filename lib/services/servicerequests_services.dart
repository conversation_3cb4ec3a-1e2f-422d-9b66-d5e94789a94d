import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:tangoworkplace/utils/request_api_utils.dart';
import '../models/servicerequest/service_request.dart';
import '../utils/common_utils.dart';
import '../utils/preferences_utils.dart';
import '../utils/connections.dart';

class ServiceRequestServices {
  static Future saveSrDetailsdata(Servicerequest sr) async {
    var result = 'error';
    try {
      String bodyjson = json.encode(sr);
      var apival = saveservicerequesturl;
      debugPrint(bodyjson);

      var resMap = await ApiService.post(apival, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          result = 'success';
        }
      }
    } catch (error) {
      debugPrint(error.toString());
    }

    return result;
  }

  static Future<int?> getsrid() async {
    int? result = 0;
    try {
      var apival = getservicerequestidurl;

      var resMap = await ApiService.get(apival);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        var status = resMap['status'] as int?;
        if (status == 0) {
          result = resMap['id'] as int?;
        }
      }
    } catch (error) {
      debugPrint(error.toString());
    } finally {}

    return result;
  }
}

//calling rest api for Servicerequests
Future<List<Servicerequest>?> fetchServiceRequests({required String queryparams}) async {
  String apival = '$servicerequesturl/search$queryparams';
  List<Servicerequest>? tagObjs;
  try {
    var resMap = await ApiService.get(apival);
    //var tagObjsJson;
    //if (resMap.isNotEmpty) {
    //if (resMap['status'] == 0) {
    var tagObjsJson = jsonDecode(resMap)['ServiceRequests'] as List?;
    if (tagObjsJson != null) {
      tagObjs = tagObjsJson.map((tagJson) => Servicerequest.fromJson(tagJson)).toList();
    }
    // }
    //}
  } catch (e) {
    debugPrint('fetchServiceRequests>>>>>>>  $e');
  }
  return tagObjs;
}

Future<Map?> addServicerequestImpl(Servicerequest sr) async {
  Map<String, dynamic> result;
  try {
    String bodyjson = json.encode({
      'priority': sr.priority,
      'status': sr.status,
      'description': sr.description,
      'entityId': sr.entityId,
      'entityType': sr.entityType,
      'requestedFor': sr.requestedFor,
      'impactedArea': sr.impactedArea,
      'problemCode': sr.problemCode,
      'phone': sr.phone,
      'notifyEmail': sr.notifyEmail,
      'issueType': sr.issueType,
      'lastUpdatedBy': sr.lastUpdatedBy,
      'serviceReqId': sr.serviceReqId,
      'floorId': sr.floorId,
      'spaceId': sr.spaceId,
    });

    bodyjson = '{"ServiceRequest": $bodyjson }';
    debugPrint(bodyjson);

    String apival = '$servicerequesturl/create';

    var resMap = await ApiService.post(apival, payloadObj: bodyjson);
    var tagObjsJson = jsonDecode(resMap) as String?;

    if (tagObjsJson == 'success') {
      result = {'result': tagObjsJson};
      return result;
    }
  } catch (error) {
    print(error);
    rethrow;
  }
  return null;
}

Future<String?> updateServicerequestImpl(Servicerequest sr, int srId) async {
  String result = 'error';
  try {
    var prio = (sr.priority != null || sr.priority == '') ? 'Low' : sr.priority;
    String bodyjson = json.encode({
      'priority': prio,
      'status': sr.status,
      'description': sr.description,
      'entityId': sr.entityId,
      'requestedFor': sr.requestedFor,
      'impactedArea': sr.impactedArea,
      'problemCode': sr.problemCode,
      'phone': sr.phone,
      'notifyEmail': sr.notifyEmail,
      'issueType': sr.issueType,
      'floorId': sr.floorId,
      'spaceId': sr.spaceId,
    });
    bodyjson = '{"ServiceRequest": $bodyjson }';
    debugPrint(bodyjson);
    String apival = '$servicerequesturl/update?servicerequestid=$srId';

    var resMap = await ApiService.post(apival, payloadObj: bodyjson);
    String? tagObjsJson = resMap;

    if (tagObjsJson == 'success') {
      return tagObjsJson;
    }
  } catch (error) {
    print(error);
  }
  print(result);
  return result;
}
