import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../models/suppliermanagement/supplier_data.dart';

class SupplierDetailsController extends GetxController {
  Rx<SupplierData> supplierrec = SupplierData().obs;
  var mode = 'view'.obs;
  var isloading = false.obs;
  Future getSupplierRecord(String? supplierid) async {
    debugPrint('------------getSupplierRecord--------');

    List<SupplierData> supp;

    Map<String, dynamic>? resMap;
    //var user = UserContext.info()!.userName;
    Map<String, dynamic> dynamicMap = {
      'a': ['supplier_id', 'EXACT', supplierid],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(suppliersurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            supp = tagObjsJson.map((tagJson) => SupplierData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${supp.length}');
            if (supp.isNotEmpty) {
              supplierrec.value = supp.first;

              mode.value = supplierrec.value.isEditable?.toUpperCase() == 'Y' ? 'edit' : 'view';
              debugPrint('supplier name>>>>${supplierrec.value.supplierName}');
              debugPrint('mode>>>>${mode.value}');
            }
          }
        }
      }
    } finally {
      //isloading.value = false;
    }
  }
}
