import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../models/sitemanagement/site/siteview.dart';
import '../../models/suppliermanagement/supplier_search.dart';

class SupplierMangementSearchController extends GetxController {
  TextEditingController searchCtrl = TextEditingController();

  RxList<SupplierSearch> suppliers = <SupplierSearch>[].obs;
  var isLoading = false.obs;

  Future getSupplierData({String? searchText}) async {
    debugPrint('------------getSupplierData--------');
    suppliers.clear();
    isLoading.value = true;
    List<SupplierSearch> supp;
    String? payloadObj;
    Map<String, dynamic>? resMap;
    if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['all_columns', 'CONTAINS', searchText],
      };
      payloadObj = CommonServices.getFilter(dynamicMap);
    }

    try {
      resMap = await CommonServices.fetchDynamicApi(suppliersearchviewurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            supp = tagObjsJson.map((tagJson) => SupplierSearch.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${supp.length}');
            suppliers.addAll(supp);
          }
        } else {
          suppliers.clear();
        }
      } else {
        suppliers.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isLoading.value = false;
    }
  }
}
