import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/suppliermanagement/supplier_data.dart';

import '../../common/component_utils.dart';
import '../../common/progess_indicator_cust.dart';
import '../../models/ta_admin/entitystatus.dart';
import '../../services/common_services.dart';
import '../../utils/connections.dart';
import '../../utils/request_api_utils.dart';
import '../ta_admin/label_controller.dart';

class SupplierGeneralController extends GetxController {
  var labelsloading = false.obs;
  var isLoading = false.obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var supplier = SupplierData().obs;
  LabelController talabel = Get.find<LabelController>();

  getlabels() async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_SUPPLIER_GENERAL', 'supplier_general');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  Future onloadPageData(SupplierData s) async {
    getlabels();
    isLoading.value = true;
    supplier.value = s;
    debugPrint('inside general --supplier name>>>>${supplier.value.supplierName}');
    statuslov.clear();
    statuslov.value = await CommonServices.getStatuses('SUPPLIER');
  }

  Future onSaveSupplierRecord() async {
    debugPrint('onSaveSupplierRecord status   ${supplier.value.status}');
    ProgressUtil.showLoaderDialog(Get.context!);
    var result = -1;
    try {
      Map m = {
        'source': 'site_general',
        'site_id': supplier.value.supplierId,
        'status': supplier.value.status,
      };
      result = await saveRecord(m);
    } catch (e) {
      debugPrint('$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    if (result == 0) {
      ComponentUtils.showsnackbar(text: "Data saved successfully...");
    } else {
      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }

  static Future saveRecord(Map m) async {
    int? status = -1;
    try {
      String bodyjson = json.encode(m);

      debugPrint(bodyjson);

      var resMap = await ApiService.post(savesiterecordurl, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        status = resMap['status'] as int?;
        debugPrint('status>>>>>$status');
      }
    } catch (error) {}

    return status;
  }
}
