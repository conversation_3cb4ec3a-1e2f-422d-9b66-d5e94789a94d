import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import '../../../models/spacemgmt/buildings/buildingsview_data.dart';
import '../../../models/spacemgmt/property/propertyview_data.dart';
import '../../../models/ta_admin/entitystatus.dart';
import '../../ta_admin/label_controller.dart';

class PropertySearchController extends GetxController {
  LabelController talabel = Get.find<LabelController>();
  TextEditingController searchController = TextEditingController();
  var tabroles = <dynamic, dynamic>{}.obs;

  RxList<PropertyViewData> propertylist = <PropertyViewData>[].obs;
  var isLoading = false.obs;
  //search widget
  var filterwidgetloading = false.obs;

  var filterList = [].obs;

  Future getPropertySearchData({String? searchText, String? action}) async {
    debugPrint('getPropertySearchData>>>');
    // var user = UserContext.info()!.userName;
    bool filterFlag = false;
    propertylist.clear();
    isLoading.value = true;
    List<PropertyViewData> propertyRec;
    String? payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap;
    var filter;

    if (searchController.text.isNotEmpty && searchController.text != '') {
      dynamicMap = {
        'a': ['all_columns', 'CONTAINS', searchController.text],
      };
      filterFlag = true;
      payloadObj = CommonServices.getFilter(dynamicMap);
    }
    try {
      resMap = await CommonServices.fetchDynamicApi(propertysearchviewurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            propertyRec = tagObjsJson.map((tagJson) => PropertyViewData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${propertyRec.length}');
            propertylist.addAll(propertyRec);
          }
        } else {
          propertylist.clear();
        }
      } else {
        propertylist.clear();
      }
    } finally {
      isLoading.value = false;
    }
  }

  // Future loadFilterLovData(String? action) async {
  //   if (action == 'onload') {
  //     await getStatusLovs();
  //     sortByLov.value.clear();
  //     sortByLov.insert(0, EntityStatus(statusCode: '', status: 'Select'));

  //     //setting sort By lov list--------------------------------------------------------------------------
  //     if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENUMBER') != null) {
  //       sortByLov.value.add(EntityStatus(
  //           status: talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENUMBER')?.value ?? 'Store Number',
  //           statusCode: 'STORE_NUMBER'));
  //     }
  //     if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENAME') != null) {
  //       sortByLov.value.add(EntityStatus(
  //           status: talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_STORENAME')?.value ?? 'Store Name', statusCode: 'STORE_NAME'));
  //     }

  //     if (talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_PROPERTYNAME') != null) {
  //       sortByLov.value.add(EntityStatus(
  //           status: talabel.get('TMCMOBILE_BUILDINGSSEARCH_FILTERS_SORTBY_PROPERTYNAME')?.value ?? 'Property Name',
  //           statusCode: 'PROPERTY_NAME'));
  //     }
  //   }
  // }

  // Future getStatusLovs() async {
  //   try {
  //     statuslov.value = await CommonServices.getStatuses('BUILDING');
  //   } catch (e) {
  //     debugPrint('getStatusLovs>>>>>>>$e');
  //   }
  // }

  // setFilterChips(bool flag) {
  //   Future.delayed(const Duration(seconds: 0), () {
  //     filterList.clear();
  //     //filterList.add(allmyfilter.value);

  //     filterList.add(GestureDetector(
  //       child: Chip(
  //         label: Text(isLoading.value ? '' : allmyfilter.value),
  //       ),
  //       onTap: () async {
  //         if (isLoading.value) {
  //           debugPrint('------');
  //         } else {
  //           if (allmyfilter.value == 'My') {
  //             allmyfilter.value = 'All';
  //           } else if (allmyfilter.value == 'All') {
  //             allmyfilter.value = 'My';
  //           }

  //           await getBuildingSearchData();
  //         }
  //       },
  //     ));

  //     if (flag) {
  //       if (status.value != null && status.value != '') {
  //         var statusname =
  //             statuslov.firstWhere((EntityStatus s) => s.statusCode == status.value, orElse: () => EntityStatus(status: '')).status ?? '';

  //         filterList.add(Chip(
  //           avatar: const Icon(Icons.flag, size: 20),
  //           label: Text(statusname),
  //           onDeleted: () async {
  //             status.value = '';
  //             await getBuildingSearchData();
  //           },
  //         ));
  //       }
  //       if (searchController.text != null && searchController.text.isNotEmpty && searchController.text != '') {
  //         filterList.add(Chip(
  //           avatar: const Icon(Icons.search, size: 20),
  //           label: Text(searchController.text),
  //           onDeleted: () async {
  //             searchController.text = '';
  //             await getBuildingSearchData();
  //           },
  //         ));
  //       }
  //     }
  //   });
  // }

  // Future<String> getUserEntityEditAccess({required String entitytype, required String entityid, String? roFlag}) async {
  //   ProgressUtil.showLoaderDialog(Get.context!);
  //   Map<dynamic, dynamic>? vmap;
  //   String res = 'view';
  //   tabroles.value = Map<dynamic, dynamic>();
  //   try {
  //     vmap = await CommonServices.userEntityEditAccessService(entityid: entityid, entitytype: entitytype);
  //     debugPrint('getUserEntityEditAccess >>>>>>>' + roFlag.toString());
  //     if (vmap != null) {
  //       tabroles.value = vmap;
  //       if (vmap['result'] != null && vmap['result'] == 'Y' && vmap['role_flag'] != null && vmap['role_flag'] == 'Y')
  //         res = 'Y';
  //       else if (vmap['result'] != null && vmap['result'] == 'CN') res = 'error';

  //       if (res != 'error') res = (res?.toUpperCase() == 'Y' && (roFlag?.toUpperCase() == 'N' || roFlag == null)) ? 'edit' : 'view';
  //     }
  //   } catch (e) {
  //     debugPrint('getUserEntityEditAccess Exception >>>>>>>>>>>$e');
  //   } finally {
  //     ProgressUtil.closeLoaderDialog(Get.context!);
  //   }
  //   debugPrint('getUserEntityEditAccess >>>>>>>' + res);
  //   return res;
  //   //}
  // }
}
