import 'dart:core';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:collection/collection.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/models/ta_admin/page_rules.dart';
//import 'package:tangoworkplace/utils/labels/label_utils.dart';
import 'package:tangoworkplace/utils/labels/labeldata_utils.dart';

import '../../models/agilequest/auth/aq_languagecodes_data.dart';
import '../../utils/labels/dbdata_utils.dart';

class LabelController extends GetxController {
  var key;
  LabelController();
  var init = true.obs;
  var locflag = false.obs;
  RxList<Applabel> hlabels = <Applabel>[].obs;
  RxList<Applabel> olabels = <Applabel>[].obs;
  RxList<AqLanguageCodesData> aqLangcodes = <AqLanguageCodesData>[].obs;
  var entityrules = [].obs;
  var labelsloading = false.obs;
  var setdataloading = false.obs;
  Rx<ReservationRules> reservationrules = ReservationRules().obs;

  getapilabels(String refr) async {
    await AppLabels.loadmetada('TMCMOBILE_HOME', refr);
  }

  getlabelsfromdb(String pkey) async {
    hlabels.value = (await AppLabels.loadlabelsfromdb(pkey))!;
  }

  Future getlabels(String pkey, String refr, {bool? ruleflag, String? filtertype, int? size, String? ruletype}) async {
    try {
      if (refr == 'home') {
        locflag.value = true;
        labelsloading.value = true;
        await AppLabels.clearolddata();
      } else {
        locflag.value = false;
      }
      await AppLabels.loadmetada(pkey, refr, filtertype: filtertype, size: size, ruletype: ruletype);
      List<Applabel>? list = await AppLabels.loadlabelsfromdb(
        pkey,
        filtertype: filtertype,
      );
      if (list != null && list.isNotEmpty) {
        if (locflag.value) {
          // hlabels.value.addAll(list);
          hlabels.value = list;
        } else {
          olabels.value = list;
        }
      }
    } catch (e) {
      debugPrint('getlabels  >>>>>>$e');
    }
    labelsloading.value = false;
    debugPrint('----------labels----------');
  }

  Applabel? get(String key) {
    Applabel? label;
    if (key.startsWith('TMCMOBILE_HOME') || key.startsWith('TMCMOBILE_COMMON_BUTTONS')) {
      if (hlabels.value.isNotEmpty) {
        // labels = locflag.value ? hlabels.value : olabels.value;
        var labels = hlabels.value;
        label = labels.firstWhereOrNull(
          (element) => element.key == key,
        );
      }
    } else {
      if (olabels.value.isNotEmpty) {
        // labels = locflag.value ? hlabels.value : olabels.value;
        var labels = olabels.value;
        label = labels.firstWhereOrNull(
          (element) => element.key == key,
        );
      }
    }
    if (label != null) {
      label.ro = label.edit?.toUpperCase() == 'TRUE' ? false : true;
    }
    return label;
  }

  Future<String> getaqlangcode(String key) async {
    return await DBDataProvider.db.getaqlangcode(key);
  }

  Future<String> getAqLabel({String? key, required String defaultTxt}) async {
    String? txt = await DBDataProvider.db.getaqlangcode(key!);
    if (txt != '') {
      defaultTxt = txt;
    }

    return defaultTxt;
  }

  AqLanguageCodesData? getAq({String? key, required String defaultTxt}) {
    AqLanguageCodesData? label;
    label = aqLangcodes.value.firstWhereOrNull((element) => element.key == key);
    if (label == null || label.tag == null) label = AqLanguageCodesData(key!, defaultTxt);

    return label;
  }
}
