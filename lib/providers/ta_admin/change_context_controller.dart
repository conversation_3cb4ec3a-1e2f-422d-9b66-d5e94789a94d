import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/ta_admin/change_context.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import '../../services/ta_admin/change_client_context_services.dart';

class ChangeContextController extends GetxController {
  Rx<bool> isLoading = false.obs;
  Rx<bool> isInitialLoading = false.obs;
  Rxn<int> clientIdSelected = Rxn<int>();
  Rxn<int> brandIdSelected = Rxn<int>();
  Rxn<int> buIdSelected = Rxn<int>();
  Rxn<String> contryCodeSelected = Rxn<String>();
  Rxn<String> languageSelected = Rxn<String>();
  Rxn<String> isBrandEnabled = Rxn<String>();
  Rxn<String> isBuEnabled = Rxn<String>();

  RxList<Client> clientList = <Client>[].obs;
  RxList<Brand> brandList = <Brand>[].obs;
  RxList<Bu> buList = <Bu>[].obs;
  RxList<Country> countryList = <Country>[].obs;
  RxList<Language> languageList = <Language>[].obs;

  String userPrefferedLocale = '';
  bool _isFormInitiallyValidated = false;
  final formKey = GlobalKey<FormState>();

  getClientsData() async {
    debugPrint('-----------------------------getClientsData-------------------------------------');
    try {
      isLoading(true);
      final allClients = await fetchClients() ?? [];
      clientList.clear();
      clientList.addAll(filterUniqueValues(allClients));
      clientList.insert(0, Client(clientId: null, clientName: 'Select a Client', value: null, text: 'Select a Client'));
      debugPrint('clientList   $clientList');
      isLoading(false);
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  getBrandsData(int clientId) async {
    debugPrint('-----------------------------getBrandsData-------------------------------------');
    try {
      isLoading(true);
      String qp = getQueryParams(clientid: clientId);
      final allBrand = await fetchBrands(queryparams: qp) ?? [];
      brandList.clear();
      brandList.addAll(filterUniqueValues(allBrand));

      debugPrint('brandList   $brandList');

      isLoading(false);
    } catch (error) {
      debugPrint('$error');
    }
  }

  getBuData(int clientId, int? brandId, String? isbrandenabled) async {
    debugPrint('-----------------------------getBuData-------------------------------------');
    try {
      isLoading(true);
      String qp = getQueryParams(clientid: clientId, brandid: brandId, isbrandenabled: isbrandenabled);
      final allBu = await fetchBu(queryparams: qp) ?? [];
      buList.clear();
      buList.addAll(filterUniqueValues(allBu));
      debugPrint('buList   $buList');

      isLoading(false);
    } catch (error) {
      debugPrint('$error');
    }
  }

  getCountryData({int? clientId, int? brandId, String? isbrandenabled, int? buId, String? isbuEnabled}) async {
    debugPrint('-----------------------------getCountryData-------------------------------------');
    try {
      isLoading(true);
      String qp =
          getQueryParams(clientid: clientId, brandid: brandId, isbrandenabled: isbrandenabled, buid: buId, isbuenabled: isbuEnabled);
      final allCountries = await fetchCountry(queryparams: qp) ?? [];
      countryList.clear();
      countryList.addAll(filterUniqueValues(allCountries));

      if (isbuEnabled == 'Y') {
        Country cn = Country();
        cn.countryName = 'All';
        cn.countryCode = 'xxxx';
        cn.value = 'xxxx';
        cn.text = 'All';
        countryList.insert(0, cn);
      }

      debugPrint('countryList   $countryList');

      isLoading(false);
    } catch (error) {
      debugPrint('$error');
    }
  }

  getLanguageData({int? clientId, int? brandId, String? isbrandenabled, int? buId, String? isbuEnabled, String? countryCode}) async {
    debugPrint('-----------------------------getLanguageData-------------------------------------');
    try {
      isLoading(true);
      String qp = getQueryParams(
          clientid: clientId, brandid: brandId, isbrandenabled: isbrandenabled, buid: buId, isbuenabled: isbuEnabled, country: countryCode);

      final allLanguages = await fetchLanguage(queryparams: qp) ?? [];
      languageList.clear();
      languageList.addAll(filterUniqueValues(allLanguages));
      debugPrint('languageList   $languageList');

      isLoading(false);
    } catch (error) {
      debugPrint('$error');
    }
  }

  List<T> filterUniqueValues<T>(List<T> inputList) {
    Set<T> uniqueSet = {};
    List<T> uniqueList = [];

    for (var item in inputList) {
      if (uniqueSet.add(item)) {
        uniqueList.add(item);
      }
    }

    return uniqueList;
  }

  String getQueryParams(
      {int? clientid, int? brandid, String? isbrandenabled, int? buid, String? isbuenabled, String? country, String? language}) {
    debugPrint(
        'clientid   $clientid    brandid    $brandid    isbrandenabled    $isbrandenabled   buid  $buid    isbuenabled    $country   buid  $country');
    var buffer = StringBuffer();
    buffer.write('?');

    (clientid != null) ? buffer.write('clientid=$clientid') : '';
    (brandid != null) ? buffer.write('&brandid=$brandid') : '';
    (isbrandenabled != null) ? buffer.write('&isbrandenabled=$isbrandenabled') : buffer.write('&isbrandenabled=N');
    (buid != null) ? buffer.write('&buid=$buid') : '';
    (isbuenabled != null) ? buffer.write('&isbuenabled=$isbuenabled') : buffer.write('&isbuenabled=N');
    (country != null && country != 'xxxx') ? buffer.write('&country=$country') : '';
    (language != null) ? buffer.write('&language=$language') : '';

    debugPrint('getQueryParams   $buffer');
    return buffer.toString();
  }

  Future<void> initialSetUp() async {
    debugPrint('-----------------------------getCurrentContext-------------------------------------');

    isInitialLoading(true);
    _isFormInitiallyValidated = false;

    String? cntry = SharedPrefUtils.readPrefStr(ConstHelper.countryCodevar);
    String? clientId = SharedPrefUtils.readPrefStr(ConstHelper.clientIdvar);
    String? brandId = SharedPrefUtils.readPrefStr(ConstHelper.brandIdvar);
    String? buId = SharedPrefUtils.readPrefStr(ConstHelper.buIdvar);
    String? langguage = SharedPrefUtils.readPrefStr(ConstHelper.langCodevar);
    String? brandEnabled = SharedPrefUtils.readPrefStr(ConstHelper.isBrandEnabledvar);
    String? buEnabled = SharedPrefUtils.readPrefStr(ConstHelper.isBuEnabledvar);

    debugPrint(
        'cntry---$cntry    clientId---$clientId   brandId---$brandId   buId---$buId    langguage---$langguage    brandEnabled---$brandEnabled    buEnabled---$buEnabled');

    brandId = brandId == '' ? null : brandId;
    buId = buId == '' ? null : buId;

    int? brand = brandId != null ? int.parse(brandId) : null;
    int? bu = buId != null ? int.parse(buId) : null;
    int? client = clientId != null ? int.parse(clientId) : null;
    String contryCode = (cntry != null && cntry != 'null') ? cntry : 'xxxx';

    await getClientsData();

    if (brandEnabled != null && brandEnabled == 'Y' && clientId != null) {
      await getBrandsData(int.parse(clientId));
    }

    if (buEnabled != null && buEnabled == 'Y' && clientId != null) {
      await getBuData(int.parse(clientId), brand, brandEnabled);
    }

    if (clientId != null) {
      await getCountryData(clientId: int.parse(clientId), brandId: brand, isbrandenabled: brandEnabled, buId: bu, isbuEnabled: buEnabled);

      await getLanguageData(
          clientId: int.parse(clientId),
          brandId: brand,
          isbrandenabled: brandEnabled,
          buId: bu,
          isbuEnabled: buEnabled,
          countryCode: contryCode);
    }

    clientIdSelected(client);
    brandIdSelected(brand);
    buIdSelected(bu);
    contryCodeSelected(contryCode);
    languageSelected(langguage);
    isBrandEnabled(brandEnabled);
    isBuEnabled(buEnabled);

    isInitialLoading(false);
  }

  Future<String> onSavePreferences() async {
    isLoading(true);
    String result = 'error';
    debugPrint(
        'onSavePreferences()>>>>_curClientId: ${clientIdSelected.value}    _curBrandId: ${brandIdSelected.value}}   _curBuId: ${buIdSelected.value}   _curContryCode: ${contryCodeSelected.value}   _isBrandEnabled: ${isBrandEnabled.value}   _isBuEnabled: ${isBuEnabled.value}   _language: ${languageSelected.value}');

    String clientinfo = json.encode({
      'clientId': clientIdSelected.value,
      'brandId': brandIdSelected.value,
      'buId': buIdSelected.value,
      'countryCode': contryCodeSelected.value,
      'langCode': languageSelected.value,
      'isBuEnabled': isBuEnabled.value,
      'isBrandEnabled': isBrandEnabled.value,
    });

    String c = await updateContext(data: clientinfo, userPrefLocale: userPrefferedLocale);
    result = (c == 'error') ? 'error' : 'success';
    isLoading(false);
    return result;
  }

  Future<void> onChangeClient(String clientId) async {
    isLoading(true);
    brandIdSelected.value = null;
    buIdSelected.value = null;
    contryCodeSelected.value = null;
    languageSelected.value = null;

    debugPrint('clientId         $clientId');
    int? clientIdParsed = int.tryParse(clientId);
    clientIdSelected(clientIdParsed);

    final clientData = clientList.firstWhereOrNull((i) => i.clientId == clientIdParsed);

    isBrandEnabled(clientData?.isBrandSecurityEnable ?? 'N');
    isBuEnabled(clientData?.applyBuPolicy ?? 'N');
    debugPrint(
        'clientname   ${clientData?.clientName}     isbrandenable ${clientData?.isBrandSecurityEnable}    isbuenable${clientData?.applyBuPolicy}     localizationEnabled ${clientData?.localizationEnabled}    preferredLocale${clientData?.preferredLocale}');

    if (isBrandEnabled.value == 'Y' && clientIdParsed != null) {
      await getBrandsData(clientIdParsed);
    } else {
      brandList.clear();
    }

    if (isBuEnabled.value == 'Y') {
      buList.clear();
    }

    if (isBrandEnabled.value == 'N' && isBuEnabled.value == 'N' && clientIdParsed != null) {
      await getCountryData(
          clientId: clientIdParsed,
          brandId: brandIdSelected.value,
          buId: buIdSelected.value,
          isbrandenabled: isBrandEnabled.value,
          isbuEnabled: isBuEnabled.value);
    } else {
      countryList.clear();
    }

    languageList.clear();

    userPrefferedLocale = clientData?.preferredLocale ?? '';

    isLoading(false);
    validateForm(onSavedPressed: false);
  }

  Future<void> onChangeBrand(String? brand) async {
    isLoading(true);
    buIdSelected.value = null;
    contryCodeSelected.value = null;
    languageSelected.value = null;
    int? brandIdParsed = int.tryParse(brand ?? '');
    int? clientId = clientIdSelected.value;
    if (clientId != null) {
      await getBuData(clientId, brandIdParsed, isBrandEnabled.value);
    }
    brandIdSelected.value = brandIdParsed;

    isLoading(false);
    validateForm(onSavedPressed: false);
  }

  Future<void> onChangeBu(String? bu) async {
    isLoading(true);
    contryCodeSelected.value = null;
    languageSelected.value = null;
    int? buIdParsed = int.tryParse(bu ?? '');
    int? clientId = clientIdSelected.value;
    await getCountryData(
        clientId: clientId,
        isbrandenabled: isBrandEnabled.value,
        isbuEnabled: isBuEnabled.value,
        brandId: brandIdSelected.value,
        buId: buIdParsed);
    buIdSelected.value = buIdParsed;
    isLoading(false);
    validateForm(onSavedPressed: false);
  }

  Future<void> onChangeCountry(String? country) async {
    isLoading(true);
    languageSelected.value = null;
    int? clientId = clientIdSelected.value;
    if (country == 'xxxx') country = null;
    await getLanguageData(
        clientId: clientId,
        isbrandenabled: isBrandEnabled.value,
        isbuEnabled: isBuEnabled.value,
        brandId: brandIdSelected.value,
        buId: buIdSelected.value,
        countryCode: country);
    contryCodeSelected.value = country;
    isLoading(false);
    validateForm(onSavedPressed: false);
  }

  onChangeLanguage(String language) {
    languageSelected.value = language;
    validateForm(onSavedPressed: false);
  }

  bool validateForm({required bool onSavedPressed}) {
    if (onSavedPressed) {
      _isFormInitiallyValidated = true;
    }

    if (_isFormInitiallyValidated) {
      return formKey.currentState?.validate() ?? false;
    } else {
      return false;
    }
  }
}
