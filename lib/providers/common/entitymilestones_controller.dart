import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/milestone.dart';
import 'package:tangoworkplace/models/common/milestone_checklist.dart';
import 'package:tangoworkplace/models/common/milestone_resource.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common/entitymilestone_services.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

import '../../common/component_utils.dart';

class EntityMilestonesController extends GetxController {
  dynamic args = Get.arguments;

  TextEditingController searchController = TextEditingController();
  var searchflag = false.obs;

  RxList<Milestone> milestones = <Milestone>[].obs;
  var oldmilestone = Milestone().obs;
  var isloading = false.obs;
  var daterange = 'ALL'.obs;
  var labelsloading = false.obs;
  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_PROJECTS_SCHEDULE', 'project_schedule', filtertype: 'CONTAINS');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  loadEntityMilestones(String? entityType, int? entityId, {String? searchText}) async {
    debugPrint('------------loadEntityMilestones--------$entityType');
    try {
      isloading(true);
      milestones.clear();
      List<Milestone> milest;
      Map<String, dynamic>? resMap;
      String filter;

      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityId.toString()],
        'b': ['entity_type', 'EXACT', entityType],
      };
      if (daterange.value == 'WEEK') {
        dynamicMap['c'] = ['wek', 'EXACT', daterange.value];
      } else if (daterange.value == 'MONTH') {
        dynamicMap['c'] = ['mth', 'EXACT', daterange.value];
      } else if (daterange.value == 'PASTDUE') {
        dynamicMap['c'] = ['pastdue', 'EXACT', 'PAST'];
      }

      if (searchText != null && searchText.isNotEmpty) {
        dynamicMap['d'] = ['all_columns', 'CONTAINS', searchText];
      }
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);

      resMap = await MilestoneServices.fetchMilestones(payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            milest = tagObjsJson.map((tagJson) => Milestone.fromJson(tagJson)).toList();
            debugPrint('milestones Count------ ${milest.length}');
            milestones.addAll(milest);
          }
        } else {
          milestones.clear();
        }
      } else {
        milestones.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();
    if (searchText == null) {
      searchflag.value = false;
      searchController.text = '';
    }
  }

  RxList<MilestoneChecklist> mlschecklist = <MilestoneChecklist>[].obs;
  RxList<MilestoneResource> mlsresources = <MilestoneResource>[].obs;
  var ischecklistloading = false.obs;
  var isresourcelistloading = false.obs;

  Future loadmilestonechecklist(int? milestoneid) async {
    debugPrint('------------loadmilestonechecklist--------');
    try {
      ischecklistloading.value = true;
      mlschecklist.clear();
      List<MilestoneChecklist> chklist;
      Map<String, dynamic>? resMap;
      String filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['milestone_id', 'EXACT', milestoneid.toString()]
      };
      filter = CommonServices.getFilter(dynamicMap, size: 500);
      debugPrint('filter---------' + filter);
      //}
      resMap = await MilestoneServices.fetchMilestones(payloadObj: filter, url: milestonechecklisturl);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            chklist = tagObjsJson.map((tagJson) => MilestoneChecklist.fromJson(tagJson)).toList();
            debugPrint('milestones Count------ ${chklist.length}');
            mlschecklist.addAll(chklist);
          }
        } else {
          mlschecklist.clear();
        }
      } else {
        mlschecklist.clear();
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      ischecklistloading.value = false;
      update();
    }
  }

  var checklistname = TextEditingController();
  var chklistoption = false.obs;

  void clearchecklistform() {
    checklistname.text = '';
    chklistoption.value = false;
  }

  Future addEditchecklist(int? milestoneid, {String? type, int? checklistId}) async {
    String msg = 'Unknown Error Occurred';
    try {
      actualfinishCtrl.text = oldmilestone.value.actualFinish ?? '';
      Map m = {
        'milestoneid': milestoneid,
        'name': checklistname.text,
        'chklistoption': chklistoption.value ? 'Y' : 'N',
      };
      String url = addmilstonechecklisturl;
      if (type == 'edit') {
        url = updatechecklisturl;
        m['checklistId'] = checklistId;
      }
      clearchecklistform();

      ProgressUtil.closeLoaderDialog(Get.context!);
      ProgressUtil.showLoaderDialog(Get.context!);
      var status = await CommonServices.postItem(m: m, url: url);
      debugPrint('status>>>>>>>>$status');

      if (status == 'success') {
        msg = type == 'edit' ? "Record Updated successfully..." : "New Item added successfully...";
      }
    } catch (e) {
      debugPrint('addEditchecklist>>>>>>>>>>>$e');
    } finally {
      Get.back();

      Future.delayed(const Duration(seconds: 0), () async {
        await loadmilestonechecklist(milestoneid);
        ischecklistloading.value = false;
      });
      ComponentUtils.showpopup(msg: msg);
    }
  }

  loadmilestoneresources(int? milestoneid) async {
    debugPrint('------------loadmilestoneresources--------');
    try {
      isresourcelistloading(true);
      mlsresources.clear();
      List<MilestoneResource> rsclist;
      Map<String, dynamic>? resMap;
      String filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['milestone_id', 'EXACT', milestoneid.toString()]
      };
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      //}
      resMap = await MilestoneServices.fetchMilestones(payloadObj: filter, url: milestoneresourcesurl);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            rsclist = tagObjsJson.map((tagJson) => MilestoneResource.fromJson(tagJson)).toList();
            debugPrint('milestones resources Count------ ${rsclist.length}');
            mlsresources.addAll(rsclist);
          }
        } else {
          mlsresources.clear();
        }
      } else {
        mlsresources.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isresourcelistloading(false);
    update();
  }

  var resourcename = TextEditingController();
  var resourceemail = TextEditingController();
  var resourceescnoti = false.obs;
  var resourcecmpltnoti = false.obs;
  RxList<LookupValues> resorolelov = <LookupValues>[].obs;
  var selectedresorole = ''.obs;
  var resorceformloading = false.obs;

  clearresourceform() {
    resourcename.text = '';
    resourceemail.text = '';
    resourceescnoti.value = false;
    resourcecmpltnoti.value = false;
    selectedresorole.value = '';
  }

  addEditresource(int? milestoneid, {String? type, int? resourceid}) async {
    try {
      Map m = {
        'milestoneid': milestoneid,
        'name': resourcename.text,
        'role': selectedresorole.value,
        'email': resourceemail.text ?? '',
        'escnotify': resourceescnoti.value ? 'Y' : 'N',
        'compnotify': resourcecmpltnoti.value ? 'Y' : 'N',
      };
      String url = addmilstoneresourcesurl;
      if (type == 'edit') {
        m['resourceid'] = resourceid;
        url = updatemilstoneresourcesurl;
      }
      Get.back();
      ProgressUtil.showLoaderDialog(Get.context!);
      var status = await CommonServices.postItem(m: m, url: url);
      if (status == 'success') {
        Get.back();
        await loadmilestoneresources(milestoneid);
        String msg = type == 'edit' ? "Record Updated successfully..." : "New Item added successfully...";
        ComponentUtils.showsnackbar(text: msg);
      } else {
        Get.back();
        ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      }
    } catch (e) {
      Get.back();
      ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      debugPrint('$e');
    }
    clearresourceform();
  }

  Future getResourceRoleLov() async {
    try {
      resorceformloading.value = true;
      resorolelov.value = await (fetchLovs('MILESTONE_ROLES', '', false) as Future<List<LookupValues>>);
      resorolelov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select Role'));
    } catch (error) {
      print(error);
    }
    Future.delayed(
      const Duration(seconds: 2),
      () {
        resorceformloading.value = false;
      },
    );
  }

  //details page--------------------------------------------
  var forecaststartCtrl = TextEditingController();
  var forecastendCtrl = TextEditingController();
  var actualstartCtrl = TextEditingController();
  var actualfinishCtrl = TextEditingController();

  var duration = 0.obs;
  var percentComplete = 0.0.obs;

  onDetFormsave(Milestone mil) async {
    try {
      ProgressUtil.showLoaderDialog(Get.context!);
      debugPrint('forecaststartCtrl --------${forecaststartCtrl.text}');
      debugPrint('forecastendCtrl --------${forecastendCtrl.text}');
      debugPrint('actualstartCtrl --------${actualstartCtrl.text}');

      debugPrint('percentComplete --------${percentComplete.value}');

      if ((forecastendCtrl.text.isNotEmpty) && (actualfinishCtrl.text.isNotEmpty)) {
        DateFormat dateFormat = DateFormat("MM/dd/yyyy");
        DateTime fe = dateFormat.parse(forecastendCtrl.text);
        DateTime af = dateFormat.parse(actualfinishCtrl.text);
        debugPrint('fe  $fe   af   $af');
        if (fe.isAfter(af) || fe.isAtSameMomentAs(af)) {
          percentComplete.value = 100;
        }
      }

      Map m = {
        'milestoneid': mil.milestoneId,
        'forecaststart': forecaststartCtrl.text,
        'forecastend': forecastendCtrl.text,
        'actualstart': actualstartCtrl.text,
        'actualfinish': actualfinishCtrl.text,
        'duration': duration.value ?? '',
        'percentComplete': percentComplete.value ?? 0.0,
        //'notApplicableFlag': notapplicableflag.value,
        //'cExtAttr1': cextattr1.value,
      };

      String result;

      result = await (CommonServices.postItem(m: m, url: updatemilestoneurl));
      ProgressUtil.closeLoaderDialog(Get.context!);
      if (result == 'success') {
        Get.back();
        loadEntityMilestones(mil.entityType, mil.entityId);
        ComponentUtils.showsnackbar(text: "Data saved successfully...");
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      }
    } catch (e) {
      debugPrint('onDetFormsave >>> $e');
      ProgressUtil.closeLoaderDialog(Get.context!);
      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }

  bool onMilestoneActualDateValidation() {
    bool flag = true;
    if (mlschecklist.value.isNotEmpty && mlschecklist.value.isNotEmpty) {
      for (var element in mlschecklist.value) {
        if (flag && element.applicableFlag?.toUpperCase() != 'Y' && element.checklistOption?.toUpperCase() != 'Y') {
          flag = false;
        }
      }
    }
    return flag;
  }
}
