import 'package:get/get.dart';

import '../../models/common/document.dart';
import '../../screens/common/utils/tree/tree_building.dart';
import '../../screens/common/utils/tree/tree_node.dart';
import '../../services/common/documents_services.dart';

class DocumentsTreeController extends GetxController {
  RxList<TreeNode<Document>> documentsList = <TreeNode<Document>>[].obs;
  var isLoading = false.obs;

  void loadInitial(int? rootFolderId) async {
    isLoading(true);
    documentsList.clear();

    final documentsFetched =
        await DocumentsServices.fetchDocuments(rootFolderId);
    final documentsTreeNodes = parseDocumentsResponse(documentsFetched);
    documentsList.addAll(documentsTreeNodes);

    isLoading(false);
  }
}