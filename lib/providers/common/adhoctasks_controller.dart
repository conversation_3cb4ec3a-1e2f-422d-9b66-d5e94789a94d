import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/adhoctask.dart';
import 'package:tangoworkplace/models/common/adhoctemplate.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/ta_admin/entitystatus.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common/adhoctasks_services.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import '../../common/component_utils.dart';

class EntityAdhicTasksController extends GetxController {
  LabelController talabel = Get.find<LabelController>();
  var init = false.obs;
  var listflag = true.obs;
  RxList<AdhocTask> adhoctaskdata = <AdhocTask>[].obs;
  var isloading = false.obs;
  var isdetailsloading = false.obs;
  var generatebtnbinding = false.obs;
  var selectedtemplate = ''.obs;
  RxList<AdhocTemplate> templatelov = <AdhocTemplate>[].obs;
  var taskfilter = 'All'.obs;
  var isContractor = 'N'.obs;
  var at_mode = ''.obs;
  var templateloading = false.obs;

  @override
  Future<void> onInit() async {
    var user = await CommonUtils.getUserdata();
    isContractor.value = user.isContractor!;
    super.onInit();
  }

  var labelsloading = false.obs;
  Future getlabels(LabelController talabel) async {
    try {
      await talabel.getlabels('TMCMOBILE_COMMON_ADHOCTASKS', 'adhoctask');
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future gettemplateslov(String? entityType) async {
    String filter;
    List<AdhocTemplate> temprec;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_type', 'EXACT', entityType],
      };

      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);

      templateloading.value = true;
      resMap = await AdhocTaskServices.fetchDynamicApi(payloadObj: filter, url: adhoctemplatesurl);

      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            temprec = tagObjsJson.map((tagJson) => AdhocTemplate.fromJson(tagJson)).toList();
            debugPrint('templates Count------ ${temprec.length}');

            templatelov.value = temprec;
          }
        }
      }
    } catch (e) {
      debugPrint('$e');
    }
    templateloading.value = false;
  }

  generateItemsFromTemplate({int? entityId, String? entityType}) async {
    debugPrint('selectedtemplate        ${selectedtemplate.value}');
    try {
      Map m = {
        'entityId': entityId,
        'templateId': selectedtemplate.value,
        'entityType': entityType,
      };

      ProgressUtil.showLoaderDialog(Get.context!);
      var status = await AdhocTaskServices.generatefromtemplate(m);
      Get.back();
      if (status == 'success') {
        loadAdhocTasks(entityType: entityType, entityId: entityId);
      } else {
        ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      }
    } catch (e) {
      ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      debugPrint('$e');
    }
  }

  Future loadAdhocTasks(
      {String? entityType,
      int? entityId,
      int? subentityid,
      String? isContractor,
      bool? loadmore,
      String? filter}) async {
    debugPrint('------------loadAdhocTasks--------$entityType');
    isloading(true);
    List<AdhocTask> adhoctaskRec;
    adhoctaskdata.clear();
    Map<String, dynamic>? resMap;

    var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);

    Map<String, dynamic> dynamicMap = {
      'a': ['entity_id', 'EXACT', entityId.toString()],
      'b': ['entity_type', 'EXACT', entityType],
      'c': ['p_user', 'EXACT', username],
    };
    if (isContractor == 'Y' || filter == 'My') {
      dynamicMap['d'] = ['p_isContractor', 'EXACT', 'Y'];
    } else {
      dynamicMap['d'] = ['p_isContractor', 'EXACT', 'N'];
    }

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await AdhocTaskServices.fetchDynamicApi(payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            adhoctaskRec = tagObjsJson.map((tagJson) => AdhocTask.fromJson(tagJson)).toList();
            debugPrint('Adhoctask list Count------ ${adhoctaskRec.length}');
            //if ((adhoctaskRec.isEmpty || adhoctaskRec.length < 1) && isContractor != 'Y' && filter == null) {
            //  gettemplateslov(entityType);
            // showtemplateform();
            // listflag.value = false;
            //  } else {
            //   listflag.value = true;
            // }
            adhoctaskdata.addAll(adhoctaskRec);
          }
        } else {
          adhoctaskdata.clear();
        }
      } else {
        adhoctaskdata.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    if (filter == null) taskfilter.value = 'All';
    isloading(false);
  }

  showtemplateform() {
    listflag.value = false;
  }

  //----------details---------------
  Rx<AdhocTask> adhoctask = AdhocTask().obs;
  var adhoctaskid = 0.obs;
  RxList<LookupValues> tasktypelov = <LookupValues>[].obs;
  RxList<LookupValues> prioritylov = <LookupValues>[].obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var taskseq = 0.obs;
  var tasktype = ''.obs;
  var taskname = ''.obs;
  var taskdesc = ''.obs;
  var startdate = TextEditingController();
  var finishdate = TextEditingController();
  var actualfinish = TextEditingController();

  var notif1 = 0.obs;
  var notif2 = 0.obs;
  var notif3 = 0.obs;
  var status = ''.obs;
  var priority = ''.obs;
  var emailaddress = ''.obs;
  var sendemail = false.obs;
  var associatedfileids = ''.obs;
  //var emailbody = ''.obs;
  //var includeme = false.obs;
  var emailbodyCtrl = TextEditingController();
  var includemecheck = false.obs;
  var sendNotifications = false.obs;

  var assignToCtrl = TextEditingController();

  // Future<void> getAdhocTaskId(String at_mode) async {
  //   if (at_mode == 'create') {
  //     //int id = await AdhocTaskServices.getAdhocTaskId();
  //     //adhoctaskid.value = id;
  //     adhoctask.value = AdhocTask(
  //         //adhocTaskId: id,
  //         status: 'Draft');
  //   }
  // }

  Future setCurrentRow() async {
    // Future.delayed(Duration(seconds: 1), () async {
    try {
      AdhocTask a;
      //isdetailsloading.value = true;
      a = adhoctask.value;
      adhoctaskid.value = a.adhocTaskId ?? 0;
      debugPrint('a.status >>>>>>>>>> ${a.status}');
      //a.status = (a?.status.toString() == null || a?.status.toString() == '' || a?.status.toString() == 'null') ? '' : a.status;
      status.value = at_mode.value == 'create' ? 'Draft' : a.status ?? '';
      sendNotifications.value = false;
      taskdesc.value = a.taskDesc ?? '';

      // assignedto.value = a.assignedTo;

      if (talabel.get('TMCMOBILE_COMMON_ADHOCTASKS_DETAILS_TASKTYPE') != null) await getTaskTypeLov();
      if (talabel.get('TMCMOBILE_COMMON_ADHOCTASKS_DETAILS_STATUS') != null) await getStatusLov();
      if (talabel.get('TMCMOBILE_COMMON_ADHOCTASKS_DETAILS_PRIORITY') != null) await getPriorityLov();
      tasktype.value = a.taskType ?? '';

      priority.value = a.priority ?? '';
      // tasktypelov.value = await CommonServices.getLookups('TMCS_ADHOC_TASKS', tasktypelov);
      //statuslov.value = await CommonServices.getStatuses('ADHOCTASK', statuslov);
      if (at_mode.value != 'create') {
        startdate.text = ComponentUtils.dateToString(a.taskStart);
        finishdate.text = ComponentUtils.dateToString(a.taskFinish);
        actualfinish.text = ComponentUtils.dateToString(a.actualFinsh);

        associatedfileids.value = a.assosiatedFileIds ?? '';
        assignToCtrl.text = a.assignedTo ?? '';
      } else if (at_mode.value == 'create') {
        startdate.text = '';
        finishdate.text = '';
        actualfinish.text = '';
        assignToCtrl.text = '';
      }
      Future.delayed(
        const Duration(seconds: 1),
        () {
          isdetailsloading.value = false;
        },
      );
    } catch (error) {
      debugPrint('setCurrentRow >>>>>>>>>> $error');
      isdetailsloading.value = false;
    }
    //});
    isdetailsloading.value = false;
  }

  Future getTaskTypeLov() async {
    try {
      tasktypelov.value = await fetchLovs('TMCS_ADHOC_TASKS', '', false) ?? <LookupValues>[];
      tasktypelov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));
    } catch (error) {
      debugPrint('$error');
    }
  }

  Future getPriorityLov() async {
    try {
      prioritylov.value = await fetchLovs('ADHOCTASK_PRIORITY', '', false) ?? <LookupValues>[];
      prioritylov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));
    } catch (error) {
      debugPrint('$error');
    }
  }

  Future getStatusLov() async {
    Map<String, dynamic>? resMap;
    try {
      statuslov.value.clear();
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_type', 'EXACT', 'ADHOCTASK'],
      };
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(entitystatusesurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        statuslov.value = tagObjsJson.map((tagJson) => EntityStatus.fromJson(tagJson)).toList();
        statuslov.value.insert(0, EntityStatus(statusCode: '', status: 'Select'));
      }
    } catch (e) {
      debugPrint('fetchDynamicApi getStatusLov>>>>>>>' '$e');
    }
  }

  onDetFormsave(AdhocTask a, String? entityType, int? entityId, String type) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    try {
      Map m = {
        'adhocTaskId': adhoctaskid.value,
        'taskSeq': taskseq.value,
        'entityType': entityType,
        'entityId': entityId,
        'taskType': tasktype.value ?? '',
        'taskName': taskname.value,
        'taskDesc': taskdesc.value,
        'taskStart': startdate.text,
        'taskFinish': finishdate.text,
        'actualFinsh': actualfinish.text,
        'assignedTo': type == 'Draft' ? '' : assignToCtrl.text,
        'notification1': notif1.value,
        'notification2': notif2.value,
        'notification3': notif3.value,
        'status': status.value ?? '',
        'priority': priority.value ?? '',
        'sendemail': sendemail.value,
        'includeme': includemecheck.value,
        'associatedfileids': associatedfileids.value,
        'isstatuschange': (a.status == status.value) ? 'N' : 'Y',
        'emailaddress': emailaddress.value,
        'createdBy': a.createdBy,
        'emailbody': emailbodyCtrl.text,
        'actiontype': type,
        'sendNotifications': sendNotifications.value,
      };
      debugPrint('m------$m');

      int? result;

      result = await AdhocTaskServices.saveDetAdhocTaskImpl(m, at_mode.value);
      if (result != null && result > 0) {
        if (at_mode.value == 'create') {
          adhoctaskid.value = result;
          // AdhocTask rec = adhoctask.value;
          adhoctask.value.adhocTaskId = result;
          debugPrint('${adhoctask.value.adhocTaskId}--------id-----$result');
          at_mode.value = 'edit';

          ComponentUtils.showsnackbar(text: "Data saved successfully...");
        } else {
          Get.back();
          if (type == 'Submit' && sendNotifications.value) Get.back();
          loadAdhocTasks(entityId: entityId, entityType: entityType);
        }
      } else {
        if (type == 'Submit' && sendNotifications.value) Get.back();
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      }
    } catch (e) {
      debugPrint('$e');
    }
    ProgressUtil.closeLoaderDialog(Get.context!);
  }
}
