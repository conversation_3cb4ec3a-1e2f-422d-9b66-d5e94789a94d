import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/common/utils/dmsfile.dart';
import 'package:tangoworkplace/providers/common/adhoctasks_controller.dart';
import 'package:tangoworkplace/services/common/adhoctasks_services.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/services/utils/docattachment_services.dart';
import 'package:tangoworkplace/utils/connections.dart';
import 'package:tangoworkplace/utils/constvariables.dart';
import 'package:tangoworkplace/utils/preferences_utils.dart';

import '../../../common/component_utils.dart';

class DocAttachmentController extends GetxController {
  EntityAdhicTasksController adhoctaskCtrl = Get.find<EntityAdhicTasksController>();
  var isLoading = false.obs;
  RxList<DmsFile> uploadedDocs = <DmsFile>[].obs;
  RxList<DmsFile> entityDocs = <DmsFile>[].obs;
  RxList<DmsFile> attachmentDocs = <DmsFile>[].obs;
  var tempfileids = [].obs;
  RxList<DmsFile> tempfiles = <DmsFile>[].obs;

  var tempuploadedlist = [].obs;

  Future fetchEntityDmsFiles({int? entityid, String? entitytype, int? subentityid, String? subentitytype}) async {
    isLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap;
      if (entityid != null && entitytype != null) {
        dynamicMap = {
          'a': ['entity_id', 'EXACT', entityid.toString()],
          'b': ['entity_type', 'EXACT', entitytype],
        };
      } else {
        dynamicMap = {
          'a': ['sub_entity_id', 'EXACT', subentityid?.toString()],
          'b': ['sub_entity_type', 'EXACT', subentitytype],
        };
      }
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(entitydocsurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        entityDocs.value = tagObjsJson.map((tagJson) => DmsFile.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchDmsFiles>>>>>>>' '$e');
    }
    isLoading.value = false;
  }

  Future fetchDmsFiles({int? entityid, String? entitytype, int? subentityid, String? subentitytype}) async {
    isLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);
      Map<String, dynamic> dynamicMap;
      if (entityid != null && entitytype != null) {
        dynamicMap = {
          'a': ['entity_id', 'EXACT', entityid.toString()],
          'b': ['entity_type', 'EXACT', entitytype],
        };
      } else {
        dynamicMap = {
          'a': ['sub_entity_id', 'EXACT', subentityid?.toString()],
          'b': ['sub_entity_type', 'EXACT', subentitytype],
          'c': ['p_user', 'EXACT', username],
        };
      }
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(dmsfilesurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        uploadedDocs.value = tagObjsJson.map((tagJson) => DmsFile.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchDmsFiles>>>>>>>' '$e');
    }
    isLoading.value = false;
  }

  uploadFiles(List<File> files, {String? entityType, int? entityId, int? subentityid, String? subentitytype}) async {
    try {
      print('Inside upload $entityId');
      File f;
      List<String> paths = [];
      for (var i = 0; i < files.length; i++) {
        f = files[i];
        debugPrint('f    $f');
        paths.add(f.path);
      }

      String res = await DocAttachmentServices.uploadFiles(
          entityId: entityId,
          entityType: entityType,
          subentitytype: subentitytype,
          subentityid: subentityid,
          paths: paths);
      if (res == 'success') {
        Future.delayed(const Duration(seconds: 8), () {
          fetchDmsFiles(subentityid: subentityid, subentitytype: subentitytype);
          Get.back();
        });
      } else {
        Get.back();
      }
    } catch (e) {
      debugPrint('$e');
    }
  }

  var isattachLoading = false.obs;
  Future getattachments(String? fileids) async {
    isattachLoading.value = true;
    Map<String, dynamic>? resMap;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['fileId_List', 'EXACT', fileids],
      };
      var payloadObj = CommonServices.getFilter(dynamicMap);

      resMap = await CommonServices.fetchDynamicApi(dmsfilesbyidurl, payloadObj: payloadObj);
      debugPrint('$resMap');

      var tagObjsJson = resMap!['records'] as List?;
      if (tagObjsJson != null) {
        attachmentDocs.value = tagObjsJson.map((tagJson) => DmsFile.fromJson(tagJson)).toList();
      }
    } catch (e) {
      debugPrint('fetchDynamicApi fetchDmsFiles>>>>>>>' '$e');
    }
    isattachLoading.value = false;
  }

  deletefilefromlist(String fileid) {
    if (tempfileids.value.isNotEmpty) {
      tempfileids.value.remove(fileid);

      debugPrint('deletefilefromlist---------${tempfileids.value}');
    }
  }

  addfileIdsToAttachments() {
    String temp;
    if (tempfiles.isNotEmpty) {
      temp = tempfileids.value.join(',');

      //debugPrint('------------------------${temp}');
      for (var element in tempfiles.value) {
        if (temp != null && temp.contains(element.fileId.toString())) {
          // debugPrint('------------------------${element.fileId}');
        } else {
          // debugPrint('else------------------------${element.fileId}');
          tempfileids.value.add(element.fileId?.toString());
          attachmentDocs.value.add(element);
          //  debugPrint('else------------------------${attachmentDocs.value}');
        }
      }
      attachmentDocs.refresh();
    }
  }

  Future saveDocuments() async {
    try {
      String result = 'success';

      adhoctaskCtrl.associatedfileids.value = tempfileids.value.join(',');

      Map m = {
        'adhocTaskId': adhoctaskCtrl.adhoctaskid.value,
        'associatedfileids': adhoctaskCtrl.associatedfileids.value,
        //fileids for deleting in dms file
        'fileids': tempuploadedlist.value.join(','),
      };

      result = await AdhocTaskServices.saveDocAttachemnts(m);

      if (result == 'success') {
        Get.back();
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while adding data...");
      }
    } catch (e) {
      debugPrint(' saveDocuments>>>>>>>' '$e');
    }
  }
}
