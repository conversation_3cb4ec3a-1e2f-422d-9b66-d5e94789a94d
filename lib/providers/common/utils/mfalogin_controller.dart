import 'dart:async';

import 'package:get/get.dart';

class MfaLoginController extends GetxController {
  var otpval = ''.obs;
  var otperrflag = false.obs;
  var errormsg = ''.obs;
  //var otptime = 0.obs;
  var remtime = ''.obs;
  var expiryflag = false.obs;

  cleardata() {
    otpval.value = '';
    otperrflag.value = false;
    errormsg.value = '';
    // otptime.value = 0;
    remtime.value = '';
    expiryflag.value = false;
  }

  void initializeTimer({required int mfaexpiry}) {
    mfaexpiry = mfaexpiry * 60 ?? 120;
    // otptime.value = 120;
    Timer.periodic(const Duration(seconds: 1), (val) {
      mfaexpiry--;
      remtime.value = formatedTime(timeInSecond: mfaexpiry, minute: '0', second: '0');

      if (mfaexpiry == 0) {
        val.cancel();
        expiryflag.value = true;
      }
    });
  }

  formatedTime({required int timeInSecond, String? minute, String? second}) {
    int sec = timeInSecond % 60;
    int min = (timeInSecond / 60).floor();
    minute = min.toString().length <= 1 ? "0$min" : "$min";
    second = sec.toString().length <= 1 ? "0$sec" : "$sec";
    return "$minute : $second";
  }
}
