import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/common/photo.dart';
import 'package:tangoworkplace/services/common/pictures_services.dart';
import 'package:tangoworkplace/utils/custom_exception.dart';

import '../../common/component_utils.dart';
import '../../common/widgets/components.dart';

class PhotosController extends GetxController {
  static PhotosController instance = Get.find();
  dynamic args = Get.arguments;

  RxList<Photo> photos = <Photo>[].obs;
  var isloading = false.obs;
  var templist = [].obs;

  loadPhotos(String entityType, int? entityId, {bool? loadmore}) async {
    debugPrint('------------loadPhotos--------$entityType');
    isloading(true);
    if (loadmore == null || !loadmore) photos.clear();
    try {
      List<Photo> pics = await (PhotoServices.fetchPhotos(entityType, entityId));

      photos.addAll(pics);
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();
  }

  addPhotos() {
    debugPrint('---addPhotos------');
    Get.back();
  }

  uploadPhotos(String path, String entityType, int? entityId) async {
    try {
      print('Inside upload $entityId');
      //final bytes = imageFile.readAsBytesSync();

      //String imgstring = base64Encode(bytes);
      // String bodyjson = json.encode({
      //   'fileItem': img64,
      // });

      String res = await PhotoServices.addPhoto(entityType: entityType, entityId: entityId, path: path);
      if (res == 'success') {
        Future.delayed(const Duration(seconds: 5), () {
          loadPhotos(entityType, entityId);
          Get.back();
        });
      } else {
        Get.back();
      }
      debugPrint('resut00--------------$res');
    } catch (e) {
      debugPrint('$e');
    }
  }

  uploadmultiplePhotos(List<File> files, String entityType, int? entityId) async {
    try {
      print('Inside upload $entityId');
      File f;
      List<String> paths = [];
      for (var i = 0; i < files.length; i++) {
        f = files[i];
        debugPrint('f    $f');
        paths.add(f.path);
      }

      String res = await PhotoServices.uploadphotos(entityType: entityType, entityId: entityId, paths: paths);
      if (res == 'success') {
        Future.delayed(const Duration(seconds: 10), () {
          loadPhotos(entityType, entityId);
          Get.back();
        });
      } else {
        Get.back();
      }
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future deletePhotos() async {
    if (templist.value.isNotEmpty && templist.value.isNotEmpty) {
      try {
        String result = 'error';

        Map m = {
          'fileids': templist.value.join(','),
        };
        debugPrint('files-----------$m');

        result = await PhotoServices.deletePhotos(m);

        if (result == 'success') {
          Get.back();
          ComponentUtils.showsnackbar(text: "Data upadated successfully...");
        } else {
          ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while updating data...");
        }
      } catch (e) {
        debugPrint(' saveDocuments>>>>>>>' '$e');
      } finally {}
    } else {
      Get.back();
    }
  }
}
