import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/project/budget/projectbaselineverions.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class ProjectBaselineVersionsController extends GetxController {
  var isLoading = false.obs;
  RxList<ProjBaselineVersions> baselineverdatadata = <ProjBaselineVersions>[].obs;
  // var meetingminute = MeetingMinute().obs;
  //var isdetailsloading = false.obs;
  //var mm_mode = ''.obs;
  //var meetingTypelov = <LookupValues>[].obs;

  // var labelsloading = false.obs;
  // Future getlabels(LabelController talabel) async {
  //   try {
  //     await talabel.getlabels('TMCMOBILE_COMMON_MEETINGMINUTES', 'entitymeetingminutes');
  //   } catch (e) {
  //     debugPrint(e);
  //   }
  // }

  Future loadProjBaselineVersionsData({int? entityid, String? entitytype}) async {
    debugPrint('------------loadProjBaselineVersionsData--------$entityid');
    isLoading(true);
    List<ProjBaselineVersions> rec;
    baselineverdatadata.clear();
    Map<String, dynamic>? resMap;

    //var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);

    Map<String, dynamic> dynamicMap = {
      'a': ['project_id', 'EXACT', entityid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(projbaselineversionsurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            rec = tagObjsJson.map((tagJson) => ProjBaselineVersions.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${rec.length}');

            baselineverdatadata.addAll(rec);
          }
        } else {
          baselineverdatadata.clear();
        }
      } else {
        baselineverdatadata.clear();
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      isLoading(false);
    }
  }
}
