import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/common/entity_comment.dart';
import 'package:tangoworkplace/screens/common/comments/entity_comments.dart';
import 'package:tangoworkplace/services/common/entitycomments_services.dart';

import '../../common/component_utils.dart';
import '../../models/lookup_values.dart';

class EntityCommentsController extends GetxController {
  static EntityCommentsController instance = Get.find();
  dynamic args = Get.arguments;

  RxList<EntityComment> entitycomments = <EntityComment>[].obs;
  var isLoading = false.obs;
  var addCommentCtrl = TextEditingController();
  var commentType = ''.obs;

  // loadEntityComments1({String entityType, int entityId, int subentityid, bool loadmore}) async {
  //   debugPrint('------------loadEntityComments--------$entityType');
  //   isLoading(true);
  //   if (loadmore == null || !loadmore) entitycomments.clear();
  //   try {
  //     List<EntityComment> comms = await EntityCommentsServices.fetchEntityComments(entityType, entityId, subentityid: subentityid);

  //     entitycomments.addAll(comms);
  //     update();
  //   } catch (e) {
  //     debugPrint(e);
  //   }
  //   isLoading(false);
  // }

  Future loadEntityComments({String? entityType, int? entityId, int? subentityid, bool? loadmore}) async {
    debugPrint('------------loadEntityComments--------');
    isLoading(true);
    entitycomments.clear();
    isLoading.value = true;
    List<EntityComment> comm;
    String payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap = {
      'a': ['entity_type', 'EXACT', entityType],
      'b': ['entity_id', 'EXACT', entityId.toString()],
    };
    // if (searchText != null) {
    //   dynamicMap['b'] = ['all_columns', 'CONTAINS', searchText];

    //   payloadObj = CommonServices.getFilter(dynamicMap);
    // }
    payloadObj = CommonServices.getFilter(dynamicMap);
    try {
      resMap = await CommonServices.fetchDynamicApi(entitycommentsurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            comm = tagObjsJson.map((tagJson) => EntityComment.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${comm.length}');
            entitycomments.addAll(comm);
          }
        } else {
          entitycomments.clear();
        }
      } else {
        entitycomments.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isLoading.value = false;
    }
  }

  addComments({String? entitytype, int? entityid, String? comments, int? subentityid}) async {
    debugPrint('---addComments------${addCommentCtrl.text}');
    try {
      Map m = {
        'comments': addCommentCtrl.text,
        'entityid': entityid,
        'entitytype': entitytype,
        'commentscategory': 'Notes',
        'commenttype': commentType.value
      };
      m['subentityid'] = subentityid;
      addCommentCtrl.text = '';
      Get.back();
      ProgressUtil.showLoaderDialog(Get.context!);
      var status = await EntityCommentsServices.addComments(m);
      if (status == 'success') {
        Get.back();
        loadEntityComments(entityType: entitytype, entityId: entityid, subentityid: subentityid);
        ComponentUtils.showsnackbar(text: "Comment added successfully...");
      } else {
        Get.back();
        ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      }
    } catch (e) {
      Get.back();
      ComponentUtils.showsnackbar(text: "Unknown Error Occurred");
      debugPrint('$e');
    }
  }

  var fetchlovs = true.obs;
  var lovloading = false.obs;
  RxList<LookupValues> commentTypelov = <LookupValues>[].obs;
  Future getLovItems() async {
    debugPrint('commentTypelov ----- ${commentTypelov.value}');
    if (fetchlovs.value) {
      commentTypelov.value.clear();
      lovloading.value = true;
      try {
        var jsonObj = await CommonServices.fetchMultiLovs('COMMENT_TYPE');
        if (jsonObj != null) {
          debugPrint('jsonObj ----- $jsonObj');
          var mtt = jsonObj['COMMENT_TYPE'] as List;

          if (mtt.isNotEmpty) commentTypelov.value = mtt.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
          commentTypelov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

          fetchlovs.value = false;
        }
      } catch (error) {
        debugPrint('$error');
      } finally {
        lovloading.value = false;
      }
    }
  }
}
