import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/common/document.dart';
import '../../screens/common/utils/tree/tree_building.dart';
import '../../screens/common/utils/tree/tree_node.dart';
import '../../services/common/documents_services.dart';

class TreeNodeController<T> extends GetxController {
  TreeNode<T> node;
  Rx<bool> isExpanded = false.obs;
  Rx<bool> isLoading = false.obs;
  RxList<TreeNode<T>> children = <TreeNode<T>>[].obs;

  TreeNodeController(this.node);

  void toggleNodeExpanded() async {
    node.isExpanded = !node.isExpanded;
    if (node.isExpanded) {
      isLoading(true);

      final nodeValue = node.node;

      if (nodeValue is Document) {
        final documentsFetched =
        await DocumentsServices.fetchDocuments(nodeValue.id);
        final documents = parseDocumentsResponse(documentsFetched);

        debugPrint('Documents amount: ${documents.length}');
        node.children = documents;
        children(documents.cast<TreeNode<T>>());
        isLoading(false);
      }
    }
    isExpanded(node.isExpanded);
    update();
  }
}
