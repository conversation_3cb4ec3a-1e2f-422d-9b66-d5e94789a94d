import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

class LocationGeneralController extends GetxController {
  var labelsloading = false.obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_LOCATIONS_GENERAL', 'location_general');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }
}
