import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/location/locationentity/locationentity.dart';

class LocationEntitySearchController extends GetxController {
  TextEditingController searchController = TextEditingController();

  RxList<LocationEntity> locentitylist = <LocationEntity>[].obs;
  var isLoading = false.obs;

  Future getLocEntitySearchData({String? searchText, String? entitytype}) async {
    debugPrint('------------getLocEntitySearchData--------');
    locentitylist.clear();
    isLoading.value = true;
    List<LocationEntity> locEntities;
    String payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap = {
      'a': ['p_entityType', 'EXACT', entitytype ?? 'STORE'],
    };
    if (searchText != null) {
      dynamicMap['b'] = ['all_columns', 'CONTAINS', searchText];

      payloadObj = CommonServices.getFilter(dynamicMap);
    }
    payloadObj = CommonServices.getFilter(dynamicMap);
    try {
      resMap = await CommonServices.fetchDynamicApi(locationentityviewurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            locEntities = tagObjsJson.map((tagJson) => LocationEntity.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${locEntities.length}');
            locentitylist.addAll(locEntities);
          }
        } else {
          locentitylist.clear();
        }
      } else {
        locentitylist.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isLoading.value = false;
    }
  }
}
