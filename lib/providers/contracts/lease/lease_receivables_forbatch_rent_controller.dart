import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/contracts/lease/leasereceivablesforbatchrentdata.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class LeaseReceivablesforBatchRentController extends GetxController {
  var isLoading = false.obs;
  RxList<LeaseReceivablesforBatchRentData> lbrpsList = <LeaseReceivablesforBatchRentData>[].obs;
  Future loadReceivablesforBatch(int? batchid) async {
    debugPrint('------------loadReceivablesforBatch--------$batchid');
    isLoading(true);
    List<LeaseReceivablesforBatchRentData> reportRec;
    lbrpsList.clear();
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['lease_receivable_batch_id', 'EXACT', batchid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(leasereceivablesforbatchrenturl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            reportRec = tagObjsJson.map((tagJson) => LeaseReceivablesforBatchRentData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${reportRec.length}');

            lbrpsList.addAll(reportRec);
          }
        } else {
          lbrpsList.clear();
        }
      } else {
        lbrpsList.clear();
      }
    } catch (e) {
      debugPrint('loadReceivablesforBatch>>>>>>>>$e');
    }
    isLoading(false);
  }
}
