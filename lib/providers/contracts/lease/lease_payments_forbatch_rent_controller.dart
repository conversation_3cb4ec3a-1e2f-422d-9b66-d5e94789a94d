import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/contracts/lease/leasepaymentsforbatchrentdata.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class LeasePaymentsforBatchRentController extends GetxController {
  var isLoading = false.obs;
  RxList<LeasePaymentsforBatchRentData> lbrpsList = <LeasePaymentsforBatchRentData>[].obs;
  Future loadpmtsforbatchrent(int? batchid) async {
    debugPrint('------------loadpmtsforbatchrent--------$batchid');
    isLoading(true);
    List<LeasePaymentsforBatchRentData> reportRec;
    lbrpsList.clear();
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['lease_payment_batch_id', 'EXACT', batchid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(leasepaymentsforbatchrenturl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            reportRec = tagObjsJson.map((tagJson) => LeasePaymentsforBatchRentData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${reportRec.length}');

            lbrpsList.addAll(reportRec);
          }
        } else {
          lbrpsList.clear();
        }
      } else {
        lbrpsList.clear();
      }
    } catch (e) {
      debugPrint('loadpmtsforbatchrent>>>>>>>>$e');
    }
    isLoading(false);
  }
}
