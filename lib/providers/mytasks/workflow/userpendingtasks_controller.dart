import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/mytasks/workflow/budgetpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/copendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/documentpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/leasependingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/popendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/projectpendingtask.dart';
import 'package:tangoworkplace/models/mytasks/workflow/user_pending_tasks.dart';
import 'package:tangoworkplace/models/mytasks/workflow/wfhistory.dart';
import 'package:tangoworkplace/services/mytasks/workflow/userpendingtasks_services.dart';

import '../../../common/component_utils.dart';
import '../../../models/mytasks/workflow/budgetchgreqtask.dart';
import '../../../models/mytasks/workflow/invoicependingtask.dart';
import '../../../models/mytasks/workflow/leasebatchrentpendingtask.dart';
import '../../../models/mytasks/workflow/leaseotppendingtask.dart';
import '../../../models/mytasks/workflow/leaserecurringcosttask.dart';
import '../../../models/mytasks/workflow/sitependingtask.dart';
import '../../../models/sitemanagement/site/siteview.dart';
import '../../ta_admin/label_controller.dart';

class MyApprovalsController extends GetxController {
  static MyApprovalsController instance = Get.find();
  LabelController talabel = Get.find<LabelController>();

  RxList<UserPendingTask> usertasks = <UserPendingTask>[].obs;
  List<Widget> tabs = [];
  int get entitycount => usertasks != null ? usertasks.length : 0;
  int? get tasksCount => entitycount > 0 ? usertasks.first.total : 0;
  var approvarrole = 'na'.obs;

  //details tab variables
  var detLoading = false.obs;
  var sitetaskdata = SitePendingTask().obs;

  tablist() {
    List<Widget> tabList = [];
    if (entitycount > 0) {
      for (var element in usertasks) {
        tabList.add(
          // Tab(text: element.entitylabel,)
          getTab(element.entitylabel!, element.entitycount),
        );
        debugPrint('${element.entitylabel}');
      }
    }
    tabs = tabList;
  }

  getTab(String tabname, int? taskcount) {
    return Tab(
      child: Row(
        children: <Widget>[
          Text(tabname),
          const SizedBox(
            width: 5.0,
          ),
          Container(
            padding: const EdgeInsets.all(1),
            decoration: BoxDecoration(
              color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
              borderRadius: BorderRadius.circular(6),
            ),
            constraints: const BoxConstraints(
              minWidth: 14,
              minHeight: 14,
            ),
            child: Text(
              taskcount.toString(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 11,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
      //text: 'Projects',
    );
  }

  var params = ''.obs;
  var entityTaskslabelsLoading = false.obs;
  var isListLoading = false.obs;
  var isloading = false.obs;
  var ishistloading = false.obs;

  var projecttaskcnt = '...'.obs;
  var budgettaskcnt = '...'.obs;
  var budgetcotaskcnt = '...'.obs;
  var potaskcnt = '...'.obs;
  var cotaskcnt = '...'.obs;
  var ewataskcnt = '...'.obs;
  var invoicetaskcnt = '...'.obs;
  var leasetaskcnt = '...'.obs;
  var leaseotptaskcnt = '...'.obs;
  var leaserecurcosttaskcnt = '...'.obs;
  var leasebatchrenttaskcnt = '...'.obs;
  var doctaskcnt = '...'.obs;
  var sitetaskcnt = '...'.obs;

  Future getlabels() async {
    entityTaskslabelsLoading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_APPROVALS', 'approvals', filtertype: 'page');
    } catch (e) {
      debugPrint('$e');
    } finally {
      entityTaskslabelsLoading.value = false;
    }
  }

  Future loadtaskcount({String? searchText, String? navFrom}) async {
    try {
      debugPrint('---loaduserpendingtasks------');
      isListLoading(true);
      usertasks.clear();

      String filter;

      var user = await CommonUtils.getUserdata();
      debugPrint('>>>>>>>>>>>>>>>>${jsonEncode(user)}');

      String? cntry = SharedPrefUtils.readPrefStr(ConstHelper.countryCodevar);
      String? curClientId = SharedPrefUtils.readPrefStr(ConstHelper.clientIdvar);
      String? curBrandId = SharedPrefUtils.readPrefStr(ConstHelper.brandIdvar);
      String? curBuId = SharedPrefUtils.readPrefStr(ConstHelper.buIdvar);
      curBrandId = curBrandId == '' ? null : curBrandId;
      curBuId = curBuId == '' ? null : curBuId;
      String curContryCode = (cntry != null && cntry != 'null') ? cntry : 'xxxx';
      String? isBrandEnabled = SharedPrefUtils.readPrefStr(ConstHelper.isBrandEnabledvar);
      String? isBuEnabled = SharedPrefUtils.readPrefStr(ConstHelper.isBuEnabledvar);
      String? curLanguage = SharedPrefUtils.readPrefStr(ConstHelper.langCodevar);
      debugPrint(
          '_curClientId: $curClientId    _curBrandId: $curBrandId   _curBuId: $curBuId   _curContryCode: $curContryCode   _isBrandEnabled: $isBrandEnabled   _isBuEnabled: $isBuEnabled   _language: $curLanguage');

      Map<String, dynamic> dynamicMap = {
        'a': ['p_user', 'EXACT', user.userName],
        'b': ['p_country', 'EXACT', curContryCode ?? 'ABC'],
        'c': ['p_clientId', 'EXACT', curClientId],
        'd': ['p_brandId', 'EXACT', curBrandId ?? 'ABC'],
        'e': ['p_buid', 'EXACT', curBuId ?? 'ABC'],
        'f': ['p_buenabled', 'EXACT', isBuEnabled],
        'g': ['p_isbrandenabled', 'EXACT', isBrandEnabled],
      };
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      params.value = filter;
      //if (navFrom.value == null || navFrom.value == 'project') {
      if (talabel.get('TMCMOBILE_APPROVALS_PROJECTS') != null) {
        projecttaskcnt.value = await (getTaskCount(filter: filter, url: userprojecttaskcounturl, taskname: 'project'));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_BUDGET') != null) {
        budgettaskcnt.value =
            (await (getTaskCount(filter: filter, url: userprojectbudgettaskcounturl, taskname: 'budget')));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_BUDGETCHGREQ') != null) {
        budgetcotaskcnt.value =
            (await (getTaskCount(filter: filter, url: userprojectbudgetcotaskcounturl, taskname: 'budgetco')));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_PO') != null) {
        potaskcnt.value = await (getTaskCount(filter: filter, url: userpotaskcounturl, taskname: 'po'));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_CO') != null) {
        cotaskcnt.value = await (getTaskCount(filter: filter, url: usercotaskcounturl, taskname: 'co'));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_EWA') != null) {
        ewataskcnt.value = await (getTaskCount(filter: filter, url: userewataskcounturl, taskname: 'ewa'));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_INVOICE') != null) {
        invoicetaskcnt.value = await (getTaskCount(taskname: 'invoice', filter: filter, url: userinvoicetaskcounturl));
      }
      //  }

      if (talabel.get('TMCMOBILE_APPROVALS_SITES') != null) {
        sitetaskcnt.value = await (getTaskCount(taskname: 'sites', filter: filter, url: sitetaskscounturl));
      }
      // if (navFrom.value == null || navFrom.value == 'lease') {
      if (talabel.get('TMCMOBILE_APPROVALS_LEASE') != null) {
        leasetaskcnt.value = await (getTaskCount(taskname: 'lease', filter: filter, url: userleasetaskcounturl));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_LEASEBATCHRENT') != null) {
        leasebatchrenttaskcnt.value =
            await (getTaskCount(taskname: 'LEASEBATCH', filter: filter, url: userleasebatchrenttaskcounturl));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_LEASEOTP') != null) {
        leaseotptaskcnt.value =
            await (getTaskCount(taskname: 'leaseotp', filter: filter, url: userleaseotptaskcounturl));
      }
      if (talabel.get('TMCMOBILE_APPROVALS_LEASERECURRINGCOST') != null) {
        leaserecurcosttaskcnt.value =
            await (getTaskCount(taskname: 'leaserecurcost', filter: filter, url: userleaserecurringcosttaskcounturl));
      }
      //   }
      // if (navFrom.value == null) {

      // }

      if (talabel.get('TMCMOBILE_APPROVALS_DOCUMENTS') != null) {
        doctaskcnt.value = await (getTaskCount(taskname: 'docs', filter: filter, url: userdoctaskcounturl));
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      update();
      isListLoading(false);
    }
  }

  Future<String> getTaskCount({var taskname, dynamic filter, required var url}) async {
    Map<String, dynamic>? resMap;
    List<UserPendingTask> utasks;
    String? taskcnt;
    debugPrint('--------getting $taskname task count------ ');
    try {
      resMap = await UserPendingTaskServices.fetchUserpendingtasks(payloadObj: filter, url: url);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            utasks = tagObjsJson.map((tagJson) => UserPendingTask.fromJson(tagJson)).toList();
            debugPrint('User Tasks Count------ ${utasks.length}');
            if (utasks.isNotEmpty) {
              taskcnt = utasks.first.count?.toString();

              debugPrint('cnt------ $taskcnt');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('getTaskCount>>>>>>>>$e');
      taskcnt = '0';
    }
    return taskcnt ?? '0';
  }

  TextEditingController taskListSearchController = TextEditingController();

  Future fetchEntityTaskList(String? entitytype) async {
    if (entitytype == 'PROJECT') {
      await fetchProjectUsertaskList(entitytype);
    } else if (entitytype == 'BUDGET') {
      await fetchBudgetUsertaskList(entitytype);
    } else if (entitytype == 'BUDGETCO') {
      await fetchBudgetcoUsertaskList(entitytype);
    } else if (entitytype == 'PO') {
      await fetchPoUsertaskList(entitytype);
    } else if (entitytype == 'CO') {
      await fetchCoUsertaskList(entitytype);
    } else if (entitytype == 'SITE') {
      await fetchSiteUsertaskList(entitytype);
    } else if (entitytype == 'INVOICE') {
      await fetchInvoiceUsertaskList(entitytype);
    } else if (entitytype == 'LEASE') {
      await fetchLeaseUsertaskList(entitytype);
    } else if (entitytype == 'LEASEBATCH') {
      await fetchLeaseBatchRentUsertaskList(entitytype);
    } else if (entitytype == 'LEASEOTP') {
      await fetchLeaseOtpUsertaskList(entitytype);
    } else if (entitytype == 'LEASEREC') {
      await fetchLeaseRecurCosttaskList(entitytype);
    } else if (entitytype == 'DOCUMENT') {
      await fetchDocsUsertaskList(entitytype);
    }
  }

  RxList<BudgetPendingTask> budgettasklist = <BudgetPendingTask>[].obs;
  RxList<BudgetChgReqTaskData> budgetchgreqlist = <BudgetChgReqTaskData>[].obs;
  RxList<ProjectPendingTask> projecttasklist = <ProjectPendingTask>[].obs;
  RxList<PoPendingTask> potasklist = <PoPendingTask>[].obs;
  RxList<CoPendingTask> cotasklist = <CoPendingTask>[].obs;
  RxList<InvoicePendingTask> invoicetasklist = <InvoicePendingTask>[].obs;
  RxList<DocumentPendingTask> documenttasklist = <DocumentPendingTask>[].obs;
  RxList<LeasePendingTask> leasetasklist = <LeasePendingTask>[].obs;
  RxList<LeaseBatchRentPendingTask> leasebatchrenttasklist = <LeaseBatchRentPendingTask>[].obs;
  RxList<LeaseOtpPendingTask> leaseotptasklist = <LeaseOtpPendingTask>[].obs;
  RxList<LeaseRecurringCostTask> leaserecurringcostlist = <LeaseRecurringCostTask>[].obs;
  RxList<SitePendingTask> sitetasklist = <SitePendingTask>[].obs;
  var entitytasklist = [].obs;

  fetchBudgetUsertaskList(String? entitytype) async {
    try {
      isloading(true);
      debugPrint('---loaduserpendingtasks------');
      budgettasklist.clear();
      List<BudgetPendingTask> tasklist;
      Map<String, dynamic>? resMap;

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype, payloadObj: params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => BudgetPendingTask.fromJson(tagJson)).toList();
            debugPrint('BudgetPendingTasks Count------ ${tasklist.length}');

            budgettasklist.addAll(tasklist);
          }
        } else {
          budgettasklist.clear();
        }
      } else {
        budgettasklist.clear();
      }
      await userRoleAccess('BUDGET');
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();
  }

  fetchBudgetcoUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---loaduserpendingtasks------');
      budgetchgreqlist.clear();
      List<BudgetChgReqTaskData> tasklist;
      Map<String, dynamic>? resMap;
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);

      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => BudgetChgReqTaskData.fromJson(tagJson)).toList();
            debugPrint('BudgetPendingTasks Count------ ${tasklist.length}');

            budgetchgreqlist.addAll(tasklist);
          }
        } else {
          budgetchgreqlist.clear();
        }
      } else {
        budgetchgreqlist.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();
  }

  fetchProjectUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchProjectUsertaskList------');
      projecttasklist.clear();
      List<ProjectPendingTask> tasklist;
      Map<String, dynamic>? resMap;
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);

      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => ProjectPendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchProjectUsertaskList Count------ ${tasklist.length}');

            projecttasklist.addAll(tasklist);
          }
        } else {
          projecttasklist.clear();
        }
      } else {
        projecttasklist.clear();
      }
      await userRoleAccess('PROJECT');
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();
  }

  fetchPoUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---loaduserpendingtasks------');
      potasklist.clear();
      List<PoPendingTask> tasklist;
      Map<String, dynamic>? resMap;
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);

      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => PoPendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchPoUsertaskList Count------ ${tasklist.length}');

            potasklist.addAll(tasklist);
          }
        } else {
          potasklist.clear();
        }
      } else {
        potasklist.clear();
      }
      await userRoleAccess('PO');
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();
  }

  fetchCoUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchCoUsertaskList------');
      cotasklist.clear();
      List<CoPendingTask> tasklist;
      Map<String, dynamic>? resMap;
      debugPrint('searchtext--------------$searchtext');
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);

      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => CoPendingTask.fromJson(tagJson)).toList();
            debugPrint('invoice Count------ ${tasklist.length}');

            cotasklist.addAll(tasklist);
          }
        } else {
          cotasklist.clear();
        }
      } else {
        cotasklist.clear();
      }
      await userRoleAccess('CO');
    } catch (e) {
      debugPrint('$e');
    }
    isloading(false);
    update();
  }

  Future fetchInvoiceUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchInvoiceUsertaskList------');
      invoicetasklist.clear();
      List<InvoicePendingTask> tasklist;
      Map<String, dynamic>? resMap;
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => InvoicePendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchInvoiceUsertaskList Count------ ${tasklist.length}');

            invoicetasklist.addAll(tasklist);
          }
        } else {
          invoicetasklist.clear();
        }
      } else {
        invoicetasklist.clear();
      }
      await userRoleAccess('INV');
    } catch (e) {
      debugPrint('$e');
    } finally {
      isloading(false);
      update();
    }
  }

  // fetchDocUsertaskList(String? entitytype) async {
  //   try {
  //     isloading(true);
  //     debugPrint('---loaduserpendingtasks------');
  //     documenttasklist.clear();
  //     List<DocumentPendingTask> tasklist;
  //     Map<String, dynamic>? resMap;

  //     resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype, payloadObj: params.value);
  //     if (resMap != null) {
  //       var status = resMap['status'] as int?;
  //       if (status == 0) {
  //         var tagObjsJson = resMap['records'] as List?;
  //         if (tagObjsJson != null) {
  //           tasklist = tagObjsJson.map((tagJson) => DocumentPendingTask.fromJson(tagJson)).toList();
  //           debugPrint('fetchDocUsertaskList Count------ ${tasklist.length}');

  //           documenttasklist.addAll(tasklist);
  //         }
  //       } else {
  //         documenttasklist.clear();
  //       }
  //     } else {
  //       documenttasklist.clear();
  //     }
  //     await userRoleAccess('DOCUMENT');
  //   } catch (e) {
  //     debugPrint('$e');
  //   }
  //   isloading(false);
  //   update();
  // }

  Future fetchLeaseRecurCosttaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchLeaseRecurCosttaskList------');
      leaserecurringcostlist.clear();
      List<LeaseRecurringCostTask> tasklist;
      Map<String, dynamic>? resMap;
      debugPrint('params.value------ ${params.value}');
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => LeaseRecurringCostTask.fromJson(tagJson)).toList();
            debugPrint('fetchLeaseRecurCosttaskList Count------ ${tasklist.length}');

            leaserecurringcostlist.addAll(tasklist);
          }
        } else {
          leaserecurringcostlist.clear();
        }
      } else {
        leaserecurringcostlist.clear();
      }
    } catch (e) {
      debugPrint('fetchLeaseRecurCosttaskList>>>>>>>>$e');
    } finally {
      isloading(false);
      update();
    }
  }

  Future fetchLeaseOtpUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchLeaseOtpUsertaskList------');
      leaseotptasklist.clear();
      List<LeaseOtpPendingTask> tasklist;
      Map<String, dynamic>? resMap;
      debugPrint('params.value------ ${params.value}');
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => LeaseOtpPendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchLeaseOtpUsertaskList Count------ ${tasklist.length}');

            leaseotptasklist.addAll(tasklist);
          }
        } else {
          leaseotptasklist.clear();
        }
      } else {
        leaseotptasklist.clear();
      }
    } catch (e) {
      debugPrint('fetchLeaseOtpUsertaskList>>>>>>>>$e');
    } finally {
      isloading(false);
      update();
    }
  }

  Future fetchLeaseBatchRentUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchLeaseBatchRentUsertaskList------');
      leasebatchrenttasklist.clear();
      List<LeaseBatchRentPendingTask> tasklist;
      Map<String, dynamic>? resMap;
      debugPrint('params.value------ ${params.value}');
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => LeaseBatchRentPendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchLeaseBatchRentUsertaskList Count------ ${tasklist.length}');

            leasebatchrenttasklist.addAll(tasklist);
          }
        } else {
          leasebatchrenttasklist.clear();
        }
      } else {
        leasebatchrenttasklist.clear();
      }
    } catch (e) {
      debugPrint('fetchLeaseBatchRentUsertaskList>>>>>>>>$e');
    } finally {
      isloading(false);
      update();
    }
  }

  Future fetchLeaseUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchLeaseUsertaskList------');
      leasetasklist.clear();
      List<LeasePendingTask> tasklist;
      Map<String, dynamic>? resMap;
      debugPrint('params.value------ ${params.value}');
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => LeasePendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchLeaseUsertaskList Count------ ${tasklist.length}');

            leasetasklist.addAll(tasklist);
          }
        } else {
          leasetasklist.clear();
        }
      } else {
        leasetasklist.clear();
      }
    } catch (e) {
      debugPrint('fetchLeaseUsertaskList>>>>>>>>$e');
    } finally {
      isloading(false);
      update();
    }
  }

  Future fetchDocsUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchDocsUsertaskList------');
      documenttasklist.clear();
      List<DocumentPendingTask> tasklist;
      Map<String, dynamic>? resMap;
      debugPrint('params.value------ ${params.value}');
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => DocumentPendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchLeaseUsertaskList Count------ ${tasklist.length}');

            documenttasklist.addAll(tasklist);
          }
        } else {
          documenttasklist.clear();
        }
      } else {
        documenttasklist.clear();
      }
      await userRoleAccess('DOCUMENT');
    } catch (e) {
      debugPrint('fetchDocsUsertaskList>>>>>>$e');
    } finally {
      isloading(false);
      update();
    }
  }

  Future fetchSiteUsertaskList(String? entitytype, {String? searchtext}) async {
    try {
      isloading(true);
      debugPrint('---fetchSiteUsertaskList------');
      sitetasklist.clear();
      List<SitePendingTask> tasklist;
      Map<String, dynamic>? resMap;
      debugPrint('params.value------ ${params.value}');
      String? srchTxtParams;
      if (searchtext != null && searchtext != '') srchTxtParams = addSearchtextInQueryParams(params.value, searchtext);

      resMap = await UserPendingTaskServices.fetchUserpendingtaskList(entitytype,
          payloadObj: (searchtext != null && searchtext != '') ? srchTxtParams : params.value);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => SitePendingTask.fromJson(tagJson)).toList();
            debugPrint('fetchSiteUsertaskList Count------ ${tasklist.length}');

            sitetasklist.addAll(tasklist);
          }
        } else {
          sitetasklist.clear();
        }
      } else {
        sitetasklist.clear();
      }
      // await userRoleAccess('SITE');
    } catch (e) {
      debugPrint('fetchSiteUsertaskList>>>>>>$e');
    } finally {
      isloading(false);
      update();
    }
  }

  var wfhistorylist = [].obs;

  fetchApprovalHistory(String? entitytype, String? entityid) async {
    try {
      debugPrint('---fetchApprovalHistory------');
      ishistloading(true);
      wfhistorylist.clear();
      List<Wfhistory> tasklist;
      Map<String, dynamic>? resMap;

      resMap = await UserPendingTaskServices.fetchWfHistoryList(entitytype, entityid);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            tasklist = tagObjsJson.map((tagJson) => Wfhistory.fromJson(tagJson)).toList();
            debugPrint('wfhistory Count------ ${tasklist.length}');

            wfhistorylist.addAll(tasklist);
          }
        } else {
          wfhistorylist.clear();
        }
      } else {
        wfhistorylist.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    ishistloading(false);
    update();
  }

  var addCommentCtrl = TextEditingController();
  var commentValidate = true.obs;
  var bottombtnsflag = true.obs;

  resetCommentfield() {
    addCommentCtrl.text = '';
    commentValidate(true);
  }

  bool checkcommentvalidate() {
    debugPrint('addCommentCtrl   ${addCommentCtrl.value.text}');
    bool flag = true;
    if (addCommentCtrl.value.text == '') {
      commentValidate(false);
      flag = false;
    } else {
      commentValidate(true);
    }
    return flag;
  }

  Future<String?> submitwftask(String status, String? assignee, int taskid,
      {int? projectid, String? entitytype}) async {
    var result = 'error';
    bool flag = false;
    String msg = 'Error occurred  while submitting response ';
    try {
      //if (checkcommentvalidate()) {
      Map<String, dynamic>? result;

      debugPrint('status $status assignee $assignee entityid $taskid  comment   ${addCommentCtrl.value.text}');
      result = await UserPendingTaskServices.submitwftask(status, assignee, taskid, addCommentCtrl.value.text ?? '');
      if (result != null && result.isNotEmpty) {
        if (ComponentUtils.equalsIgnoreCase(result['statusmessage'], 'success')) {
          //debugPrint('approval has been successfully processed');

          msg = status == 'approved'
              ? 'Approval has been successfully processed'
              : 'Rejection has been successfully processed';
          flag = true;

          if (entitytype == 'BUDGET' && projectid != null && status == 'approved') {
            Future.delayed(const Duration(seconds: 2), () async {
              await callbaselineBudgetByWF(projectid.toString(), addCommentCtrl.value.text ?? '');
            });
          }
        } else {
          debugPrint('Error occurred  while submitting response ' + result['statusmessage']);
          msg = 'Error occurred  while submitting response ' + result['statusmessage'];
        }
      } else {
        debugPrint('Error occurred  while submitting response ');
      }
      //  }
    } catch (e) {
      debugPrint('$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    Get.back();

    resetCommentfield();
    if (flag) {
      bottombtnsflag(false);
      ComponentUtils.showpopup(type: 'Info', msg: msg);
      loadtaskcount();
    } else {
      ComponentUtils.showpopup(type: 'Error', msg: msg);
    }
    return null;
  }

  Future<void> callbaselineBudgetByWF(String projectid, String comments) async {
    try {
      await UserPendingTaskServices.callbaselineBudgetByWF(projectid, comments);
    } catch (e) {
      debugPrint('$e');
    }
  }

  String? addSearchtextInQueryParams(String prms, String searchtext) {
    if (prms != null) {
      Map vMap = jsonDecode(prms);
      List l = vMap['filters'];
      String tmp = '{"field":"all_columns","operator": "CONTAINS","value": "$searchtext"}';
      l.add(tmp);
      vMap['filters'] = l;
      debugPrint('final map   ${vMap.toString()}');
      return vMap.toString();
    }
    return null;
  }

  Future<void> userRoleAccess(String entitytype) async {
    try {
      var map = await CommonServices.userRoleAccessService(entitytype: entitytype, module: 'workflow');
      debugPrint('res>>>>>$map');
      if (map != null && map['approver-role'] != null && map['approver-role'] == 'yes') {
        approvarrole.value = 'approver';
      } else {
        approvarrole.value = 'no-access';
      }
    } catch (e) {
      approvarrole.value = 'no-access';
      debugPrint('userRoleAccess>>>>>>>$e');
    }
  }

  //resfreshing site status

  Future<void> getSiteDatainfo() async {
    debugPrint('------------getSiteDatainfo--------');

    List<SiteView> sites;
    String payloadObj;
    Map<String, dynamic>? resMap;
    String? siteid = sitetaskdata.value.entityid;
    debugPrint('Site ID >>>$siteid');
    if (siteid != null) {
      detLoading.value = true;
      Map<String, dynamic> dynamicMap = {
        'a': ['site_id', 'EXACT', siteid],
      };
      payloadObj = CommonServices.getFilter(dynamicMap);

      try {
        resMap = await CommonServices.fetchDynamicApi(sitesearchviewurl, payloadObj: payloadObj);
        if (resMap != null) {
          var status = resMap['status'] as int?;
          if (status == 0) {
            var tagObjsJson = resMap['records'] as List?;
            if (tagObjsJson != null) {
              sites = tagObjsJson.map((tagJson) => SiteView.fromJson(tagJson)).toList();
              debugPrint(' list Count------ ${sites.length}');
              if (sites.isNotEmpty) {
                SiteView stp = sites.first;
                sitetaskdata.value.dealStatusDesc = stp.status;
              }
            }
          }
        }
      } catch (error) {
        debugPrint('getSiteDatainfo>>>${error.toString()}');
      } finally {
        detLoading.value = false;
      }
    }
  }
}
