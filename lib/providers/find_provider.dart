import 'package:flutter/material.dart';
import '../services/find_services.dart';
import '../models/find.dart';
import '../utils/constvariables.dart';

class FindProvider with ChangeNotifier {
  //FindProvider();

  List<FindPerson>? findPersonsList;
  int? findPersonsCnt;
  List<FindSpace?>? findSpacesList;
  int? findSpacesCnt;
  bool _loading = false;
  String _tab = 'persons';
  bool _hasMore = false;
  bool? _reachedMax;
  final int _fetchCount = ConstHelper.fetchRowsCount; //default fetch rows count
  int _pageNum = 0;
  List<FindPerson>? perList;
  List<FindSpace?>? spcList;

  FindPerson? personData;
  FindSpace? spaceData;

  bool get isLoading => _loading;
  bool? get isReachedNax => _reachedMax;
  bool get hasMore => _hasMore;
  String get isTab => _tab;

  setTab(String tabname) {
    _tab = tabname;
    notifyListeners();
  }

  getFindPersonsData(String allCol, String name, String dept) async {
    try {
      _loading = true;
      notifyListeners();

      String queryparams = getFindPersonsQP(allCol, name, dept, 0) ?? '';
      findPersonsList = (await fetchPersons(queryparams: queryparams));
      findPersonsCnt = (await fetchFindCount('persons', queryparams: queryparams));
      debugPrint('person recordscount   $findPersonsCnt');

      _hasMore = findPersonsList!.length == _fetchCount;
      _loading = false;

      notifyListeners();
    } catch (error) {
      rethrow;
    }
  }

  getMoreFindPersonsData(String allCol, String name, String dept, {bool? loadMore}) async {
    try {
      String queryparams;
      _pageNum = findPersonsList!.length ~/ _fetchCount;
      queryparams = getFindPersonsQP(allCol, name, dept, _pageNum) ?? '';

      perList = await fetchPersons(queryparams: queryparams);
      if (perList!.isNotEmpty) {
        _hasMore = perList!.length == _fetchCount;
        findPersonsList!.addAll(perList!);
        perList!.clear();
      } else {
        _hasMore = false;
      }

      debugPrint('person recordscount   $findPersonsCnt');

      notifyListeners();
    } catch (error) {
      rethrow;
    }
  }

  getFindSpacesData(
      String allCol, String buildingName, String floorName, String spaceNum, String spaceType, String spaceName) async {
    try {
      _loading = true;
      notifyListeners();

      String queryparams = getFindSpacesQP(allCol, buildingName, floorName, spaceNum, spaceType, spaceName, 0) ?? '';
      findSpacesList = (await fetchSpaces(queryparams: queryparams));
      findSpacesCnt = (await fetchFindCount('spaces', queryparams: queryparams));

      debugPrint('space recordscount   $findSpacesCnt');

      _hasMore = findSpacesList!.length == _fetchCount;
      _loading = false;
      notifyListeners();
    } catch (error) {
      rethrow;
    }
  }

  getMoreFindSpacesData(
      String allCol, String buildingName, String floorName, String spaceNum, String spaceType, String spaceName,
      {bool? loadMore}) async {
    try {
      String queryparams;
      _pageNum = findSpacesList!.length ~/ _fetchCount;
      queryparams = getFindSpacesQP(allCol, buildingName, floorName, spaceNum, spaceType, spaceName, _pageNum) ?? '';

      spcList = await fetchSpaces(queryparams: queryparams);

      if (spcList!.isNotEmpty) {
        _hasMore = spcList!.length == _fetchCount;
        findSpacesList?.addAll(spcList!);
        spcList!.clear();
      } else {
        _hasMore = false;
      }

      debugPrint('person recordscount   $findPersonsCnt');

      notifyListeners();
    } catch (error) {
      rethrow;
    }
  }

  String getFindPersonsQP(String allCol, String name, String dept, int offset) {
    debugPrint('allCol   $allCol    name    $name    dept    $dept   offset  $offset');
    var buffer = StringBuffer();
    bool flag = false;

    if (allCol.isNotEmpty) {
      buffer.write('?allCol=$allCol');
      flag = true;
    }
    if (name.isNotEmpty || dept.isNotEmpty || offset > 0) {
      flag ? buffer.write('&') : buffer.write('?');
      buffer.write('name=$name');
      buffer.write('&dept=$dept');
      buffer.write('&offset=$offset');
    }

    debugPrint('qp   $buffer');
    return buffer.toString();
  }

  String getFindSpacesQP(String allCol, String buildingName, String floorName, String spaceNum, String spaceType,
      String spaceName, int offset) {
    debugPrint(
        'allCol-$allCol  buildingName-$buildingName  floorName-$floorName spaceNum-$spaceNum  spaceType-$spaceType  spaceName-$spaceName');
    var buffer = StringBuffer();
    bool flag = false;
    if (allCol.isNotEmpty) {
      buffer.write('?allCol=$allCol');
      flag = true;
    }
    if (buildingName.isNotEmpty ||
        floorName.isNotEmpty ||
        spaceNum.isNotEmpty ||
        spaceType.isNotEmpty ||
        spaceName.isNotEmpty ||
        offset > 0) {
      flag ? buffer.write('&') : buffer.write('?');
      buffer.write('buildingName=$buildingName');
      buffer.write('&floorName=$floorName');
      buffer.write('&spaceNum=$spaceNum');
      buffer.write('&spaceType=$spaceType');
      buffer.write('&spaceName=$spaceName');
      buffer.write('&offset=$offset');
    }

    debugPrint('qp   $buffer');
    return buffer.toString();
  }

  getFindPersonDeatils(int? personId) async {
    try {
      personData = (await fetchPersonDetails(personId));

      notifyListeners();
    } catch (error) {
      rethrow;
    }
  }

  getFindSpaceDeatils(int? spaceId) async {
    try {
      spcList = await fetchSpaceDetails(spaceId);
      if (spcList!.isNotEmpty) {
        spaceData = spcList?.first;
      } else {
        spaceData = FindSpace();
      }

      notifyListeners();
    } catch (error) {
      rethrow;
    }
  }
}
