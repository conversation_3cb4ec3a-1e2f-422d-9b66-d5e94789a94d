import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:tangoworkplace/models/comments.dart';
import 'package:tangoworkplace/models/lookup_values.dart';
import 'package:tangoworkplace/models/person_list.dart';
import 'package:tangoworkplace/models/pictures.dart';
import 'package:tangoworkplace/models/recent_reservations.dart';
import '../models/building.dart';
import '../models/floor.dart';
import '../models/property.dart';
import '../models/reservations.dart';
import '../models/space.dart';
import '../services/reservations_services.dart';

class ReservationProvider with ChangeNotifier {
  ReservationProvider();

  List<Reservations>? post;
  List<Property>? propertyLov;
  bool loading = false, completedloading = false;
  List<Building>? buildingLov;
  List<Floor>? floorLov;
  List<Space>? spaceLov;
  List<Reservations>? completedReservations;
  List<Comments>? commentsList;
  List<Pictures>? picturesList;
  List<RecentReservations>? recentReservationsLov;
  List<RecentReservations>? locationsLov;
  List<PersonList>? assignedToLov;
  List<PersonList>? recentAssignedToLov;
  List<LookupValues>? spaceAttributesLov;
  bool isspaceLovLoading = false;

  getReservationsData(context) async {
    loading = true;
    post = (await getReservations(context));

    print(post);
    loading = false;

    notifyListeners();
  }

  getCompletedReservationsImpl(context) async {
    print('Inside getCompletedReservations');
    completedloading = true;
    completedReservations = (await getCompletedReservationsWS(context));

    print(completedReservations);
    completedloading = false;

    notifyListeners();
  }

  Future<void> addReservation(Reservations reserobj, {context}) async {
    try {
      Reservations updatedObj = await addReservationImpl(reserobj);
      getReservationsData(context);

      // post.insert(0, updatedObj); // at the start of the list
      // notifyListeners();
    } catch (error) {
      print(error);
      rethrow;
    }
  }

  Future<String?> updateReservation(Reservations reserobj, int? requestId) async {
    String? result = '';
    try {
      final reservationIndex = post!.indexWhere((reservation) => reservation.reservationId == requestId);
      if (reservationIndex >= 0) {
        result = await (updateReservationImpl(reserobj, requestId) as Future<String>);

        post![reservationIndex] = reserobj;
        notifyListeners();
      } else {
        // throw CustomException("Error occured while updating reservation");
      }
    } catch (error) {
      print(error);
    }
    print('update reservation $result');
    return result;
  }

  Reservations findById(int? id) {
    return post!.firstWhere((reservobj) => reservobj.reservationId == id);
  }

  Reservations findCompletedById(int? id) {
    return completedReservations!.firstWhere((reservobj) => reservobj.reservationId == id);
  }

  Future<List<Property>?> getPropertyLov(context) async {
    propertyLov = (await getPropertyLovObj(context));
    notifyListeners();

    return propertyLov;
  }

  List<Building>? getBuildingByProperty(int? propertyId) {
    Property p = propertyLov!.firstWhere((item) => item.propertyId == propertyId);
    var lov = p.buildings!;
    buildingLov = List<Building>.from(lov);

    notifyListeners();

    return buildingLov;
  }

  List<Floor>? getFloorByBuilding(int? buildingId) {
    floorLov = buildingLov!.firstWhere((item) => item.buildingId == buildingId).floors;
    notifyListeners();

    return floorLov;
  }

  Future<List<Space>?> getSpace(
      context, int? buildingId, int? floorId, String? startdate, String? enddate, String? selectedAttributes,
      {String? requestId}) async {
    try {
      setSpaceLovLoading(true);
      spaceLov = (await getSpaceLovObj(context, buildingId, floorId, startdate, enddate, selectedAttributes,
          requestId: requestId));
    } catch (e) {
      print(e);
    }
    setSpaceLovLoading(false);

    return spaceLov;
  }

  setSpaceLovLoading(bool load) async {
    isspaceLovLoading = load;
    notifyListeners();
  }

  String? findBySpace(int id) {
    Space obj = spaceLov!.firstWhere((spaceObj) => spaceObj.spaceId == id);
    return obj.spaceDisplayName;
  }

  Future<List<RecentReservations>?> getRecentReservationLoc(context, String startdate, String enddate) async {
    recentReservationsLov = await getRecentReserLocInfo(context, startdate, enddate);
    notifyListeners();

    return recentReservationsLov;
  }

  Future<List<RecentReservations>?> getPropertyBuildingFloorLoc(
      context, String? startdate, String? enddate, String searchtext, String? selectedAttributes) async {
    locationsLov = (await getPropertyBuildingFloorLocInfo(context, startdate, enddate, searchtext, selectedAttributes));
    notifyListeners();

    return recentReservationsLov;
  }

  Future<List<PersonList>?> getAssignedToLov(context, String searchtext) async {
    assignedToLov = (await getPersonListInfo(context, searchtext));
    notifyListeners();

    return assignedToLov;
  }

  Future<String?> updateStatus(context, int? reservationid, String status) async {
    String? result = (await updateReservationStatus(context, status, reservationid));
    notifyListeners();

    return result;
  }

  Future<String?> updateComments(context, int? reservationid, String comments, int rating, int? spaceid) async {
    String? result = (await updateReservationComments(context, reservationid, comments, rating, spaceid));
    notifyListeners();

    return result;
  }

  Future<List?> getAssignees(context, int? reservationid) async {
    List? assignees = (await getAssigneesImpl(context, reservationid));
    notifyListeners();

    return assignees;
  }

  Future<List<Comments>?> getComments(context, int? entityid, String entitytype) async {
    commentsList = (await (getCommentsImpl(context, entityid, entitytype) as Future<List<Comments>?>));
    notifyListeners();

    return commentsList;
  }

  Future<List<Pictures>?> getPictures(context, int? entityid, String entitytype) async {
    picturesList = (await (getPicturesImpl(context, entityid, entitytype) as Future<List<Pictures>?>));
    notifyListeners();

    return picturesList;
  }

  Future<List<PersonList>?> getRecentAssignedToLov(context) async {
    recentAssignedToLov = (await getRecentPersonImpl(context));
    notifyListeners();

    return recentAssignedToLov;
  }

  Future<List<LookupValues>?> getLookupValues(context, String lookupcode) async {
    spaceAttributesLov = (await getLookupValuesImpl(context, lookupcode));
    if (spaceAttributesLov != null) spaceAttributesLov!.sort((a, b) => a.lookupValue!.compareTo(b.lookupValue!));

    notifyListeners();

    return spaceAttributesLov;
  }

  Future<String> getPersonId(String userid) async {
    String uid = '';
    try {
      uid = await ReservationServices.getPersonId(userid);
    } catch (error) {
      uid = '';
      debugPrint('getPersonId>>>>>> $error');
    }
    return uid;
  }
}
