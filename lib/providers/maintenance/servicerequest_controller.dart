import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/common/entity_data.dart';
import 'package:tangoworkplace/models/servicerequest/sr_assetinstance.dart';
import 'package:tangoworkplace/models/servicerequest/sr_space.dart';

import '../../common/component_utils.dart';
import '../../common/progess_indicator_cust.dart';
import '../../models/lookup_values.dart';
import '../../models/servicerequest/service_request.dart';
import '../../models/servicerequest/sr_floor.dart';
import '../../models/servicerequest/sr_personprimeloc.dart';
import '../../models/ta_admin/entitystatus.dart';
import '../../services/common_services.dart';
import '../../services/servicerequests_services.dart';
import '../../utils/common_utils.dart';
import '../../utils/connections.dart';
import '../../utils/user_secure_storage.dart';
import '../ta_admin/label_controller.dart';

class ServiceRequestController extends GetxController {
  LabelController talabel = Get.find<LabelController>();
  var isListLoading = false.obs;
  RxList<Servicerequest> srList = <Servicerequest>[].obs;
  Rx<Servicerequest> servicerequest = Servicerequest().obs;
  var srstatus = 'open'.obs;
  var readonly = false.obs;
  var filterwidgetloading = false.obs;
  TextEditingController searchController = TextEditingController();
  var srSortBy = ''.obs;
  RxList<EntityStatus> sortByLov = <EntityStatus>[].obs;
  var srFilterBy = '1'.obs;
  RxList<EntityStatus> filterByLov = <EntityStatus>[].obs;
  var filterList = [].obs;
  var openCloseFilter = 'Open'.obs;

  Future getServicerequestData({String? searchText, String? action}) async {
    var user = UserContext.info()!.userName;
    var statuslist = ['Closed', 'Work Complete'];
    try {
      isListLoading(true);
      srList.clear();
      List<Servicerequest>? srRec;

      var payloadObj;
      Map<String, dynamic>? resMap;
      Map<String, dynamic> dynamicMap;
      String filter;

      dynamicMap = {
        'a': ['created_by', 'EXACT', user!.toUpperCase()],
        'b': ['status', openCloseFilter.value == 'Completed' ? 'IN' : 'NOT_IN', statuslist.join(',')],
        'd': ['daterange', 'EXACT', srFilterBy.value],
        'e': ['requested_for', 'EXACT', user.toUpperCase()],
      };

      if (searchController.text.isNotEmpty && searchController.text != '') {
        dynamicMap['c'] = ['all_columns', 'CONTAINS', searchController.text];
      }

      if (srSortBy.value.isNotEmpty) {
        filter = CommonServices.getFilter(dynamicMap, size: 100, sortby: srSortBy.value);
      } else {
        filter = CommonServices.getFilter(dynamicMap, size: 100);
      }
      debugPrint('filter---------$filter');

      resMap = await CommonServices.fetchDynamicApi(servicerequestsearchurl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            srRec = tagObjsJson.map((tagJson) => Servicerequest.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${srRec.length}');
            srList.addAll(srRec);
          }
        } else {
          srList.clear();
        }
      } else {
        srList.clear();
      }
    } finally {
      isListLoading.value = false;
      setFilterChips(true);
      loadFilterLovData(action);
    }
  }

  Future getMoreServicerequestData(String status, {bool? loadMore, String? searchText}) async {
    // try {
    String queryparams;
    // _pageNum = serviceRequestList.length ~/ _fetchCount;
    //queryparams = '?status=$status' + '&searchText=' + searchText + '&offset=' + _pageNum.toString();

    //     srList = await fetchServiceRequests(queryparams: queryparams);
    //     if (srList.isNotEmpty) {
    //       _hasMore = srList.length == _fetchCount;
    //       serviceRequestList.addAll(srList);
    //       srList.clear();
    //     } else {
    //       _hasMore = false;
    //     }

    //     debugPrint('SR recordscount   $serviceRequestsCnt');

    //     notifyListeners();
    //   } catch (error) {
    //     throw error;
    //   }
  }

  loadFilterLovData(String? action) {
    if (action == 'onload') {
      sortByLov.value.clear();
      sortByLov.insert(0, EntityStatus(statusCode: '', status: 'Select'));
      sortByLov.add(EntityStatus(status: 'Service Request Number', statusCode: 'Service_Req_Number'));
      sortByLov.add(EntityStatus(status: 'Store Name', statusCode: 'Store_Name'));
      sortByLov.add(EntityStatus(status: 'Issue Type', statusCode: 'Issue_Type'));
      sortByLov.add(EntityStatus(status: 'Impacted Area', statusCode: 'Impacted_Area'));
      sortByLov.add(EntityStatus(status: 'Creation Date', statusCode: 'Creation_Date'));

      //setting sort By lov list--------------------------------------------------------------------------
      // if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SRNUMBER') != null)
      //   sortByLov.value.add(EntityStatus(
      //       status: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_SRNUMBER')?.value ?? 'ServiceReqNumber', statusCode: 'ServiceReqNumber'));
      // if (talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITENAME') != null)
      //   sortByLov.value.add(EntityStatus(
      //       status: talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITENAME')?.value ?? 'StoreName', statusCode: 'StoreName'));

      // if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE') != null)
      //   sortByLov.value.add(
      //       EntityStatus(status: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_ISSUETYPE')?.value ?? 'IssueType', statusCode: 'IssueType'));

      // if (talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_IMPACTEDAREA') != null)
      //   sortByLov.value.add(EntityStatus(
      //       status: talabel.get('TMCMOBILE_SERVICEREQUEST_DETAILS_IMPACTEDAREA')?.value ?? 'ImpactedArea', statusCode: 'ImpactedArea'));

      // if (talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_OWNERSHIPTYPE') != null)
      //   sortByLov.value.add(EntityStatus(
      //       status: talabel.get('TMCSSERVICEREQUESTSEARCHPG_GENERAL_CREATIONDATE')?.value ?? 'CreationDate', statusCode: 'CreationDate'));
      filterByLov.value.clear();
      filterByLov.insert(0, EntityStatus(statusCode: '1', status: 'All'));
      filterByLov.add(EntityStatus(status: 'Last 30 days', statusCode: '30'));
      filterByLov.add(EntityStatus(status: 'Last 60 days', statusCode: '60'));
      filterByLov.add(EntityStatus(status: 'Last 90 days', statusCode: '90'));
    }
  }

  setFilterChips(bool flag) {
    Future.delayed(const Duration(seconds: 0), () {
      filterList.clear();
      //filterList.add(allmyfilter.value);

      filterList.add(GestureDetector(
        child: Chip(
          label: Text(isListLoading.value ? '' : openCloseFilter.value),
        ),
        onTap: () async {
          if (isListLoading.value) {
            debugPrint('------');
          } else {
            if (openCloseFilter.value == 'Open') {
              openCloseFilter.value = 'Completed';
            } else if (openCloseFilter.value == 'Completed') {
              openCloseFilter.value = 'Open';
            }

            await getServicerequestData();
          }
        },
      ));

      if (flag) {
        if (searchController.text.isNotEmpty && searchController.text != '') {
          filterList.add(Chip(
            avatar: const Icon(Icons.search, size: 20),
            label: Text(searchController.text),
            onDeleted: () async {
              searchController.text = '';
              await getServicerequestData();
            },
          ));
        }

        if (srFilterBy.value.isNotEmpty && srFilterBy.value != '1') {
          var statusname = filterByLov
                  .firstWhere((EntityStatus s) => s.statusCode == srFilterBy.value,
                      orElse: () => EntityStatus(status: ''))
                  .status ??
              '';

          filterList.add(Chip(
            avatar: const Icon(Icons.flag, size: 20),
            label: Text(statusname),
            onDeleted: () async {
              srFilterBy.value = '1';
              await getServicerequestData();
            },
          ));
        }
      }
    });
  }

  //details page-----------------------------------------------

  var isdetailsloading = false.obs;
  var sr_mode = ''.obs;
  var labelsloading = false.obs;
  RxList<LookupValues> issueTypeLov = <LookupValues>[].obs;
  RxList<LookupValues> impactAreaLov = <LookupValues>[].obs;
  RxList<LookupValues> problemCodeLov = <LookupValues>[].obs;
  RxList<SrFloors> floorLov = <SrFloors>[].obs;
  var locationCtrl = TextEditingController();
  var spaceNameCtrl = TextEditingController();
  var assetInstanceNameCtrl = TextEditingController();
  RxList<SrPersonPrimeLoc> personPrimeLocData = <SrPersonPrimeLoc>[].obs;

  Future getlabels(LabelController talabel) async {
    try {
      await talabel.getlabels('TMCMOBILE_SERVICEREQUEST', 'servicerequest');
    } catch (e) {
      debugPrint('$e');
    }
  }

  Future setCurrentRow() async {
    try {
      debugPrint('----------------------------setCurrentRow-----------------------------');
      readonly.value = !(servicerequest.value.status == 'Draft' || servicerequest.value.status == 'Submit Pending');
      List<LookupValues>? sit = await (fetchLovs('SR_ISSUE_TYPE', '', true));
      issueTypeLov.value = sit ?? <LookupValues>[];

      if (sr_mode.value == 'create') {
        int? id = await (getsrid());
        if (id != null && id > 0) {
          servicerequest.value.serviceReqId = id;

          var user = await CommonUtils.getUserdata();
          debugPrint('>>>>>>>>>>>>>>>>${jsonEncode(user)}');

          servicerequest.value.requestedFor = user.userName;
          servicerequest.value.phone = user.phoneNumber;

          List<SrPersonPrimeLoc>? personlocdata =
              user.personId != null ? await loadPersonPrimeLocationData(user.personId?.toString()) : null;
          SrPersonPrimeLoc spp =
              (personlocdata != null && personlocdata.isNotEmpty) ? personlocdata.first : SrPersonPrimeLoc();
          debugPrint('SrPersonPrimeLoc    ${jsonEncode(spp)}');
          if (spp.buildingId != null) {
            servicerequest.value.entityId = spp.buildingId;
            locationCtrl.text = spp.storeName!;
            servicerequest.value.floorId = spp.floorId;
            servicerequest.value.spaceId = spp.spaceId;
            spaceNameCtrl.text = spp.spaceDisplayName.toString();
          } else {
            locationCtrl.text = '';
            spaceNameCtrl.text = '';
          }
          await singleExtLovFetch();
        } else {
          Get.back();
          return null;
        }
        assetInstanceNameCtrl.text = '';
      } else {
        locationCtrl.text = servicerequest.value.storeName ?? '';
        spaceNameCtrl.text = servicerequest.value.spaceDisplayName ?? '';
        assetInstanceNameCtrl.text = servicerequest.value.assetInstanceName ?? '';

        List<LookupValues>? irl = servicerequest.value.issueType != null
            ? await (fetchLovs('SR_IMPACTED_AREA', servicerequest.value.issueType!, true))
            : <LookupValues>[];
        List<LookupValues>? spc = servicerequest.value.impactedArea != null
            ? await (fetchLovs('SR_PROBLEM_CODE', servicerequest.value.impactedArea!, true))
            : <LookupValues>[];

        impactAreaLov.value = irl ?? <LookupValues>[];
        problemCodeLov.value = spc ?? <LookupValues>[];
      }
      floorLov.clear();
      await getFloorLovData();
      // debugPrint('entity id>>>>>>>>>>>>>>>>${servicerequest.value.entityId}');
      // debugPrint('floor id>>>>>>>>>>>>>>>>${servicerequest.value.floorId}');
      // debugPrint('issue type>>>>>>>>>>>>>>>>${servicerequest.value.issueType}');
      // debugPrint('area>>>>>>>>>>>>>>>>${servicerequest.value.impactedArea}');
      // debugPrint('prblm code>>>>>>>>>>>>>>>>${servicerequest.value.problemCode}');
      //debugPrint('floor id>>>>>>>>>>>>>>>>${servicerequest.value.floorId}');
    } finally {
      isdetailsloading.value = false;
    }
  }

  Future singleExtLovFetch() async {
    if (issueTypeLov.isNotEmpty && issueTypeLov.value.length == 1) {
      servicerequest.value.issueType = issueTypeLov.value.first.lookupCode;
      await onIssueTypeVC(servicerequest.value.issueType!);
    }
  }

  Future getsrid() async {
    debugPrint('------------getsrid--------');
    int? id = 0;
    try {
      id = await (ServiceRequestServices.getsrid());
    } finally {}
    debugPrint('------------getsrid--------$id');
    return id;
  }

  Future<List<SrPersonPrimeLoc>> loadPersonPrimeLocationData(String? personid) async {
    debugPrint('------------loadPersonPrimeLocationData--------$personid');

    List<SrPersonPrimeLoc> personRec;
    personPrimeLocData.clear();

    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['p_personId', 'EXACT', personid],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(srpersonprimelocurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            personRec = tagObjsJson.map((tagJson) => SrPersonPrimeLoc.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${personPrimeLocData.length}');
            if (personRec.isNotEmpty) personPrimeLocData.addAll(personRec);
          }
        } else {
          personPrimeLocData.clear();
        }
      } else {
        personPrimeLocData.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }

    return personPrimeLocData;
  }

  Future onIssueTypeVC(String issueType) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    try {
      servicerequest.value.impactedArea = null;
      servicerequest.value.problemCode = null;
      problemCodeLov.clear();
      List<LookupValues>? ial =
          issueType != null ? await (fetchLovs('SR_IMPACTED_AREA', issueType, true)) : <LookupValues>[];

      impactAreaLov.value = ial ?? <LookupValues>[];
      if (impactAreaLov.isNotEmpty && impactAreaLov.value.length == 1) {
        servicerequest.value.impactedArea = impactAreaLov.value.first.lookupCode;
        await onImpactedAreaVC(servicerequest.value.impactedArea!);
      }
    } finally {
      Get.back();
    }
  }

  Future onImpactedAreaVC(String impactedArea) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    try {
      servicerequest.value.problemCode = null;
      List<LookupValues>? pcl =
          impactedArea != null ? await (fetchLovs('SR_PROBLEM_CODE', impactedArea, true)) : <LookupValues>[];
      problemCodeLov.value = pcl ?? <LookupValues>[];
      if (problemCodeLov.isNotEmpty && problemCodeLov.value.length == 1) {
        servicerequest.value.problemCode = problemCodeLov.value.first.lookupCode;
      }
    } finally {
      Get.back();
    }
  }

  Future getFloorLovData() async {
    debugPrint('------------loadFloorsData--------${servicerequest.value.entityId}');
    if (servicerequest.value.entityId != null) {
      floorLov.value.clear();

      Map<String, dynamic>? resMap;

      Map<String, dynamic> dynamicMap = {
        'a': ['building_id', 'EXACT', servicerequest.value.entityId?.toString()],
        'b': ['building_status_type', 'EXACT', 'AC'],
      };

      var payloadObj = CommonServices.getFilter(dynamicMap);

      try {
        resMap = await CommonServices.fetchDynamicApi(srfloorsurl, payloadObj: payloadObj);
        if (resMap != null) {
          var status = resMap['status'] as int?;
          if (status == 0) {
            var tagObjsJson = resMap['records'] as List?;
            if (tagObjsJson != null) {
              floorLov.value = tagObjsJson.map((tagJson) => SrFloors.fromJson(tagJson)).toList();
              floorLov.value.insert(0, SrFloors(storeId: 0, storeName: 'Select a Floor'));
              debugPrint(' Floor Count------ ${floorLov.value.length}');
            }
          } else {
            floorLov.clear();
          }
        } else {
          floorLov.clear();
        }
      } catch (e) {
        debugPrint('$e');
      } finally {}
    } else {
      floorLov.value.insert(0, SrFloors(storeId: 0, storeName: 'Select a Floor'));
    }
  }

  var srinptlovDataLoading = false.obs;
  RxList<EntityData> entityList = <EntityData>[].obs;
  var iptlovsearchCtrl = TextEditingController();
  RxList<SrSpace> spacesList = <SrSpace>[].obs;
  RxList<SrAssetInstance> assetinstList = <SrAssetInstance>[].obs;

  Future getStoreEntityData({required String filter}) async {
    try {
      srinptlovDataLoading.value = true;
      entityList.clear();
      List<EntityData>? reportRec;

      String queryparams = '?filter=$filter';
      reportRec = await fetchEntities('SR_LOCATION', queryparams);
      entityList.addAll(reportRec!);
      // } catch (error) {
      //   print(error);
    } finally {
      srinptlovDataLoading.value = false;
    }
  }

  Future getSpacesData(String? buildingid, String? floorid, {String? filter}) async {
    debugPrint('------------loadSpacesData--------$floorid');
    spacesList.clear();
    srinptlovDataLoading.value = true;
    List<SrSpace> spacesRec;
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['building_id', 'EXACT', buildingid],
    };
    if (floorid != null && floorid != '0') {
      dynamicMap['b'] = ['floor_id', 'EXACT', floorid];
    }
    if (filter != null && filter.isNotEmpty) {
      dynamicMap['c'] = ['all_columns', 'CONTAINS', filter];
      //dynamicMap['d'] = ['space_use', 'CONTAINS', filter];
    }

    var payloadObj = CommonServices.getFilter(dynamicMap, size: 500, sortby: 'space_display_name');

    try {
      resMap = await CommonServices.fetchDynamicApi(srspacesurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            spacesRec = tagObjsJson.map((tagJson) => SrSpace.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${spacesRec.length}');
            spacesList.addAll(spacesRec);
          }
        } else {
          spacesList.clear();
        }
      } else {
        spacesList.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      srinptlovDataLoading.value = false;
    }
  }

  Future onSaveServiceRequest() async {
    servicerequest.value.actionType = sr_mode.value;
    servicerequest.value.floorId = servicerequest.value.floorId == 0 ? null : servicerequest.value.floorId;
    debugPrint('service request data    ${jsonEncode(servicerequest.value)}');
    ProgressUtil.showLoaderDialog(Get.context!);
    bool flag = false;
    try {
      var result;

      result = await ServiceRequestServices.saveSrDetailsdata(servicerequest.value);
      debugPrint('result-------$result');
      if (result != null && result == 'success') {
        flag = true;
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
      if (flag) {
        Get.back();
        getServicerequestData();
        ComponentUtils.showsnackbar(text: "Data saved successfully...");
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      }
    }
  }

  Future getAssetInstanceData({String? filter, String? assetinstid}) async {
    debugPrint('------------getAssetInstanceData--------');
    assetinstList.clear();
    srinptlovDataLoading.value = true;
    List<SrAssetInstance> rec;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap = HashMap<String, dynamic>();
    if (assetinstid != null) {
      dynamicMap['asi'] = ['asset_instance_id', 'EXACT', assetinstid];
    } else {
      dynamicMap['a'] = ['location_id', 'EXACT', servicerequest.value.entityId?.toString()];

      if (servicerequest.value.spaceId != null && servicerequest.value.spaceId != 0) {
        dynamicMap.update('a', (value) => ['space_id', 'EXACT', servicerequest.value.spaceId?.toString()]);
      } else if (servicerequest.value.floorId != null) {
        dynamicMap.update('a', (value) => ['floor_id', 'EXACT', servicerequest.value.floorId?.toString()]);
      }

      if (filter != null && filter.isNotEmpty) {
        dynamicMap['d'] = ['all_columns', 'CONTAINS', filter];
      }
    }

    var payloadObj = CommonServices.getFilter(dynamicMap, size: 500, sortby: 'asset_instance_name');

    try {
      resMap = await CommonServices.fetchDynamicApi(srassetnamelovurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            rec = tagObjsJson.map((tagJson) => SrAssetInstance.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${rec.length}');
            assetinstList.addAll(rec);
          }
        } else {
          assetinstList.clear();
        }
      } else {
        assetinstList.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      srinptlovDataLoading.value = false;
    }
  }
}
