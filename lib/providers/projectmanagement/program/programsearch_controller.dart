import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/services/projectmanagement/project/project_search_services.dart';

import '../../../models/program/programs_search_data.dart';
import '../../../models/ta_admin/entitystatus.dart';

class ProgramSearchController extends GetxController {
  final Rx<TextEditingController> searchController = TextEditingController().obs;

  RxList<ProgramsSearchData> programs = <ProgramsSearchData>[].obs;
  var isLoading = false.obs;
  var tabroles = <dynamic, dynamic>{}.obs;
  var allmyfilter = 'My'.obs;
  var ongngcmpltd = 'Ongoing'.obs;
  var searchCtrl = TextEditingController();
  var statusTypeRadio = ''.obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var status = ''.obs;

  @override
  void onClose() {
    searchController.close();
    debugPrint('------------onClose--------');
    super.onClose();
  }

  Future fetchPrograms() async {
    debugPrint('------------fetchPrograms--------');
    isLoading(true);
    List<ProgramsSearchData> prog;
    programs.clear();
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['all_columns', 'CONTAINS', searchCtrl.text],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(programsearchviewurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            prog = tagObjsJson.map((tagJson) => ProgramsSearchData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${prog.length}');

            programs.addAll(prog);
          }
        } else {
          programs.clear();
        }
      } else {
        programs.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isLoading(false);
  }

  Future<String> getUserEntityEditAccess({required String entitytype, required String entityid, String? roFlag}) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    Map<dynamic, dynamic>? vmap;
    String res = 'view';
    tabroles.value = <dynamic, dynamic>{};
    try {
      vmap = await CommonServices.userEntityEditAccessService(entityid: entityid, entitytype: entitytype);
      debugPrint('getUserEntityEditAccess >>>>>>>$roFlag');
      if (vmap != null) {
        tabroles.value = vmap;
        if (vmap['result'] != null && vmap['result'] == 'Y' && vmap['role_flag'] != null && vmap['role_flag'] == 'Y') {
          res = 'Y';
        } else if (vmap['result'] != null && vmap['result'] == 'CN') res = 'error';

        if (res != 'error') res = (res.toUpperCase() == 'Y' && roFlag?.toUpperCase() == 'N') ? 'edit' : 'view';
      }
    } catch (e) {
      debugPrint('getUserEntityEditAccess Exception >>>>>>>>>>>$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    debugPrint('getUserEntityEditAccess >>>>>>>$res');
    return res;
  }
}
