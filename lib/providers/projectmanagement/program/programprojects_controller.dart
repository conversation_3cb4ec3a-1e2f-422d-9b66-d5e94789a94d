import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../models/program/program_projects_data.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class ProgramProjectsController extends GetxController {
  var labelsloading = false.obs;
  var isLoading = false.obs;
  var programProjectsList = <ProgramProjectsData>[].obs;

  getlabels(LabelController talabel) async {
    isLoading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_PROGRAMS_PROJECTS', 'program_projects');
    } catch (e) {
      debugPrint('$e');
    }
    isLoading.value = false;
  }

  Future loadProgramProjectsData(int? ptogramId) async {
    debugPrint('------------loadProgramProjectsData--------$ptogramId');
    isLoading(true);
    List<ProgramProjectsData> projRec;
    programProjectsList.clear();
    Map<String, dynamic>? resMap;

    //var username = SharedPrefUtils.readPrefStr(ConstHelper.userNamevar);

    Map<String, dynamic> dynamicMap = {
      'a': ['program_id', 'EXACT', ptogramId.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(programprojectsurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            projRec = tagObjsJson.map((tagJson) => ProgramProjectsData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${projRec.length}');

            programProjectsList.addAll(projRec);
          }
        } else {
          programProjectsList.clear();
        }
      } else {
        programProjectsList.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    isLoading(false);
  }
}
