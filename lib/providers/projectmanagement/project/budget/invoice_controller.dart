import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/budget/budget.dart';
import 'package:tangoworkplace/models/project/budget/budgetoverview.dart';
import 'package:tangoworkplace/models/project/budget/invhdr.dart';
import 'package:tangoworkplace/models/project/budget/pohdr.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

class InvoiceController extends GetxController {
  RxList<InvHdr> invdata = <InvHdr>[].obs;
  var ishdrloading = false.obs;
  var tblflag = false.obs;
  TextEditingController invoiceSrchTxt = TextEditingController();

  Future<void> loadINVheaderData({int? projectid, String? searchtext}) async {
    debugPrint('------------loadINVheaderData--------$projectid');
    try {
      ishdrloading.value = true;
      List<InvHdr> invh;

      Map<String, dynamic>? resMap;
      String filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['p_user_name', 'EXACT', UserContext.info()!.userName],
        'b': ['p_isContractor', 'EXACT', UserContext.info()!.isContractor],
        'c': ['project_id', 'EXACT', projectid.toString()],
      };
      if (searchtext != null && searchtext != '') {
        dynamicMap['xy'] = ['all_columns', 'CONTAINS', searchtext];
      }
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      //}
      resMap = await CommonServices.fetchDynamicApi(invhdrurl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            invh = tagObjsJson.map((tagJson) => InvHdr.fromJson(tagJson)).toList();
            debugPrint('Inv header Count------ ${invh.length}');
            // invdata.value = (invh.isNotEmpty && invh.length > 0) ? invh : null;
            invdata.value = invh;
          }
        } else {
          invdata.value.clear();
        }
      } else {
        invdata.value.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    ishdrloading.value = false;
  }
}
