import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/project/budget/budget.dart';
import 'package:tangoworkplace/models/project/budget/budgetoverview.dart';
import 'package:tangoworkplace/models/project/budget/pohdr.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

class PoController extends GetxController {
  RxList<PoHdr> podata = <PoHdr>[].obs;
  var ishdrloading = false.obs;
  var tblflag = false.obs;

  loadPOheaderData({int? projectid}) async {
    debugPrint('------------loadPOheaderData--------$projectid');
    try {
      ishdrloading.value = true;
      List<PoHdr> poh;

      Map<String, dynamic>? resMap;
      String filter;
      //if (searchText != null) {
      Map<String, dynamic> dynamicMap = {
        'a': ['p_user_name', 'EXACT', UserContext.info()!.userName],
        'b': ['p_isContractor', 'EXACT', UserContext.info()!.isContractor],
        'c': ['project_id', 'EXACT', projectid.toString()],
      };
      filter = CommonServices.getFilter(dynamicMap);
      debugPrint('filter---------' + filter);
      //}
      resMap = await CommonServices.fetchDynamicApi(pohdrurl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            poh = tagObjsJson.map((tagJson) => PoHdr.fromJson(tagJson)).toList();
            debugPrint('po header Count------ ${poh.length}');
            //podata.value = (poh.isNotEmpty && poh.length > 0) ? poh : null;

            podata.value = poh;
          }
        } else {
          podata.value.clear();
        }
      } else {
        podata.value.clear();
      }
    } catch (e) {
      debugPrint('$e');
    }
    ishdrloading.value = false;
  }
}
