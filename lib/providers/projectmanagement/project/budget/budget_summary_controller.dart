import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../models/project/budget/budget.dart';
import '../../../../models/project/budget/budgettotal.dart';
import '../../../../screens/common/utils/tree/tree_node.dart';
import '../../../../services/common_services.dart';
import '../../../../utils/connections.dart';

class BudgetSummaryController extends GetxController {
  final isLoading = false.obs;
  List<TreeNode<BudgetTotal>> budgetData = [];
  RxList<TreeNode<BudgetTotal>> budgetDataToShow =
      <TreeNode<BudgetTotal>>[].obs;

  void loadSummary({int? entityId}) async {
    isLoading(true);
    if (entityId != null) {
      Map<String, dynamic>? budgetMap;
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityId.toString()],
        'b': ['entity_type', 'EXACT', 'PROJECT'],
      };

      var payloadObj =
          CommonServices.getFilter(dynamicMap, offset: 1, size: 100);
      budgetMap = await CommonServices.fetchDynamicApi(sumbudeturl,
          payloadObj: payloadObj);

      if (budgetMap != null) {
        var status = budgetMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = budgetMap['records'] as List?;
          if (tagObjsJson != null) {
            final data = tagObjsJson
                .map((tagJson) => BudgetTotal.fromJson(tagJson))
                .toList();
            debugPrint('Budget Total Count------ ${data.length}');
            budgetData = data
                .map((node) => TreeNode(node, Container(),
                    isFolder: true, isExpanded: false, isBold: true, level: 0))
                .toList();
          }
        }
      }
    }
    budgetDataToShow.value = budgetData;
    isLoading(false);
  }

  Future<void> loadBudget(
      TreeNode<BudgetTotal> nodeParent, int? entityId) async {
    if (entityId != null) {
      Map<String, dynamic>? budgetMap;
      Map<String, dynamic> dynamicMap = {
        'a': ['entity_id', 'EXACT', entityId.toString()],
        'b': ['entity_type', 'EXACT', 'PROJECT'],
        'c': ['division', 'EXACT', nodeParent.node.division],
      };

      var payloadObj =
          CommonServices.getFilter(dynamicMap, offset: 1, size: 500);
      budgetMap = await CommonServices.fetchDynamicApi(budgeturl,
          payloadObj: payloadObj);

      if (budgetMap != null) {
        var status = budgetMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = budgetMap['records'] as List?;
          if (tagObjsJson != null) {
            final data = tagObjsJson
                .map((tagJson) => Budget.fromJson(tagJson))
                .toList()
                .map(
                  (budgetItem) => BudgetTotal(
                    division: budgetItem.division,
                    entityId: budgetItem.entityId,
                    entityType: budgetItem.entityType,
                    initialBudget: budgetItem.initialBudget?.toDouble(),
                    approvedBudget: budgetItem.approvedBudget?.toDouble(),
                    commitment: budgetItem.commitment?.toDouble(),
                    changeOrder: budgetItem.changeOrder,
                    actualCost: budgetItem.actualCost?.toDouble(),
                    estimatedCost: budgetItem.estimatedCost?.toDouble(),
                    category: budgetItem.taskType,
                  ),
                ).toList();

            debugPrint('Budget for $entityId Count------ ${data.length}');

            List<BudgetTotal> parentsNodes = [];

            for (var budgetTotal in data) {
              if(!parentsNodes.map((e) => e.category).contains(budgetTotal.category)) {
                final categoryBudget = data.where((element) => element.category == budgetTotal.category).toList();
                var budgetData = BudgetTotal(
                  division: categoryBudget.first.division,
                  entityId: categoryBudget.first.entityId,
                  entityType: categoryBudget.first.entityType,
                  initialBudget: 0,
                  approvedBudget:  0,
                  commitment: 0,
                  changeOrder: categoryBudget.first.changeOrder,
                  actualCost: 0,
                  estimatedCost: 0,
                  category: categoryBudget.first.category,
                );
                for(var budgetItem in categoryBudget) {
                  budgetData.initialBudget = (budgetData.initialBudget ?? 0) + (budgetItem.initialBudget ?? 0);
                  budgetData.approvedBudget = (budgetData.approvedBudget ?? 0) + (budgetItem.approvedBudget ?? 0);
                  budgetData.commitment = (budgetData.commitment ?? 0) + (budgetItem.commitment ?? 0);
                  budgetData.actualCost = (budgetData.actualCost ?? 0) + (budgetItem.actualCost ?? 0);
                  budgetData.estimatedCost = (budgetData.estimatedCost ?? 0) + (budgetItem.estimatedCost ?? 0);
                }
                parentsNodes.add(budgetData);
              }
            }

            final expandedValues = parentsNodes.map((node) {
              final onlyZero = isZero(node);
              final children = data
                  .where((element) => element.category == node.category && (onlyZero || !isZero(element)))
                  .map((child) => TreeNode(child, Container(),
                      isFolder: false,
                      isExpanded: false,
                      isBold: false,
                      level: 2))
                  .toList();
              return TreeNode(node, Container(),
                  isFolder: true,
                  isExpanded: false,
                  isBold: false,
                  children: children,
                  level: 1);
            }).toList();

            final index = budgetData.indexWhere(
                (TreeNode<BudgetTotal> element) =>
                    element.node == nodeParent.node);
            budgetData[index].children = expandedValues;
          }
        }
      }
    }
  }

  bool isZero(BudgetTotal budget) {
    return (budget.initialBudget == null || budget.initialBudget == 0) 
      && (budget.approvedBudget == null || budget.approvedBudget == 0)
      && (budget.commitment == null || budget.commitment == 0)
      &&  (budget.actualCost == null || budget.actualCost == 0)
      &&  (budget.estimatedCost == null || budget.estimatedCost == 0);
  }

  void expandNode(TreeNode<BudgetTotal> node, int? mainEntityId) async {
    if (node.isFolder) {
      final index = budgetData.indexWhere((TreeNode<BudgetTotal> element) =>
          element.node == node.node && element.level == node.level);
      final indexShow = budgetDataToShow.indexWhere(
          (TreeNode<BudgetTotal> element) =>
              element.node == node.node && element.level == node.level);
      if (node.isExpanded) {
        final nextIndex = budgetDataToShow
            .mapIndexed((index, element) => {index: element})
            .whereIndexed((index, element) =>
                (element.entries.first.value.level ?? 0) <= (node.level ?? 0) &&
                index > indexShow)
            .firstOrNull
            ?.entries
            .first
            .key;

        final lastIndex = nextIndex ?? budgetDataToShow.length;
        budgetDataToShow.removeRange(indexShow + 1, lastIndex);
      } else {
        if (budgetData[index].children == null) {
          budgetData[indexShow].isRefreshing = true;
          budgetDataToShow.refresh();

          await loadBudget(node, mainEntityId);

          budgetData[indexShow].isRefreshing = false;
          budgetDataToShow.refresh();
        }
        final children = budgetData[index].children;
        if (children != null) {
          for (var element in children) {
            element.isExpanded = false;
          }
          budgetDataToShow.insertAll(
              indexShow + 1, node.children as List<TreeNode<BudgetTotal>>);
        }
      }
      budgetDataToShow[indexShow].isExpanded = !node.isExpanded;
      budgetDataToShow.refresh();
    }
  }
}