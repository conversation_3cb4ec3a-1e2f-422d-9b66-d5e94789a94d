import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

class ProjectGeneralController extends GetxController {
  var labelsloading = false.obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_PROJECTS_GENERAL', 'project_general');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }
}
