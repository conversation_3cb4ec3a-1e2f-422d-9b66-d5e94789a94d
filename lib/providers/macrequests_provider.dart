import 'package:flutter/material.dart';
import '../models/building.dart';
import '../models/floor.dart';
import '../models/macrequests.dart';
import '../models/property.dart';
import '../models/space.dart';
import '../services/macrequest_services.dart';

class MacRequestProvider with ChangeNotifier {
  MacRequestProvider();

  List<MacRequest?>? macRequestsList;
  List<Property>? propertyLov;
  bool loading = false;
  List<Building>? buildingLov;
  List<Floor>? floorLov;
  List<Space>? spaceLov;

  getMacRequestsData(context) async {
    loading = true;
    macRequestsList = (await fetchMacRequests(context));

    print(macRequestsList);
    loading = false;

    notifyListeners();
  }

  Future<void> addMacRequests(MacRequest macObj) async {
    try {
      MacRequest? newObj = await addMacRequestImpl(macObj);

      macRequestsList!.insert(0, newObj); // at the start of the list
      notifyListeners();
    } catch (error) {
      print(error);
      rethrow;
    }
  }

  MacRequest? findById(int id) {
    return macRequestsList!.firstWhere((macobj) => macobj!.requestId == id);
  }

  Future<List<Property>?> getPropertyLov(context) async {
    propertyLov = (await getPropertyLovObj(context));
    notifyListeners();

    return propertyLov;
  }

  List<Building>? getBuildingByProperty(int? propertyId) {
    Property p = propertyLov!.firstWhere((item) => item.propertyId == propertyId);
    var lov = p.buildings!;
    buildingLov = List<Building>.from(lov);

    notifyListeners();

    return buildingLov;
  }

  List<Floor>? getFloorByBuilding(int? buildingId) {
    floorLov = buildingLov!.firstWhere((item) => item.buildingId == buildingId).floors;

    notifyListeners();

    return floorLov;
  }

  Future<List<Space>?> getSpace(context, int? buildingId, int? floorId, String startdate, String enddate) async {
    spaceLov = (await getSpaceLovObj(context, buildingId, floorId, startdate, enddate));

    print(spaceLov);
    notifyListeners();

    return spaceLov;
  }
}
