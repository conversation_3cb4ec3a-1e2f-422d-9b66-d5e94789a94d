import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../models/location/mac/macrequestsearch.dart';

class MacRequestController extends GetxController {
  var searchCtrl = TextEditingController();
  RxList<MacRequestSearchRec> macrequestlist = <MacRequestSearchRec>[].obs;
  var ismacsearchlistloading = false.obs;

  Future loadMacRequestSearchData() async {
    debugPrint('------------loadMacRequestData--------');
    ismacsearchlistloading(true);
    List<MacRequestSearchRec> rec;
    macrequestlist.clear();
    Map<String, dynamic>? resMap;

    var user = await CommonUtils.getUserdata();

    Map<String, dynamic> dynamicMap = {
      'a': ['p_loggedUser', 'EXACT', user.userName ?? ''],
      'b': ['p_myMacFlag', 'EXACT', 'N'],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(macrequestsearchurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            rec = tagObjsJson.map((tagJson) => MacRequestSearchRec.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${rec.length}');

            macrequestlist.addAll(rec);
          }
        } else {
          macrequestlist.clear();
        }
      } else {
        macrequestlist.clear();
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      ismacsearchlistloading(false);
    }
  }
}
