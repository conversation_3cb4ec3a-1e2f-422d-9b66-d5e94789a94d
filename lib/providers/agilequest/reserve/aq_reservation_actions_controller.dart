import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/utils/aq_reservation_cancel_validator.dart';
import 'package:tangoworkplace/providers/agilequest/reserve/utils/aq_reservation_extension.dart';
import '../../../models/agilequest/date/aq_date.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/aq_reserve_const.dart';
import '../../../models/agilequest/types/aq_reserve_states.dart';
import '../../../models/agilequest/user/aq_user_view_data.dart';
import '../../../services/agilequest/aq_api_client.dart';
import '../../../services/agilequest/backendData/requests/reserve/aq_reservation_action_request.dart';
import '../../../services/agilequest/backendData/requests/reserve/aq_reservation_dategap_request.dart';
import '../aqhome_controller.dart';
import '../user/aq_user_extensions.dart';

class AqReservationActionsController extends GetxController {
  final List<AqUserViewData> _delegateUsers = [];
  AqHomeController aqHomeCtrl = Get.find<AqHomeController>();
  Rx<bool> isLoading = false.obs;
  VoidCallback _actionPerformed = () {};

  Future<void> initialSetup(VoidCallback actionPerformed) async {
    _delegateUsers.clear();
    _actionPerformed = actionPerformed;
    final currentUserId = aqHomeCtrl.aqUserData.value.sysidApplicationUser ?? 0;
    final delegates = await AqApiClient.delegatesUsers(currentUserId);
    if (delegates.views != null && delegates.views?.isNotEmpty == true) {
      _delegateUsers.addAll(delegates.views ?? []);
    } else {
      return;
    }
  }

  bool isCancelBtn(AqReserveViewData rv) {
    var validator = ReservationCancelValidator(rv, aqHomeCtrl.aqUserData.value);
    // debugPrint('${validator.canShowCancelSimple()}');
    // debugPrint('canShowCancelRecurrent>>>>>>>>>>>${validator.canShowCancelRecurrent()}');
    // debugPrint('isRecurringNotException>>>>>>${ReservationExtension.re.isRecurringNotException(rv)}');
    // debugPrint('canShowCancelLTR>>>>>>>>>>${validator.canShowCancelLTR()}');
    // debugPrint('isActionWithReservationEnabled>>>>>>>${isActionWithReservationEnabled(rv)}');
    return (validator.canShowCancelSimple() ||
            (validator.canShowCancelRecurrent() &&
                ReservationExtension.re.isRecurringNotException(rv)) ||
            validator.canShowCancelLTR()) &&
        isActionWithReservationEnabled(rv);
  }

  bool isDeleteBtn(AqReserveViewData rv) {
    return rv.sysidPurposeType == AqReservationPurposeType.draft.id;
  }

  bool isReservationPartOfEvent(AqReserveViewData rv) =>
      rv.sysidEventType == EventType.MULTIPOINT.type;

  bool isActionWithReservationEnabled(AqReserveViewData rv) {
    bool res = (rv.sysidOwner ==
            aqHomeCtrl.aqUserData.value.sysidApplicationUser ||
        rv.sysidCreator == aqHomeCtrl.aqUserData.value.sysidApplicationUser ||
        (AqUserExtension.au.canManageAnotherUsersRequests() == true &&
            ReservationExtension.re.isRequest(rv.sysidReservationState)) ||
        (AqUserExtension.au.canManageAnotherUsersReservations() == true &&
            !ReservationExtension.re.isRequest(rv.sysidReservationState)) ||
        _delegateUsers.map((user) => user.sysidUser).contains(rv.sysidOwner));
    return res;
  }

  bool isCheckInBtn(AqReserveViewData rv) {
    var checkInStatus = rv.checkInStatus ?? ReservationCheckInStatus.NONE.type;

    return rv.sysidReservationState == AqReserveStates.AWAITING_CHECKIN &&
        (checkInStatus == ReservationCheckInStatus.CHECK_IN.type ||
            checkInStatus == ReservationCheckInStatus.CHECK_IN_NO_ROLL.type) &&
        (rv.discriminator == ReserveCategoryType.ROOM.name) &&
        isActionWithReservationEnabled(rv);
  }

  bool isCheckOutBtn(AqReserveViewData rv) {
    var checkInStatus = rv.checkInStatus ?? ReservationCheckInStatus.NONE.type;
    return rv.sysidReservationState == AqReserveStates.ACTIVE_PENDING &&
        (checkInStatus == ReservationCheckInStatus.CHECK_OUT.type) &&
        (rv.discriminator == ReserveCategoryType.ROOM.name) &&
        isActionWithReservationEnabled(rv);
  }

  bool isCopyBtn(AqReserveViewData rv) {
    return (AqReserveStates.completed
            .any((e) => e == rv.sysidReservationState)) &&
        !(isReservationPartOfEvent(rv)) &&
        isActionWithReservationEnabled(rv);
  }

  bool isEndBtn(AqReserveViewData rv) {
    var checkInStatus = rv.checkInStatus ?? ReservationCheckInStatus.NONE.type;
    var reservationTimeStarted = rv.aqStartTime?.toDate() != null
        ? (DateTime.now().isAfter(rv.aqStartTime!.toDate()))
        : false;
    bool isEndBtn = false;
    isEndBtn = (rv.sysidReservationState == AqReserveStates.ACTIVE_PENDING &&
        (checkInStatus == ReservationCheckInStatus.NONE.type) &&
        !(rv.reqCheckIn ?? false) &&
        isActionWithReservationEnabled(rv) &&
        reservationTimeStarted);
    return isEndBtn;
  }

  Future<void> callAction(AqReservationAction ara) async {
    isLoading(true);
    var aral = <AqReservationAction>[];
    aral.add(ara);
   
    await AqApiClient.resrveAction(AqReservationActionList(changes: aral));
    isLoading(false);
    _actionPerformed();
    Get.back();
  }

  Future<void> changeReservDateRange(
      AqReserveViewData rv, AqDate stopTime, AqDate restartTime) async {
    isLoading(true);
    await AqApiClient.reserveGapCancel(AqReservationDateGapReq(
        stopTime: stopTime,
        reStartTime: restartTime,
        suppressEmail: false,
        sysidReservation: rv.sysidReservation,
        sysidReservationActionType: ReservationActionType.CANCEL.id));
    isLoading(false);
    _actionPerformed();
    Get.back();    
  }

  Future<void> deleteDraftResrevation(AqReserveViewData rv) async {
    isLoading(true);
    await AqApiClient.deleteReserve(rv.confirmationNumber.toString());
    isLoading(false);
    _actionPerformed();
    Get.back();
  }
}
