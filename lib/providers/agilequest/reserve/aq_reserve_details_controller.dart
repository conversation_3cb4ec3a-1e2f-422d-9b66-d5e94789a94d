import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/objects/dates/aq_dates_selection_criteria.dart';
import 'package:tangoworkplace/models/agilequest/reserve/aq_invitee.dart';
import 'package:tangoworkplace/models/agilequest/reserve/aq_reservation.dart';
import 'package:tangoworkplace/models/agilequest/types/aq_reserve_const.dart';
import 'package:tangoworkplace/models/agilequest/types/reserve/aq_reserve_details_view_mode.dart';

import '../../../models/agilequest/date/aq_date.dart';
import '../../../models/agilequest/objects/dates/aq_dates.dart';
import '../../../models/agilequest/reserve/aq_reservation_group.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/resource/aq_resource_data.dart';
import '../../../models/agilequest/resource/aq_resource_images_data.dart';
import '../../../models/agilequest/types/dates/aq_calendar_type.dart';
import '../../../models/agilequest/types/dates/aq_date_selection_mode.dart';
import '../../../services/agilequest/aq_api_client.dart';
import 'package:collection/collection.dart';

import '../../ta_admin/label_controller.dart';

class AqReserveDetailsController extends GetxController {
  final LabelController talabel = Get.find<LabelController>();
  
  Rx<AqResourceData> resourceData = AqResourceData().obs;
  RxList<AqResourceImagesData> imagesData = <AqResourceImagesData>[].obs;
  Rx<bool> isLoading = false.obs;

  // VALUES
  Rx<AqDatesSelectionCriteria?> datesSelected = AqDatesSelectionCriteria(
          calendarType: AqCalendarType.makeAndManageReservation,
          selectionMode: AqDateSelectionMode.standard)
      .obs;
  RxList<AqInvitee> inviteesSelected = <AqInvitee>[].obs;
  Rx<String> reservationNameValue = ''.obs;
  Rx<String> reservationCommentsValue = ''.obs;
  Rx<String> reservationDeliveryValue = ''.obs;
  Rx<int> reservationQuantityValue = 0.obs;
  Rx<bool> isSubmitEnabled = false.obs;

  // VISIBILITY
  Rx<bool> isDeliveryVisible = false.obs;
  Rx<bool> isQuantityVisible = false.obs;

  // INPUT DATA
  AqReservationGroup? event;
  AqReservation? reservation;
  AqDatesSelectionCriteria? _initialDates;

  final reservationNameTextController = TextEditingController();
  final reservationCommentsTextController = TextEditingController();
  final reservationDeliveryTextController = TextEditingController();
  final reservationQuantityTextController = TextEditingController();
  final reservationOwnerTextController = TextEditingController();
  final reservationDatesTextController = TextEditingController();

  Future<void> initialSetup(
      AqReserveViewData reservationView, AqReserveDetailsViewMode mode) async {
    isLoading(true);

    final event =
        await AqApiClient.getEvent(reservationView.confirmationNumber ?? 0);
    this.event = event;

    final reservation = (event.reservations ?? []).firstWhere((element) =>
        element.sysidReservation == reservationView.sysidReservation);

    final category = await AqApiClient.categoryTypes().then((value) =>
        value.categories.firstWhere(
            (element) => element.sysidCategory == reservation.sysidCategory));

    this.reservation = reservation;

    await fetchResourceData(reservation.sysidResource);

    final dateCriteria = mode == AqReserveDetailsViewMode.RECURRING
        ? AqDatesSelectionCriteria(
            calendarType: AqCalendarType.makeAndManageReservation,
            selectionMode: AqDateSelectionMode.standard,
            recurrenceRule: event.recurrenceRule,
            recurringStartAndEnd: AqDates(
                startDateTime: event.aqEventStartTime,
                endDateTime: event.aqEventEndTime),
            dateAndTime: null,
            isAvailableOnlyForSelectedMode: true,
            borderDates: AqDates(
                startDateTime: AqDate.fromDate(DateTime.now(),
                    reservationView.aqStartTime?.tzIdValue ?? ''),
                endDateTime: null),
          )
        : AqDatesSelectionCriteria(
            calendarType: AqCalendarType.makeAndManageReservation,
            selectionMode: AqDateSelectionMode.standard,
            recurrenceRule: null,
            recurringStartAndEnd: null,
            dateAndTime: AqDates(
                startDateTime: reservationView.aqStartTime,
                endDateTime: reservationView.aqEndTime),
            isAvailableOnlyForSelectedMode: true,
            borderDates: AqDates(
                startDateTime: AqDate.fromDate(DateTime.now(),
                    reservationView.aqStartTime?.tzIdValue ?? ''),
                endDateTime: null),
          );

    _initialDates = dateCriteria;
    datesSelected(dateCriteria);

    reservationDatesTextController.text = datesSelected.value?.displayDates(talabel) ?? '';

    final List<AqInvitee> inviteesToManage = [];
    for (final invitee in reservation.invitation?.reservationInvitees ?? []) {
      inviteesToManage.add(invitee);
    }
    inviteesSelected(inviteesToManage);

    reservationNameValue(reservation.name.trim());
    reservationNameTextController.text = reservation.name;

    reservationCommentsValue(reservation.emailComments?.trim() ?? '');
    reservationCommentsTextController.text =
        reservation.emailComments?.trim() ?? '';

    if (reservationView.ownerMiddleInitial != null &&
        reservationView.ownerMiddleInitial?.isNotEmpty == true) {
      final ownerName =
          '${reservationView.ownerFirstName} ${reservationView.ownerMiddleInitial} ${reservationView.ownerLastName}';
      reservationOwnerTextController.text = ownerName;
    } else {
      final ownerName =
          '${reservationView.ownerFirstName} ${reservationView.ownerLastName}';
      reservationOwnerTextController.text = ownerName;
    }

    if (category.discriminator == ReserveCategoryType.POOL ||
        category.discriminator == ReserveCategoryType.SRVC) {
      isDeliveryVisible(true);
      reservationDeliveryValue(reservation.deliveryLocation?.trim() ?? '');
      reservationDeliveryTextController.text =
          reservation.deliveryLocation?.trim() ?? '';

      // TODO: Add check for SERVICE category for PRICING UNIT type
      isQuantityVisible(true);
      reservationQuantityValue(reservation.quantity ?? 0);
      reservationQuantityTextController.text =
          (reservation.quantity ?? 0).toString();
    } else {
      isQuantityVisible(false);
      isDeliveryVisible(false);
    }

    validateForm();
    isLoading(false);
  }

  Future<void> fetchResourceData(int resourceId) async {
    try {
      final aqResourceViewsResponse =
          await AqApiClient.fetchResourceData(resourceId);

      imagesData.value = aqResourceViewsResponse.images ?? [];
    } catch (e) {
      debugPrint('fetchResourceData>>>>>>$e');
      throw Exception("fetchResourceData error");
    }
  }

  void datesUpdated(AqDatesSelectionCriteria updatedDates) {
    datesSelected(updatedDates);
    reservationDatesTextController.text = datesSelected.value?.displayDates(talabel) ?? '';
    validateForm();
  }

  void inviteesUpdated(List<AqInvitee> inviteesChanged) {
    inviteesSelected.clear();
    inviteesSelected(inviteesChanged);
    inviteesSelected.refresh();
    validateForm();
  }

  void deleteInvitee(AqInvitee invitee) {
    inviteesSelected.removeWhere((element) => element == invitee);
    inviteesSelected.refresh();
    validateForm();
  }

  void validateForm() {
    // is anything changed in form

    final isDatesChanged = _initialDates != datesSelected.value;

    final originalInvitees = reservation?.invitation?.reservationInvitees
        ?.map((e) => e.emailAddress);
    final selectedInvitees =
        inviteesSelected.map((element) => element.emailAddress).toList();

    final isInviteesChanged = selectedInvitees.length !=
            (originalInvitees?.length ?? 0) ||
        const UnorderedIterableEquality().equals(
                selectedInvitees, originalInvitees ?? List<String>.empty()) ==
            false;

    final isReservationNameChanged =
        (reservation?.name.trim() ?? '') != reservationNameValue.value.trim();

    final isCommentChanged = (reservation?.emailComments?.trim() ?? '') !=
        reservationCommentsValue.value.trim();

    final isReservationDeliveryChanged =
        (reservation?.deliveryLocation?.trim() ?? '') !=
            reservationDeliveryValue.value.trim();

    final isQuantityChanged =
        (reservation?.quantity ?? 0) != reservationQuantityValue.value;

    final isChanged = isInviteesChanged ||
        isDatesChanged ||
        isReservationNameChanged ||
        isReservationDeliveryChanged ||
        isQuantityChanged ||
        isCommentChanged;

    // CHECK VALIDATION

    final isReservationNameValid = reservationNameValue.value.trim().isNotEmpty;

    final isQuantityValid = reservationQuantityValue.value > 0;

    final isValid = isReservationNameValid && isQuantityValid;
    isSubmitEnabled(isValid && isChanged);
  }

  void reservationNameChanged(String value) {
    reservationNameValue(value.trim());
    validateForm();
  }

  void reservationCommentsChanged(String value) {
    reservationCommentsValue(value.trim());
    validateForm();
  }

  void reservationDeliveryChanged(String value) {
    reservationDeliveryChanged(value.trim());
    validateForm();
  }

  void reservationQuantityChanged(String value) {
    reservationQuantityValue(int.parse(value));
    validateForm();
  }
}
