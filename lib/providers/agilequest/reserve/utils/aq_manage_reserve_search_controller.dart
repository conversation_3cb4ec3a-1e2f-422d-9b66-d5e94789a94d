import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/models/agilequest/location/aq_location_data.dart';
import 'package:tangoworkplace/models/agilequest/resource/aq_resource_category.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/services/agilequest/aq_api_client.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/location/aq_search_locations_request.dart';

import '../../../../models/agilequest/date/aq_date.dart';
import '../../../../models/agilequest/types/aq_reserve_const.dart';
import '../../aqhome_controller.dart';

class AqManageReserveSearchController extends GetxController {
  AqHomeController aqHomeCtrl = Get.find<AqHomeController>();
  Rx<AqResourceCategory> categorySelected = AqResourceCategory.all().obs;
  Rx<AqDate> startDateSelected = AqDate.fromDate(DateTime.now(), DateTime.now().timeZoneName).obs;
  Rx<AqDate> endDateSelected = AqDate.fromDate(DateTime.now(), DateTime.now().timeZoneName).endOfDay().obs;
  Rx<AqLocationData> selectedLocation = AqLocationData.all().obs;

  var startDateController = TextEditingController();
  var endDateController = TextEditingController();
  var locationController = TextEditingController();

  Rx<bool> isLoading = false.obs;
  RxList<AqResourceCategory> availableCategories = <AqResourceCategory>[].obs;
  List<AqResourceCategory> allCategories = <AqResourceCategory>[];

  Future<void> locationChanged(AqLocationData? location, {bool isInitialChange = false}) async {
    isLoading(true);
    AqLocationData locationToChange;
    if (location?.sysidLocatorNode == selectedLocation.value.sysidLocatorNode && !isInitialChange) {
      locationToChange = AqLocationData.all();
    } else if (location != null) {
      locationToChange = location;
    } else {
      locationToChange = AqLocationData.all();
    }
    locationController.text = locationToChange.name;

    selectedLocation(locationToChange);

    availableCategories.clear();
    if (selectedLocation.value.sysidLocatorNode == -1) {
      availableCategories.addAll(allCategories);
    } else {
      final body = AqSearchLocationsRequest(
        sysidApplicationUser: aqHomeCtrl.aqUserData.value.sysidApplicationUser ?? -1,
        sysidAllocRelationshipTypes: [2, 3],
        sysidLocatorNode: selectedLocation.value.sysidParentNode ?? selectedLocation.value.sysidLocatorNode,
        locationDataTypeValue: AqLocationDataType.nodeLocationsActiveAllocated.id,
      );

      final locationResponse = await AqApiClient.searchLocations(body);
      final filteredCategories = allCategories
          .where(
            (category) => locationResponse.sysidCategories?.contains(category.sysidCategory) == true,
          )
          .toList();
      filteredCategories.add(AqResourceCategory.all());
      availableCategories.addAll(filteredCategories);
    }

    isLoading(false);
  }

  void categoryChanged(int id) {
    final category = allCategories.firstWhereOrNull((category) => category.sysidCategory == id);
    categorySelected(category ?? AqResourceCategory.all());
  }

  Future<void> initialLoad(
    AqDate initialStartDate,
    AqDate initialEndDate, {
    AqResourceCategory? initialCategory,
    AqLocationData? initialLocation,
  }) async {
    isLoading(true);

    availableCategories.clear();
    allCategories.clear();

    final categories = await AqApiClient.categoryTypes();
    final listOfFetchedCategories = categories.categories;
    listOfFetchedCategories.add(AqResourceCategory.all());

    availableCategories.addAll(listOfFetchedCategories);
    allCategories.addAll(listOfFetchedCategories);

    categorySelected(initialCategory ?? AqResourceCategory.all());

    await locationChanged(initialLocation, isInitialChange: true);

    startDateController.text = initialStartDate.displayString();
    startDateSelected(initialStartDate);

    endDateController.text = initialEndDate.displayString();
    endDateSelected(initialEndDate);

    isLoading(false);
  }

  void updateStartTime(String date) {
    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = AqDate.fromDate(dateConverted, DateTime.now().timeZoneName);
    final endDate = endDateSelected.value.toDate();
    final selectedDate = aqDate.toDate();
    final differenceEndDate = endDate.difference(startDateSelected.value.toDate());

    startDateController.text = aqDate.displayString();
    startDateSelected(aqDate);

    if (selectedDate.isAfter(endDate)) {
      final newEndDate = selectedDate.add(differenceEndDate);
      final newAqEndDate = AqDate.fromDate(newEndDate, DateTime.now().timeZoneName);
      endDateController.text = newAqEndDate.displayString();
      endDateSelected(aqDate);
    }
  }

  void updateEndTime(String date) {
    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = AqDate.fromDate(dateConverted, DateTime.now().timeZoneName).endOfDay();

    if (aqDate.toDate().difference(startDateSelected.value.toDate()).isNegative) {
      Get.defaultDialog(title: "Error");
    } else {
      endDateController.text = aqDate.displayString();
      endDateSelected(aqDate);
    }
  }

  @override
  void dispose() {
    startDateController.dispose();
    endDateController.dispose();
    locationController.dispose();
    super.dispose();
  }
}
