
import '../../../../models/agilequest/auth/aq_user_data.dart';
import '../../../../models/agilequest/types/aq_reserve_states.dart';
import '../../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../user/aq_user_extensions.dart';
import 'aq_reservation_extension.dart';

class ReservationCancelValidator {
  AqReserveViewData? rv;
  AqUserData? user;
  ReservationCancelValidator(this.rv, this.user);

  bool canShowCancelSimple() {
    if (ReservationExtension.re.isContinuous(rv)) {
      return false;
    }

    if ((ReservationExtension.re.isContinuous(rv) || ReservationExtension.re.isNoEndDate(rv)) &&
        (ReservationExtension.re.isCheckedInOrActiveStarted(rv) || ReservationExtension.re.isNotCheckedInStarted(rv))) {
      return false;
    }
    if (AqReserveStates.INITIAL == rv?.sysidReservationState) {
      return false;
    }

    if (AqReserveStates.completed.any((e) => e == rv?.sysidReservationState)) {
      return false;
    }

    if (ReservationExtension.re.isChild(rv)) {
      return false;
    }
    if (ReservationExtension.re.isNotCheckedInStarted(rv) || ReservationExtension.re.isCheckedInOrActiveStarted(rv)) {
      return false;
    }
    return true;
  }

  bool canShowCancelRecurrent() {
    if (AqReserveStates.INITIAL == rv?.sysidReservationState) {
      return false;
    }

    if (AqReserveStates.completed.any((e) => e == rv?.sysidReservationState)) {
      return false;
    }
    if (ReservationExtension.re.isNotCheckedInStarted(rv) || ReservationExtension.re.isCheckedInOrActiveStarted(rv)) {
      return false;
    }

    return !ReservationExtension.re.isChild(rv) && ReservationExtension.re.isFromRecurringSeries(rv);
  }

  bool canShowCancelLTR() {
    if (AqReserveStates.INITIAL == rv?.sysidReservationState) {
      return false;
    }

    if (AqReserveStates.completed.any((e) => e == rv?.sysidReservationState)) {
      return false;
    }

    return !ReservationExtension.re.isChild(rv) &&
        ReservationExtension.re.isContinuous(rv) &&
        (!ReservationExtension.re.isNoEndDate(rv) || AqUserExtension.au.canCancelNoEndReservations());
  }
}
