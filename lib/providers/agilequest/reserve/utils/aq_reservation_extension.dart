import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';

import '../../../../models/agilequest/types/aq_reserve_const.dart';
import '../../../../models/agilequest/types/aq_reserve_states.dart';
import '../../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../../utils/agilquest/aq_resource_data.dart';
import '../../../ta_admin/label_controller.dart';

class ReservationExtension {
  ReservationExtension._();
  LabelController talabel = Get.find<LabelController>();
  static final ReservationExtension re = ReservationExtension._();
  bool isChild(AqReserveViewData? rv) => rv?.sysidParentReservation != null;

  bool isFromRecurringSeries(AqReserveViewData? rv) =>
      rv?.sysidOrgDurationType == ReservationOrgDurationType.RECURRING.type;

  bool isRecurringNotException(AqReserveViewData? rv) =>
      rv?.sysidDurationType == ReservationOrgDurationType.RECURRING.type;

  bool isRequest(int? state) {
    return AqReserveStates.pendingRequest.contains(state);
  }

  bool isContinuous(AqReserveViewData? rv) {
    return rv?.sysidDurationType ==
            ReservationOrgDurationType.CONTINUOUS.type ||
        isNoEndDate(rv);
  }

  bool isNoEndDate(AqReserveViewData? rv) {
    return rv?.sysidDurationType == ReservationOrgDurationType.OPEN_ENDED.type;
  }

  bool isCheckedInOrActiveStarted(AqReserveViewData? rv) {
    AqDate? aq = rv?.aqStartTime;

    return rv?.aqStartTime != null
        ? ((rv?.sysidReservationState == AqReserveStates.ACTIVE_PENDING) &&
            (aq!.toDate().compareTo(DateTime.now()) < 0))
        : false;
  }

  bool isNotCheckedInStarted(AqReserveViewData? rv) {
    AqDate? aq = rv?.aqStartTime;

    return rv?.aqStartTime != null
        ? ((rv?.sysidReservationState == AqReserveStates.AWAITING_CHECKIN) &&
            (aq!.toDate().compareTo(DateTime.now()) < 0))
        : false;
  }

  bool isStarted(AqReserveViewData? rv) {
    DateTime? aqs = rv?.aqStartTime?.localDate();
    if (aqs != null) {
      DateTime aqst = DateUtils.dateOnly(aqs);
      //DateTime aqst = DateTime(aqs?.year ?? 0, aqs?.month ?? 0, aqs?.dayOfMonth ?? 0);
      DateTime now = DateUtils.dateOnly(DateTime.now());
      return (aqst.isBefore(now) || aqst.isAtSameMomentAs(now));
    }
    return false;
  }

  Future<String> statusDescription(AqReserveViewData? rv) async {
    String statusDesc = 'Unknown State';
    bool reqCheckIn = rv?.reqCheckIn ?? false;
    var reservationTimeStarted = false;
    //debugPrint('starttime>>>>>>>>>>>${rv?.aqStartTime?.toDate()}');
    //debugPrint('current time>>>>>>>>>>>${DateTime.now()}');

    reservationTimeStarted = rv?.aqStartTime?.toDate() != null
        ? (DateTime.now().isAfter(rv!.aqStartTime!.toDate()))
        : false;

    //debugPrint('sysidReservationState>>>>>>>>>>>${rv?.sysidReservationState}');
    switch (rv?.sysidReservationState) {
      case 1: //AqReserveStates.AWAITING_DECISION
        statusDesc = await talabel.getAqLabel(
            key: 'aq.mobile.navigation.menu.manageReservations',
            defaultTxt: AqText.state_awaiting_decision);
        break;
      case 2: //AqReserveStates.AWAITING_CHECKIN
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.awaitingCheckIn',
            defaultTxt: AqText.state_awaiting_check_in);
        break;
      case 3:
        {
          //AqReserveStates.ACTIVE_PENDING
          if (rv?.checkInStatus == ReservationCheckInStatus.CHECK_OUT.type ||
              rv?.checkInStatus == ReservationCheckInStatus.NONE.type) {
            statusDesc = await talabel.getAqLabel(
                key: 'aq.reservations.status.checkedIn',
                defaultTxt: AqText.state_checked_in);
            break;
          } else if (reservationTimeStarted) {
            statusDesc = await talabel.getAqLabel(
                key: 'aq.reservations.status.active',
                defaultTxt: AqText.state_active);
            break;
          } else {
            statusDesc = await talabel.getAqLabel(
                key: 'aq.reservations.status.pending',
                defaultTxt: AqText.state_pending);
            break;
          }
        }
      case 4: //AqReserveStates.DENIED
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.denied',
            defaultTxt: AqText.state_request_denied);
        break;
      case 5: //AqReserveStates.EXPIRED
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.expired',
            defaultTxt: AqText.state_expired);
        break;
      case 6: //AqReserveStates.BUMPED
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.bumped',
            defaultTxt: AqText.state_bumped);
        break;
      case 7: //AqReserveStates.CANCELED
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.canceled',
            defaultTxt: AqText.state_canceled);
        break;
      case 8:
        if (reqCheckIn) {
          //AqReserveStates.CHECKEDOUT
          statusDesc = await talabel.getAqLabel(
              key: 'aq.reservations.status.checkedOut',
              defaultTxt: 'Reservation is Checked Out');
          break;
        } else {
          statusDesc = await talabel.getAqLabel(
              key: 'aq.reservations.status.ended',
              defaultTxt: 'Reservation Ended');
          break;
        }
      case 9: //AqReserveStates.CANCELED_WITHIN_LEAD_TIME
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.canceledWithinLead',
            defaultTxt: AqText.state_canceled_within_lead);
        break;
      case 10: //AqReserveStates.AWAITING_PARENT_DECISION_RESERVE
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.multiAsset.reserve.awaitingDecision',
            defaultTxt: AqText.state_multiasset_reserve_awaiting_decision);
        break;
      case 11: //AqReserveStates.AWAITING_PARENT_DECISION_REQUEST
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.multiAsset.request.awaitingDecision',
            defaultTxt: AqText.state_multiasset_reserve_awaiting_decision);
        break;
      case 0: //AqReserveStates.INITIAL
        statusDesc = await talabel.getAqLabel(
            key: 'aq.reservations.status.draft',
            defaultTxt: AqText.state_draft_reservation);
        break;
      default:
        statusDesc = 'Unknown State';
        break;
    }
    //debugPrint('statusDesc>>>>>>>>>>>$statusDesc');
    return statusDesc;
  }
}
