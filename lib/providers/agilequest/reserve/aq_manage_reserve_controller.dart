import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date_range.dart';
import 'package:tangoworkplace/models/agilequest/location/aq_location_data.dart';
import 'package:tangoworkplace/models/agilequest/resource/aq_resource_category.dart';
import 'package:tangoworkplace/models/agilequest/types/aq_reserve_const.dart';
import 'package:tangoworkplace/models/agilequest/types/aq_reserve_states.dart';
import 'package:tangoworkplace/models/agilequest/reserve/aq_reserve_view_data.dart';
import 'package:tangoworkplace/models/agilequest/user/aq_user_view_data.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/services/agilequest/aq_api_client.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/reserve/aq_reserve_view_criteria_request.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/agilequest/date/aq_date.dart';
import '../../../models/agilequest/location/aq_resource_location.dart';
import '../../../services/agilequest/backendData/requests/reserve/aq_reservation_action_request.dart';
import '../../../services/agilequest/backendData/requests/reserve/aq_reservation_dategap_request.dart';
import '../../../utils/agilquest/aq_resource_data.dart';
import '../../ta_admin/label_controller.dart';
import '../aqhome_controller.dart';
import '../user/aq_user_extensions.dart';
import 'utils/aq_reservation_cancel_validator.dart';
import 'utils/aq_reservation_extension.dart';

class AqManageReserveController extends GetxController {
  LabelController talabel = Get.find<LabelController>();
  Rx<bool> isLoading = false.obs;
  var cancelBtnTxt = 'Cancel'.obs;
  var checkinBtnTxt = 'Check In'.obs;
  var tabtype = 0.obs;

  RxList<AqReserveViewData> reserveData = <AqReserveViewData>[].obs;
  RxMap<String, List<AqReserveViewData>> rsrvDateData = <String, List<AqReserveViewData>>{}.obs;
  AqHomeController aqHomeCtrl = Get.find<AqHomeController>();

  final List<AqUserViewData> _delegateUsers = [];
  var aqReserveViewCriteriaRequest = AqReserveViewCriteriaRequest().obs;

  AqDate? startDateSelected;
  AqDate? endDateSelected;
  AqLocationData? locationSelected;
  AqResourceCategory? categorySelected;
  int filterSelected = AqManageReservePageType.active.id;

  bool isReservationPartOfEvent(AqReserveViewData rv) => rv.sysidEventType == EventType.MULTIPOINT.type;

  bool isReserveRecur(AqReserveViewData rv) => rv.sysidDurationType == ReservationOrgDurationType.RECURRING.type;

  bool isCheckInBtn(AqReserveViewData rv) {
    var checkInStatus = rv.checkInStatus ?? ReservationCheckInStatus.NONE.type;

    return rv.sysidReservationState == AqReserveStates.AWAITING_CHECKIN &&
        (checkInStatus == ReservationCheckInStatus.CHECK_IN.type ||
            checkInStatus == ReservationCheckInStatus.CHECK_IN_NO_ROLL.type) &&
        (rv.discriminator == ReserveCategoryType.ROOM.name) &&
        isActionWithReservationEnabled(rv);
  }

  bool isCheckOutBtn(AqReserveViewData rv) {
    var checkInStatus = rv.checkInStatus ?? ReservationCheckInStatus.NONE.type;
    return rv.sysidReservationState == AqReserveStates.ACTIVE_PENDING &&
        (checkInStatus == ReservationCheckInStatus.CHECK_OUT.type) &&
        (rv.discriminator == ReserveCategoryType.ROOM.name) &&
        isActionWithReservationEnabled(rv);
  }

  bool isCopyBtn(AqReserveViewData rv) {
    return (AqReserveStates.completed.any((e) => e == rv.sysidReservationState)) &&
        !(isReservationPartOfEvent(rv)) &&
        isActionWithReservationEnabled(rv);
  }

  bool isEndBtn(AqReserveViewData rv) {
    var checkInStatus = rv.checkInStatus ?? ReservationCheckInStatus.NONE.type;
    var reservationTimeStarted =
        rv.aqStartTime?.toDate() != null ? (DateTime.now().isAfter(rv.aqStartTime!.toDate())) : false;
    bool isEndBtn = false;
    isEndBtn = (rv.sysidReservationState == AqReserveStates.ACTIVE_PENDING &&
        (checkInStatus == ReservationCheckInStatus.NONE.type) &&
        !(rv.reqCheckIn ?? false) &&
        isActionWithReservationEnabled(rv) &&
        reservationTimeStarted); //??
    // false;
    return isEndBtn;
  }

  bool isDeleteBtn() {
    return tabtype.value == AqManageReservePageType.draft.id;
  }

  bool isViewBtn(AqReserveViewData rv) {
    return isReservationPartOfEvent(rv) && isActionWithReservationEnabled(rv);
  }

  bool isCancelBtn(AqReserveViewData rv) {
    var validator = ReservationCancelValidator(rv, aqHomeCtrl.aqUserData.value);
    // debugPrint('${validator.canShowCancelSimple()}');
    // debugPrint('canShowCancelRecurrent>>>>>>>>>>>${validator.canShowCancelRecurrent()}');
    // debugPrint('isRecurringNotException>>>>>>${ReservationExtension.re.isRecurringNotException(rv)}');
    // debugPrint('canShowCancelLTR>>>>>>>>>>${validator.canShowCancelLTR()}');
    // debugPrint('isActionWithReservationEnabled>>>>>>>${isActionWithReservationEnabled(rv)}');
    return (validator.canShowCancelSimple() ||
            (validator.canShowCancelRecurrent() && ReservationExtension.re.isRecurringNotException(rv)) ||
            validator.canShowCancelLTR()) &&
        isActionWithReservationEnabled(rv);
  }

  bool isActionWithReservationEnabled(AqReserveViewData rv) {
    //debugPrint('sysidApplicationUser>>>>>>>${aqHomeCtrl.aqUserData.value.sysidApplicationUser}');
    bool res = (rv.sysidOwner == aqHomeCtrl.aqUserData.value.sysidApplicationUser ||
        rv.sysidCreator == aqHomeCtrl.aqUserData.value.sysidApplicationUser ||
        (AqUserExtension.au.canManageAnotherUsersRequests() == true &&
            ReservationExtension.re.isRequest(rv.sysidReservationState)) ||
        (AqUserExtension.au.canManageAnotherUsersReservations() == true &&
            !ReservationExtension.re.isRequest(rv.sysidReservationState)) ||
        _delegateUsers.map((user) => user.sysidUser).contains(rv.sysidOwner));
    return res;
  }

  bool reserveBtnActions(AqReserveViewData rv) {
    return (isCheckOutBtn(rv) ||
            isCheckInBtn(rv) ||
            isCopyBtn(rv) ||
            isEndBtn(rv) ||
            isCancelBtn(rv) ||
            isViewBtn(rv)) &&
        isActionWithReservationEnabled(rv);
  }

  void filterReservations({AqDate? start, AqDate? end, AqResourceCategory? category, AqLocationData? location}) {
    startDateSelected = start ?? startDateSelected;
    endDateSelected = end ?? endDateSelected;
    if (category?.sysidCategory == -1) {
      categorySelected = null;
    } else {
      categorySelected = category ?? categorySelected;
    }

    if (location?.sysidLocatorNode == -1) {
      locationSelected = null;
    } else {
      locationSelected = location ?? locationSelected;
    }

    fetchReservations(filter: filterSelected);
  }

  Future<void> updatedView() async {
    isLoading(true);
    await setReserveData(aqReserveViewCriteriaRequest.value);
    isLoading(false);
  }

  void _clearFiltersOnTabsSwitch() {
    startDateSelected = null;
    endDateSelected = null;
    locationSelected = null;
    categorySelected = null;
  }

  Future<void> fetchReservations({
    int? filter,
  }) async {
    isLoading(true);

    if (filterSelected != filter) {
      _clearFiltersOnTabsSwitch();
    }

    filterSelected = filter ?? filterSelected;

    Future.delayed(const Duration(seconds: 1), () async {
      try {
        final currentUserId = aqHomeCtrl.aqUserData.value.sysidApplicationUser ?? 0;

        // Owner setup
        List<int> userOwners = [];

        // User owners setup
        if (filter == AqManageReservePageType.delegates.id) {
          _delegateUsers.clear();

          final delegates = await AqApiClient.delegatesUsers(currentUserId);
          if (delegates.views != null && delegates.views?.isNotEmpty == true) {
            _delegateUsers.addAll(delegates.views ?? []);
            userOwners.addAll(delegates.views?.map((userView) => userView.sysidUser ?? 0) ?? []);
          } else {
            return;
          }
        } else {
          userOwners.add(currentUserId);
        }

        // Creator setup
        List<int>? userCreators;
        if (filter != AqManageReservePageType.delegates.id) {
          userCreators = [currentUserId];
        }

        // States setup
        List<int> reserveStates = [
          AqReserveStates.AWAITING_CHECKIN,
          AqReserveStates.ACTIVE_PENDING,
        ];

        if (filter != null) {
          if (filter == AqManageReservePageType.delegates.id) {
            //delegate
            reserveStates = AqReserveStates.delegates;
          } else if (filter == AqManageReservePageType.draft.id) {
            //Draft
            reserveStates = [];
          } else if (filter == AqManageReservePageType.requests.id) {
            //Requests
            reserveStates = AqReserveStates.pendingRequest;
          } else if (filter == AqManageReservePageType.completed.id) {
            //completed
            reserveStates = AqReserveStates.completed;
          }
        }

        //Dates setup
        if (startDateSelected == null) {
          if (filter == AqManageReservePageType.completed.id) {
            startDateSelected =
                AqDate.fromDate(DateTime.now().subtract(const Duration(days: 14)), DateTime.now().timeZoneName)
                    .startOfDay();
          } else {
            startDateSelected = AqDate.fromDate(DateTime.now(), DateTime.now().timeZoneName).startOfDay();
          }
        }

        if (endDateSelected == null) {
          if (filter == AqManageReservePageType.completed.id) {
            endDateSelected = AqDate.fromDate(DateTime.now(), DateTime.now().timeZoneName).endOfDay();
          } else {
            endDateSelected =
                AqDate.fromDate(DateTime.now().add(const Duration(days: 14)), DateTime.now().timeZoneName).endOfDay();
          }
        }

        // Locations setup
        List<int>? selectedLocationId;
        if (locationSelected != null) {
          selectedLocationId = [locationSelected?.sysidLocatorNode ?? 0];
        }

        // Categories setup
        List<int>? selectedCategoriesId;
        if (categorySelected != null) {
          selectedCategoriesId = [categorySelected?.sysidCategory ?? 0];
        }

        //Purpose type setup
        String purposeType = filter == AqManageReservePageType.draft.id ? 'DRAFT' : 'RESERVATION';

        //Parents visibility setup
        bool isOnlyParents = filter != AqManageReservePageType.requests.id;

        final body = AqReserveViewCriteriaRequest(
          startRow: 0,
          maxRows: 100,
          onlyParents: isOnlyParents,
          sortAscending: filter == AqManageReservePageType.completed.id ? false : true,
          purposeType: purposeType,
          sortColumn: 'START_TIME',
          sysidUserOwners: userOwners,
          sysidUserCreators: userCreators,
          reservationStates: reserveStates,
          sysidCategories: selectedCategoriesId,
          sysidLocatorNodes: selectedLocationId,
          usersArgsOred: true,
          dates: [
            AqDateRange(
              startDateSelected ?? AqDate.fromDate(DateTime.now(), DateTime.now().timeZoneName),
              endDateSelected ?? AqDate.fromDate(DateTime.now(), DateTime.now().timeZoneName),
            )
          ],
        );

        await setReserveData(body);
      } catch (e) {
        debugPrint('[AqManageReserveController].fetchReservations >>>>>>> $e');
      } finally {
        isLoading(false);
      }
    });
  }

  String getAddress(AqReserveViewData rv) {
    AqResourceLocation? rvlocs = rv.resourceLocations?.first;
    var resourceName = rv.resourceName ?? '';
    String atTxt = talabel.getAq(key: 'aq.mobile.common.labels.at', defaultTxt: AqText.loc_at)?.tag ?? '';
    var locationName = '';
    if (rv.resourceLocations?.length == 1) {
      locationName = '$atTxt ${rvlocs?.venueName ?? ''}';
    } else {
      locationName =
          talabel.getAq(key: 'aq.mobile.common.values.lowercase.multiple', defaultTxt: AqText.loc_multiple)?.tag ?? '';
    }
    locationName = '$resourceName $locationName';
    return locationName;
  }

  Future<void> setReserveData(AqReserveViewCriteriaRequest body) async {
    try {
      aqReserveViewCriteriaRequest.value = body;
      debugPrint('body>>>>>>>>${jsonEncode(body)}');
      reserveData.clear();
      rsrvDateData.clear();
      final reservationViewsResponse = await AqApiClient.reservationViews(body);
      if (reservationViewsResponse.views.isNotEmpty) {
        var status = '';
        var address = '';

        for (AqReserveViewData element in reservationViewsResponse.views) {
          status = await ReservationExtension.re.statusDescription(element);
          address = getAddress(element);
          element.tempStatus = status;
          element.tempAddress = address;

          //debugPrint('eee>>>>>>${element.aqStartTime?.toDate()}');
          element.aqTempStartTime = element.aqStartTime?.displayString(type: 'fm');
          element.aqStartDateTime = element.aqStartTime?.toDate();
        }

        reservationViewsResponse.views.sort((a, b) {
          return DateTime.parse(DateFormat("yyyy-MM-dd").format(a.aqStartDateTime ?? DateTime.now()))
              .compareTo(DateTime.parse(DateFormat("yyyy-MM-dd").format(b.aqStartDateTime ?? DateTime.now())));
        });

        // rsrvDateData.value = groupBy(reservationViewsResponse.views, (AqReserveViewData obj) {
        //   return obj.aqStartDateTime != null ? DateFormat("yMMMMd").format(obj.aqStartDateTime ?? DateTime.now()) : '';
        // });

        rsrvDateData.value = groupBy(reservationViewsResponse.views, (AqReserveViewData obj) {
          if (obj.aqStartDateTime!.isBefore(DateTime.now())) {
            obj.aqTempStartTime = DateFormat("yMMMMd").format(DateTime.now());
          }
          return obj.aqTempStartTime ?? '';
        });

        reserveData.addAll(reservationViewsResponse.views);
      } else {
        reserveData.clear();
        rsrvDateData.clear();
      }
    } catch (e) {
      debugPrint('setReserveData>>>>>>$e');
      throw Exception("setReserveData error");
    }
  }

  Future<void> callAction(AqReservationAction ara) async {
    var aral = <AqReservationAction>[];
    aral.add(ara);
    isLoading(true);
    await AqApiClient.resrveAction(AqReservationActionList(changes: aral));
    await setReserveData(aqReserveViewCriteriaRequest.value);
    isLoading(false);
  }

  Future<void> changeReservDateRange(AqReserveViewData rv, AqDate stopTime, AqDate restartTime) async {
    isLoading(true);
    await AqApiClient.reserveGapCancel(AqReservationDateGapReq(
        stopTime: stopTime,
        reStartTime: restartTime,
        suppressEmail: false,
        sysidReservation: rv.sysidReservation,
        sysidReservationActionType: ReservationActionType.CANCEL.id));

    await setReserveData(aqReserveViewCriteriaRequest.value);

    isLoading(false);
  }

  Future<void> deleteDraftResrevation(AqReserveViewData rv) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    try {
      await AqApiClient.deleteReserve(rv.confirmationNumber.toString());
      Future.delayed(const Duration(seconds: 1), () async {
        await setReserveData(aqReserveViewCriteriaRequest.value);
      });
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
  }
}
