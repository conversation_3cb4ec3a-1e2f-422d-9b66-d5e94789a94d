
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';

import '../../../models/agilequest/date/aq_date.dart';
import '../../../models/agilequest/reserve/aq_reserve_view_data.dart';
import '../../../models/agilequest/types/dates/aq_date_pattern_type.dart';
import '../../../utils/agilquest/aq_resource_data.dart';
import '../../ta_admin/label_controller.dart';

class AqCancelRangeController extends GetxController {

  LabelController talabel = Get.find<LabelController>();

  Rx<AqDate> startDateTimeForRangeCancelationSelected = AqDate(tzIdValue: '').obs;
  Rx<AqDate> endDateTimeForRangeCancelationSelected = AqDate(tzIdValue: '').obs;
  Rx<AqDate> endDateTimeBorderSelected = AqDate(tzIdValue: '').obs;
  Rx<bool> isSubmitEnabledForCancelRange = true.obs;

    var cancelFromDateCtrl = TextEditingController();
  var cancelToDateCtrl = TextEditingController();
  var cancelFromTimeCtrl = TextEditingController();
  var cancelToTimeCtrl = TextEditingController();

   // CANCEL DATE RANGE
  void setupRangeForCancelation(AqReserveViewData reservation) {
    startDateTimeForRangeCancelationSelected(reservation.aqStartTime);
    cancelFromDateCtrl.text = reservation.aqStartTime?.displayDateString(
            talabel,
            withTimeZone: false,
            pattern: AqDatePatternType.SHORT_DATE) ??
        '';
    cancelFromTimeCtrl.text = reservation.aqStartTime?.displayDateString(
            talabel,
            withTimeZone: false,
            pattern: AqDatePatternType.TIME) ??
        '';
    if (reservation.aqEndTime?.noEndDate == true) {
      final dateForCancelation = reservation.aqStartTime
          ?.copyWith(discriminator: 'AqNoEndDate', noEndDate: true);
      endDateTimeForRangeCancelationSelected(dateForCancelation);
    } else {
      endDateTimeForRangeCancelationSelected(reservation.aqEndTime);
    }
    endDateTimeBorderSelected(reservation.aqEndTime);
    cancelToDateCtrl.text = reservation.aqEndTime?.displayDateString(talabel,
            withTimeZone: false, pattern: AqDatePatternType.SHORT_DATE) ??
        '';
    cancelToTimeCtrl.text = reservation.aqEndTime?.displayDateString(talabel,
            withTimeZone: false, pattern: AqDatePatternType.TIME) ??
        '';
    validateCancelRangeForm();
  }

  void changeStartDateForRangeCancelation(String date) {
    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = startDateTimeForRangeCancelationSelected.value.copyWith(
        year: dateConverted.year,
        month: dateConverted.month,
        dayOfMonth: dateConverted.day);
    final difference = aqDate
        .toDate()
        .difference(endDateTimeForRangeCancelationSelected.value.toDate());

    if (difference.inMinutes > 0 &&
        endDateTimeForRangeCancelationSelected.value.noEndDate == false) {
      showError(
          "Error",
          talabel
              .getAq(
                  key: 'aq.web.calendar.datetime.save.error.endBeforStart',
                  defaultTxt: AqText.errorDateTimeEndBeforeStart)!
              .tag);
    } else {
      cancelFromDateCtrl.text = aqDate.displayDateString(talabel,
          pattern: AqDatePatternType.SHORT_DATE);
      startDateTimeForRangeCancelationSelected(aqDate);
      if (endDateTimeForRangeCancelationSelected.value.noEndDate == true) {
        final dateForCancelation = startDateTimeForRangeCancelationSelected
            .value
            .copyWith(discriminator: 'AqNoEndDate', noEndDate: true);
        endDateTimeForRangeCancelationSelected(dateForCancelation);
      }
      validateCancelRangeForm();
    }
  }

  void changeStartTimeForRangeCancelation(String time) {
    final dateConverted = DateFormat('HH:mm').parse(time);
    final aqDate = startDateTimeForRangeCancelationSelected.value
        .copyWith(hour: dateConverted.hour, minutes: dateConverted.minute);
    final difference = aqDate
        .toDate()
        .difference(endDateTimeForRangeCancelationSelected.value.toDate());

    if (difference.inMinutes > 0 &&
        endDateTimeForRangeCancelationSelected.value.noEndDate == false) {
      showError(
          "Error",
          talabel
              .getAq(
                  key: 'aq.web.calendar.datetime.save.error.endBeforStart',
                  defaultTxt: AqText.errorDateTimeEndBeforeStart)!
              .tag);
    } else {
      cancelFromTimeCtrl.text =
          aqDate.displayDateString(talabel, pattern: AqDatePatternType.TIME);
      startDateTimeForRangeCancelationSelected(aqDate);
      if (endDateTimeForRangeCancelationSelected.value.noEndDate == true) {
        final dateForCancelation = startDateTimeForRangeCancelationSelected
            .value
            .copyWith(discriminator: 'AqNoEndDate', noEndDate: true);
        endDateTimeForRangeCancelationSelected(dateForCancelation);
      }
      validateCancelRangeForm();
    }
  }

  void changeEndDateForRangeCancelation(String date) {
    if (endDateTimeForRangeCancelationSelected.value.noEndDate == true) {
      final dateForCancelation = startDateTimeForRangeCancelationSelected.value
          .copyWith(discriminator: 'AqNoEndDate', noEndDate: true);
      endDateTimeForRangeCancelationSelected(dateForCancelation);
    }

    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = endDateTimeForRangeCancelationSelected.value.copyWith(
        year: dateConverted.year,
        month: dateConverted.month,
        dayOfMonth: dateConverted.day,
        discriminator: 'AqDate',
        noEndDate: false);

    final difference = aqDate
        .toDate()
        .difference(startDateTimeForRangeCancelationSelected.value.toDate());

    if (difference.inMinutes < 0) {
      showError(
          "Error",
          talabel
              .getAq(
                  key: 'aq.web.calendar.datetime.save.error.endBeforStart',
                  defaultTxt: AqText.errorDateTimeEndBeforeStart)!
              .tag);
    } else {
      cancelToDateCtrl.text = aqDate.displayDateString(talabel,
          pattern: AqDatePatternType.SHORT_DATE);
      cancelToTimeCtrl.text =
          aqDate.displayDateString(talabel, pattern: AqDatePatternType.TIME);
      endDateTimeForRangeCancelationSelected(aqDate);
      endDateTimeBorderSelected(aqDate);
      validateCancelRangeForm();
    }
  }

  void changeEndTimeForRangeCancelation(String time) {
    if (endDateTimeForRangeCancelationSelected.value.noEndDate == true) {
      final dateForCancelation = startDateTimeForRangeCancelationSelected.value
          .copyWith(discriminator: 'AqNoEndDate', noEndDate: true);
      endDateTimeForRangeCancelationSelected(dateForCancelation);
    }

    final dateConverted = DateFormat('HH:mm').parse(time);
    final aqDate = endDateTimeForRangeCancelationSelected.value.copyWith(
        hour: dateConverted.hour,
        minutes: dateConverted.minute,
        discriminator: 'AqDate',
        noEndDate: false);
    final difference = aqDate
        .toDate()
        .difference(startDateTimeForRangeCancelationSelected.value.toDate());

    if (difference.inMinutes < 0) {
      showError(
          "Error",
          talabel
              .getAq(
                  key: 'aq.web.calendar.datetime.save.error.endBeforStart',
                  defaultTxt: AqText.errorDateTimeEndBeforeStart)!
              .tag);
    } else {
      cancelToTimeCtrl.text =
          aqDate.displayDateString(talabel, pattern: AqDatePatternType.TIME);
      cancelToDateCtrl.text = aqDate.displayDateString(talabel,
          pattern: AqDatePatternType.SHORT_DATE);
      endDateTimeForRangeCancelationSelected(aqDate);
      endDateTimeBorderSelected(aqDate);
      validateCancelRangeForm();
    }
  }

  void validateCancelRangeForm() {
    final difference = endDateTimeForRangeCancelationSelected.value
        .toDate()
        .difference(startDateTimeForRangeCancelationSelected.value.toDate());
    isSubmitEnabledForCancelRange(difference.inMinutes != 0 &&
        endDateTimeForRangeCancelationSelected.value.noEndDate != true);
  }

  void showError(String title, String text) {
    Get.defaultDialog(
      title: title,
      middleText: text,
      confirmTextColor: Colors.white,
      onConfirm: () {
        Get.back();
      },
      textConfirm: talabel
          .getAq(key: 'aq.cmd.close', defaultTxt: AqText.button_close)!
          .tag,
    );
  }

}