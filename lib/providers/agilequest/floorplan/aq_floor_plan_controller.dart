import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:xml/xml.dart';
import 'package:flutter/services.dart' show rootBundle;

class AqFloorPlanController extends GetxController {
  var allLayers = <XmlTag>[].obs;
  var basePlanLayer = <XmlTag>[].obs;
  var spacePolygonLayer = <XmlTag>[].obs;
  var isLoading = false.obs;
  var offsetData = <Offset>[].obs;
  var svgdata = <List<Offset>>[].obs;
  //var scaleFactor = 0.35.obs;
  var scaleFactor = 1.0.obs;
  var baseScaleFactor = 0.5.obs;
//layers
  var baselayer = true.obs;
  var spacelayer = true.obs;
  var layerMap = <String, bool>{'base_layer': true, 'space_layer': true}.obs;

  Future<void> loadSvgImage() async {
    isLoading(true);
    debugPrint('------------loadSvgImage-------------');
    String svgrootStr = 'lib/icons/test/test.svg';
    //SvgPicture svgpic = await SvgPicture.asset(svgrootStr);
    //debugPrint('height>>>${svgpic.height} >>>width>>>${svgpic.width}');

    String generalString = await rootBundle.loadString(svgrootStr);

    XmlDocument document = XmlDocument.parse(generalString);
    final gtags = document.findAllElements('g');
    XmlTag xmltag;

    String style;
    String id;
    String dpoints;
    String layer;
    //var offsetList = <Offset>[];
    var coords;
    allLayers.clear();
    basePlanLayer.clear();
    spacePolygonLayer.clear();

    for (var g in gtags) {
      if (g.getAttribute('id')?.toString() == 'basePlanLayer_1' && layerMap.value['base_layer']!) {
        //debugPrint('--------------basePlanLayer_1--------------');
        final bplPolylines = g.findAllElements('polyline');

        for (var element in bplPolylines) {
          dpoints = element.getAttribute('points').toString();
          style = element.getAttribute('style').toString();
          id = element.getAttribute('id').toString();

          xmltag = XmlTag(dpoints: dpoints, id: id, style: style, layer: 'basePlanLayer_1', tag: 'polyline');
          // ppp.value.add(dpoints);
          basePlanLayer.value.add(xmltag);
          allLayers.value.add(xmltag);
          xmltag = XmlTag();
          xmltag = XmlTag();
        }

        final bplPaths = g.findAllElements('path');
        for (var element in bplPaths) {
          dpoints = element.getAttribute('d').toString();
          style = element.getAttribute('style').toString();
          //id = element.getAttribute('id').toString();

          xmltag = XmlTag(dpoints: dpoints, id: '', style: style, layer: 'basePlanLayer_1', tag: 'path');
          // ppp.value.add(dpoints);
          basePlanLayer.value.add(xmltag);
          allLayers.value.add(xmltag);
          xmltag = XmlTag();
          xmltag = XmlTag();
        }
      }

      if (g.getAttribute('id')?.toString() == 'spacePolygonLayer_1' && layerMap.value['space_layer']!) {
        // debugPrint('--------------spacePolygonLayer_1--------------');

        final splPolylines = g.findAllElements('polyline');

        for (var element in splPolylines) {
          dpoints = element.getAttribute('points').toString();
          style = element.getAttribute('style').toString();
          id = element.getAttribute('id').toString();

          xmltag = XmlTag(dpoints: dpoints, id: id, style: style, layer: 'spacePolygonLayer_1', tag: 'polyline');
          spacePolygonLayer.value.add(xmltag);
          allLayers.value.add(xmltag);
          xmltag = XmlTag();
          xmltag = XmlTag();
        }
      }
    }
    isLoading(false);
    debugPrint('------------loadSvgImage--done-----------');
  }
}

class XmlTag {
  String? cls;
  String? id;
  String? dpoints;
  String? style;
  String? visibility;
  String? layer;
  String? tag;
  XmlTag({this.cls, this.id, this.dpoints, this.style, this.visibility, this.layer, this.tag});
}
