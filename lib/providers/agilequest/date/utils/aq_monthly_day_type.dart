enum AqMonthlyDayType {
  DAY(1),
  WEEK_DAY(2),
  WEEKEND_DAY(3),
  SUNDAY(4),
  MONDAY(5),
  TUESDAY(6),
  WEDNESDAY(7),
  THURSDAY(8),
  FRIDAY(9),
  SATURDAY(10);

  final int id;
  const AqMonthlyDayType(this.id);
}

extension AqMonthlyDayTypeExtension on AqMonthlyDayType {
  String get displayNameForWidget {
    switch (this) {
      case AqMonthlyDayType.DAY:
        return "Day";
      case AqMonthlyDayType.WEEK_DAY:
        return "Week Day";
      case AqMonthlyDayType.WEEKEND_DAY:
        return "Weekend Day";
      case AqMonthlyDayType.SUNDAY:
        return "Sunday";
      case AqMonthlyDayType.MONDAY:
        return "Monday";
      case AqMonthlyDayType.TUESDAY:
        return "Tuesday";
      case AqMonthlyDayType.WEDNESDAY:
        return "Wednesday";
      case AqMonthlyDayType.THURSDAY:
        return "Thursday";
      case AqMonthlyDayType.FRIDAY:
        return "Friday";
      case AqMonthlyDayType.SATURDAY:
        return "Saturday";
      default:
        return "";
    }
  }

  String get tagForWidget {
    switch (this) {
      case AqMonthlyDayType.DAY:
        return "aq.mobile.monthly.day.type.day.title";
      case AqMonthlyDayType.WEEK_DAY:
        return "aq.mobile.monthly.day.type.week.day.title";
      case AqMonthlyDayType.WEEKEND_DAY:
        return "aq.mobile.monthly.day.type.weekend.day.title";
      case AqMonthlyDayType.SUNDAY:
        return "aq.mobile.monthly.day.type.sunday.title";
      case AqMonthlyDayType.MONDAY:
        return "aq.mobile.monthly.day.type.monday.title";
      case AqMonthlyDayType.TUESDAY:
        return "aq.mobile.monthly.day.type.tuesday.title";
      case AqMonthlyDayType.WEDNESDAY:
        return "aq.mobile.monthly.day.type.wednesday.title";
      case AqMonthlyDayType.THURSDAY:
        return "aq.mobile.monthly.day.type.thursday.title";
      case AqMonthlyDayType.FRIDAY:
        return "aq.mobile.monthly.day.type.friday.title";
      case AqMonthlyDayType.SATURDAY:
        return "aq.mobile.monthly.day.type.sturday.title";
      default:
        return "";
    }
  }
}

class AqMonthlyDayTypeHelper {
  static AqMonthlyDayType? from(int? id) {
    return AqMonthlyDayType.values.firstWhere((type) => type.id == id,
        orElse: () => AqMonthlyDayType.DAY);
  }
}
