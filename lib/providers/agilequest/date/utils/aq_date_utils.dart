
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_end_reccurrence_type.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../models/agilequest/types/dates/aq_date_pattern_type.dart';
import '../../../../models/agilequest/types/dates/aq_weekday.dart';
import '../../../../utils/agilquest/aq_resource_data.dart';
import 'aq_month_occurrence_type.dart';
import 'aq_monthly_day_type.dart';
import 'aq_montly_day_occurrence_type.dart';
import 'aq_reccurrence_selection_type.dart';

class AQDateUtils {
  
  static const int timePickerMinutesInterval = 1;
  static const int amountOfDaysInWeek = 7;
  static const int startRangeReccurence = 1;
  static const int endRangeReccurence = 30;
  static const int endRangeRecurrenceMontly = 99;
  static const int endRangeRecurrenceAfterType = 999;
  static const int timeDifference = 60;

  static List<MonthlyDayOccurrenceType> allMonthDayOccurrenceTypes = [
    MonthlyDayOccurrenceType.FIRST,
    MonthlyDayOccurrenceType.SECOND,
    MonthlyDayOccurrenceType.THIRD,
    MonthlyDayOccurrenceType.FOURTH,
    MonthlyDayOccurrenceType.LAST
  ];

   static List<AqMonthOccurrenceType> allMonthDayOccurrencePatternTypes = [
    AqMonthOccurrenceType.DAY,
    AqMonthOccurrenceType.THE
  ];

   static List<AqWeekday> allWeekDays = [
					AqWeekday.SUNDAY,
					AqWeekday.MONDAY,
					AqWeekday.TUESDAY,
					AqWeekday.WEDNESDAY,
					AqWeekday.THURSDAY,
					AqWeekday.FRIDAY,
					AqWeekday.SATURDAY
   ];

  static List<AqMonthlyDayType> allMonthDayTypes = [
    AqMonthlyDayType.DAY,
    AqMonthlyDayType.WEEK_DAY,
    AqMonthlyDayType.WEEKEND_DAY,
    AqMonthlyDayType.SUNDAY,
    AqMonthlyDayType.MONDAY,
    AqMonthlyDayType.TUESDAY,
    AqMonthlyDayType.WEDNESDAY,
    AqMonthlyDayType.THURSDAY,
    AqMonthlyDayType.FRIDAY,
    AqMonthlyDayType.SATURDAY
  ];

  static List<AqRecurrenceSelectionType> allRecurrenceTypes = [
    AqRecurrenceSelectionType.DAILY,
    AqRecurrenceSelectionType.WEEKLY,
    AqRecurrenceSelectionType.MONTHLY
  ];

  static List<AqEndReccurrenceType> allRecurrenceEndTypes = [
    AqEndReccurrenceType.AFTER,
    AqEndReccurrenceType.BY,
  ];

  AQDateUtils._();
    static String getDatePattern(LabelController talabel, AqDatePatternType type) {
      switch (type) {
      case AqDatePatternType.TIME: 
        return talabel.getAq(key: 'aq.mobile.time.format', defaultTxt: AqText.timeFormat)?.tag ?? '';

      case AqDatePatternType.SHORT_DATE_TIME: 
        return talabel.getAq(key: 'aq.mobile.short.date.time.format', defaultTxt: AqText.shortDateTimeFormat)?.tag ?? '';
      
      case AqDatePatternType.FULL_DATE_TIME: 
        return talabel.getAq(key: 'aq.mobile.full.date.time.format', defaultTxt: AqText.fullDateTimeFormat)?.tag ?? '';
      
      case AqDatePatternType.FULL_DATE: 
      return talabel.getAq(key: 'aq.mobile.full.date.format', defaultTxt: AqText.fullDateFormat)?.tag ?? '';
      
      case AqDatePatternType.SHORT_DATE:
        return talabel.getAq(key: 'aq.mobile.short.date.format', defaultTxt: AqText.shortDateFormat)?.tag ?? '';
    }
  }
}