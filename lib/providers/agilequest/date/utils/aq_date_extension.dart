import 'package:intl/intl.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_utils.dart';
import 'package:timezone/standalone.dart';

import '../../../../models/agilequest/types/dates/aq_date_pattern_type.dart';
import '../../../../utils/agilquest/aq_resource_data.dart';
import '../../../ta_admin/label_controller.dart';

extension AQDateExtension on AqDate {
  AqDate endOfDay() {
    return AqDate(
      year: year,
      month: month,
      dayOfMonth: dayOfMonth,
      hour: 23,
      minute: 59,
      discriminator: discriminator,
      tzIdValue: tzIdValue,
    );
  }

  AqDate startOfDay() {
    return AqDate(
      year: year,
      month: month,
      dayOfMonth: dayOfMonth,
      hour: 0,
      minute: 0,
      discriminator: discriminator,
      tzIdValue: tzIdValue,
    );
  }

  bool isNoEndDate() {
    return discriminator == 'AqNoEndDate';
  }

  String displayString({String? type}) {
    if (type == 'fm') {
      return DateFormat("yMMMMd").format(toDate());
    } else if (noEndDate == true || discriminator == 'AqNoEndDate') {
      return 'No End Date';
    } else {
      //return type == 'fm' ? DateFormat("yMMMMd").format(toDate()) : DateFormat("MM/dd/yyyy").format(toDate());
      return DateFormat("MM/dd/yyyy").format(toDate());
    }
  }

  String displayDateString(LabelController talable,
      {bool withTimeZone = false,
      AqDatePatternType pattern = AqDatePatternType.FULL_DATE_TIME, bool skipNoEndDate = false}) {
    if ((noEndDate == true || discriminator == 'AqNoEndDate') && !skipNoEndDate) {
      return talable.getAq(
                  key: 'aq.web.calendar.datetime.label.noEndDate',
                  defaultTxt: AqText.noEndDate)?.tag ?? '';
    } else {
      final datePattern = AQDateUtils.getDatePattern(talable, pattern);
      if (withTimeZone) {
        final locationTz = getLocation(tzIdValue);
        final tzString = TZDateTime.now(locationTz).timeZoneName.toString();
        return "${DateFormat(datePattern).format(toDate())} $tzString";
      } else {
        return DateFormat(datePattern).format(toDate());
      }
    }
  }

  DateTime localDate() {
    final locationTz = getLocation(tzIdValue);
    final tzValue = TZDateTime.now(locationTz);
    final serviceOffset = tzValue.timeZoneOffset.inMinutes;
    final localOffset = DateTime.now().timeZoneOffset.inMinutes;
    final offset = (serviceOffset - localOffset);
    final aqDateInLocal = addMinutes(-offset);
    return aqDateInLocal.toDate();
  }

  DateTime toDate() {
    return DateTime(
        year ?? 0, month ?? 0, dayOfMonth ?? 0, hour ?? 0, minute ?? 0);
  }

  AqDate addMinutes(int minutes) {
    final date = toDate().add(Duration(minutes: minutes));
    return AqDate.fromDate(date, tzIdValue);
  }

  AqDate copyWith(
      {int? year,
      int? month,
      int? dayOfMonth,
      int? hour,
      int? minutes,
      String? discriminator,
      String? tzValue, 
      bool? noEndDate}) {
    return AqDate(
      year: year ?? this.year,
      month: month ?? this.month,
      dayOfMonth: dayOfMonth ?? this.dayOfMonth,
      hour: hour ?? this.hour,
      minute: minutes ?? minute,
      discriminator: discriminator ?? this.discriminator,
      tzIdValue: tzValue ?? tzIdValue,
      noEndDate: noEndDate ?? this.noEndDate
    );
  }
}
