import 'package:tangoworkplace/models/agilequest/types/dates/aq_recurrence_pattern_type.dart';

enum AqRecurrenceSelectionType {
  DAILY,
  WEEKLY,
  MONTHLY;

  int get position {
    switch (this) {
      case AqRecurrenceSelectionType.DAILY:
        return 0;
      case AqRecurrenceSelectionType.WEEKLY:
        return 1;
      case AqRecurrenceSelectionType.MONTHLY:
        return 2;
    }
  }

  String get languageTag {
    switch (this) {
      case AqRecurrenceSelectionType.DAILY:
        return "aq.mobile.recurring.pattern.type.daily";
      case AqRecurrenceSelectionType.WEEKLY:
        return "aq.mobile.recurring.pattern.type.weekly";
      case AqRecurrenceSelectionType.MONTHLY:
        return "aq.mobile.recurring.pattern.type.monthly";
    }
  }

  String get displayName {
    switch (this) {
      case AqRecurrenceSelectionType.DAILY:
        return "Daily";
      case AqRecurrenceSelectionType.WEEKLY:
        return "Weekly";
      case AqRecurrenceSelectionType.MONTHLY:
        return "Monthly";
    }
  }

  AqRecurrencePatternType getPatternType(bool isForDateMonthType) {
    switch (this) {
      case AqRecurrenceSelectionType.DAILY:
        return AqRecurrencePatternType.DAILY;
      case AqRecurrenceSelectionType.WEEKLY:
        return AqRecurrencePatternType.WEEKLY;
      case AqRecurrenceSelectionType.MONTHLY:
        return isForDateMonthType
            ? AqRecurrencePatternType.MONTHLY_DATE
            : AqRecurrencePatternType.MONTHLY_DAY;
    }
  }

  static AqRecurrenceSelectionType? from(int position) {
    return values.firstWhere((type) => type.position == position, orElse: () => AqRecurrenceSelectionType.DAILY);
  }

  static AqRecurrenceSelectionType? fromPatternId(int id) {
    final pattern = AqRecurrencePatternType.values.firstWhere((element) => element.id == id);
    switch (pattern) {
      case AqRecurrencePatternType.DAILY:
        return AqRecurrenceSelectionType.DAILY;
      case AqRecurrencePatternType.WEEK_DAYS:
        return AqRecurrenceSelectionType.WEEKLY;
      case AqRecurrencePatternType.WEEKLY:
        return AqRecurrenceSelectionType.WEEKLY;
      case AqRecurrencePatternType.MONTHLY_DATE:
        return AqRecurrenceSelectionType.MONTHLY;
      case AqRecurrencePatternType.MONTHLY_DAY:
        return AqRecurrenceSelectionType.MONTHLY;
    }
  }

  static AqRecurrenceSelectionType? fromPattern(AqRecurrencePatternType patternType) {
    switch (patternType) {
      case AqRecurrencePatternType.DAILY:
        return DAILY;
      case AqRecurrencePatternType.WEEK_DAYS:
      case AqRecurrencePatternType.WEEKLY:
        return WEEKLY;
      case AqRecurrencePatternType.MONTHLY_DATE:
      case AqRecurrencePatternType.MONTHLY_DAY:
        return MONTHLY;
    }
  }
}