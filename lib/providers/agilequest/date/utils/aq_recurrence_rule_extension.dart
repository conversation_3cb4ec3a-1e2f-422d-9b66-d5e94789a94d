import 'package:tangoworkplace/models/agilequest/date/aq_recurrence_rule.dart';
import 'package:tangoworkplace/models/agilequest/types/dates/aq_recurrence_pattern_type.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_monthly_day_type.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../../models/agilequest/objects/dates/aq_weekly_recurrence_pattern.dart';
import 'aq_month_type.dart';
import 'aq_montly_day_occurrence_type.dart';

extension AqRecurrenceRuleExtension on AqRecurrenceRule {
  String formatted(LabelController languageTags) {
    String rule;
    if(sysidRecurrencePatternType == AqRecurrencePatternType.DAILY.id) {
      rule = dailyPattern(this, languageTags);
    } else if(sysidRecurrencePatternType == AqRecurrencePatternType.WEEKLY.id) {
      rule = weeklyPattern(this, languageTags);
    } else if(sysidRecurrencePatternType == AqRecurrencePatternType.MONTHLY_DATE.id) {
      rule = monthlyDatePattern(this, languageTags);
    } else if(sysidRecurrencePatternType == AqRecurrencePatternType.MONTHLY_DAY.id) {
      rule = monthlyDayPattern(this, languageTags);
    } else {
      rule = '';
    }
    
    final endTime = aqEndDate;
    if (endTime != null) {
      final month =
          AqMonthType.getNameOfMonth((endTime.month ?? 0) - 1, languageTags);
      final recurringText = languageTags
          .getAq(
              key: 'aq.recurrence.period.endsBy',
              defaultTxt: '{0}, ends by {1}')!
          .tag
          .replaceFirst("{0}", rule)
          .replaceFirst("{1}", "$month ${endTime.dayOfMonth}, ${endTime.year}")
          .replaceAll("\$", "");
      rule = recurringText;
    }
    if (occurrences != null) {
      final recurringText = languageTags
          .getAq(
              key: 'aq.recurrence.period.endsAfter',
              defaultTxt: '{0}, ends after {1} occurrences')!
          .tag
          .replaceFirst("{0}", rule)
          .replaceFirst("{1}", occurrences.toString())
          .replaceAll("\$", "");
      rule = recurringText;
    }
    return rule;
  }

  String dailyPattern(AqRecurrenceRule it, LabelController languageTags) {
    return languageTags
        .getAq(
            key: 'aq.recurrence.period.days',
            defaultTxt: 'Recurring every {0} days')!
        .tag
        .replaceFirst("{0}", it.interval.toString())
        .replaceAll("\$", "");
  }

  String weeklyPattern(AqRecurrenceRule it, LabelController languageTags) {
    final listDays = [
      AqWeeklyRecurrencePattern(
          weekDay: languageTags
              .getAq(key: 'aq.week.sunday', defaultTxt: 'Sunday')!
              .tag,
          isSelected: it.onSunday ?? false),
      AqWeeklyRecurrencePattern(
          weekDay: languageTags
              .getAq(key: 'aq.week.monday', defaultTxt: 'Monday')!
              .tag,
          isSelected: it.onMonday ?? false),
      AqWeeklyRecurrencePattern(
          weekDay: languageTags
              .getAq(key: 'aq.week.tuesday', defaultTxt: 'Tuesday')!
              .tag,
          isSelected: it.onTuesday ?? false),
      AqWeeklyRecurrencePattern(
          weekDay: languageTags
              .getAq(key: 'aq.week.wednesday', defaultTxt: 'Wednesday')!
              .tag,
          isSelected: it.onWednesday ?? false),
      AqWeeklyRecurrencePattern(
          weekDay: languageTags
              .getAq(key: 'aq.week.thursday', defaultTxt: 'Thursday')!
              .tag,
          isSelected: it.onThursday ?? false),
      AqWeeklyRecurrencePattern(
          weekDay: languageTags
              .getAq(key: 'aq.week.friday', defaultTxt: 'Friday')!
              .tag,
          isSelected: it.onFriday ?? false),
      AqWeeklyRecurrencePattern(
          weekDay: languageTags
              .getAq(key: 'aq.week.saturday', defaultTxt: 'Saturday')!
              .tag,
          isSelected: it.onSaturday ?? false)
    ];

    String daysString = "";
    for (var day in listDays.where((item) => item.isSelected)) {
      daysString = "$daysString ${day.weekDay}";
    }
    return languageTags
        .getAq(
            key: 'aq.recurrence.period.weeks.weekday',
            defaultTxt: 'Recurring every {0} weeks on {1}')!
        .tag
        .replaceFirst("{0}", it.interval.toString())
        .replaceFirst("{1}", daysString.trim())
        .replaceAll("\$", "");
  }

  String monthlyDayPattern(AqRecurrenceRule it, LabelController languageTags) {
    String monthlyDayType = "";
    for (var item in AqMonthlyDayType.values
        .where((item) => item.index == it.sysidMonthlyDayType)) {
      monthlyDayType = item.displayNameForWidget;
    }
    String monthlyDayOccurrenceType = "";
    for (var item in MonthlyDayOccurrenceType.values
        .where((item) => item.index == it.sysidMonthlyDayOccurrenceType)) {
      monthlyDayOccurrenceType = item.displayNameForWidget;
    }
    return languageTags
        .getAq(
            key: 'aq.recurrence.period.months.conditional.weekOrWeekday',
            defaultTxt: 'Recurring on the {0} {1} of every {2} months')!
        .tag
        .replaceFirst("{0}", monthlyDayOccurrenceType)
        .replaceFirst("{1}", monthlyDayType)
        .replaceFirst("{2}", it.interval.toString())
        .replaceAll("\$", "");
  }

  String monthlyDatePattern(AqRecurrenceRule it, LabelController languageTags) {
    return languageTags
        .getAq(
            key: 'aq.recurrence.period.months.conditional.dayOfMonth',
            defaultTxt: 'Recurring on day {0} every {1} months')!
        .tag
        .replaceFirst("{0}", it.theDate.toString())
        .replaceFirst("{1}", it.interval.toString())
        .replaceAll("\$", "");
  }
}