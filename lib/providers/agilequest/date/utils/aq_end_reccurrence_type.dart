enum AqEndReccurrenceType {
  BY, 
  AFTER
}

extension AqEndReccurrenceTypeExtension on AqEndReccurrenceType {
  String get displayNameForWidget {
    switch (this) {
      case AqEndReccurrenceType.BY:
        return "By";
      case AqEndReccurrenceType.AFTER:
        return "After";
      default:
        return "";
    }
  }

  String get tagForWidget {
    switch (this) {
      case AqEndReccurrenceType.BY:
        return "aq.mobile.end.pattern.type.by";
      case AqEndReccurrenceType.AFTER:
        return "aq.mobile.end.pattern.type.after";
      default:
        return "";
    }
  }
}

AqEndReccurrenceType? from(int? id) {
  return AqEndReccurrenceType.values.firstWhere((type) => type.index == id, orElse: () => AqEndReccurrenceType.BY);
}
