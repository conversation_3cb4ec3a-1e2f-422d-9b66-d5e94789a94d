import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

enum AqMonthType {
  JANUARY(0),
  FEBRUARY(1),
  MARCH(2),
  APRIL(3),
  MAY(4),
  JUN<PERSON>(5),
  JULY(6),
  AUGUST(7),
  SEPTEMBER(8),
  OCTOBER(9),
  NOVEMBER(10),
  DECEMBER(11);

  final int code;
  const AqMonthType(this.code);

  static AqMonthType? from(int code) {
    return AqMonthType.values.firstWhere((month) => month.code == code, orElse: () => AqMonthType.JANUARY);
  }

  static String getNameOfMonth(int codeValue, LabelController talabel) {
    switch (from(codeValue)) {
      case AqMonthType.JANUARY:
        return talabel.getAq(key: 'aq.calendar.january.long', defaultTxt: 'January')!.tag;
      case AqMonthType.FEBRUARY:
        return talabel.getAq(key: 'aq.calendar.february.long', defaultTxt: 'February')!.tag;
      case AqMonthType.MARCH:
        return talabel.getAq(key: 'aq.calendar.march.long', defaultTxt: 'March')!.tag;
      case AqMonthType.APRIL:
        return talabel.getAq(key: 'aq.calendar.april.long', defaultTxt: 'April')!.tag;
      case AqMonthType.MAY:
        return talabel.getAq(key: 'aq.calendar.may.long', defaultTxt: 'May')!.tag;
      case AqMonthType.JUNE:
        return talabel.getAq(key: 'aq.calendar.june.long', defaultTxt: 'June')!.tag;
      case AqMonthType.JULY:
        return talabel.getAq(key: 'aq.calendar.july.long', defaultTxt: 'July')!.tag;
      case AqMonthType.AUGUST:
        return talabel.getAq(key: 'aq.calendar.august.long', defaultTxt: 'August')!.tag;
      case AqMonthType.SEPTEMBER:
        return talabel.getAq(key: 'aq.calendar.september.long', defaultTxt: 'September')!.tag;
      case AqMonthType.OCTOBER:
        return talabel.getAq(key: 'aq.calendar.october.long', defaultTxt: 'October')!.tag;
      case AqMonthType.NOVEMBER:
        return talabel.getAq(key: 'aq.calendar.november.long', defaultTxt: 'November')!.tag;
      case AqMonthType.DECEMBER:
        return talabel.getAq(key: 'aq.calendar.december.long', defaultTxt: 'December')!.tag;
      default:
        return "";
    }
  }
}