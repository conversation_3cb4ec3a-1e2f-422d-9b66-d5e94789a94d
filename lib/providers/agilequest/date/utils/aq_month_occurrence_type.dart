enum AqMonthOccurrenceType {
  DAY, 
  THE
}

extension AqMonthOccurrenceTypeExtension on AqMonthOccurrenceType {
  String get displayNameForWidget {
    switch (this) {
      case AqMonthOccurrenceType.DAY:
        return "Day";
      case AqMonthOccurrenceType.THE:
        return "The";
      default:
        return "";
    }
  }

  String get tagForWidget {
    switch (this) {
      case AqMonthOccurrenceType.DAY:
        return "aq.mobile.month.pattern.type.day.title";
      case AqMonthOccurrenceType.THE:
        return "aq.mobile.month.pattern.type.the.title";
      default:
        return "";
    }
  }
}

AqMonthOccurrenceType? from(int? id) {
  return AqMonthOccurrenceType.values.firstWhere((type) => type.index == id, orElse: () => AqMonthOccurrenceType.THE);
}
