enum MonthlyDayOccurrenceType {
  FIRST(1),
  SECOND(2),
  THIRD(3),
  FOURTH(4),
  LAST(5);

  final int id;
  const MonthlyDayOccurrenceType(this.id);
}

extension MonthlyDayOccurrenceTypeExtension on MonthlyDayOccurrenceType {
  String get displayNameForWidget {
    switch (this) {
      case MonthlyDayOccurrenceType.FIRST:
        return "First";
      case MonthlyDayOccurrenceType.SECOND:
        return "Second";
      case MonthlyDayOccurrenceType.THIRD:
        return "Third";
      case MonthlyDayOccurrenceType.FOURTH:
        return "Fourth";
      case MonthlyDayOccurrenceType.LAST:
        return "Last";
      default:
        return "";
    }
  }

  String get tagForWidget {
    switch (this) {
      case MonthlyDayOccurrenceType.FIRST:
        return "aq.mobile.monthly.day.occurrence.type.first.title";
      case MonthlyDayOccurrenceType.SECOND:
        return "aq.mobile.monthly.day.occurrence.type.second.title";
      case MonthlyDayOccurrenceType.THIRD:
        return "aq.mobile.monthly.day.occurrence.type.third.title";
      case MonthlyDayOccurrenceType.FOURTH:
        return "aq.mobile.monthly.day.occurrence.type.fourth.title";
      case MonthlyDayOccurrenceType.LAST:
        return "aq.mobile.monthly.day.occurrence.type.last.title";
      default:
        return "";
    }
  }
}

class MonthlyDayOccurrenceTypeHelper {
  static MonthlyDayOccurrenceType? from(int? id) {
    return MonthlyDayOccurrenceType.values.firstWhere(
      (type) => type.id == id,
      orElse: () => MonthlyDayOccurrenceType.FIRST,
    );
  }
}