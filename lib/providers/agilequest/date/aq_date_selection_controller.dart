import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_recurrence_rule.dart';
import 'package:tangoworkplace/models/agilequest/objects/dates/aq_dates.dart';
import 'package:tangoworkplace/models/agilequest/objects/dates/aq_dates_selection_criteria.dart';
import 'package:tangoworkplace/models/agilequest/types/dates/aq_date_pattern_type.dart';
import 'package:tangoworkplace/models/agilequest/types/dates/aq_weekday.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_utils.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_end_reccurrence_type.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_month_occurrence_type.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_monthly_day_type.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_montly_day_occurrence_type.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_reccurrence_selection_type.dart';
import 'package:tangoworkplace/utils/agilquest/aq_resource_data.dart';

import '../../../models/agilequest/date/aq_date.dart';
import '../../../models/agilequest/types/dates/aq_recurrence_pattern_type.dart';
import '../../ta_admin/label_controller.dart';

class AqDateSelectionController extends GetxController
    with GetTickerProviderStateMixin {
  var startDateStandardController = TextEditingController();
  var endDateStandardController = TextEditingController();
  var startTimeStandardController = TextEditingController();
  var endTimeStandardController = TextEditingController();
  var startDateRecurringController = TextEditingController();
  var startTimeRecurringController = TextEditingController();
  var endTimeRecurringController = TextEditingController();
  var endByDateRecurringController = TextEditingController();

  final LabelController talabel = Get.find<LabelController>();
  late TabController tabController;

  Rx<AqDate> startDateStandardSelected =
      AqDate.fromDate(DateTime.now(), '').obs;
  Rx<AqDate> endDateStandardSelected = AqDate.fromDate(DateTime.now(), '').obs;
  Rx<AqDate> startDateRecurringSelected =
      AqDate.fromDate(DateTime.now(), '').obs;
  Rx<AqDate> endDateRecurringSelected = AqDate.fromDate(DateTime.now(), '').obs;
  Rx<AqDate> endByDateRecurringSelected =
      AqDate.fromDate(DateTime.now(), '').obs;
  Rx<AqRecurrenceSelectionType> recurrencePatternTypeSelected =
      AqRecurrenceSelectionType.DAILY.obs;
  Rx<int> occurencesPatternSelected = 0.obs;
  Rx<int> intervalPatternSelected = 0.obs;
  Rx<AqEndReccurrenceType> recurrenceEndPatternTypeSelected =
      AqEndReccurrenceType.AFTER.obs;
  Rx<AqMonthOccurrenceType> monthlyPatternTypeSelected =
      AqMonthOccurrenceType.DAY.obs;
  Rx<MonthlyDayOccurrenceType> monthlyDayOccurencePatternTypeSelected =
      MonthlyDayOccurrenceType.FIRST.obs;
  Rx<AqMonthlyDayType> monthlyDayTypeSelected = AqMonthlyDayType.DAY.obs;
  Rx<int> monthlyDayPatternSelected = 0.obs;
  RxList<int> recurrenceWeekdaysSelected = <int>[].obs;
  RxList<AqWeekday> weekdaysToDisplay = AQDateUtils.allWeekDays.obs;
  Rx<String> intervalPatternTypeLabel = 'Every day(s)'.obs;
  Rx<bool> noEndDateCheckBox = false.obs;
  Rx<bool> isSubmitRecurringEnabled = false.obs;

  AqDatesSelectionCriteria? _initialValue;

  void setupInitialValues(
      BuildContext context, AqDatesSelectionCriteria initialValue) {
    _initialValue = initialValue;

    tabController = TabController(vsync: this, length: 2);

    if (initialValue.dateAndTime != null) {
      tabController.index = 0;

      final isNoEndDate =
          initialValue.dateAndTime?.endDateTime?.isNoEndDate() ?? false;
      final endDate = initialValue.dateAndTime?.endDateTime;
      final startDate = initialValue.dateAndTime?.startDateTime;
      noEndDateCheckBox(isNoEndDate);

      if (endDate != null && !isNoEndDate) {
        endDateStandardController.text = endDate.displayDateString(talabel,
            pattern: AqDatePatternType.FULL_DATE);
        endTimeStandardController.text = endDate.displayDateString(talabel,
            withTimeZone: true, pattern: AqDatePatternType.TIME);
        endDateStandardSelected(endDate);
      } else if(isNoEndDate) {
        endDateStandardSelected(endDate);
      }

      if (startDate != null) {
        startDateStandardController.text = startDate.displayDateString(talabel,
            pattern: AqDatePatternType.FULL_DATE);
        startTimeStandardController.text = startDate.displayDateString(talabel,
            withTimeZone: true, pattern: AqDatePatternType.TIME);
        startDateStandardSelected(startDate);
      }
    } else {
      tabController.index = 1;

      // Recurring pattern
      final patternType = AqRecurrenceSelectionType.fromPatternId(
          initialValue.recurrenceRule?.sysidRecurrencePatternType ?? 0);
      recurrencePatternTypeChanged(patternType?.position ?? 0);

      // Interval
      intervalPatternSelected(initialValue.recurrenceRule?.interval);

      // Start date & time
      final recurrenceStartDate =
          initialValue.recurringStartAndEnd?.startDateTime;
      startTimeRecurringController.text =
          recurrenceStartDate?.displayDateString(talabel,
                  withTimeZone: true, pattern: AqDatePatternType.TIME) ??
              '';
      startDateRecurringController.text =
          recurrenceStartDate?.displayDateString(talabel,
                  pattern: AqDatePatternType.FULL_DATE) ??
              '';
      startDateRecurringSelected(recurrenceStartDate);

      //End time
      final recurrenceEndDate = initialValue.recurringStartAndEnd?.endDateTime;
      endTimeRecurringController.text = recurrenceEndDate?.displayDateString(
              talabel,
              withTimeZone: true,
              pattern: AqDatePatternType.TIME) ??
          '';
      endDateRecurringSelected(recurrenceEndDate);

      //Weekdays selection
      if (initialValue.recurrenceRule?.onSunday == true) {
        recurrenceWeekdaysSelected.add(AqWeekday.SUNDAY.id);
      }
      if (initialValue.recurrenceRule?.onMonday == true) {
        recurrenceWeekdaysSelected.add(AqWeekday.MONDAY.id);
      }
      if (initialValue.recurrenceRule?.onTuesday == true) {
        recurrenceWeekdaysSelected.add(AqWeekday.TUESDAY.id);
      }
      if (initialValue.recurrenceRule?.onWednesday == true) {
        recurrenceWeekdaysSelected.add(AqWeekday.WEDNESDAY.id);
      }
      if (initialValue.recurrenceRule?.onThursday == true) {
        recurrenceWeekdaysSelected.add(AqWeekday.THURSDAY.id);
      }
      if (initialValue.recurrenceRule?.onFriday == true) {
        recurrenceWeekdaysSelected.add(AqWeekday.FRIDAY.id);
      }
      if (initialValue.recurrenceRule?.onSaturday == true) {
        recurrenceWeekdaysSelected.add(AqWeekday.SATURDAY.id);
      }

      //Monthly pattern
      if (initialValue.recurrenceRule?.sysidRecurrencePatternType ==
          AqRecurrencePatternType.MONTHLY_DATE.id) {
        monthlyPatternTypeSelected(AqMonthOccurrenceType.DAY);
        monthlyDayPatternSelected(initialValue.recurrenceRule?.theDate ?? 0);
      } else if (initialValue.recurrenceRule?.sysidRecurrencePatternType ==
          AqRecurrencePatternType.MONTHLY_DAY.id) {
        monthlyPatternTypeSelected(AqMonthOccurrenceType.THE);

        final dayOccurenceType = MonthlyDayOccurrenceTypeHelper.from(
            initialValue.recurrenceRule?.sysidMonthlyDayOccurrenceType);
        monthlyDayOccurencePatternTypeSelected(dayOccurenceType);

        final montlyDayType = AqMonthlyDayTypeHelper.from(
            initialValue.recurrenceRule?.sysidMonthlyDayType);
        monthlyDayTypeSelected(montlyDayType);
      }

      //End Date Occurences or By Date
      occurencesPatternSelected(initialValue.recurrenceRule?.occurrences);
      endByDateRecurringController.text = initialValue.recurrenceRule?.aqEndDate
              ?.displayDateString(talabel,
                  pattern: AqDatePatternType.FULL_DATE) ??
          '';
      endByDateRecurringSelected(initialValue.recurrenceRule?.aqEndDate);

      if (initialValue.recurrenceRule?.occurrences == null) {
        recurrenceEndPatternTypeSelected(AqEndReccurrenceType.BY);
      } else {
        recurrenceEndPatternTypeSelected(AqEndReccurrenceType.AFTER);
      }

      final isRecurringValid = validateRecurrinPattern();
      isSubmitRecurringEnabled(isRecurringValid);
    }
  }

// STANDARD DATE SELECTION
  void updateStartDateStandard(String date) {
    final endDate = endDateStandardSelected.value.toDate();
    final differenceEndDate =
        endDate.difference(startDateStandardSelected.value.toDate());

    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = startDateStandardSelected.value.copyWith(
        year: dateConverted.year,
        month: dateConverted.month,
        dayOfMonth: dateConverted.day);
    final selectedDate = aqDate.toDate();

    startDateStandardController.text =
        aqDate.displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);
    startTimeStandardController.text = aqDate.displayDateString(talabel,
        withTimeZone: true, pattern: AqDatePatternType.TIME);
    startDateStandardSelected(aqDate);

    final newEndDate = selectedDate.add(differenceEndDate);
    final newAqEndDate =
        AqDate.fromDate(newEndDate, _initialValue?.tzValue() ?? '',
         discriminator: endDateStandardSelected.value.discriminator);
    endDateStandardController.text = newAqEndDate.displayDateString(talabel,
        pattern: AqDatePatternType.FULL_DATE);
    endTimeStandardController.text = newAqEndDate.displayDateString(talabel,
        withTimeZone: true, pattern: AqDatePatternType.TIME);
    endDateStandardSelected(newAqEndDate);
  }

  void updateEndDateStandard(String date) {
    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = endDateStandardSelected.value.copyWith(
        year: dateConverted.year,
        month: dateConverted.month,
        dayOfMonth: dateConverted.day);

    if (aqDate.toDate().difference(startDateStandardSelected.value.toDate()).isNegative) {
      showError(
          "Error",
          talabel.getAq(
                  key: 'aq.web.calendar.datetime.save.error.endBeforStart',
                  defaultTxt: AqText.errorDateTimeEndBeforeStart)!.tag);
    } else {
      endDateStandardController.text = aqDate.displayDateString(talabel,
          pattern: AqDatePatternType.FULL_DATE);
      endTimeStandardController.text = aqDate.displayDateString(talabel,
          withTimeZone: true, pattern: AqDatePatternType.TIME);
      endDateStandardSelected(aqDate);
    }
  }

  void updateStartTimeStandard(String time) {
    final endDate = endDateStandardSelected.value.toDate();
    final differenceEndDate =
        endDate.difference(startDateStandardSelected.value.toDate());
    final dateConverted = DateFormat('HH:mm').parse(time);
    final aqDate = startDateStandardSelected.value
        .copyWith(hour: dateConverted.hour, minutes: dateConverted.minute);
    final selectedDate = aqDate.toDate();

    startDateStandardController.text =
        aqDate.displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);
    startTimeStandardController.text = aqDate.displayDateString(talabel,
        withTimeZone: true, pattern: AqDatePatternType.TIME);
    startDateStandardSelected(aqDate);

    final newEndDate = selectedDate.add(differenceEndDate);
    final newAqEndDate =
        AqDate.fromDate(newEndDate, _initialValue?.tzValue() ?? '',
        discriminator: endDateStandardSelected.value.discriminator);
    endDateStandardController.text = newAqEndDate.displayDateString(talabel,
        pattern: AqDatePatternType.FULL_DATE);
    endTimeStandardController.text = newAqEndDate.displayDateString(talabel,
        withTimeZone: true, pattern: AqDatePatternType.TIME);
    endDateStandardSelected(newAqEndDate);
  }

  void updateEndTimeStandard(String time) {
    final dateConverted = DateFormat('HH:mm').parse(time);
    final aqDate = endDateStandardSelected.value
        .copyWith(hour: dateConverted.hour, minutes: dateConverted.minute);

    if (aqDate.toDate().difference(startDateStandardSelected.value.toDate()).isNegative) {
      showError(
          "Error",
          talabel.getAq(
                  key: 'aq.web.calendar.datetime.save.error.endBeforStart',
                  defaultTxt: AqText.errorDateTimeEndBeforeStart)!.tag);
    } else {
      endDateStandardController.text = aqDate.displayDateString(talabel,
          pattern: AqDatePatternType.FULL_DATE);
      endTimeStandardController.text = aqDate.displayDateString(talabel,
          withTimeZone: true, pattern: AqDatePatternType.TIME);
      endDateStandardSelected(aqDate);
    }
  }

  void updateNoEndDateStandard(bool isChecked) {
    if (isChecked) {
      endDateStandardSelected(endDateStandardSelected.value.copyWith(discriminator: 'AqNoEndDate'));
    } else {
      endDateStandardSelected(endDateStandardSelected.value.copyWith(discriminator: 'AqDate'));
      endDateStandardController.text = endDateStandardSelected.value.displayDateString(talabel,
          pattern: AqDatePatternType.FULL_DATE);
      endTimeStandardController.text = endDateStandardSelected.value.displayDateString(talabel,
          withTimeZone: true, pattern: AqDatePatternType.TIME);
    }
    noEndDateCheckBox(isChecked);
  }

  AqDatesSelectionCriteria? submitStandardButtonPressed() {
    return _initialValue?.copyWith(
        recurrenceRule: null,
        recurringStartAndEnd: null,
        dateAndTime: AqDates(
            startDateTime: startDateStandardSelected.value,
            endDateTime: endDateStandardSelected.value));
  }

  // RECURRING PATTERN SELECTION

  void updateStartDateRecurring(String date) {
    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = startDateRecurringSelected.value.copyWith(
        year: dateConverted.year,
        month: dateConverted.month,
        dayOfMonth: dateConverted.day);

    startDateRecurringController.text =
        aqDate.displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);

    startDateRecurringSelected(aqDate);
  }

  void updateStartTimeRecurring(String time) {
    final endDate = endDateRecurringSelected.value.toDate();
    final differenceEndDate =
        endDate.difference(startDateRecurringSelected.value.toDate());
    final dateConverted = DateFormat('HH:mm').parse(time);
    final aqDate = startDateRecurringSelected.value
        .copyWith(hour: dateConverted.hour, minutes: dateConverted.minute);
    final selectedDate = aqDate.toDate();

    startDateRecurringController.text =
        aqDate.displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);
    startTimeRecurringController.text = aqDate.displayDateString(talabel,
        withTimeZone: true, pattern: AqDatePatternType.TIME);
    startDateRecurringSelected(aqDate);

    final newEndDate = selectedDate.add(differenceEndDate);
     final newAqEndDate = AqDate(
        year: aqDate.year,
        month: aqDate.month,
        dayOfMonth: aqDate.dayOfMonth,
        hour: newEndDate.hour,
        minute: newEndDate.minute,
        tzIdValue: _initialValue!.tzValue());
    endTimeRecurringController.text = newAqEndDate.displayDateString(talabel,
        withTimeZone: true, pattern: AqDatePatternType.TIME);
    endDateRecurringSelected(newAqEndDate);
  }

  void updateEndTimeRecurring(String time) {
    final dateConverted = DateFormat('HH:mm').parse(time);
    final aqDate = startDateRecurringSelected.value
        .copyWith(hour: dateConverted.hour, minutes: dateConverted.minute);

    if (aqDate.toDate().difference(startDateRecurringSelected.value.toDate()).isNegative) {
      showError(
          "Error",
          talabel.getAq(
                  key: 'aq.web.calendar.datetime.save.error.endBeforStart',
                  defaultTxt: AqText.errorDateTimeEndBeforeStart)!.tag);
    } else {
      endTimeRecurringController.text = aqDate.displayDateString(talabel,
          withTimeZone: true, pattern: AqDatePatternType.TIME);
      endDateRecurringSelected(aqDate);
    }
  }

  void updateEndByDate(String date) {
    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = endByDateRecurringSelected.value.copyWith(
        year: dateConverted.year,
        month: dateConverted.month,
        dayOfMonth: dateConverted.day);

    endByDateRecurringController.text =
        aqDate.displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);
    endByDateRecurringSelected(aqDate);
  }

  void recurrencePatternTypeChanged(int id) {
    final pattern = AqRecurrenceSelectionType.from(id);
    switch (pattern) {
      case AqRecurrenceSelectionType.DAILY:
        intervalPatternTypeLabel('Every day(s)');
        break;
      case AqRecurrenceSelectionType.WEEKLY:
        intervalPatternTypeLabel('Every week(s)');
        break;
      case AqRecurrenceSelectionType.MONTHLY:
        intervalPatternTypeLabel('Every month(s)');
        break;
      case null:
        break;
    }
    recurrencePatternTypeSelected(pattern);
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void intervalChanged(String value) {
    final occurencces = int.parse(value);
    occurencesPatternSelected(occurencces);
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void occurencesChanged(String value) {
    final occurencces = int.parse(value);
    occurencesPatternSelected(occurencces);
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void monthlyOccurenceTypeChanged(int id) {
    monthlyPatternTypeSelected(AqMonthOccurrenceType.values
        .firstWhere((element) => element.index == id));
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void monthlyDayTypeChanged(int id) {
    monthlyDayTypeSelected(AqMonthlyDayTypeHelper.from(id));
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void monthlyDayOccurenceTypeChanged(int id) {
    monthlyDayOccurencePatternTypeSelected(
        MonthlyDayOccurrenceTypeHelper.from(id));
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void montlyDayPatternChanged(int value) {
    monthlyDayPatternSelected(value);
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void recurrencePatternEndTypeChanged(int index) {
    final endPattern = AQDateUtils.allRecurrenceEndTypes
        .firstWhere((element) => element.index == index);
    recurrenceEndPatternTypeSelected(endPattern);
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void weekDaySelected(int id) {
    final listOfPreviouslySelected = recurrenceWeekdaysSelected.toList();
    if (listOfPreviouslySelected.contains(id)) {
      listOfPreviouslySelected.removeWhere((element) => element == id);
      recurrenceWeekdaysSelected(listOfPreviouslySelected);
    } else {
      listOfPreviouslySelected.add(id);
      recurrenceWeekdaysSelected(listOfPreviouslySelected);
    }
    final isRecurringValid = validateRecurrinPattern();
    isSubmitRecurringEnabled(isRecurringValid);
  }

  void showError(String title, String text) {
    Get.defaultDialog(
      title: title,
      middleText: text,
      confirmTextColor: Colors.white,
      onConfirm: () {
        Get.back();
      },
      textConfirm: talabel.getAq(key: 'aq.cmd.close', defaultTxt: AqText.button_close)!.tag,
    );
  }

  AqDatesSelectionCriteria? submitRecurringButtonPressed(
      AqDatesSelectionCriteria initialValue) {
    if (endDateRecurringSelected.value.toDate().difference(startDateRecurringSelected.value.toDate()).isNegative) {
      showError(
        "Error",
        talabel.getAq(
          key:'aq.web.calendar.datetime.save.error.endBeforStart',
          defaultTxt:AqText.errorDateTimeEndBeforeStart)!.tag);
      return null;
    } else {
      final recurrencePatternType = getRecurrencePattern();

      return _initialValue?.copyWith(
          recurrenceRule: recurrencePatternType,
          recurringStartAndEnd: AqDates(
              startDateTime: startDateRecurringSelected.value,
              endDateTime: endDateRecurringSelected.value),
          dateAndTime: null);
    }
  }

  bool validateRecurrinPattern() {
    final recurrencePattern = getRecurrencePattern();

    switch (recurrencePatternTypeSelected.value) {
      case AqRecurrenceSelectionType.DAILY:
        return (recurrencePattern.interval != 0 &&
                recurrencePattern.interval != null) &&
            ((recurrencePattern.occurrences != null &&
                    recurrencePattern.occurrences != 0) ||
                recurrenceEndPatternTypeSelected.value ==
                    AqEndReccurrenceType.BY);
      case AqRecurrenceSelectionType.WEEKLY:
        return (recurrencePattern.interval != 0 &&
                recurrencePattern.interval != null) &&
            ((recurrencePattern.occurrences != null &&
                    recurrencePattern.occurrences != 0) ||
                recurrenceEndPatternTypeSelected.value ==
                    AqEndReccurrenceType.BY) &&
            (recurrencePattern.onSunday == true ||
                recurrencePattern.onMonday == true ||
                recurrencePattern.onTuesday == true ||
                recurrencePattern.onWednesday == true ||
                recurrencePattern.onThursday == true ||
                recurrencePattern.onFriday == true ||
                recurrencePattern.onSaturday == true);

      case AqRecurrenceSelectionType.MONTHLY:
        return (recurrencePattern.interval != 0 &&
                recurrencePattern.interval != null) &&
            ((recurrencePattern.occurrences != null &&
                    recurrencePattern.occurrences != 0) ||
                recurrenceEndPatternTypeSelected.value ==
                    AqEndReccurrenceType.BY) &&
            ((recurrencePattern.sysidRecurrencePatternType ==
                        AqRecurrencePatternType.MONTHLY_DAY.id &&
                    recurrencePattern.sysidMonthlyDayOccurrenceType != null &&
                    recurrencePattern.sysidMonthlyDayType != null) ||
                (recurrencePattern.sysidRecurrencePatternType ==
                        AqRecurrencePatternType.MONTHLY_DATE.id &&
                    recurrencePattern.theDate != null));
    }
  }

  AqRecurrenceRule getRecurrencePattern() {
    AqRecurrenceRule recurrenceRule;
    final occurences =
        recurrenceEndPatternTypeSelected.value == AqEndReccurrenceType.AFTER
            ? occurencesPatternSelected.value
            : null;
    final interval =
        recurrencePatternTypeSelected.value == AqRecurrenceSelectionType.MONTHLY
            ? intervalPatternSelected.value
            : (intervalPatternSelected.value >= 1 &&
                    intervalPatternSelected.value <= 30
                ? intervalPatternSelected.value
                : null);

    switch (recurrencePatternTypeSelected.value) {
      case AqRecurrenceSelectionType.DAILY:
        recurrenceRule = AqRecurrenceRule(
            sysidRecurrencePatternType:
                recurrencePatternTypeSelected.value.getPatternType(false).id,
            sysidRecurrenceRule:
                _initialValue?.recurrenceRule?.sysidRecurrenceRule,
            sysidMonthlyDayOccurrenceType: null,
            sysidMonthlyDayType: null,
            occurrences: occurences,
            interval: interval,
            onSunday: null,
            onMonday: null,
            onTuesday: null,
            onWednesday: null,
            onThursday: null,
            onFriday: null,
            onSaturday: null,
            theDate: null,
            aqStartDate: startDateRecurringSelected.value,
            aqEndDate: occurences == null ?
                endByDateRecurringSelected.value.copyWith(hour: 0, minutes: 0) : null);
        break;
      case AqRecurrenceSelectionType.WEEKLY:
        recurrenceRule = AqRecurrenceRule(
            sysidRecurrencePatternType:
                recurrencePatternTypeSelected.value.getPatternType(false).id,
            sysidRecurrenceRule:
                _initialValue?.recurrenceRule?.sysidRecurrenceRule,
            sysidMonthlyDayOccurrenceType: null,
            sysidMonthlyDayType: null,
            occurrences: occurences,
            interval: interval,
            onSunday: recurrenceWeekdaysSelected
                .toList()
                .contains(AqWeekday.SUNDAY.id),
            onMonday: recurrenceWeekdaysSelected
                .toList()
                .contains(AqWeekday.MONDAY.id),
            onTuesday: recurrenceWeekdaysSelected
                .toList()
                .contains(AqWeekday.TUESDAY.id),
            onWednesday: recurrenceWeekdaysSelected
                .toList()
                .contains(AqWeekday.WEDNESDAY.id),
            onThursday: recurrenceWeekdaysSelected
                .toList()
                .contains(AqWeekday.THURSDAY.id),
            onFriday: recurrenceWeekdaysSelected
                .toList()
                .contains(AqWeekday.FRIDAY.id),
            onSaturday: recurrenceWeekdaysSelected
                .toList()
                .contains(AqWeekday.SATURDAY.id),
            theDate: null,
            aqStartDate: startDateRecurringSelected.value,
            aqEndDate: occurences == null ?
                endByDateRecurringSelected.value.copyWith(hour: 0, minutes: 0) : null);
        break;
      case AqRecurrenceSelectionType.MONTHLY:
        final patternType =
            monthlyPatternTypeSelected.value == AqMonthOccurrenceType.THE
                ? AqRecurrencePatternType.MONTHLY_DAY
                : AqRecurrencePatternType.MONTHLY_DATE;
        final theDate = patternType == AqRecurrencePatternType.MONTHLY_DATE
            ? monthlyDayPatternSelected.value
            : null;
        final sysidMonthlyDayOccurrenceType =
            patternType == AqRecurrencePatternType.MONTHLY_DAY
                ? monthlyDayOccurencePatternTypeSelected.value.id
                : null;
        final sysidMonthlyDayType =
            patternType == AqRecurrencePatternType.MONTHLY_DAY
                ? monthlyDayTypeSelected.value.id
                : null;
        recurrenceRule = AqRecurrenceRule(
            sysidRecurrencePatternType: patternType.id,
            sysidRecurrenceRule:
                _initialValue?.recurrenceRule?.sysidRecurrenceRule,
            sysidMonthlyDayOccurrenceType: sysidMonthlyDayOccurrenceType,
            sysidMonthlyDayType: sysidMonthlyDayType,
            occurrences: occurences,
            interval: interval,
            onSunday: null,
            onMonday: null,
            onTuesday: null,
            onWednesday: null,
            onThursday: null,
            onFriday: null,
            onSaturday: null,
            theDate: theDate,
            aqStartDate: startDateRecurringSelected.value,
            aqEndDate: occurences == null ?
                endByDateRecurringSelected.value.copyWith(hour: 0, minutes: 0) : null);
        break;
    }
    return recurrenceRule;
  }
}
