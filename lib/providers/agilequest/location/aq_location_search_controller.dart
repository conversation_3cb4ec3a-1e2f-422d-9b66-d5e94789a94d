import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/services/agilequest/aq_api_client.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/location/aq_search_locations_request.dart';

import '../../../models/agilequest/location/aq_location_data.dart';
import '../../../models/agilequest/types/aq_reserve_const.dart';
import '../aqhome_controller.dart';

class AqLocationSearchController extends GetxController {
  TextEditingController searchController = TextEditingController();
  AqHomeController aqHomeCtrl = Get.find<AqHomeController>();
  var locationsToDisplay = <AqLocationData>[].obs;
  final _locationsFetched = <AqLocationData>[];
  var isLoading = false.obs;

  void uploadLocations() async {
    isLoading(true);
    _locationsFetched.clear();
    locationsToDisplay.clear();

    final body = AqSearchLocationsRequest(
      sysidApplicationUser: aqHomeCtrl.aqUserData.value.sysidApplicationUser ?? -1,
      sysidAllocRelationshipTypes: [1, 2, 3],
      locationDataTypeValue: AqLocationDataType.topLocationsActiveAllocated.id,
    );

    final locationsResponse = await AqApiClient.searchLocations(body);
    _locationsFetched.addAll(locationsResponse.locations ?? []);
    locationsToDisplay.addAll(locationsResponse.locations ?? []);

    isLoading(false);
  }

  void filterLocations(String searchText) async {
    isLoading(true);

    locationsToDisplay.clear();
    if (searchText == '') {
      locationsToDisplay.addAll(_locationsFetched);
    } else {
      final filteredLocations = _locationsFetched.where(
        (location) => location.name.toLowerCase().contains(searchText.trim().toLowerCase()),
      );
      locationsToDisplay.addAll(filteredLocations);
    }

    isLoading(false);
  }

  @override
  void dispose() {
    super.dispose();
    searchController.dispose();
  }
}
