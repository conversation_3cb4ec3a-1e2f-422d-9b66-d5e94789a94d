import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/auth/aq_user_data.dart';

import '../../models/agilequest/auth/aq_app_properties_data.dart';
import '../../models/agilequest/auth/aq_currency_type_data.dart';
import '../../models/agilequest/auth/aq_user_auth_rule_data.dart';

class AqHomeController extends GetxController {
  var aqUserData = AqUserData().obs;
  RxList<AqUserAuthRulesData> userRules = <AqUserAuthRulesData>[].obs;
  RxList<AqAppPropertiesData> aqAppProperties = <AqAppPropertiesData>[].obs;
  RxList<AqCurrencyTypeData> aqCurrencyTypes = <AqCurrencyTypeData>[].obs;
  var aqAuthRules = [].obs;
}
