import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/agilequest/user/aq_user_view_data.dart';
import 'package:tangoworkplace/services/agilequest/aq_api_client.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/user/aq_search_user_view_body.dart';

import '../../../models/agilequest/reserve/aq_invitee.dart';
import '../../ta_admin/label_controller.dart';

class AqAddParticipantsController extends GetxController {
  final LabelController talabel = Get.find<LabelController>();
  TextEditingController searchController = TextEditingController();
  RxList<AqInvitee> inviteesSelected = <AqInvitee>[].obs;
  RxList<AqUserViewData> inviteesFound = <AqUserViewData>[].obs;
  Rx<bool> isLoading = false.obs;

  void setupInitial(List<AqInvitee> invitees) {
    inviteesSelected(invitees);
  }

  void searchForUsers() async {
    final value = searchController.text.trim();
    if (value.isNotEmpty && value.length >= 3) {
      isLoading(true);
      final name =
          value.contains(' ') ? value.substring(0, value.indexOf(' ')) : value;
      final surname =
          value.contains(' ') ? value.substring(value.indexOf(' ') + 1) : name;

      final body = AqSearchUserViewBody(
          maxRows: -1,
          startRow: 0,
          firstName: name,
          lastName: surname.isNotEmpty == true ? surname : name,
          sortAscending: true,
          sortColumn: 'LAST_NAME');
      final users = await AqApiClient.searchUserViews(body);
      if(users.views?.isEmpty == true && value.isEmail) {
        final invitee = AqUserViewData(emailAddress: value);
        inviteesFound.clear();
        inviteesFound.add(invitee);
      } else {
        inviteesFound.clear();
        inviteesFound.addAll(users.views ?? []);
      }
      isLoading(false);
    } 
  }

  void selectUser(AqUserViewData invitee) {
    final inviteeToAdd = AqInvitee(
        sysidHost: invitee.sysidUser ?? 0,
        emailAddress: invitee.emailAddress ?? '',
        firstName: invitee.firstName,
        lastName: invitee.lastName,
        middleInitial: invitee.middleInitial,
        isOwner: false,
    );
    inviteesSelected.add(inviteeToAdd);
    inviteesFound.refresh();
  }

  void deselectUser(String email) {
    inviteesSelected.removeWhere((element) => element.emailAddress == email);
    inviteesFound.refresh();
  }

  void clearEverything() {
    inviteesFound.clear();
    searchController.text = '';
  }
}
