import 'package:get/get.dart';

import '../../../models/agilequest/auth/aq_auth_rules_const.dart';
import '../../../models/agilequest/auth/aq_user_auth_rule_data.dart';
import '../aqhome_controller.dart';

class AqUserExtension {
  AqUserExtension._();
  static final AqUserExtension au = AqUserExtension._();
  AqHomeController aqHomeCtrl = Get.find<AqHomeController>();

  bool canModifyFinalReservations() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.MODIFY_FINAL_RESERVATIONS);
  }

  bool canManageReviews() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.MANAGE_RESOURCE_REVIEWS);
  }

  bool canCheckInWorkspaces() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.CAN_CHECKIN_WS_WITH_MOBILE_APP);
  }

  bool canCheckInCollbarationSpaces() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.CAN_CHECKIN_CR_WITH_MOBILE_APP);
  }

  bool canCheckInReservationWithRules(int? type) {
    bool res = false;

    switch (type) {
      case 1: //RoomType.CONFERENCE
        res = canCheckInCollbarationSpaces();
        break;
      case 2: //RoomType.WORKSPACE
        res = canCheckInWorkspaces();
        break;
      default:
        res = true;
        break;
    }
    return res;
  }

  bool canManageAnotherUsersReservations() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.MANAGE_ANOTHERS_RESERVATIONS);
  }

  bool canManageAnotherUsersRequests() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.MANAGE_ANOTHERS_REQUESTS);
  }

  bool canCancelNED() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.CANCEL_NO_END_DATE_RESERVATION);
  }

  bool canCancelNoEndReservations() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.CANCEL_NO_END_DATE_RESERVATION);
  }

  bool canManageOwnProfile() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.MANAGE_OWN_USER_PROFILE);
  }

  bool canMakeAndModifyRecurringReservations() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.MAKE_RECURRING_RESERVATIONS);
  }

  bool canCreateWhenAssetClosed() {
    return aqHomeCtrl.userRules
        .any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.ALLOW_RESERVATIONS_ON_CLOSED_TIME);
  }

  bool canCreateAndManageLongReservations() {
    return aqHomeCtrl.userRules.any((AqUserAuthRulesData a) => a.sysidAuthorizationRule == AqAuthRulesConst.MAKE_AND_MANAGE_LONG_TERM_RESV);
  }
}
