import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AqPhotoViewerController extends GetxController {
  
  Rx<int> imageIndexSelected = 0.obs;
  final pageController = PageController();

  void initialSetup() {
    imageIndexSelected(0);
    
    pageController.addListener(() {
      final page = pageController.page ?? 0.0;
      if((page % 1) == 0) {
        imageIndexSelected(page.toInt());
      }
    });
  }
}
