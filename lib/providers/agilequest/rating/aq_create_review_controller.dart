import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tangoworkplace/models/agilequest/reviews/aq_review_view.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/services/agilequest/aq_api_client.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/review/aq_review_request_body.dart';

import '../../../models/agilequest/date/aq_date.dart';
import '../../../models/agilequest/types/dates/aq_date_pattern_type.dart';
import '../../ta_admin/label_controller.dart';

class AqCreateReviewController extends GetxController {
  var dateOfReservationController = TextEditingController();
  var commentController = TextEditingController();
  final LabelController talabel = Get.find<LabelController>();
  AqReviewView? _reviewToEdit;
  int _resourceId = 0;

  Rx<int> ratingValueSelected = 0.obs;
  Rx<String> reviewComment = ''.obs;
  Rx<AqDate> dateOfReservationSelected =
      AqDate.fromDate(DateTime.now(), '').obs;
  Rx<bool> isLoading = false.obs;
  Rx<bool> isReservationDateSwitched = false.obs;

  void initialSetup(AqReviewView? reviewToEdit, int resourceId) {
    _reviewToEdit = reviewToEdit;
    _resourceId = resourceId;
    if (reviewToEdit != null) {
      reviewComment(reviewToEdit.comment);
      commentController.text = reviewToEdit.comment ?? '';
      ratingValueSelected(reviewToEdit.rating);
      if (reviewToEdit.aqStartTime != null) {
        isReservationDateSwitched(true);
        dateOfReservationSelected(reviewToEdit.aqStartTime);
        dateOfReservationController.text = reviewToEdit.aqStartTime!
            .displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);
      } else {
        isReservationDateSwitched(false);
        dateOfReservationSelected(AqDate.fromDate(DateTime.now(), ''));
        dateOfReservationController.text = '';
      }
    } else {
      reviewComment('');
      commentController.text = '';
      dateOfReservationController.text = '';
      ratingValueSelected(0);
      isReservationDateSwitched(false);
      dateOfReservationSelected(AqDate.fromDate(DateTime.now(), ''));
    }
  }

  void switchDateReservation(bool isSelected) {
    isReservationDateSwitched(isSelected);
    dateOfReservationController.text = dateOfReservationSelected.value.displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);
  }

  void commentChanged(String value) {
    reviewComment(value);
  }

  void ratingChanged(int index) {
    ratingValueSelected(index + 1);
  }

  void dateOfReservationChanged(String date) {
    final dateConverted = DateFormat("MM/dd/yyyy").parse(date);
    final aqDate = dateOfReservationSelected.value.copyWith(
        year: dateConverted.year,
        month: dateConverted.month,
        dayOfMonth: dateConverted.day);
    dateOfReservationController.text =
        aqDate.displayDateString(talabel, pattern: AqDatePatternType.FULL_DATE);
    dateOfReservationSelected(aqDate);
  }

  Future<void> submitData() async {
    isLoading(true);
    if(_reviewToEdit != null) {
      final body = AqReviewRequestBody(
        sysidReview: _reviewToEdit?.sysidReview,
        sysidResource: _reviewToEdit?.sysidResource ?? 0,
        comment: commentController.text.isEmpty ? null : commentController.text,
        rating: ratingValueSelected.value,
        aqStartTime: isReservationDateSwitched.value ? dateOfReservationSelected.value : null);
      await AqApiClient.updateReviews(body);
      isLoading(false);
    } else {
      final body = AqReviewRequestBody(
        sysidReview: null,
        sysidResource: _resourceId,
        comment: commentController.text.isEmpty ? null : commentController.text,
        rating: ratingValueSelected.value,
        aqStartTime: isReservationDateSwitched.value ? dateOfReservationSelected.value : null);
      await AqApiClient.createReviews(body);
      isLoading(false);
    }
  }
}
