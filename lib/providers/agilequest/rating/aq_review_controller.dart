import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/services/agilequest/backendData/requests/review/aq_reviews_request_body.dart';

import '../../../models/agilequest/reviews/aq_review_view.dart';
import '../../../services/agilequest/aq_api_client.dart';

class AqReviewController extends GetxController {
  int _startRow = 0;
  int _maxRows = 10;
  int _oldStartRow = 0;
  bool hasNext = true;
  bool isInitial = true;

  RxList<AqReviewView> reviewsList = <AqReviewView>[].obs;
  Rx<double> ratingValue = 0.0.obs;
  Rx<int> amountValue = 0.obs;
  Rx<bool> isLoading = false.obs;
  Rx<bool> isLoadingMore = false.obs;

  final ScrollController scrollController = ScrollController();
  int idResource = 0;

  void initState() {
    scrollController.addListener(_onScroll);
  }

  _onScroll() {
    if (scrollController.offset >=
        scrollController.position.maxScrollExtent &&
        !scrollController.position.outOfRange) {
      uploadComments(idResource);
    }
  }

  Future<void> uploadComments(int sysidResource, {bool isManaging = false}) async {
    idResource = sysidResource;
    if (hasNext) {
      _oldStartRow = _startRow;
      if(isInitial || isManaging) isLoading(true);
      if(!isInitial && !isManaging) isLoadingMore(true);
      final body = AqReviewsRequestBody(
          startRow: _startRow, maxRows: _maxRows, sysidResource: sysidResource);
      final reviews = await AqApiClient.getReviews(body);
      if(isInitial || isManaging) isLoading(false);
      if(!isInitial && !isManaging) isLoadingMore(false);
      _startRow = reviews.nextStartRow ?? 0;
      _maxRows = 10;
      hasNext = reviews.hasAdditional;
      if(isManaging) {
        reviewsList.clear();
        reviewsList.addAll(reviews.views); 
      } else {
         reviewsList.addAll(reviews.views);
      }
      ratingValue(reviews.averageRating ?? 0);
      amountValue(reviews.totalReviews);
      isInitial = false;
    }
  }

  Future<void> removeReview(int reviewId) async {
    await AqApiClient.deleteReviews(reviewId);
    hasNext = true;
    _startRow = 0;
    _maxRows = _oldStartRow + 10;
    await uploadComments(idResource, isManaging: true);
  }

  Future<void> refreshReviews() async {
    hasNext = true;
    _startRow = 0;
    _maxRows = _oldStartRow + 10;
    await uploadComments(idResource, isManaging: true); 
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }
}