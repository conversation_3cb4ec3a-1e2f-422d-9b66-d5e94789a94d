import 'dart:async';
import 'dart:collection';
import 'package:clippy_flutter/clippy_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'dart:ui' as ui;
import 'dart:typed_data';

import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:map_launcher/map_launcher.dart' as maplaunch;
import 'package:permission_handler/permission_handler.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/sitemanagement/locate/locatedata.dart';
import 'package:tangoworkplace/screens/sitemanagement/site/site_details.dart';
import 'package:tangoworkplace/screens/sitemanagement/target/target_details.dart';

import '../../../common/component_utils.dart';
import '../../../common/widgets/component_widgets/custommapinfowindowctrl.dart';

class LocateController extends GetxController {
  var mapzoom = 12.0.obs;
  Rx<MapType> mapType = MapType.normal.obs;
  var locaccess = false.obs;

  var initloading = false.obs;
  var maplocflag = 'current'.obs;
  var mapflag = true.obs;
  late GoogleMapController gmapcontroller;
  Completer<GoogleMapController> gcompleter = Completer();

  RxSet<Marker> markers = <Marker>{}.obs;

  RxList<String> entityfilterlist = <String>[].obs;
  RxList<String> tempFilters = <String>[].obs;
  var sitesfilter = false.obs;
  var storesfilter = false.obs;
  var targetsfilter = false.obs;

  var lattitude = 0.0.obs;
  var longitude = 0.0.obs;
  var crntlattitude = 0.0.obs;
  var crntlongitude = 0.0.obs;

  var searchController = TextEditingController();
  var dataloadflag = 'mapcurrent'.obs;

  RxList<LocateData> entityList = <LocateData>[].obs;
  RxList<LocateData> entityTripList = <LocateData>[].obs;
  var isLoading = false.obs;
  var crntaddrsflag = true.obs;

  CustomInfoWindowController customInfoWindowController = CustomInfoWindowController();

  late Uint8List targetmarker;
  late Uint8List sitemarker;
  late Uint8List storemarker;

  @override
  Future<void> onInit() async {
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    try {
      var brandid = await SharedPrefUtils.readPrefStr(ConstHelper.brandIdvar);

      var brand = (brandid != null && brandid != '' ? brandid?.toString() : '0')!;
      debugPrint('----------onReady----------');

      targetmarker =
          await CommonUtils.networkImageToByte(await ApiService.mapiconapi('target', brand), height: 60, width: 60);
      sitemarker =
          await CommonUtils.networkImageToByte(await ApiService.mapiconapi('site', brand), height: 60, width: 60);
      storemarker =
          await CommonUtils.networkImageToByte(await ApiService.mapiconapi('store', brand), height: 60, width: 60);
    } catch (e) {
      Uint8List marker = await CommonUtils.getBytesFromAsset('lib/icons/locate_loc.png', 60);
      targetmarker = marker;
      sitemarker = marker;
      storemarker = marker;
    }
  }

  @override
  void onClose() {
    debugPrint('------------onClose--------');
  }

  void onMapTypeButtonPressed() {
    mapType.value = mapType.value == MapType.normal ? MapType.satellite : MapType.normal;
  }

  Future determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return ComponentUtils.showpopup(msg: 'Location services are disabled...');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        debugPrint('-------------Location permissions are denied-------------------');
        return ComponentUtils.showpopup(msg: 'Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      debugPrint('------------- denied forever-------------------');
      return ComponentUtils.showpopup(msg: 'Location permissions are permanently denied..');
    }
    var locperaccess = await Permission.location.request();
    debugPrint('location isDenied----${locperaccess.isDenied}');
    debugPrint('location isGranted----${locperaccess.isGranted}');
    debugPrint('location isLimited----${locperaccess.isLimited}');
    debugPrint('location isPermanentlyDenied----${locperaccess.isPermanentlyDenied}');
    debugPrint('location isRestricted----${locperaccess.isRestricted}');
    debugPrint('location isBlank----${locperaccess.isBlank}');
    if (!locperaccess.isGranted) {
      ComponentUtils.showpopup(msg: 'Please enable location ...');
    } else if (locperaccess.isGranted) {
      locaccess.value = true;
      mapType.value = MapType.normal;
      mapzoom.value = 12.0;
      searchController.text = '';

      initloading.value = true;
      maplocflag.value = 'current';
      return Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.best,
              forceAndroidLocationManager: Get.width > 600 && GetPlatform.isAndroid ? false : true)
          .then((Position pos) {
        lattitude.value = pos.latitude;
        longitude.value = pos.longitude;
        crntlattitude.value = pos.latitude;
        crntlongitude.value = pos.longitude;

        markers.value.clear();
        Marker marker = Marker(
          markerId: const MarkerId('Current'),
          position: LatLng(pos.latitude ?? 0.0, pos.longitude ?? 0.0),
          icon: BitmapDescriptor.defaultMarker,
          //onTap: () {
          //do something here
          // }
        );

        markers.value.add(marker);

        initloading.value = false;
        debugPrint('----current-------${pos.latitude}------${pos.longitude}');
      }).catchError((e) {
        initloading.value = false;
        debugPrint(e);
      });
    }
  }

  Future getEntityData() async {
    debugPrint('------------getEntityData--------');

    entityList.clear();
    List<LocateData> data;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap = HashMap<String, dynamic>();
    String api = nearestentitiesurl;
    initloading.value = true;
    mapType.value = MapType.normal;
    mapzoom.value = 12.0;
    markers.value.clear();
    if (dataloadflag.value == 'search') {
      maplocflag.value = 'search';
      api = locateentitiesurl;
      dynamicMap['a'] = ['full_address', 'CONTAINS', searchController.text];
    } else {
      maplocflag.value = 'current';
      dynamicMap = {
        'a': ['p_latitude', 'EXACT', crntlattitude.value.toString()],
        'b': ['p_longitude', 'EXACT', crntlongitude.value.toString()],
        'c': ['p_distance', 'EXACT', '20'],
      };
    }
    if (entityfilterlist.value.isNotEmpty) {
      dynamicMap['x'] = ['entity_type', 'IN', entityfilterlist.value.join(',')];
    } else {
      dynamicMap['x'] = ['entity_type', 'EXACT', 'XXXYY'];
    }

    var payloadObj = CommonServices.getFilter(dynamicMap, size: 100);

    try {
      resMap = await CommonServices.fetchDynamicApi(api, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            data = tagObjsJson.map((tagJson) => LocateData.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${data.length}');
            entityList.addAll(data);
            int i = 0;
            debugPrint(' maplocflag.value------ ${maplocflag.value}');
            for (var loc in data) {
              if (loc.latitude != null && loc.longitude != null) {
                //debugPrint('name ${loc.entityName}');
                i++;
                if (i == 1 && maplocflag.value != 'current') {
                  longitude.value = loc.longitude!;
                  lattitude.value = loc.latitude!;
                }
                if (maplocflag.value == 'current') {
                  longitude.value = crntlongitude.value;
                  lattitude.value = crntlattitude.value;
                  Marker cmarker = Marker(
                    markerId: const MarkerId('Current'),
                    position: LatLng(crntlattitude.value ?? 0.0, crntlongitude.value ?? 0.0),
                    icon: BitmapDescriptor.defaultMarker,
                    //onTap: () {
                    //do something here
                    // }
                  );

                  markers.value.add(cmarker);
                }
                markers.value.add(
                  Marker(
                    markerId: MarkerId(loc.entityId.toString()),
                    position: LatLng(loc.latitude!, loc.longitude!),
                    icon: loc.entityType?.toUpperCase() == 'TARGET'
                        ? BitmapDescriptor.fromBytes(targetmarker)
                        : loc.entityType?.toUpperCase() == 'SITE'
                            ? BitmapDescriptor.fromBytes(sitemarker)
                            : loc.entityType?.toUpperCase() == 'STORE'
                                ? BitmapDescriptor.fromBytes(storemarker)
                                : BitmapDescriptor.defaultMarker,
                    onTap: () {
                      mapinfowindow(loc);
                    },
                  ),
                );
              }
            }
          }
        } else {
          entityList.clear();
        }
      } else {
        entityList.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      initloading.value = false;
      // update();
    }
  }

  addmarker(String type) {
    Marker marker = Marker(
      markerId: const MarkerId('sss'),
      position: const LatLng(32.9407, -96.9847),
      icon: BitmapDescriptor.defaultMarker,
      //onTap: () {
      //do something here
      // }
    );
    debugPrint('-----clicked------');
    markers.add(marker);
  }

  void mapinfowindow(LocateData loc) {
    LatLng latLng = LatLng(loc.latitude!, loc.longitude!);
    String? entitynm = loc.entityName != null && loc.entityName!.length > 15
        ? '${loc.entityName!.substring(0, 15)}...'
        : loc.entityName;
    return customInfoWindowController.addInfoWindow!(
      Column(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              width: double.infinity,
              height: double.infinity,
              child: Padding(
                padding: const EdgeInsets.all(1.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          loc.entityType!,
                          style: TextStyle(color: ComponentUtils.primecolor, fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                        Text(
                          entitynm ?? '',
                          style: TextStyle(fontSize: 10),
                        ),
                      ],
                    ),
                    GestureDetector(
                      child: Icon(Icons.info_outline),
                      onTap: () {
                        customInfoWindowController.hideInfoWindow!();
                        // showEntitybottomsheet(loc);
                        showEntitydatapopup(loc);
                        debugPrint('---------------------------');
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          Triangle.isosceles(
            edge: Edge.BOTTOM,
            child: Container(
              color: Colors.white,
              width: 20.0,
              height: 10.0,
            ),
          ),
        ],
      ),
      latLng,
    );
  }

  Widget entityDataBox(LocateData loc) {
    return Container(
        //height: 150,
        child: Column(
      children: [
        Text(
          loc.entityName!,
          style: TextStyle(color: ComponentUtils.primary, fontWeight: FontWeight.bold, fontSize: 12),
        ),
        Text(
          loc.address!,
          style: TextStyle(color: ComponentUtils.primary, fontSize: 12),
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              loc.city!,
              style: TextStyle(color: ComponentUtils.primary, fontSize: 12),
            ),
            Text(
              loc.state!,
              style: TextStyle(color: ComponentUtils.primary, fontSize: 12),
            )
          ],
        ),
        // Row(
        //   mainAxisSize: MainAxisSize.max,
        //   mainAxisAlignment: MainAxisAlignment.center,
        //   crossAxisAlignment: CrossAxisAlignment.center,
        //   children: [
        //     Text(
        //       loc.entityNumber?.toString() ?? '',
        //       style: TextStyle(color: ComponentUtils.primary, fontSize: 12),
        //     )
        //   ],
        // ),
        const SizedBox(
          height: 25,
        ),
        if (crntlattitude.value != 0.0)
          Container(
            margin: const EdgeInsets.fromLTRB(0, 0, 0, 1),
            padding: const EdgeInsets.all(15.0),
            // decoration: BoxDecoration(
            //   border: Border.all(color: Colors.black38),
            //   borderRadius: BorderRadius.circular(15.0),
            // ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Text(
                //   'Point:',
                //   style: TextStyle(fontSize: 11, fontWeight: FontWeight.bold, color: ComponentUtils.primary),
                // ),
                // Column(
                //   mainAxisSize: MainAxisSize.max,
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   crossAxisAlignment: CrossAxisAlignment.center,
                //   children: [
                GestureDetector(
                  child: infowindowContainer('From here', null),
                  onTap: () async {
                    debugPrint('--------Directions From here---------');
                    final List<maplaunch.AvailableMap> availableMaps = await maplaunch.MapLauncher.installedMaps;

                    bool flag = true;
                    for (var element in availableMaps) {
                      debugPrint('element-------$element');
                      if (element.mapName.contains('Google')) {
                        element.showDirections(
                            destination: maplaunch.Coords(crntlattitude.value, crntlongitude.value),
                            origin: maplaunch.Coords(loc.latitude!, loc.longitude!),
                            directionsMode: maplaunch.DirectionsMode.driving,
                            originTitle: loc.entityName?.toString() ?? '',
                            destinationTitle: 'Current Address');
                        flag = false;
                      }
                    }
                    if (flag) {
                      await availableMaps.first.showDirections(
                          destination: maplaunch.Coords(crntlattitude.value, crntlongitude.value),
                          origin: maplaunch.Coords(loc.latitude!, loc.longitude!),
                          directionsMode: maplaunch.DirectionsMode.driving,
                          originTitle: loc.entityName?.toString() ?? '',
                          destinationTitle: 'Current Address');
                    }
                  },
                ),
                const SizedBox(width: 20),
                GestureDetector(
                  child: infowindowContainer('To here', null),
                  onTap: () async {
                    debugPrint('--------Directions To here---------');
                    final List<maplaunch.AvailableMap> availableMaps = await maplaunch.MapLauncher.installedMaps;
                    bool flag = true;
                    for (var element in availableMaps) {
                      debugPrint('element-------$element');
                      if (element.mapName.contains('Google')) {
                        element.showDirections(
                            destination: maplaunch.Coords(loc.latitude!, loc.longitude!),
                            origin: maplaunch.Coords(crntlattitude.value, crntlongitude.value),
                            directionsMode: maplaunch.DirectionsMode.driving,
                            destinationTitle: loc.entityName?.toString() ?? '',
                            originTitle: 'Current Address');
                        flag = false;
                      }
                    }
                    if (flag) {
                      await availableMaps.first.showDirections(
                          destination: maplaunch.Coords(loc.latitude!, loc.longitude!),
                          origin: maplaunch.Coords(crntlattitude.value, crntlongitude.value),
                          directionsMode: maplaunch.DirectionsMode.driving,
                          destinationTitle: loc.entityName?.toString() ?? '',
                          originTitle: 'Current Address');
                    }
                  },
                ),
              ],
            ),
          ),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              child: infowindowContainer('Close', Icons.close),
              onTap: () {
                Get.back();
              },
            ),
            GestureDetector(
              child: infowindowContainer('Trip', Icons.add),
              onTap: () {
                debugPrint('--------Trip---------');
                int? eid = loc.entityId;
                bool flag = true;
                for (var element in entityTripList) {
                  if (element.entityId == eid) flag = false;
                }
                if (flag) entityTripList.value.add(loc);
                crntaddrsflag.value = true;
                tripplanwindow(loc);
              },
            ),
            GestureDetector(
              child: infowindowContainer('More', Icons.info_outline),
              onTap: () {
                debugPrint('--------More---------');
                if (loc.entityType == 'TARGET') {
                  Get.back();
                  Get.to(() => TargetDetails(
                        nav_from: 'locate',
                        targetid: loc.entityId,
                        targetname: loc.entityName,
                      ));
                } else if (loc.entityType == 'SITE') {
                  Get.back();
                  Get.to(() => SiteDetails(
                        nav_from: 'locate',
                        siteid: loc.entityId,
                        sitename: loc.entityName,
                      ));
                } else {
                  return;
                }
              },
            ),
          ],
        ),
      ],
    ));
  }

  Widget infowindowContainer(String txt, IconData? icon) {
    return Container(
      constraints: const BoxConstraints(
        minWidth: 25,
        minHeight: 30,
      ),
      // width: 70,
      // height: 35,
      //margin: EdgeInsets.fromLTRB(20, 5, 20, 5),
      padding: const EdgeInsets.only(top: 3, left: 5, right: 7, bottom: 3),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black26),
        color: Colors.white,
        borderRadius: BorderRadius.circular(5.0),
        boxShadow: const [
          BoxShadow(
            color: Colors.grey,
            blurRadius: 5,
            //offset: Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        // mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (icon != null) Icon(icon, color: ComponentUtils.primecolor),
          if (icon != null)
            const SizedBox(
              width: 4,
            ),
          Text(
            txt,
            style: TextStyle(fontSize: 11, color: ComponentUtils.primecolor),
          ),
        ],
      ),
    );
  }

  void showEntitydatapopup(LocateData loc) {
    Get.defaultDialog(
      title: loc.entityType!,
      titleStyle: const TextStyle(
        fontSize: 15, //color: ComponentUtils.primecolor,
      ),
      content: entityDataBox(loc),
    );
  }

  void showEntitybottomsheet(LocateData loc) {
    Get.bottomSheet(
      entityDataBox(loc),
      //barrierColor: Colors.red[50],
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(35), topRight: Radius.circular(35)),
          side: BorderSide(
            width: 1,
            color: Colors.grey,
          )),
      enableDrag: false,
    );
  }

  void tripplanwindow(LocateData loc) {
    Get.defaultDialog(
      backgroundColor: CommonUtils.createMaterialColor(const Color(0XFFf2f2f2)),
      title: 'Trip Details',
      titleStyle: const TextStyle(
        fontSize: 15, //color: ComponentUtils.primecolor,
      ),
      content: SizedBox(
        width: Get.width * 0.68,
        height: Get.height * 0.68,
        child: Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                if (crntlattitude.value != 0.0)
                  const Text(
                    'Departure :',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            Obx(
              () => crntaddrsflag.value && (crntlattitude.value != 0.0)
                  ? Card(
                      child: ListTile(
                        title: const Text(
                          'Current Address',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                        trailing: IconButton(
                          icon: Icon(Icons.delete, color: ComponentUtils.primecolor),
                          onPressed: () {
                            crntaddrsflag.value = false;
                          },
                        ),
                      ),
                    )
                  : Container(),
            ),
            const Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(
                  'Destinations :',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
              ],
            ),
            Expanded(
              child: Obx(
                () => ReorderableListView.builder(
                    itemCount: entityTripList.length,
                    itemBuilder: (context, index) {
                      return triplisttile(context, entityTripList[index]);
                    },
                    // The reorder function
                    onReorder: (oldIndex, newIndex) {
                      // setState(() {
                      if (newIndex > oldIndex) {
                        newIndex = newIndex - 1;
                      }
                      final element = entityTripList.removeAt(oldIndex);
                      entityTripList.insert(newIndex, element);
                      // });
                    }),
              ),
            ),
          ],
        ),
      ),
      actions: [
        Obx(
          () => Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                child: infowindowContainer('Close', null),
                onTap: () {
                  Get.back();
                },
              ),
              const SizedBox(
                width: 5,
              ),
              if ((crntaddrsflag.value && entityTripList.isNotEmpty) ||
                  (!crntaddrsflag.value && entityTripList.length > 1))
                GestureDetector(
                  child: infowindowContainer('Plan Route', null),
                  onTap: () async {
                    debugPrint('--------------------');
                    //List<maplaunch.Coords> Coords = [];
                    List<maplaunch.Waypoint> waypoints = [];
                    String? dTitle;
                    String? oTitle;
                    late maplaunch.Coords destination;
                    int i = 0;
                    int j = entityTripList.value.length;
                    maplaunch.Coords origin = maplaunch.Coords(crntlattitude.value ?? 0.0, crntlongitude.value ?? 0.0);
                    for (var item in entityTripList) {
                      i++;

                      if (i == 1 && ((crntlattitude.value == 0.0) || crntaddrsflag.isFalse)) {
                        origin = maplaunch.Coords(item.latitude!, item.longitude!);
                        oTitle = item.entityName?.toString() ?? '';
                      } else if (i == j) {
                        destination = maplaunch.Coords(item.latitude!, item.longitude!);
                        dTitle = item.entityName?.toString() ?? '';
                      } else {
                        // waypoints.add(maplaunch.Coords(item.latitude!, item.longitude!));

                        waypoints.add(maplaunch.Waypoint(item.latitude!, item.longitude!, ''));
                      }
                    }

                    bool flag = true;
                    final List<maplaunch.AvailableMap> availableMaps = await maplaunch.MapLauncher.installedMaps;
                    for (var element in availableMaps) {
                      debugPrint('element-------$element');
                      if (element.mapName.contains('Google')) {
                        element.showDirections(
                          destination: destination,
                          destinationTitle: dTitle,
                          originTitle: oTitle,
                          origin: origin,
                          waypoints: waypoints,
                          directionsMode: maplaunch.DirectionsMode.driving,
                        );
                        flag = false;
                      }
                    }
                    if (flag) {
                      await availableMaps.first.showDirections(
                        destination: destination,
                        destinationTitle: dTitle,
                        originTitle: oTitle,
                        origin: origin,
                        waypoints: waypoints,
                        directionsMode: maplaunch.DirectionsMode.driving,
                      );
                    }
                  },
                ),
              const SizedBox(
                width: 4,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget triplisttile(BuildContext context, LocateData loc) {
    return Card(
      key: ValueKey(loc),
      child: ListTile(
        title: Text(
          loc.address ?? '',
          style: const TextStyle(fontSize: 12),
        ),
        subtitle: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              loc.city ?? '',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(
              width: 2,
            ),
            Text(
              loc.state ?? '',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing:
            ((crntaddrsflag.value && entityTripList.isNotEmpty) || (!crntaddrsflag.value && entityTripList.length > 1))
                ? IconButton(
                    icon: Icon(Icons.delete, color: ComponentUtils.primecolor),
                    onPressed: () {
                      debugPrint('-----------');
                      entityTripList.removeWhere((element) => element.entityId == loc.entityId);
                    },
                  )
                : IconButton(
                    icon: const Icon(Icons.minimize, color: Colors.white),
                    onPressed: () {
                      debugPrint('-----------');
                      return;
                    },
                  ),
      ),
    );
  }
}
