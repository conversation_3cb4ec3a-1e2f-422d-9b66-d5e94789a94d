import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/sitemanagement/site/sitebvo.dart';
import 'package:tangoworkplace/models/sitemanagement/target/targetfv.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/sitemanagement/target/target_services.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../common/widgets/components.dart';
import '../../../models/lookup_values.dart';

class TargetFvController extends GetxController {
  var labelsloading = false.obs;
  var isDataLoading = false.obs;
  RxList<SiteBvo> sitebvoList = <SiteBvo>[].obs;
  Rx<TargetFV> fvdata = TargetFV().obs;

  RxList<LookupValues> devphasinglov = <LookupValues>[].obs;
  RxList<LookupValues> devprioritylov = <LookupValues>[].obs;
  RxList<LookupValues> recostintalov = <LookupValues>[].obs;
  RxList<LookupValues> abilityimagelov = <LookupValues>[].obs;
  RxList<LookupValues> trafficratinglov = <LookupValues>[].obs;
  RxList<LookupValues> devtargetyearlov = <LookupValues>[].obs;
  RxList<LookupValues> cextlo21lov = <LookupValues>[].obs;
  RxList<LookupValues> difficultyentrylov = <LookupValues>[].obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_TARGET_FIELDVALIDATION', 'target_fv');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  String lookupdata() {
    return 'DEV PHASING,Development Priority,Real Estate Cost in Trade Area,Difficulty of Entry,Ability to Get Image,Traffic Rating,Development Target Year,TGTLOV21';
  }

  Future getFieldValidData(int? targetid) async {
    try {
      isDataLoading(true);
      fvdata.value = TargetFV();
      String lookupcodes = lookupdata();
      var resMap = await TargetServices.gettargetfvdate(targetid, lookupcodes);
      clearlovs();
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['fvitems']['TARGET_FV'] as List;
          List<TargetFV> fvlist = tagObjsJson.map((tagJson) => TargetFV.fromJson(tagJson)).toList();
          debugPrint('fvlist-----${fvlist.first}');
          fvdata.value = fvlist.isNotEmpty ? fvlist.first : TargetFV();
          debugPrint('fvdata-----${fvdata.value}');
          if (fvdata.value.targetId != null) {
            var lovObj = resMap['lovData'] as Map?;
            if (lovObj != null) {
              var devpha = lovObj['DEV PHASING'] as List?;
              var devpri = lovObj['Development Priority'] as List?;
              var recoita = lovObj['Real Estate Cost in Trade Area'] as List?;
              var diffent = lovObj['Difficulty of Entry'] as List?;
              var abiim = lovObj['Ability to Get Image'] as List?;
              var trara = lovObj['Traffic Rating'] as List?;
              var devtayr = lovObj['Development Target Year'] as List?;
              var tgt21 = lovObj['TGTLOV21'] as List?;

              if (devpha != null && devpha.isNotEmpty) {
                devphasinglov.value = devpha.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              }
              devphasinglov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

              if (devpri != null && devpri.isNotEmpty) {
                devprioritylov.value = devpri.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              }
              devprioritylov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

              if (recoita != null && recoita.isNotEmpty) {
                recostintalov.value = recoita.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              }
              recostintalov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

              if (abiim != null && abiim.isNotEmpty) {
                abilityimagelov.value = abiim.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              }
              abilityimagelov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

              if (trara != null && trara.isNotEmpty) {
                trafficratinglov.value = trara.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              }
              trafficratinglov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

              if (devtayr != null && devtayr.isNotEmpty) {
                devtargetyearlov.value = devtayr.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              }
              devtargetyearlov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

              if (tgt21 != null && tgt21.isNotEmpty)
                cextlo21lov.value = tgt21.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              cextlo21lov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));

              if (diffent != null && diffent.isNotEmpty) {
                difficultyentrylov.value = diffent.map((tagJson) => LookupValues.fromJson(tagJson)).toList();
              }
              difficultyentrylov.value.insert(0, LookupValues(lookupCode: '', lookupValue: 'Select'));
            }
          }
        }
      }
    } catch (error) {
      debugPrint('error-----${error.toString()}');
      fvdata.value = TargetFV();
    } finally {
      isDataLoading(false);
    }
  }

  clearlovs() {
    devphasinglov.value.clear();
    devprioritylov.value.clear();
    recostintalov.value.clear();
    abilityimagelov.value.clear();
    trafficratinglov.value.clear();
    devtargetyearlov.value.clear();
    cextlo21lov.value.clear();
    difficultyentrylov.value.clear();
  }

  Future onSaveFieldValidation() async {
    debugPrint('field validation data    ${jsonEncode(fvdata.value)}');
    ProgressUtil.showLoaderDialog(Get.context!);
    bool flag = false;
    try {
      var result;

      result = await TargetServices.saveFieldValidationData(fvdata.value);
      debugPrint('result-------$result');
      if (result != null && result == 'success') {
        flag = true;
      }
    } catch (e) {
      debugPrint('$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
      if (flag) {
        ComponentUtils.showsnackbar(text: "Data saved successfully...");
      } else {
        ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
      }
    }
  }
}
