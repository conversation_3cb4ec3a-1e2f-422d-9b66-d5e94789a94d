import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../models/sitemanagement/target/targetview.dart';

class TargetGeneralController extends GetxController {
  var labelsloading = false.obs;
  var isloading = false.obs;
  Rx<TargetView> tragetrec = TargetView().obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_TARGET_GENERAL', 'target_general');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }
}
