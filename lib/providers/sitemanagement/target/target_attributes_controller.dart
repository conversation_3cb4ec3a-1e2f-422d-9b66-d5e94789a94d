import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/sitemanagement/target/targetbvo.dart';
import '../../../models/sitemanagement/target/targetview.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../../utils/request_api_utils.dart';

class TargetAttributesController extends GetxController {
  var labelsloading = false.obs;
  var isloading = false.obs;
  Rx<Targetbvo> tragetrec = Targetbvo().obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_TARGET_ATTRIBUTES', 'target_attributes');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  Future onSaveAttributes(Targetbvo t) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    var result = -1;
    try {
      Map m = {
        'source': 'target_attributes',
        'target_id': t.targetId,
        'sales_forecast': t.salesForecast,
        'sales_forecast2': t.salesForecast2,
        'sales_forecast3': t.salesForecast3,
      };
      result = await saveRecord(m);
    } catch (e) {
      debugPrint('$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    if (result == 0) {
      ComponentUtils.showsnackbar(text: "Data saved successfully...");
    } else {
      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }

  static Future saveRecord(Map m) async {
    int? status = -1;
    try {
      String bodyjson = json.encode(m);

      debugPrint(bodyjson);

      var resMap = await ApiService.post(saveattributesurl, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        status = resMap['status'] as int?;
        debugPrint('status>>>>>$status');
      }
    } catch (error) {}

    return status;
  }
}
