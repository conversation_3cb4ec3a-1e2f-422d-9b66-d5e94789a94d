import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../common/progess_indicator_cust.dart';
import '../../../models/sitemanagement/target/targetbvo.dart';
import '../../../models/sitemanagement/target/targetview.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class TargetDetailsController extends GetxController {
  var tabroles = <dynamic, dynamic>{}.obs;
  var mode = ''.obs;

  Rx<Targetbvo> targetrec = Targetbvo().obs;
  var isloading = false.obs;
  Future getTargetRecord(String? targetid) async {
    debugPrint('------------getTargetRecord--------');

    List<Targetbvo> trgt;
    String payloadObj;
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['target_id', 'EXACT', targetid],
    };

    payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(targetbvourl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            trgt = tagObjsJson.map((tagJson) => Targetbvo.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${trgt.length}');
            if (trgt.isNotEmpty) targetrec.value = trgt.first;
          }
        } else {}
      }
    } finally {
      //isloading.value = false;
    }
  }

  Future<String> getUserEntityEditAccess({required String entitytype, required String entityid, String? roFlag}) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    Map<dynamic, dynamic>? vmap;
    String res = 'view';
    tabroles.value = <dynamic, dynamic>{};
    try {
      vmap = await CommonServices.userEntityEditAccessService(entityid: entityid, entitytype: entitytype);
      debugPrint('getUserEntityEditAccess >>>>>>>$roFlag');
      if (vmap != null) {
        tabroles.value = vmap;
        if (vmap['result'] != null && vmap['result'] == 'Y' && vmap['role_flag'] != null && vmap['role_flag'] == 'Y') {
          res = 'Y';
        } else if (vmap['result'] != null && vmap['result'] == 'CN') res = 'view';

        if (res != 'error')
          res = (res.toUpperCase() == 'Y' && (roFlag?.toUpperCase() == 'N' || roFlag == null)) ? 'edit' : 'view';
      }
    } catch (e) {
      debugPrint('getUserEntityEditAccess Exception >>>>>>>>>>>$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    debugPrint('getUserEntityEditAccess >>>>>>>$res');
    mode.value = res;
    return res;
    //}
  }
}
