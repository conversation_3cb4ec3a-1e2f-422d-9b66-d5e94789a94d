import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/sitemanagement/site/siteview.dart';

import '../../../models/sitemanagement/target/targetview.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../../utils/user_secure_storage.dart';

class SiteDetailsController extends GetxController {
  Rx<SiteView> siterec = SiteView().obs;
  var isloading = false.obs;
  Future getSiteRecord(String? siteid) async {
    debugPrint('------------getSiteRecord--------');

    List<SiteView> siter;
    String payloadObj;
    Map<String, dynamic>? resMap;
    var user = UserContext.info()!.userName;
    Map<String, dynamic> dynamicMap = {
      'a': ['site_id', 'EXACT', siteid],
      'b': ['p_user_name', 'EXACT', user!.toUpperCase()],
      'c': ['p_mySitesFlag', 'EXACT', 'N'],
    };

    payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(sitesearchviewurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            siter = tagObjsJson.map((tagJson) => SiteView.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${siter.length}');
            if (siter.isNotEmpty) siterec.value = siter.first;
          }
        } else {}
      }
    } finally {
      //isloading.value = false;
    }
  }
}
