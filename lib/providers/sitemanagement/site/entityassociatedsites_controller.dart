import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/sitemanagement/site/sitebvo.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/services/common_services.dart';
import 'package:tangoworkplace/utils/connections.dart';

class EntityAssociatedSitesController extends GetxController {
  dynamic args = Get.arguments;

  RxList<SiteBvo> sitebvoList = <SiteBvo>[].obs;
  var isloading = false.obs;

  var labelsloading = false.obs;
  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_SITE_GENERAL', 'site_general');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  Future getSitesBvo(int? targetid) async {
    debugPrint('------------getSitesBvo--------');
    sitebvoList.clear();
    isloading.value = true;
    List<SiteBvo> rec;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap = {
      'a': ['target_id', 'EXACT', targetid?.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap, size: 500);

    try {
      resMap = await CommonServices.fetchDynamicApi(sitesbvourl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            rec = tagObjsJson.map((tagJson) => SiteBvo.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${rec.length}');
            sitebvoList.addAll(rec);
          }
        } else {
          sitebvoList.clear();
        }
      } else {
        sitebvoList.clear();
      }
      // } catch (error) {
      //   print(error);
    } finally {
      isloading.value = false;
    }
  }
}
