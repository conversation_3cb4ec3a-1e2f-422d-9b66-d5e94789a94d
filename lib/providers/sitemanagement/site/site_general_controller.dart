import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/models/sitemanagement/site/siteview.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../common/component_utils.dart';
import '../../../common/progess_indicator_cust.dart';
import '../../../models/ta_admin/entitystatus.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../../utils/request_api_utils.dart';

class SiteGeneralController extends GetxController {
  var labelsloading = false.obs;
  var isLoading = false.obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var siteview = SiteView().obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_SITE_GENERAL', 'site_general');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  Future onloadPageData(SiteView s) async {
    siteview.value = s;
    statuslov.clear();
    statuslov.value = await CommonServices.getStatuses('SITE');
  }

  Future onSaveSiteRecord() async {
    debugPrint('onSaveSiteRecord status   ${siteview.value.status}');
    ProgressUtil.showLoaderDialog(Get.context!);
    var result = -1;
    try {
      Map m = {
        'source': 'site_general',
        'site_id': siteview.value.siteId,
        'status': siteview.value.cExtAttr19,
      };
      result = await saveRecord(m);
    } catch (e) {
      debugPrint('$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    if (result == 0) {
      ComponentUtils.showsnackbar(text: "Data saved successfully...");
    } else {
      ComponentUtils.showpopup(type: 'Error', msg: "Error occurred while saving data...");
    }
  }

  static Future saveRecord(Map m) async {
    int? status = -1;
    try {
      String bodyjson = json.encode(m);

      debugPrint(bodyjson);

      var resMap = await ApiService.post(savesiterecordurl, payloadObj: bodyjson);
      if (resMap != null) {
        resMap = jsonDecode(resMap);

        status = resMap['status'] as int?;
        debugPrint('status>>>>>$status');
      }
    } catch (error) {}

    return status;
  }
}
