import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';

import '../../../models/sitemanagement/site/siteview.dart';
import '../../../models/ta_admin/entitystatus.dart';
import '../../ta_admin/label_controller.dart';

class SiteSearchController extends GetxController {
  LabelController talabel = Get.find<LabelController>();
  TextEditingController searchController = TextEditingController();
  var tabroles = <dynamic, dynamic>{}.obs;

  RxList<SiteView> sitelist = <SiteView>[].obs;
  var isLoading = false.obs;
  //search widget
  var filterwidgetloading = false.obs;
  var allmyfilter = 'My'.obs;
  var filterList = [].obs;
  //RxList<EntityStatus> siteTypeLov = <EntityStatus>[].obs;
  // var siteTypeFilter = ''.obs;
  RxList<EntityStatus> statuslov = <EntityStatus>[].obs;
  var status = ''.obs;
  var statusname = ''.obs;
  RxList<EntityStatus> sortByLov = <EntityStatus>[].obs;
  var sortBy = ''.obs;

  @override
  void onClose() {
    debugPrint('------------onClose--------');
    super.onClose();
  }

  Future getSiteSearchData({String? searchText, String? action}) async {
    debugPrint('------------getSiteSearchData--------');
    var user = UserContext.info()!.userName;
    bool filterFlag = false;
    sitelist.clear();
    isLoading.value = true;
    List<SiteView> sites;
    var payloadObj;
    Map<String, dynamic>? resMap;
    Map<String, dynamic> dynamicMap;
    String filter;

    if (searchController.text.isNotEmpty && searchController.text != '') {
      dynamicMap = {
        'a': ['all_columns', 'CONTAINS', searchController.text],
        'b': ['p_user_name', 'EXACT', user!.toUpperCase()],
      };
      filterFlag = true;
    } else {
      dynamicMap = {
        'b': ['p_user_name', 'EXACT', user!.toUpperCase()],
      };
    }
    dynamicMap['mpf'] = ['p_mySitesFlag', 'EXACT', (allmyfilter.value == 'My') ? 'Y' : 'N'];

    if (status.value != '') {
      dynamicMap['stst'] = ['c_ext_attr19', 'EXACT', status.value];
      filterFlag = true;
    }

    if (sortBy.value != '') {
      filter = CommonServices.getFilter(dynamicMap, size: 100, sortby: sortBy.value);
    } else {
      filter = CommonServices.getFilter(dynamicMap, size: 100);
    }
    debugPrint('filter---------' + filter);
    try {
      resMap = await CommonServices.fetchDynamicApi(sitesearchviewurl, payloadObj: filter);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            sites = tagObjsJson.map((tagJson) => SiteView.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${sites.length}');
            sitelist.addAll(sites);
          }
        } else {
          sitelist.clear();
        }
      } else {
        sitelist.clear();
      }
    } finally {
      isLoading.value = false;
      setFilterChips(filterFlag);
      loadFilterLovData(action);
    }
  }

  loadFilterLovData(String? action) {
    if (action == 'onload') {
      getStatusLovs();
      sortByLov.value.clear();
      sortByLov.insert(0, EntityStatus(statusCode: '', status: 'Select'));

      //setting sort By lov list--------------------------------------------------------------------------
      if (talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITEID') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITEID')?.value ?? 'Site Id',
            statusCode: 'SITE_ID'));
      }
      if (talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITENAME') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITENAME')?.value ?? 'Site Name',
            statusCode: 'SITE_NAME'));
      }

      if (talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITETYPE') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_SITETYPE')?.value ?? 'Site Type',
            statusCode: 'SITE_TYPE'));
      }

      if (talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_FACILITYTYPE') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_FACILITYTYPE')?.value ?? 'Facility Type',
            statusCode: 'FACILITY_TYPE'));
      }

      if (talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_OWNERSHIPTYPE') != null) {
        sortByLov.value.add(EntityStatus(
            status: talabel.get('TMCMOBILE_SITESEARCH_SEARCHFILTERS_SORTBY_OWNERSHIPTYPE')?.value ?? 'Ownership Type',
            statusCode: 'OWNERSHIP_TYPE'));
      }
    }
  }

  Future getStatusLovs() async {
    try {
      statuslov.value = await CommonServices.getStatuses('SITE');
    } catch (e) {
      debugPrint('getStatusLovs>>>>>>>$e');
    }
  }

  setFilterChips(bool flag) {
    Future.delayed(const Duration(seconds: 0), () {
      filterList.clear();
      //filterList.add(allmyfilter.value);

      filterList.add(GestureDetector(
        child: Chip(
          label: Text(isLoading.value ? '' : allmyfilter.value),
        ),
        onTap: () async {
          if (isLoading.value) {
            debugPrint('------');
          } else {
            if (allmyfilter.value == 'My') {
              allmyfilter.value = 'All';
            } else if (allmyfilter.value == 'All') {
              allmyfilter.value = 'My';
            }

            await getSiteSearchData();
          }
        },
      ));

      if (flag) {
        if (searchController.text.isNotEmpty && searchController.text != '') {
          filterList.add(Chip(
            avatar: const Icon(Icons.search, size: 20),
            label: Text(searchController.text),
            onDeleted: () async {
              searchController.text = '';
              await getSiteSearchData();
            },
          ));
        }
        if (status.value != '') {
          var statusname = statuslov
                  .firstWhere((EntityStatus s) => s.statusCode == status.value, orElse: () => EntityStatus(status: ''))
                  .status ??
              '';

          filterList.add(Chip(
            avatar: const Icon(Icons.flag, size: 20),
            label: Text(statusname),
            onDeleted: () async {
              status.value = '';
              await getSiteSearchData();
            },
          ));
        }
      }
    });
  }

  Future<String> getUserEntityEditAccess({required String entitytype, required String entityid, String? roFlag}) async {
    ProgressUtil.showLoaderDialog(Get.context!);
    Map<dynamic, dynamic>? vmap;
    String res = 'view';
    tabroles.value = <dynamic, dynamic>{};
    try {
      vmap = await CommonServices.userEntityEditAccessService(entityid: entityid, entitytype: entitytype);
      debugPrint('getUserEntityEditAccess >>>>>>>$roFlag');
      if (vmap != null) {
        tabroles.value = vmap;
        if (vmap['result'] != null && vmap['result'] == 'Y' && vmap['role_flag'] != null && vmap['role_flag'] == 'Y') {
          res = 'Y';
        } else if (vmap['result'] != null && vmap['result'] == 'CN') res = 'error';

        if (res != 'error')
          res = (res.toUpperCase() == 'Y' && (roFlag?.toUpperCase() == 'N' || roFlag == null)) ? 'edit' : 'view';
      }
    } catch (e) {
      debugPrint('getUserEntityEditAccess Exception >>>>>>>>>>>$e');
    } finally {
      ProgressUtil.closeLoaderDialog(Get.context!);
    }
    debugPrint('getUserEntityEditAccess >>>>>>>$res');
    return res;
    //}
  }
}
