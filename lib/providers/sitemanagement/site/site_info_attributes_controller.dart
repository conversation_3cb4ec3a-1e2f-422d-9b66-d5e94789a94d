import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../../models/sitemanagement/site/site_informantion_attributes_data.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';

class SiteInfoAttributesController extends GetxController {
  var labelsloading = false.obs;
  var isLoading = false.obs;
  Rx<SiteInformationAttributes> siteInfoRow = SiteInformationAttributes().obs;

  getlabels(LabelController talabel) async {
    labelsloading.value = true;
    try {
      await talabel.getlabels('TMCMOBILE_SITE_SITEINFOATTRIBUTES', 'site_info_attributes');
    } catch (e) {
      debugPrint('$e');
    }
    labelsloading.value = false;
  }

  Future getSiteInfoData(int? siteid) async {
    isLoading(true);
    siteInfoRow.value = SiteInformationAttributes();

    List<SiteInformationAttributes> records;
    Map<String, dynamic>? resMap;

    Map<String, dynamic> dynamicMap = {
      'a': ['site_id', 'EXACT', siteid.toString()],
    };

    var payloadObj = CommonServices.getFilter(dynamicMap);

    try {
      resMap = await CommonServices.fetchDynamicApi(siteinfotabattributesurl, payloadObj: payloadObj);
      if (resMap != null) {
        var status = resMap['status'] as int?;
        if (status == 0) {
          var tagObjsJson = resMap['records'] as List?;
          if (tagObjsJson != null) {
            records = tagObjsJson.map((tagJson) => SiteInformationAttributes.fromJson(tagJson)).toList();
            debugPrint(' list Count------ ${records.length}');
            if (records.isNotEmpty) siteInfoRow.value = records.first;
          }
        }
      }
    } catch (e) {
      debugPrint('$e');
      siteInfoRow.value = SiteInformationAttributes();
    }
    isLoading(false);
  }
}
