class EntityComment {
  String? comments;
  String? reviewDate;
  String? reviewedBy;
  int? commentId;

  EntityComment({
    this.comments,
    this.reviewDate,
    this.reviewedBy,
    this.commentId,
  });
  EntityComment.fromJson(Map<String, dynamic> json) {
    comments = json["comments"]?.toString() ?? '';
    reviewDate = json["creation_date"]?.toString() ?? '';
    reviewedBy = json["created_by"]?.toString() ?? '';
    commentId = json["comment_id"]?.toInt();
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["comments"] = comments;
    data["reviewDate"] = reviewDate;
    data["reviewedBy"] = reviewedBy;
    data["commentId"] = commentId;
    return data;
  }
}
