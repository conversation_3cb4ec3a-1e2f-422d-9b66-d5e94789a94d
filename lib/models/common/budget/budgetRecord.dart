class BudgetRecord {
  int? budgetId;
  String? entityType;
  int? entityId;
  String? taskName;
  String? description;
  int? initialBudget;
  int? approvedBudget;
  int? commitment;
  int? changeOrder;
  int? actualCost;
  int? estimatedCost;
  int? orgId;
  int? clientId;
  int? brandId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  int? objectVersionNumber;
  String? taskType;
  String? taskTypeDesc;
  int? lineItemSeq;
  String? division;
  String? divisionDesc;
  int? budgetTemplateDtlId;
  int? actualRcvd;
  int? initialTemplateAmount;
  String? subTaskName;
  String? subTaskType;
  String? rollupFlag;
  String? budgetLineType;
  int? taskNumber;
  int? earnedAmount;
  int? actualRcvdLocal;
  int? actualCostLocal;
  int? approvedBudgetLocal;
  int? changeOrderLocal;
  int? commitmentLocal;
  int? earnedAmountLocal;
  int? estimatedCostLocal;
  int? initialBudgetLocal;
  int? eatc;
  int? deprLife;
  String? budgetlastComment;
  String? segment1;
  String? segment2;
  String? segment3;
  String? segment4;
  String? segment5;
  String? segment6;
  int? pendingCommitment;
  int? pendingCommitmentCo;
  int? totalApprovedBudget;
  String? expenseType;
  String? expenseTypeDesc;
  String? accountType;
  String? accountTypeDesc;
  String? remodelType;
  String? remodelTypeDesc;
  int? masterProjectId;
  int? approvedEwa;
  int? pendingEwa;
  int? storeId;
  String? storeName;
  int? quantity;
  int? rate;
  String? unit;
  String? dataEntryLevel;
  String? dataEntryLevelDesc;
  String? lineType;
  String? projectNumber;
  int? initialApprovedBudget;
  String? projectName;
  int? nExtAttr1;
  int? nExtAttr2;
  String? taskTypeTemp;
  String? divisionTemp;
  int? budgetSqFt;
  int? totalAnticipatedCommitment;
  String? storeNumber;
  int? ncalcAttr1;
  int? ncalcAttr2;
  int? ncalcAttr3;
  int? ncalcAttr4;
  int? ncalcAttr5;
  int? pendingAccruedTaxAmount;
  int? accruedTaxAmount;
  int? allocCurrentFy;
  int? allocCurrentFy_1;
  int? allocCurrentFy_2;
  int? allocCurrentFy_3;
  int? allocCurrentFy_4;
  int? allocNoOfMonths;
  String? allocStartDate;

  BudgetRecord({
    this.budgetId,
    this.entityType,
    this.entityId,
    this.taskName,
    this.description,
    this.initialBudget,
    this.approvedBudget,
    this.commitment,
    this.changeOrder,
    this.actualCost,
    this.estimatedCost,
    this.orgId,
    this.clientId,
    this.brandId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.objectVersionNumber,
    this.taskType,
    this.taskTypeDesc,
    this.lineItemSeq,
    this.division,
    this.divisionDesc,
    this.budgetTemplateDtlId,
    this.actualRcvd,
    this.initialTemplateAmount,
    this.subTaskName,
    this.subTaskType,
    this.rollupFlag,
    this.budgetLineType,
    this.taskNumber,
    this.earnedAmount,
    this.actualRcvdLocal,
    this.actualCostLocal,
    this.approvedBudgetLocal,
    this.changeOrderLocal,
    this.commitmentLocal,
    this.earnedAmountLocal,
    this.estimatedCostLocal,
    this.initialBudgetLocal,
    this.eatc,
    this.deprLife,
    this.budgetlastComment,
    this.segment1,
    this.segment2,
    this.segment3,
    this.segment4,
    this.segment5,
    this.segment6,
    this.pendingCommitment,
    this.pendingCommitmentCo,
    this.totalApprovedBudget,
    this.expenseType,
    this.expenseTypeDesc,
    this.accountType,
    this.accountTypeDesc,
    this.remodelType,
    this.remodelTypeDesc,
    this.masterProjectId,
    this.approvedEwa,
    this.pendingEwa,
    this.storeId,
    this.storeName,
    this.quantity,
    this.rate,
    this.unit,
    this.dataEntryLevel,
    this.dataEntryLevelDesc,
    this.lineType,
    this.projectNumber,
    this.initialApprovedBudget,
    this.projectName,
    this.nExtAttr1,
    this.nExtAttr2,
    this.taskTypeTemp,
    this.divisionTemp,
    this.budgetSqFt,
    this.totalAnticipatedCommitment,
    this.storeNumber,
    this.ncalcAttr1,
    this.ncalcAttr2,
    this.ncalcAttr3,
    this.ncalcAttr4,
    this.ncalcAttr5,
    this.pendingAccruedTaxAmount,
    this.accruedTaxAmount,
    this.allocCurrentFy,
    this.allocCurrentFy_1,
    this.allocCurrentFy_2,
    this.allocCurrentFy_3,
    this.allocCurrentFy_4,
    this.allocNoOfMonths,
    this.allocStartDate,
  });

  // factory BudgetRecord.fromJson(Map<String, dynamic> json) {
  //   return BudgetRecord(
  //     budgetId: json['budget_id'] ?? 0,
  //     entityType: json['entity_type'] ?? '',
  //     entityId: json['entity_id'] ?? 0,
  //     taskName: json['task_name'] ?? '',
  //     description: json['description'] ?? '',
  //     initialBudget: (json['initial_budget'] ?? 0).toDouble(),
  //     approvedBudget: (json['approved_budget'] ?? 0).toDouble(),
  //     projectName: json['project_name'] ?? '',
  //   );
  // }

  BudgetRecord.fromJson(Map<String, dynamic> json) {
    budgetId = json['budget_id']?.toInt();
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    taskName = json['task_name']?.toString();
    description = json['description']?.toString();
    initialBudget = json['initial_budget']?.toInt();
    approvedBudget = json['approved_budget']?.toInt();
    commitment = json['commitment']?.toInt();
    changeOrder = json['change_order']?.toInt();
    actualCost = json['actual_cost']?.toInt();
    estimatedCost = json['estimated_cost']?.toInt();
    orgId = json['org_id']?.toInt();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    objectVersionNumber = json['object_version_number']?.toInt();
    taskType = json['task_type']?.toString();
    taskTypeDesc = json['task_type_desc']?.toString();
    lineItemSeq = json['line_item_seq']?.toInt();
    division = json['division']?.toString();
    divisionDesc = json['division_desc']?.toString();
    budgetTemplateDtlId = json['budget_template_dtl_id']?.toInt();
    actualRcvd = json['actual_rcvd']?.toInt();
    initialTemplateAmount = json['initial_template_amount']?.toInt();
    subTaskName = json['sub_task_name']?.toString();
    subTaskType = json['sub_task_type']?.toString();
    rollupFlag = json['rollup_flag']?.toString();
    budgetLineType = json['budget_line_type']?.toString();
    taskNumber = json['task_number']?.toInt();
    earnedAmount = json['earned_amount']?.toInt();
    actualRcvdLocal = json['actual_rcvd_local']?.toInt();
    actualCostLocal = json['actual_cost_local']?.toInt();
    approvedBudgetLocal = json['approved_budget_local']?.toInt();
    changeOrderLocal = json['change_order_local']?.toInt();
    commitmentLocal = json['commitment_local']?.toInt();
    earnedAmountLocal = json['earned_amount_local']?.toInt();
    estimatedCostLocal = json['estimated_cost_local']?.toInt();
    initialBudgetLocal = json['initial_budget_local']?.toInt();
    eatc = json['eatc']?.toInt();
    deprLife = json['depr_life']?.toInt();
    budgetlastComment = json['budgetlast_comment']?.toString();
    segment1 = json['segment1']?.toString();
    segment2 = json['segment2']?.toString();
    segment3 = json['segment3']?.toString();
    segment4 = json['segment4']?.toString();
    segment5 = json['segment5']?.toString();
    segment6 = json['segment6']?.toString();
    pendingCommitment = json['pending_commitment']?.toInt();
    pendingCommitmentCo = json['pending_commitment_co']?.toInt();
    totalApprovedBudget = json['total_approved_budget']?.toInt();
    expenseType = json['expense_type']?.toString();
    expenseTypeDesc = json['expense_type_desc']?.toString();
    accountType = json['account_type']?.toString();
    accountTypeDesc = json['account_type_desc']?.toString();
    remodelType = json['remodel_type']?.toString();
    remodelTypeDesc = json['remodel_type_desc']?.toString();
    masterProjectId = json['master_project_id']?.toInt();
    approvedEwa = json['approved_ewa']?.toInt();
    pendingEwa = json['pending_ewa']?.toInt();
    storeId = json['store_id']?.toInt();
    storeName = json['store_name']?.toString();
    quantity = json['quantity']?.toInt();
    rate = json['rate']?.toInt();
    unit = json['unit']?.toString();
    dataEntryLevel = json['data_entry_level']?.toString();
    dataEntryLevelDesc = json['data_entry_level_desc']?.toString();
    lineType = json['line_type']?.toString();
    projectNumber = json['project_number']?.toString();
    initialApprovedBudget = json['initial_approved_budget']?.toInt();
    projectName = json['project_name']?.toString();
    nExtAttr1 = json['n_ext_attr1']?.toInt();
    nExtAttr2 = json['n_ext_attr2']?.toInt();
    taskTypeTemp = json['task_type_temp']?.toString();
    divisionTemp = json['division_temp']?.toString();
    budgetSqFt = json['budget_sq_ft']?.toInt();
    totalAnticipatedCommitment = json['total_anticipated_commitment']?.toInt();
    storeNumber = json['store_number']?.toString();
    ncalcAttr1 = json['ncalc_attr1']?.toInt();
    ncalcAttr2 = json['ncalc_attr2']?.toInt();
    ncalcAttr3 = json['ncalc_attr3']?.toInt();
    ncalcAttr4 = json['ncalc_attr4']?.toInt();
    ncalcAttr5 = json['ncalc_attr5']?.toInt();
    pendingAccruedTaxAmount = json['pending_accrued_tax_amount']?.toInt();
    accruedTaxAmount = json['accrued_tax_amount']?.toInt();
    allocCurrentFy = json['alloc_current_fy']?.toInt();
    allocCurrentFy_1 = json['alloc_current_fy_1']?.toInt();
    allocCurrentFy_2 = json['alloc_current_fy_2']?.toInt();
    allocCurrentFy_3 = json['alloc_current_fy_3']?.toInt();
    allocCurrentFy_4 = json['alloc_current_fy_4']?.toInt();
    allocNoOfMonths = json['alloc_no_of_months']?.toInt();
    allocStartDate = json['alloc_start_date']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['budget_id'] = budgetId;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['task_name'] = taskName;
    data['description'] = description;
    data['initial_budget'] = initialBudget;
    data['approved_budget'] = approvedBudget;
    data['commitment'] = commitment;
    data['change_order'] = changeOrder;
    data['actual_cost'] = actualCost;
    data['estimated_cost'] = estimatedCost;
    data['org_id'] = orgId;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['object_version_number'] = objectVersionNumber;
    data['task_type'] = taskType;
    data['task_type_desc'] = taskTypeDesc;
    data['line_item_seq'] = lineItemSeq;
    data['division'] = division;
    data['division_desc'] = divisionDesc;
    data['budget_template_dtl_id'] = budgetTemplateDtlId;
    data['actual_rcvd'] = actualRcvd;
    data['initial_template_amount'] = initialTemplateAmount;
    data['sub_task_name'] = subTaskName;
    data['sub_task_type'] = subTaskType;
    data['rollup_flag'] = rollupFlag;
    data['budget_line_type'] = budgetLineType;
    data['task_number'] = taskNumber;
    data['earned_amount'] = earnedAmount;
    data['actual_rcvd_local'] = actualRcvdLocal;
    data['actual_cost_local'] = actualCostLocal;
    data['approved_budget_local'] = approvedBudgetLocal;
    data['change_order_local'] = changeOrderLocal;
    data['commitment_local'] = commitmentLocal;
    data['earned_amount_local'] = earnedAmountLocal;
    data['estimated_cost_local'] = estimatedCostLocal;
    data['initial_budget_local'] = initialBudgetLocal;
    data['eatc'] = eatc;
    data['depr_life'] = deprLife;
    data['budgetlast_comment'] = budgetlastComment;
    data['segment1'] = segment1;
    data['segment2'] = segment2;
    data['segment3'] = segment3;
    data['segment4'] = segment4;
    data['segment5'] = segment5;
    data['segment6'] = segment6;
    data['pending_commitment'] = pendingCommitment;
    data['pending_commitment_co'] = pendingCommitmentCo;
    data['total_approved_budget'] = totalApprovedBudget;
    data['expense_type'] = expenseType;
    data['expense_type_desc'] = expenseTypeDesc;
    data['account_type'] = accountType;
    data['account_type_desc'] = accountTypeDesc;
    data['remodel_type'] = remodelType;
    data['remodel_type_desc'] = remodelTypeDesc;
    data['master_project_id'] = masterProjectId;
    data['approved_ewa'] = approvedEwa;
    data['pending_ewa'] = pendingEwa;
    data['store_id'] = storeId;
    data['store_name'] = storeName;
    data['quantity'] = quantity;
    data['rate'] = rate;
    data['unit'] = unit;
    data['data_entry_level'] = dataEntryLevel;
    data['data_entry_level_desc'] = dataEntryLevelDesc;
    data['line_type'] = lineType;
    data['project_number'] = projectNumber;
    data['initial_approved_budget'] = initialApprovedBudget;
    data['project_name'] = projectName;
    data['n_ext_attr1'] = nExtAttr1;
    data['n_ext_attr2'] = nExtAttr2;
    data['task_type_temp'] = taskTypeTemp;
    data['division_temp'] = divisionTemp;
    data['budget_sq_ft'] = budgetSqFt;
    data['total_anticipated_commitment'] = totalAnticipatedCommitment;
    data['store_number'] = storeNumber;
    data['ncalc_attr1'] = ncalcAttr1;
    data['ncalc_attr2'] = ncalcAttr2;
    data['ncalc_attr3'] = ncalcAttr3;
    data['ncalc_attr4'] = ncalcAttr4;
    data['ncalc_attr5'] = ncalcAttr5;
    data['pending_accrued_tax_amount'] = pendingAccruedTaxAmount;
    data['accrued_tax_amount'] = accruedTaxAmount;
    data['alloc_current_fy'] = allocCurrentFy;
    data['alloc_current_fy_1'] = allocCurrentFy_1;
    data['alloc_current_fy_2'] = allocCurrentFy_2;
    data['alloc_current_fy_3'] = allocCurrentFy_3;
    data['alloc_current_fy_4'] = allocCurrentFy_4;
    data['alloc_no_of_months'] = allocNoOfMonths;
    data['alloc_start_date'] = allocStartDate;
    return data;
  }
}