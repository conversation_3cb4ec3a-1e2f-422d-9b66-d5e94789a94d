import '../utils/paginationInfo.dart';
import 'budgetRecord.dart';

class BudgetResponse {
  final int status;
  final String statusMessage;
  final int id;
  final List<BudgetRecord> records;
  final PaginationInfo paginationInfo;

  BudgetResponse({
    required this.status,
    required this.statusMessage,
    required this.id,
    required this.records,
    required this.paginationInfo,
  });

  factory BudgetResponse.fromJson(Map<String, dynamic> json) {
    var recordsList = json['records'] as List<dynamic>? ?? [];
    return BudgetResponse(
      status: json['status'] ?? 0,
      statusMessage: json['statusmessage'] ?? '',
      id: json['id'] ?? 0,
      records: recordsList.map((item) => BudgetRecord.fromJson(item)).toList(),
      paginationInfo: PaginationInfo.fromJson(json['pagination_info'] ?? {}),
    );
  }
}