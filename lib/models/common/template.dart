class Template {
  int? templateId;
  String? entityType;
  String? templateName;
  String? description;
  String? templateType;
  String? creationDate;
  String? createdBy;
  String? country;
  int? budparamCount;

  Template({
    this.templateId,
    this.entityType,
    this.templateName,
    this.description,
    this.templateType,
    this.creationDate,
    this.createdBy,
    this.country,
    this.budparamCount,
  });
  Template.fromJson(Map<String, dynamic> json) {
    templateId = json['template_id']?.toInt();
    entityType = json['entity_type']?.toString();
    templateName = json['template_name']?.toString();
    description = json['description']?.toString();
    templateType = json['template_type']?.toString();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    country = json['country']?.toString();
    budparamCount = json['budparam_count']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['template_id'] = templateId;
    data['entity_type'] = entityType;
    data['template_name'] = templateName;
    data['description'] = description;
    data['template_type'] = templateType;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['country'] = country;
    data['budparam_count'] = budparamCount;
    return data;
  }
}
