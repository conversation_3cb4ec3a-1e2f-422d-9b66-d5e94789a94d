class MilestoneChecklist {
  int? checklistId;
  int? milestoneId;
  String? checklistName;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? actualDate;
  int? fileId;
  int? entityId;
  String? entityType;
  String? fileName;
  String? milestoneActualFinish;
  String? checklistOption;
  String? applicableFlag;

  MilestoneChecklist({
    this.checklistId,
    this.milestoneId,
    this.checklistName,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.actualDate,
    this.fileId,
    this.entityId,
    this.entityType,
    this.fileName,
    this.milestoneActualFinish,
    this.checklistOption,
    this.applicableFlag,
  });
  MilestoneChecklist.fromJson(Map<String, dynamic> json) {
    checklistId = json['checklist_id']?.toInt();
    milestoneId = json['milestone_id']?.toInt();
    checklistName = json['checklist_name']?.toString() ?? '';
    creationDate = json['creation_date']?.toString() ?? '';
    createdBy = json['created_by']?.toString() ?? '';
    lastUpdateDate = json['last_update_date']?.toString() ?? '';
    lastUpdatedBy = json['last_updated_by']?.toString() ?? '';
    actualDate = json['actual_date']?.toString() ?? '';
    fileId = json['file_id']?.toInt();
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString() ?? '';
    fileName = json['file_name']?.toString() ?? '';
    milestoneActualFinish = json['milestone_actual_finish']?.toString() ?? '';
    checklistOption = json['checklist_option']?.toString() ?? 'N';
    applicableFlag = json['applicable_flag']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['checklist_id'] = checklistId;
    data['milestone_id'] = milestoneId;
    data['checklist_name'] = checklistName;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['actual_date'] = actualDate;
    data['file_id'] = fileId;
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['file_name'] = fileName;
    data['milestone_actual_finish'] = milestoneActualFinish;
    data['checklist_option'] = checklistOption;
    data['applicable_flag'] = applicableFlag;
    return data;
  }
}
