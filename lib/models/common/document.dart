class Document {
  int? parentFolderId;
  String? docType;
  int? id;
  String? docName;
  String? description;
  String? fileContentType;
  int? docCount;
  int? childcount;
  int? folderSequence;

  Document({
    this.parentFolderId,
    this.docType,
    this.id,
    this.docName,
    this.description,
    this.fileContentType,
    this.docCount,
    this.childcount,
    this.folderSequence,
  });
  Document.fromJson(Map<String, dynamic> json) {
    parentFolderId = json['parent_folder_id']?.toInt();
    docType = json['doc_type']?.toString() ?? '';
    id = json['id']?.toInt() ?? 0;
    docName = json['doc_name']?.toString() ?? '';
    description = json['description']?.toString() ?? '';
    fileContentType = json['file_content_type']?.toString() ?? '';
    docCount = json['doc_count']?.toInt() ?? 0;
    childcount = json['childcount']?.toInt() ?? 0;
    folderSequence = json['folder_sequence']?.toInt() ?? 0;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['parent_folder_id'] = parentFolderId;
    data['doc_type'] = docType;
    data['id'] = id;
    data['doc_name'] = docName;
    data['description'] = description;
    data['file_content_type'] = fileContentType;
    data['doc_count'] = docCount;
    data['childcount'] = childcount;
    data['folder_sequence'] = folderSequence;
    return data;
  }
}
