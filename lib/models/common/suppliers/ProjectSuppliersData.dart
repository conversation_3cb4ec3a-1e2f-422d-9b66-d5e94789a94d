class ProjectSuppliersData {
  int? projSuppId;
  int? projectId;
  int? supplierId;
  int? clientId;
  int? brandId;
  String? country;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? supplierNumber1;
  String? description1;
  int? supplierId1;
  String? supplierName;
  String? email;
  String? phoneNumber;
  String? contactPerson;
  String? address1;
  String? address2;
  String? city;
  String? state;
  String? zip;
  String? cExtLov1;
  String? paymentTerm;
  int? supplierRating;
  int? supplierRatingAvg;
  String? projectName;
  String? projectNumber;
  int? nExtAttr01;
  int? nExtAttr02;
  String? documentAccess;
  String? endDate;
  String? startDate;
  String? prequalified;
  String? partner;
  String? status;
  String? statusType;
  String? trades;

  ProjectSuppliersData({
    this.projSuppId,
    this.projectId,
    this.supplierId,
    this.clientId,
    this.brandId,
    this.country,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.supplierNumber1,
    this.description1,
    this.supplierId1,
    this.supplierName,
    this.email,
    this.phoneNumber,
    this.contactPerson,
    this.address1,
    this.address2,
    this.city,
    this.state,
    this.zip,
    this.cExtLov1,
    this.paymentTerm,
    this.supplierRating,
    this.supplierRatingAvg,
    this.projectName,
    this.projectNumber,
    this.nExtAttr01,
    this.nExtAttr02,
    this.documentAccess,
    this.endDate,
    this.startDate,
    this.prequalified,
    this.partner,
    this.status,
    this.statusType,
    this.trades,
  });
  ProjectSuppliersData.fromJson(Map<String, dynamic> json) {
    projSuppId = json['proj_supp_id']?.toInt();
    projectId = json['project_id']?.toInt();
    supplierId = json['supplier_id']?.toInt();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    country = json['country']?.toString();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    supplierNumber1 = json['supplier_number1']?.toString();
    description1 = json['description1']?.toString();
    supplierId1 = json['supplier_id1']?.toInt();
    supplierName = json['supplier_name']?.toString();
    email = json['email']?.toString();
    phoneNumber = json['phone_number']?.toString();
    contactPerson = json['contact_person']?.toString() ?? '';
    address1 = json['address1']?.toString();
    address2 = json['address2']?.toString();
    city = json['city']?.toString();
    state = json['state']?.toString();
    zip = json['zip']?.toString();
    cExtLov1 = json['c_ext_lov1']?.toString();
    paymentTerm = json['payment_term']?.toString();
    supplierRating = json['supplier_rating']?.toInt();
    supplierRatingAvg = json['supplier_rating_avg']?.toInt();
    projectName = json['project_name']?.toString();
    projectNumber = json['project_number']?.toString();
    nExtAttr01 = json['n_ext_attr01']?.toInt();
    nExtAttr02 = json['n_ext_attr02']?.toInt();
    documentAccess = json['document_access']?.toString();
    endDate = json['end_date']?.toString();
    startDate = json['start_date']?.toString();
    prequalified = json['prequalified']?.toString();
    partner = json['partner']?.toString();
    status = json['status']?.toString();
    statusType = json['status_type']?.toString();
    trades = json['trades']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['proj_supp_id'] = projSuppId;
    data['project_id'] = projectId;
    data['supplier_id'] = supplierId;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['country'] = country;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['supplier_number1'] = supplierNumber1;
    data['description1'] = description1;
    data['supplier_id1'] = supplierId1;
    data['supplier_name'] = supplierName;
    data['email'] = email;
    data['phone_number'] = phoneNumber;
    data['contact_person'] = contactPerson;
    data['address1'] = address1;
    data['address2'] = address2;
    data['city'] = city;
    data['state'] = state;
    data['zip'] = zip;
    data['c_ext_lov1'] = cExtLov1;
    data['payment_term'] = paymentTerm;
    data['supplier_rating'] = supplierRating;
    data['supplier_rating_avg'] = supplierRatingAvg;
    data['project_name'] = projectName;
    data['project_number'] = projectNumber;
    data['n_ext_attr01'] = nExtAttr01;
    data['n_ext_attr02'] = nExtAttr02;
    data['document_access'] = documentAccess;
    data['end_date'] = endDate;
    data['start_date'] = startDate;
    data['prequalified'] = prequalified;
    data['partner'] = partner;
    data['status'] = status;
    data['status_type'] = statusType;
    data['trades'] = trades;
    return data;
  }
}
