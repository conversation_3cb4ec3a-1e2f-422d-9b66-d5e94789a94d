class EntityData {
  final int? entityId;
  final String? entityType;
  final String? entityName;
  final String? entityNumber;
  final String? address;
  final String? city;
  final String? state;
  final String? locEntityType;
  final String? locSubEntityType;

  EntityData({
    this.entityId,
    this.entityType,
    this.entityName,
    this.entityNumber,
    this.address,
    this.city,
    this.locEntityType,
    this.locSubEntityType,
    this.state,
  });

  factory EntityData.fromJson(Map<String, dynamic> json) {
    return EntityData(
      entityId: json['entityId'],
      entityType: json['entityType'],
      entityName: json['entityName'],
      entityNumber: json['entityNumber'],
      address: json['address'],
      city: json['city'],
      locEntityType: json['locEntityType'],
      locSubEntityType: json['locSubEntityType'],
      state: json['state'],
    );
  }
}
