class Punchlist {
  int? entityId;
  String? entityType;
  int? scopePunchListId;
  int? lineItemSeq;
  String? scopePunchName;
  String? itemType;
  String? assignee;
  String? dueDate;
  String? division;
  String? notApplicableFlag;
  int? cost;
  String? maintenance;
  String? responsibilty;
  String? description;
  String? comments;
  String? comments1;
  String? comments2;
  String? comments3;
  int? photoStatus;
  String? cExtAttr1;
  String? cExtAttr2;
  String? cExtAttr3;
  String? cExtAttr4;
  String? cExtAttr5;
  String? cExtAttr6;
  String? cExtAttr7;
  String? cExtAttr8;
  String? cExtAttr9;
  String? cExtAttr10;
  String? dExtAttr1;
  String? cExtLov1;
  String? cExtLov2;
  String? cExtLov3;
  String? cExtLov4;
  String? cExtLov5;
  String? cExtLov6;
  String? cExtLov7;
  String? cExtLov8;
  String? cExtLov9;
  String? createdBy;
  String? creationDate;
  String? lastUpdatedBy;
  String? lastUpdateDate;

  Punchlist({
    this.entityId,
    this.entityType,
    this.scopePunchListId,
    this.lineItemSeq,
    this.scopePunchName,
    this.itemType,
    this.assignee,
    this.dueDate,
    this.division,
    this.notApplicableFlag,
    this.cost,
    this.maintenance,
    this.responsibilty,
    this.description,
    this.comments,
    this.comments1,
    this.comments2,
    this.comments3,
    this.photoStatus,
    this.cExtAttr1,
    this.cExtAttr2,
    this.cExtAttr3,
    this.cExtAttr4,
    this.cExtAttr5,
    this.cExtAttr6,
    this.cExtAttr7,
    this.cExtAttr8,
    this.cExtAttr9,
    this.cExtAttr10,
    this.dExtAttr1,
    this.cExtLov1,
    this.cExtLov2,
    this.cExtLov3,
    this.cExtLov4,
    this.cExtLov5,
    this.cExtLov6,
    this.cExtLov7,
    this.cExtLov8,
    this.cExtLov9,
    this.createdBy,
    this.creationDate,
    this.lastUpdatedBy,
    this.lastUpdateDate,
  });
  Punchlist.fromJson(Map<String, dynamic> json) {
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    scopePunchListId = json['scope_punch_list_id']?.toInt();
    lineItemSeq = json['line_item_seq']?.toInt();
    scopePunchName = json['scope_punch_name']?.toString();
    itemType = json['item_type']?.toString();
    assignee = json['assignee']?.toString();
    dueDate = json['due_date']?.toString();
    division = json['division']?.toString();
    notApplicableFlag = json['not_applicable_flag']?.toString();
    cost = json['cost']?.toInt();
    maintenance = json['maintenance']?.toString();
    responsibilty = json['responsibilty']?.toString();
    description = json['description']?.toString();
    comments = json['comments']?.toString();
    comments1 = json['comments1']?.toString();
    comments2 = json['comments2']?.toString();
    comments3 = json['comments3']?.toString();
    photoStatus = json['photo_status']?.toInt();
    cExtAttr1 = json['c_ext_attr1']?.toString();
    cExtAttr2 = json['c_ext_attr2']?.toString();
    cExtAttr3 = json['c_ext_attr3']?.toString();
    cExtAttr4 = json['c_ext_attr4']?.toString();
    cExtAttr5 = json['c_ext_attr5']?.toString();
    cExtAttr6 = json['c_ext_attr6']?.toString();
    cExtAttr7 = json['c_ext_attr7']?.toString();
    cExtAttr8 = json['c_ext_attr8']?.toString();
    cExtAttr9 = json['c_ext_attr9']?.toString();
    cExtAttr10 = json['c_ext_attr10']?.toString();
    dExtAttr1 = json['d_ext_attr1']?.toString();
    cExtLov1 = json['c_ext_lov1']?.toString();
    cExtLov2 = json['c_ext_lov2']?.toString();
    cExtLov3 = json['c_ext_lov3']?.toString();
    cExtLov4 = json['c_ext_lov4']?.toString();
    cExtLov5 = json['c_ext_lov5']?.toString();
    cExtLov6 = json['c_ext_lov6']?.toString();
    cExtLov7 = json['c_ext_lov7']?.toString();
    cExtLov8 = json['c_ext_lov8']?.toString();
    cExtLov9 = json['c_ext_lov9']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['scope_punch_list_id'] = scopePunchListId;
    data['line_item_seq'] = lineItemSeq;
    data['scope_punch_name'] = scopePunchName;
    data['item_type'] = itemType;
    data['assignee'] = assignee;
    data['due_date'] = dueDate;
    data['division'] = division;
    data['not_applicable_flag'] = notApplicableFlag;
    data['cost'] = cost;
    data['maintenance'] = maintenance;
    data['responsibilty'] = responsibilty;
    data['description'] = description;
    data['comments'] = comments;
    data['comments1'] = comments1;
    data['comments2'] = comments2;
    data['comments3'] = comments3;
    data['photo_status'] = photoStatus;
    data['c_ext_attr1'] = cExtAttr1;
    data['c_ext_attr2'] = cExtAttr2;
    data['c_ext_attr3'] = cExtAttr3;
    data['c_ext_attr4'] = cExtAttr4;
    data['c_ext_attr5'] = cExtAttr5;
    data['c_ext_attr6'] = cExtAttr6;
    data['c_ext_attr7'] = cExtAttr7;
    data['c_ext_attr8'] = cExtAttr8;
    data['c_ext_attr9'] = cExtAttr9;
    data['c_ext_attr10'] = cExtAttr10;
    data['d_ext_attr1'] = dExtAttr1;
    data['c_ext_lov1'] = cExtLov1;
    data['c_ext_lov2'] = cExtLov2;
    data['c_ext_lov3'] = cExtLov3;
    data['c_ext_lov4'] = cExtLov4;
    data['c_ext_lov5'] = cExtLov5;
    data['c_ext_lov6'] = cExtLov6;
    data['c_ext_lov7'] = cExtLov7;
    data['c_ext_lov8'] = cExtLov8;
    data['c_ext_lov9'] = cExtLov9;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['last_update_date'] = lastUpdateDate;
    return data;
  }
}
