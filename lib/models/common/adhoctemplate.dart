class AdhocTemplate {
  int? adhocTemplateId;
  String? description;
  String? entityType;
  String? status;
  String? templateName;

  AdhocTemplate({
    this.adhocTemplateId,
    this.description,
    this.entityType,
    this.status,
    this.templateName,
  });
  AdhocTemplate.fromJson(Map<String, dynamic> json) {
    adhocTemplateId = json['adhoc_template_id']?.toInt();
    description = json['description']?.toString();
    entityType = json['entity_type']?.toString();
    status = json['status']?.toString();
    templateName = json['template_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['adhoc_template_id'] = adhocTemplateId;
    data['description'] = description;
    data['entity_type'] = entityType;
    data['status'] = status;
    data['template_name'] = templateName;
    return data;
  }
}
