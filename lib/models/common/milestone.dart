class Milestone {
  int? milestoneId;
  String? entityType;
  int? entityId;
  String? milestoneName;
  String? description;
  String? plannedStart;
  String? plannedFinish;
  String? actualStart;
  String? actualFinish;
  int? clientId;
  int? brandId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  int? objectVersionNumber;
  String? baseLineStart;
  String? baseLineEnd;
  String? forecastStart;
  String? forecastEnd;
  int? duration;
  String? milestoneType;
  String? taskRelationship;
  int? taskNumber;
  String? tasktype;
  int? percentComplete;
  String? groupName;
  String? formName;
  String? readOnly;
  int? templateId;
  int? predcount;
  int? dispSeq;
  String? mandatory;
  String? subGroupName;
  String? pastdue;
  String? delayReason;
  String? milestonetypeval;
  String? entitystatus;

  Milestone({
    this.milestoneId,
    this.entityType,
    this.entityId,
    this.milestoneName,
    this.description,
    this.plannedStart,
    this.plannedFinish,
    this.actualStart,
    this.actualFinish,
    this.clientId,
    this.brandId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.objectVersionNumber,
    this.baseLineStart,
    this.baseLineEnd,
    this.forecastStart,
    this.forecastEnd,
    this.duration,
    this.milestoneType,
    this.taskRelationship,
    this.taskNumber,
    this.tasktype,
    this.percentComplete,
    this.groupName,
    this.formName,
    this.readOnly,
    this.templateId,
    this.predcount,
    this.dispSeq,
    this.mandatory,
    this.subGroupName,
    this.pastdue,
    this.delayReason,
    this.milestonetypeval,
    this.entitystatus,
  });
  Milestone.fromJson(Map<String, dynamic> json) {
    milestoneId = json['milestone_id']?.toInt();
    entityType = json['entity_type']?.toString() ?? '';
    entityId = json['entity_id']?.toInt();
    milestoneName = json['milestone_name']?.toString() ?? '';
    description = json['description']?.toString() ?? '';
    plannedStart = json['planned_start']?.toString() ?? '';
    plannedFinish = json['planned_finish']?.toString() ?? '';
    actualStart = json['actual_start']?.toString() ?? '';
    actualFinish = json['actual_finish']?.toString() ?? '';
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    creationDate = json['creation_date']?.toString() ?? '';
    createdBy = json['created_by']?.toString() ?? '';
    lastUpdateDate = json['last_update_date']?.toString() ?? '';
    lastUpdatedBy = json['last_updated_by']?.toString() ?? '';
    objectVersionNumber = json['object_version_number']?.toInt();
    baseLineStart = json['base_line_start']?.toString() ?? '';
    baseLineEnd = json['base_line_end']?.toString() ?? '';
    forecastStart = json['forecast_start']?.toString() ?? '';
    forecastEnd = json['forecast_end']?.toString() ?? '';
    duration = json['duration']?.toInt();
    milestoneType = json['milestone_type']?.toString() ?? '';
    taskRelationship = json['task_relationship']?.toString() ?? '';
    taskNumber = json['task_number']?.toInt();
    tasktype = json['tasktype']?.toString() ?? '';
    percentComplete = json['percent_complete']?.toInt();
    groupName = json['group_name']?.toString() ?? '';
    formName = json['form_name']?.toString() ?? '';
    readOnly = json['read_only']?.toString() ?? '';
    templateId = json['template_id']?.toInt();
    predcount = json['predcount']?.toInt();
    dispSeq = json['disp_seq']?.toInt();
    mandatory = json['mandatory']?.toString() ?? '';
    subGroupName = json['sub_group_name']?.toString() ?? '';
    pastdue = json['pastdue']?.toString() ?? '';
    delayReason = json['delay_reason']?.toString() ?? '';
    milestonetypeval = json['milestonetypeval']?.toString() ?? '';
    entitystatus = json['entitystatus']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['milestone_id'] = milestoneId;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['milestone_name'] = milestoneName;
    data['description'] = description;
    data['planned_start'] = plannedStart;
    data['planned_finish'] = plannedFinish;
    data['actual_start'] = actualStart;
    data['actual_finish'] = actualFinish;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['object_version_number'] = objectVersionNumber;
    data['base_line_start'] = baseLineStart;
    data['base_line_end'] = baseLineEnd;
    data['forecast_start'] = forecastStart;
    data['forecast_end'] = forecastEnd;
    data['duration'] = duration;
    data['milestone_type'] = milestoneType;
    data['task_relationship'] = taskRelationship;
    data['task_number'] = taskNumber;
    data['tasktype'] = tasktype;
    data['percent_complete'] = percentComplete;
    data['group_name'] = groupName;
    data['form_name'] = formName;
    data['read_only'] = readOnly;
    data['template_id'] = templateId;
    data['predcount'] = predcount;
    data['disp_seq'] = dispSeq;
    data['mandatory'] = mandatory;
    data['sub_group_name'] = subGroupName;
    data['pastdue'] = pastdue;
    data['delay_reason'] = delayReason;
    data['milestonetypeval'] = milestonetypeval;
    data['entitystatus'] = entitystatus;
    return data;
  }
}
