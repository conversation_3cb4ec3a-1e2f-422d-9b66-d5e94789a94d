class AdhocTask {
  int? adhocTaskId;
  String? entityType;
  int? entityId;
  String? taskName;
  String? taskDesc;
  String? assignedTo;
  String? taskStart;
  String? taskFinish;
  String? actualFinsh;
  int? notification1;
  int? notification2;
  int? notification3;
  String? status;
  String? creationDate;
  String? createdBy;
  String? editableFlag;
  String? tags;
  String? reminder;
  String? taskType;
  String? entityNumber;
  String? entityName;
  int? taskSeq;
  int? escaltionNotification;
  int? templateId;
  String? priority;
  String? mitigationComments;
  int? fileId;
  String? fileName;
  String? emailAddress1;
  int? riskAmount;
  String? riskMitigation;
  String? valueAdd;
  String? taskNumber;
  String? statusLov;
  String? taskInternalAction;
  String? taskNotice;
  String? assignedBy;
  String? assignedDate;
  String? assignedByEmail;
  String? statusType;
  String? assignedByName;
  String? assignedToName;
  String? subEntityType;
  int? subEntityId;
  String? assignedToUsers;
  String? sendNotificationFlag;
  String? taskTypeDesc;
  String? assosiatedFileIds;

  AdhocTask({
    this.adhocTaskId,
    this.entityType,
    this.entityId,
    this.taskName,
    this.taskDesc,
    this.assignedTo,
    this.taskStart,
    this.taskFinish,
    this.actualFinsh,
    this.notification1,
    this.notification2,
    this.notification3,
    this.status,
    this.creationDate,
    this.createdBy,
    this.editableFlag,
    this.tags,
    this.reminder,
    this.taskType,
    this.entityNumber,
    this.entityName,
    this.taskSeq,
    this.escaltionNotification,
    this.templateId,
    this.priority,
    this.mitigationComments,
    this.fileId,
    this.fileName,
    this.emailAddress1,
    this.riskAmount,
    this.riskMitigation,
    this.valueAdd,
    this.taskNumber,
    this.statusLov,
    this.taskInternalAction,
    this.taskNotice,
    this.assignedBy,
    this.assignedDate,
    this.assignedByEmail,
    this.statusType,
    this.assignedByName,
    this.assignedToName,
    this.subEntityType,
    this.subEntityId,
    this.assignedToUsers,
    this.sendNotificationFlag,
    this.taskTypeDesc,
    this.assosiatedFileIds,
  });
  AdhocTask.fromJson(Map<String, dynamic> json) {
    adhocTaskId = json['adhoc_task_id']?.toInt();
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    taskName = json['task_name']?.toString();
    taskDesc = json['task_desc']?.toString();
    assignedTo = json['assigned_to']?.toString();
    taskStart = json['task_start']?.toString();
    taskFinish = json['task_finish']?.toString();
    actualFinsh = json['actual_finsh']?.toString();
    notification1 = json['notification1']?.toInt();
    notification2 = json['notification2']?.toInt();
    notification3 = json['notification3']?.toInt();
    status = json['status']?.toString();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    editableFlag = json['editable_flag']?.toString();
    tags = json['tags']?.toString();
    reminder = json['reminder']?.toString();
    taskType = json['task_type']?.toString();
    entityNumber = json['entity_number']?.toString();
    entityName = json['entity_name']?.toString();
    taskSeq = json['task_seq']?.toInt();
    escaltionNotification = json['escaltion_notification']?.toInt();
    templateId = json['template_id']?.toInt();
    priority = json['priority']?.toString();
    mitigationComments = json['mitigation_comments']?.toString();
    fileId = json['file_id']?.toInt();
    fileName = json['file_name']?.toString();
    emailAddress1 = json['email_address1']?.toString();
    riskAmount = json['risk_amount']?.toInt();
    riskMitigation = json['risk_mitigation']?.toString();
    valueAdd = json['value_add']?.toString();
    taskNumber = json['task_number']?.toString();
    statusLov = json['status_lov']?.toString();
    taskInternalAction = json['task_internal_action']?.toString();
    taskNotice = json['task_notice']?.toString();
    assignedBy = json['assigned_by']?.toString();
    assignedDate = json['assigned_date']?.toString();
    assignedByEmail = json['assigned_by_email']?.toString();
    statusType = json['status_type']?.toString();
    assignedByName = json['assigned_by_name']?.toString();
    assignedToName = json['assigned_to_name']?.toString();
    subEntityType = json['sub_entity_type']?.toString();
    subEntityId = json['sub_entity_id']?.toInt();
    assignedToUsers = json['assigned_to_users']?.toString();
    sendNotificationFlag = json['send_notification_flag']?.toString();
    taskTypeDesc = json['task_type_desc']?.toString();
    assosiatedFileIds = json['assosiated_file_ids']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['adhoc_task_id'] = adhocTaskId;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['task_name'] = taskName;
    data['task_desc'] = taskDesc;
    data['assigned_to'] = assignedTo;
    data['task_start'] = taskStart;
    data['task_finish'] = taskFinish;
    data['actual_finsh'] = actualFinsh;
    data['notification1'] = notification1;
    data['notification2'] = notification2;
    data['notification3'] = notification3;
    data['status'] = status;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['editable_flag'] = editableFlag;
    data['tags'] = tags;
    data['reminder'] = reminder;
    data['task_type'] = taskType;
    data['entity_number'] = entityNumber;
    data['entity_name'] = entityName;
    data['task_seq'] = taskSeq;
    data['escaltion_notification'] = escaltionNotification;
    data['template_id'] = templateId;
    data['priority'] = priority;
    data['mitigation_comments'] = mitigationComments;
    data['file_id'] = fileId;
    data['file_name'] = fileName;
    data['email_address1'] = emailAddress1;
    data['risk_amount'] = riskAmount;
    data['risk_mitigation'] = riskMitigation;
    data['value_add'] = valueAdd;
    data['task_number'] = taskNumber;
    data['status_lov'] = statusLov;
    data['task_internal_action'] = taskInternalAction;
    data['task_notice'] = taskNotice;
    data['assigned_by'] = assignedBy;
    data['assigned_date'] = assignedDate;
    data['assigned_by_email'] = assignedByEmail;
    data['status_type'] = statusType;
    data['assigned_by_name'] = assignedByName;
    data['assigned_to_name'] = assignedToName;
    data['sub_entity_type'] = subEntityType;
    data['sub_entity_id'] = subEntityId;
    data['assigned_to_users'] = assignedToUsers;
    data['send_notification_flag'] = sendNotificationFlag;
    return data;
  }
}
