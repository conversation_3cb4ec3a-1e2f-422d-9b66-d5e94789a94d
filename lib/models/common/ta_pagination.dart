// To parse this JSON data, do
//
//     final pagination = paginationFromJson(jsonString);

import 'dart:convert';

Pagination paginationFromJson(String str) => Pagination.fromJson(json.decode(str));

String paginationToJson(Pagination data) => json.encode(data.toJson());

class Pagination {
  Pagination({
    this.paginationInfo,
  });

  PaginationInfo? paginationInfo;

  factory Pagination.fromJson(Map<String, dynamic> json) => Pagination(
        paginationInfo: PaginationInfo.fromJson(json["pagination_info"]),
      );

  Map<String, dynamic> toJson() => {
        "pagination_info": paginationInfo!.toJson(),
      };
}

class PaginationInfo {
  PaginationInfo({
    this.total,
    this.offset,
    this.size,
    this.start,
    this.end,
  });

  int? total;
  int? offset;
  int? size;
  int? start;
  int? end;

  factory PaginationInfo.fromJson(Map<String, dynamic> json) => PaginationInfo(
        total: json["total"],
        offset: json["offset"],
        size: json["size"],
        start: json["start"],
        end: json["end"],
      );

  Map<String, dynamic> toJson() => {
        "total": total,
        "offset": offset,
        "size": size,
        "start": start,
        "end": end,
      };
}
