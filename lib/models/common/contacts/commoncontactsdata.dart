class CommonContactsData {
  int? contactId;
  int? entityId;
  String? entityType;
  String? role;
  String? name;
  String? businessPhone;
  String? extension;
  String? cellphone;
  String? email;
  String? fax;
  String? comments;
  String? company;
  int? orgId;
  int? clientId;
  int? brandId;
  String? creationDate;
  String? createdBy;
  String? contactUserId;
  String? extContactNumber;
  String? lastName;
  String? isSystemUser;
  String? contactType;
  String? status;
  String? isVendor;
  int? seqNumber;
  int? latitude;
  int? longitude;
  String? fullname;
  String? nameFilter;
  String? name1;
  String? roleVal;
  String? fullnameFilter;
  String? workflowContactFlag;
  String? hrName;
  String? isPersonRecord;
  int? personId;
  String? employeeNumber;

  CommonContactsData({
    this.contactId,
    this.entityId,
    this.entityType,
    this.role,
    this.name,
    this.businessPhone,
    this.extension,
    this.cellphone,
    this.email,
    this.fax,
    this.comments,
    this.company,
    this.orgId,
    this.clientId,
    this.brandId,
    this.creationDate,
    this.createdBy,
    this.contactUserId,
    this.extContactNumber,
    this.lastName,
    this.isSystemUser,
    this.contactType,
    this.status,
    this.isVendor,
    this.seqNumber,
    this.latitude,
    this.longitude,
    this.fullname,
    this.nameFilter,
    this.name1,
    this.roleVal,
    this.fullnameFilter,
    this.workflowContactFlag,
    this.hrName,
    this.isPersonRecord,
    this.personId,
    this.employeeNumber,
  });
  CommonContactsData.fromJson(Map<String, dynamic> json) {
    contactId = json['contact_id']?.toInt();
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    role = json['role']?.toString();
    name = json['name']?.toString() ?? '';
    businessPhone = json['business_phone']?.toString();
    extension = json['extension']?.toString();
    cellphone = json['cellphone']?.toString();
    email = json['email']?.toString();
    fax = json['fax']?.toString();
    comments = json['comments']?.toString();
    company = json['company']?.toString();
    orgId = json['org_id']?.toInt();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    contactUserId = json['contact_user_id']?.toString();
    extContactNumber = json['ext_contact_number']?.toString();
    lastName = json['last_name']?.toString() ?? '';
    isSystemUser = json['is_system_user']?.toString();
    contactType = json['contact_type']?.toString();
    status = json['status']?.toString();
    isVendor = json['is_vendor']?.toString();
    seqNumber = json['seq_number']?.toInt();
    latitude = json['latitude']?.toInt();
    longitude = json['longitude']?.toInt();
    fullname = json['fullname']?.toString();
    nameFilter = json['name_filter']?.toString();
    name1 = json['name1']?.toString();
    roleVal = json['role_val']?.toString();
    fullnameFilter = json['fullname_filter']?.toString();
    workflowContactFlag = json['workflow_contact_flag']?.toString();
    hrName = json['hr_name']?.toString();
    isPersonRecord = json['is_person_record']?.toString();
    personId = json['person_id']?.toInt();
    employeeNumber = json['employee_number']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contact_id'] = contactId;
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['role'] = role;
    data['name'] = name;
    data['business_phone'] = businessPhone;
    data['extension'] = extension;
    data['cellphone'] = cellphone;
    data['email'] = email;
    data['fax'] = fax;
    data['comments'] = comments;
    data['company'] = company;
    data['org_id'] = orgId;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['contact_user_id'] = contactUserId;
    data['ext_contact_number'] = extContactNumber;
    data['last_name'] = lastName;
    data['is_system_user'] = isSystemUser;
    data['contact_type'] = contactType;
    data['status'] = status;
    data['is_vendor'] = isVendor;
    data['seq_number'] = seqNumber;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['fullname'] = fullname;
    data['name_filter'] = nameFilter;
    data['name1'] = name1;
    data['role_val'] = roleVal;
    data['fullname_filter'] = fullnameFilter;
    data['workflow_contact_flag'] = workflowContactFlag;
    data['hr_name'] = hrName;
    data['is_person_record'] = isPersonRecord;
    data['person_id'] = personId;
    data['employee_number'] = employeeNumber;
    return data;
  }
}
