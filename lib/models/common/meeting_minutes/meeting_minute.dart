class MeetingMinute {
  int? meetingId;
  String? organizer;
  String? meetingDate;
  String? startTime;
  String? endTime;
  String? meetingTitle;
  String? objective;
  String? agenda;
  String? notes;
  int? clientId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  int? entityId;
  String? entityType;
  int? issues;
  int? lessons;
  String? location;
  String? webexCallIn;
  String? nextLocation;
  String? nextWebexCallIn;
  String? distributionDate;
  String? meetingNumber;
  String? meetingType;
  String? status;
  int? docFolderId;
  String? action;
  String? entityName;
  String? hostnm;

  MeetingMinute({
    this.meetingId,
    this.organizer,
    this.meetingDate,
    this.startTime,
    this.endTime,
    this.meetingTitle,
    this.objective,
    this.agenda,
    this.notes,
    this.clientId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.entityId,
    this.entityType,
    this.issues,
    this.lessons,
    this.location,
    this.webexCallIn,
    this.nextLocation,
    this.nextWebexCallIn,
    this.distributionDate,
    this.meetingNumber,
    this.meetingType,
    this.status,
    this.docFolderId,
    this.action,
    this.entityName,
    this.hostnm,
  });
  MeetingMinute.fromJson(Map<String, dynamic> json) {
    meetingId = json['meeting_id']?.toInt();
    organizer = json['organizer']?.toString();
    meetingDate = json['meeting_date']?.toString();
    startTime = json['start_time']?.toString();
    endTime = json['end_time']?.toString();
    meetingTitle = json['meeting_title']?.toString();
    objective = json['objective']?.toString();
    agenda = json['agenda']?.toString();
    notes = json['notes']?.toString();
    clientId = json['client_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    issues = json['issues']?.toInt();
    lessons = json['lessons']?.toInt();
    location = json['location']?.toString();
    webexCallIn = json['webex_call_in']?.toString();
    nextLocation = json['next_location']?.toString();
    nextWebexCallIn = json['next_webex_call_in']?.toString();
    distributionDate = json['distribution_date']?.toString();
    meetingNumber = json['meeting_number']?.toString();
    meetingType = json['meeting_type']?.toString();
    status = json['status']?.toString();
    docFolderId = json['doc_folder_id']?.toInt();
    action = json['action']?.toInt();
    entityName = json['entity_name']?.toInt();
    hostnm = json['hostnm']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['meeting_id'] = meetingId;
    data['organizer'] = organizer;
    data['meeting_date'] = meetingDate;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['meeting_title'] = meetingTitle;
    data['objective'] = objective;
    data['agenda'] = agenda;
    data['notes'] = notes;
    data['client_id'] = clientId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['issues'] = issues;
    data['lessons'] = lessons;
    data['location'] = location;
    data['webex_call_in'] = webexCallIn;
    data['next_location'] = nextLocation;
    data['next_webex_call_in'] = nextWebexCallIn;
    data['distribution_date'] = distributionDate;
    data['meeting_number'] = meetingNumber;
    data['meeting_type'] = meetingType;
    data['status'] = status;
    data['doc_folder_id'] = docFolderId;
    data['action'] = action;
    data['entity_name'] = entityName;
    data['hostnm'] = hostnm;

    return data;
  }
}
