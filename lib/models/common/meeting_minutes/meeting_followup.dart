class MeetingFollowup {
  int? followupId;
  int? meetingId;
  String? issueType;
  int? itemId;
  String? title;
  String? description;
  String? riskLevel;
  String? followupNeeded;
  String? followupDate;
  String? resolved;
  String? issueTypeDesc;
  String? resolvedDesc;
  String? riskLevelDesc;
  String? followupNeededDesc;

  MeetingFollowup({
    this.followupId,
    this.meetingId,
    this.issueType,
    this.itemId,
    this.title,
    this.description,
    this.riskLevel,
    this.followupNeeded,
    this.followupDate,
    this.resolved,
    this.issueTypeDesc,
    this.resolvedDesc,
    this.riskLevelDesc,
    this.followupNeededDesc,
  });
  MeetingFollowup.fromJson(Map<String, dynamic> json) {
    followupId = json['followup_id']?.toInt();
    meetingId = json['meeting_id']?.toInt();
    issueType = json['issue_type']?.toString();
    itemId = json['item_id']?.toInt();
    title = json['title']?.toString();
    description = json['description']?.toString();
    riskLevel = json['risk_level']?.toString();
    followupNeeded = json['followup_needed']?.toString();
    followupDate = json['followup_date']?.toString();
    resolved = json['resolved']?.toString();
    issueTypeDesc = json['issue_type_desc']?.toString();
    resolvedDesc = json['resolved_desc']?.toString();
    riskLevelDesc = json['risk_level_desc']?.toString();
    followupNeededDesc = json['followup_needed_desc']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['followup_id'] = followupId;
    data['meeting_id'] = meetingId;
    data['issue_type'] = issueType;
    data['item_id'] = itemId;
    data['title'] = title;
    data['description'] = description;
    data['risk_level'] = riskLevel;
    data['followup_needed'] = followupNeeded;
    data['followup_date'] = followupDate;
    data['resolved'] = resolved;
    data['issue_type_desc'] = issueTypeDesc;
    data['resolved_desc'] = resolvedDesc;
    data['risk_level_desc'] = riskLevelDesc;
    data['followup_needed_desc'] = followupNeededDesc;
    return data;
  }
}
