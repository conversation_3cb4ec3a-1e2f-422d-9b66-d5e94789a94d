class MeetingAttendee {
  int? attendeeId;
  int? meetingId;
  String? name;
  String? emailAddress;
  String? role;
  String? attendanceFlag;
  String? lastName;

  MeetingAttendee({
    this.attendeeId,
    this.meetingId,
    this.name,
    this.emailAddress,
    this.role,
    this.attendanceFlag,
    this.lastName,
  });
  MeetingAttendee.fromJson(Map<String, dynamic> json) {
    attendeeId = json['attendee_id']?.toInt();
    meetingId = json['meeting_id']?.toInt();
    name = json['name']?.toString();
    emailAddress = json['email_address']?.toString();
    role = json['role']?.toString();
    attendanceFlag = json['attendance_flag']?.toString();
    lastName = json['last_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['attendee_id'] = attendeeId;
    data['meeting_id'] = meetingId;
    data['name'] = name;
    data['email_address'] = emailAddress;
    data['role'] = role;
    data['attendance_flag'] = attendanceFlag;
    data['last_name'] = lastName;
    return data;
  }
}
