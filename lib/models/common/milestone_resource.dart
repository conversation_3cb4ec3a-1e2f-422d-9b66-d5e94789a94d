class MilestoneResource {
  int? resourceId;
  int? milestoneId;
  String? resourceName;
  String? resourceRole;
  String? resourceRoleDesc;
  String? escalationNotification;
  String? email;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? editableFlag;
  String? completionNotification;

  MilestoneResource({
    this.resourceId,
    this.milestoneId,
    this.resourceName,
    this.resourceRole,
    this.resourceRoleDesc,
    this.escalationNotification,
    this.email,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.editableFlag,
    this.completionNotification,
  });
  MilestoneResource.fromJson(Map<String, dynamic> json) {
    resourceId = json['resource_id']?.toInt();
    milestoneId = json['milestone_id']?.toInt();
    resourceName = json['resource_name']?.toString() ?? '';
    resourceRole = json['resource_role']?.toString() ?? '';
    resourceRoleDesc = json['resource_role_desc']?.toString() ?? '';
    escalationNotification = json['escalation_notification']?.toString() ?? '';
    email = json['email']?.toString() ?? '';
    creationDate = json['creation_date']?.toString() ?? '';
    createdBy = json['created_by']?.toString() ?? '';
    lastUpdateDate = json['last_update_date']?.toString() ?? '';
    lastUpdatedBy = json['last_updated_by']?.toString() ?? '';
    editableFlag = json['editable_flag']?.toString() ?? '';
    completionNotification = json['completion_notification']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['resource_id'] = resourceId;
    data['milestone_id'] = milestoneId;
    data['resource_name'] = resourceName;
    data['resource_role'] = resourceRole;
    data['resource_role_desc'] = resourceRoleDesc;
    data['escalation_notification'] = escalationNotification;
    data['email'] = email;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['editable_flag'] = editableFlag;
    data['completion_notification'] = completionNotification;
    return data;
  }
}
