class Photo {
  int? picId;
  String? picDescription;
  String? picName;
  String? createdBy;
  String? creationDate;

  Photo({
    this.picId,
    this.picDescription,
    this.picName,
    this.createdBy,
    this.creationDate,
  });
  Photo.fromJson(Map<String, dynamic> json) {
    picId = json['picId']?.toInt();
    picDescription = json['picDescription']?.toString();
    picName = json['picName']?.toString();
    createdBy = json['createdBy']?.toString();
    creationDate = json['creationDate']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['picId'] = picId;
    data['picDescription'] = picDescription;
    data['picName'] = picName;
    data['createdBy'] = createdBy;
    data['creationDate'] = creationDate;
    return data;
  }
}
