class DmsFile {
  int? entityId;
  String? entityType;
  int? fileId;
  String? fileName;
  int? folderId;
  String? awsKey;
  String? awsVersionId;
  String? fileContentType;
  String? documentType;
  String? category;
  int? subEntityId;
  String? subEntityType;
  String? folderName;
  String? creationDate;
  bool? selectrow;

  DmsFile({
    this.entityId,
    this.entityType,
    this.fileId,
    this.fileName,
    this.folderId,
    this.awsKey,
    this.awsVersionId,
    this.fileContentType,
    this.documentType,
    this.category,
    this.subEntityId,
    this.subEntityType,
    this.folderName,
    this.creationDate,
    this.selectrow,
  });
  DmsFile.fromJson(Map<String, dynamic> json) {
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    fileId = json['file_id']?.toInt();
    fileName = json['file_name']?.toString();
    folderId = json['folder_id']?.toInt();
    awsKey = json['aws_key']?.toString();
    awsVersionId = json['aws_version_id']?.toString();
    fileContentType = json['file_content_type']?.toString();
    documentType = json['document_type']?.toString();
    category = json['category']?.toString();
    subEntityId = json['sub_entity_id']?.toInt();
    subEntityType = json['sub_entity_type']?.toString();
    folderName = json['folder_name']?.toString();
    creationDate = json['creation_date']?.toString();
    selectrow = false;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['file_id'] = fileId;
    data['file_name'] = fileName;
    data['folder_id'] = folderId;
    data['aws_key'] = awsKey;
    data['aws_version_id'] = awsVersionId;
    data['file_content_type'] = fileContentType;
    data['document_type'] = documentType;
    data['category'] = category;
    data['sub_entity_id'] = subEntityId;
    data['sub_entity_type'] = subEntityType;
    data['folder_name'] = folderName;
    return data;
  }
}
