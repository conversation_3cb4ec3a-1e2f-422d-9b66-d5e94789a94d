import 'package:tangoworkplace/utils/constvariables.dart';

class PaginationInfo {
  final int total;
  final int offset;
  final int size;
  final int start;
  final int end;

  PaginationInfo({
    required this.total,
    required this.offset,
    required this.size,
    required this.start,
    required this.end,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) {
    return PaginationInfo(
      total: json['total'] ?? ConstHelper.pageTotal,
      offset: json['offset'] ?? ConstHelper.pageOffset,
      size: json['size'] ?? ConstHelper.pageSize,
      start: json['start'] ?? ConstHelper.pageStart,
      end: json['end'] ?? ConstHelper.pageEnd,
    );
  }
}