class Contact {
  int? contactId;
  int? entityId;
  String? entityType;
  String? role;
  String? name;
  String? businessPhone;
  String? extension;
  String? cellphone;
  String? fax;
  String? comments;
  String? company;
  String? contactUserId;
  String? isSystemUser;
  String? lastName;
  String? extContactNumber;
  String? name1;
  String? email;
  String? fullname;
  String? nameFilter;
  String? sdaRole;
  String? isVendor;
  int? seqNumber;
  String? fullnameFilter;
  String? contactType;
  bool? selectrow;

  Contact({
    this.contactId,
    this.entityId,
    this.entityType,
    this.role,
    this.name,
    this.businessPhone,
    this.extension,
    this.cellphone,
    this.fax,
    this.comments,
    this.company,
    this.contactUserId,
    this.isSystemUser,
    this.lastName,
    this.extContactNumber,
    this.name1,
    this.email,
    this.fullname,
    this.nameFilter,
    this.sdaRole,
    this.isVendor,
    this.seqNumber,
    this.fullnameFilter,
    this.contactType,
    this.selectrow,
  });
  Contact.fromJson(Map<String, dynamic> json) {
    contactId = json['contact_id']?.toInt();
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    role = json['role']?.toString();
    name = json['name']?.toString();
    businessPhone = json['business_phone']?.toString();
    extension = json['extension']?.toString();
    cellphone = json['cellphone']?.toString();
    fax = json['fax']?.toString();
    comments = json['comments']?.toString();
    company = json['company']?.toString();
    contactUserId = json['contact_user_id']?.toString();
    isSystemUser = json['is_system_user']?.toString();
    lastName = json['last_name']?.toString();
    extContactNumber = json['ext_contact_number']?.toString();
    name1 = json['name1']?.toString();
    email = json['email']?.toString();
    fullname = json['fullname']?.toString();
    nameFilter = json['name_filter']?.toString();
    sdaRole = json['sda_role']?.toString();
    isVendor = json['is_vendor']?.toString();
    seqNumber = json['seq_number']?.toInt();
    fullnameFilter = json['fullname_filter']?.toString();
    contactType = json['contact_type']?.toString();
    selectrow = false;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contact_id'] = contactId;
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['role'] = role;
    data['name'] = name;
    data['business_phone'] = businessPhone;
    data['extension'] = extension;
    data['cellphone'] = cellphone;
    data['fax'] = fax;
    data['comments'] = comments;
    data['company'] = company;
    data['contact_user_id'] = contactUserId;
    data['is_system_user'] = isSystemUser;
    data['last_name'] = lastName;
    data['ext_contact_number'] = extContactNumber;
    data['name1'] = name1;
    data['email'] = email;
    data['fullname'] = fullname;
    data['name_filter'] = nameFilter;
    data['sda_role'] = sdaRole;
    data['is_vendor'] = isVendor;
    data['seq_number'] = seqNumber;
    data['fullname_filter'] = fullnameFilter;
    data['contact_type'] = contactType;
    return data;
  }
}
