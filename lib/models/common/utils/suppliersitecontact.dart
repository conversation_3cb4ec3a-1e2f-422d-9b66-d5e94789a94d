class SupplierSiteContact {
  int? contactId;
  int? supplierId;
  int? supplierSiteId;
  String? role;
  String? name;
  String? lastName;
  String? businessPhone;
  String? extension;
  String? cellphone;
  String? email;
  String? fax;
  String? comments;
  String? company;
  String? contactUserId;
  String? extContactNumber;
  String? isSystemUser;
  String? status;
  String? contactType;
  String? title;
  String? suppliername;
  String? supplierNameId;
  String? suppliertype;
  bool? selectrow;

  SupplierSiteContact({
    this.contactId,
    this.supplierId,
    this.supplierSiteId,
    this.role,
    this.name,
    this.lastName,
    this.businessPhone,
    this.extension,
    this.cellphone,
    this.email,
    this.fax,
    this.comments,
    this.company,
    this.contactUserId,
    this.extContactNumber,
    this.isSystemUser,
    this.status,
    this.contactType,
    this.title,
    this.suppliername,
    this.supplierNameId,
    this.suppliertype,
    this.selectrow,
  });
  SupplierSiteContact.fromJson(Map<String, dynamic> json) {
    contactId = json['contact_id']?.toInt();
    supplierId = json['supplier_id']?.toInt();
    supplierSiteId = json['supplier_site_id']?.toInt();
    role = json['role']?.toString();
    name = json['name']?.toString();
    lastName = json['last_name']?.toString();
    businessPhone = json['business_phone']?.toString();
    extension = json['extension']?.toString();
    cellphone = json['cellphone']?.toString();
    email = json['email']?.toString();
    fax = json['fax']?.toString();
    comments = json['comments']?.toString();
    company = json['company']?.toString();
    contactUserId = json['contact_user_id']?.toString();
    extContactNumber = json['ext_contact_number']?.toString();
    isSystemUser = json['is_system_user']?.toString();
    status = json['status']?.toString();
    contactType = json['contact_type']?.toString();
    title = json['title']?.toString();
    suppliername = json['suppliername']?.toString();
    supplierNameId = json['supplier_name_id']?.toString();
    suppliertype = json['suppliertype']?.toString();
    selectrow = false;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contact_id'] = contactId;
    data['supplier_id'] = supplierId;
    data['supplier_site_id'] = supplierSiteId;
    data['role'] = role;
    data['name'] = name;
    data['last_name'] = lastName;
    data['business_phone'] = businessPhone;
    data['extension'] = extension;
    data['cellphone'] = cellphone;
    data['email'] = email;
    data['fax'] = fax;
    data['comments'] = comments;
    data['company'] = company;
    data['contact_user_id'] = contactUserId;
    data['ext_contact_number'] = extContactNumber;
    data['is_system_user'] = isSystemUser;
    data['status'] = status;
    data['contact_type'] = contactType;
    data['title'] = title;
    data['suppliername'] = suppliername;
    data['supplier_name_id'] = supplierNameId;
    data['suppliertype'] = suppliertype;
    return data;
  }
}
