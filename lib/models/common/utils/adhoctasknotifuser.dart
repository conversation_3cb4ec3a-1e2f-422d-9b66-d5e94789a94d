class AdhocTaskNotifUser {
  int? adhocTaskId;
  String? entityType;
  int? entityId;
  String? userName;
  String? firstName;
  String? lastName;
  String? emailAddress;
  int? tmcsAdhocTasksNotifySeq;

  AdhocTaskNotifUser({
    this.adhocTaskId,
    this.entityType,
    this.entityId,
    this.userName,
    this.firstName,
    this.lastName,
    this.emailAddress,
    this.tmcsAdhocTasksNotifySeq,
  });
  AdhocTaskNotifUser.fromJson(Map<String, dynamic> json) {
    adhocTaskId = json['adhoc_task_id']?.toInt();
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    userName = json['user_name']?.toString();
    firstName = json['first_name']?.toString();
    lastName = json['last_name']?.toString();
    emailAddress = json['email_address']?.toString();
    tmcsAdhocTasksNotifySeq = json['tmcs_adhoc_tasks_notify_seq']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['adhoc_task_id'] = adhocTaskId;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['user_name'] = userName;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['email_address'] = emailAddress;
    data['tmcs_adhoc_tasks_notify_seq'] = tmcsAdhocTasksNotifySeq;
    return data;
  }
}
