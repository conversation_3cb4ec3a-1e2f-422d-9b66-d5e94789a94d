class SystemUser {
  int? userId;
  int? clientId;
  String? userName;
  String? firstName;
  String? lastName;
  String? emailAddress;
  String? userType;
  String? countryCode;
  String? fullname;
  String? fullnamesuggitems;
  bool? selectrow;

  SystemUser({
    this.userId,
    this.clientId,
    this.userName,
    this.firstName,
    this.lastName,
    this.emailAddress,
    this.userType,
    this.countryCode,
    this.fullname,
    this.fullnamesuggitems,
    this.selectrow,
  });
  SystemUser.fromJson(Map<String, dynamic> json) {
    userId = json['user_id']?.toInt();
    clientId = json['client_id']?.toInt();
    userName = json['user_name']?.toString();
    firstName = json['first_name']?.toString();
    lastName = json['last_name']?.toString();
    emailAddress = json['email_address']?.toString();
    userType = json['user_type']?.toString();
    countryCode = json['country_code']?.toString();
    fullname = json['fullname']?.toString();
    fullnamesuggitems = json['fullnamesuggitems']?.toString();
    selectrow = false;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['user_id'] = userId;
    data['client_id'] = clientId;
    data['user_name'] = userName;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['email_address'] = emailAddress;
    data['user_type'] = userType;
    data['country_code'] = countryCode;
    data['fullname'] = fullname;
    data['fullnamesuggitems'] = fullnamesuggitems;
    return data;
  }
}
