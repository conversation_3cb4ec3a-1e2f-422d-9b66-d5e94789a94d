class Projects {
  final String? projectname;
  final int? projectid;
  final int? picid;
  final String? city;
  final String? status;
  final String? address;
  final String? projectnumber;

  Projects(
      {this.projectname,
      this.projectid,
      this.picid,
      this.city,
      this.status,
      this.address,
      this.projectnumber});

  factory Projects.fromJson(Map<String, dynamic> jsonstr) {
    return Projects(
        projectname: jsonstr['projectname'] ?? '',
        projectid: jsonstr['projectid'],
        picid: jsonstr['picid'],
        city: jsonstr['city'] ?? '',
        status: jsonstr['status'] ?? '',
        address: jsonstr['address'] ?? '',
        projectnumber: jsonstr['projectNumber']);
  }
}
