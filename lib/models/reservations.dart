class Reservations {
  final int? reservationId;
  final String? reservationNumber;
  final String? reservationType;
  final String? reservationTitle;
  final String? startDate;
  final String? endDate;
  final String? status;
  final int? propertyId;
  final int? buildingId;
  final int? spaceId;
  final int? floorId;
  final String? description;
  int? rating;
  final String? selfReserve;
  final String? allDay;
  final String? serviceItems;
  final String? isRecurring;
  final String? createdBy;
  final String? propertyName;
  final String? buildingName;
  final String? floorName;
  final String? spaceDisplayName;
  final String? assignees;
  final List? spaceAttributes;

  Reservations(
      {this.reservationId,
      this.reservationNumber,
      this.reservationType,
      this.reservationTitle,
      this.startDate,
      this.endDate,
      this.status,
      this.propertyId,
      this.buildingId,
      this.spaceId,
      this.floorId,
      this.description,
      this.rating,
      this.selfReserve,
      this.allDay,
      this.serviceItems,
      this.isRecurring,
      this.createdBy,
      this.propertyName,
      this.buildingName,
      this.floorName,
      this.spaceDisplayName,
      this.assignees,
      this.spaceAttributes});

  factory Reservations.fromJson(Map<String, dynamic> json) {
    return Reservations(
        reservationId: json['reservationId'],
        reservationNumber: json['reservationNumber'],
        reservationType: json['reservationType'],
        reservationTitle: json['reservationTitle'] ?? '',
        startDate: json['startDate'],
        endDate: json['endDate'],
        status: json['status'],
        propertyId: json['propertyId'],
        buildingId: json['buildingId'],
        spaceId: json['spaceId'],
        floorId: json['floorId'],
        description: json['description'] ?? '',
        rating: json['rating'] ?? -1,
        selfReserve: json['selfReserve'],
        allDay: json['allDay'],
        serviceItems: json['serviceItems'] ?? '',
        isRecurring: json['isRecurring'] ?? '',
        createdBy: json['createdBy'],
        propertyName: json['propertyName'],
        buildingName: json['buildingName'],
        floorName: json['floorName'],
        spaceDisplayName: json['spaceDisplayName'] ?? '',
        assignees: json['assignees'] ?? '',
        spaceAttributes: json['spaceAttributes']);
  }
}
