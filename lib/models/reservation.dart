import 'dart:convert';

class Reservation {
  final int? requestId;
  final String? requestNumber;
  final String? spaceDisplayName;
  final String? status;
  final String? requestedStartDate;
  final String? requestedEndDate;
  final int? propertyId;
  final int? buildingId;
  final int? spaceId;
  final int? floorId;
  final String? requestedFor;

  Reservation({
    this.requestId,
    this.requestNumber,
    this.spaceDisplayName,
    this.status,
    this.requestedStartDate,
    this.requestedEndDate,
    this.propertyId,
    this.buildingId,
    this.spaceId,
    this.floorId,
    this.requestedFor,
  });

  Reservation copyWith({
    int? requestId,
    String? requestNumber,
    String? spaceDisplayName,
    String? status,
    String? requestedStartDate,
    String? requestedEndDate,
    int? propertyId,
    int? buildingId,
    int? spaceId,
    int? floorId,
    String? requestedFor,
  }) {
    return Reservation(
      requestId: requestId ?? this.requestId,
      requestNumber: requestNumber ?? this.requestNumber,
      spaceDisplayName: spaceDisplayName ?? this.spaceDisplayName,
      status: status ?? this.status,
      requestedStartDate: requestedStartDate ?? this.requestedStartDate,
      requestedEndDate: requestedEndDate ?? this.requestedEndDate,
      propertyId: propertyId ?? this.propertyId,
      buildingId: buildingId ?? this.buildingId,
      spaceId: spaceId ?? this.spaceId,
      floorId: floorId ?? this.floorId,
      requestedFor: requestedFor ?? this.requestedFor,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'requestId': requestId,
      'requestNumber': requestNumber,
      'spaceDisplayName': spaceDisplayName,
      'status': status,
      'requestedStartDate': requestedStartDate,
      'requestedEndDate': requestedEndDate,
      'propertyId': propertyId,
      'buildingId': buildingId,
      'spaceId': spaceId,
      'floorId': floorId,
      'requestedFor': requestedFor,
    };
  }

  factory Reservation.fromMap(Map<String, dynamic>? map) {
    if (map == null) return Reservation();

    return Reservation(
      requestId: map['requestId']?.toInt(),
      requestNumber: map['requestNumber'],
      spaceDisplayName: map['spaceDisplayName'],
      status: map['status'],
      requestedStartDate: map['requestedStartDate'],
      requestedEndDate: map['requestedEndDate'],
      propertyId: map['propertyId']?.toInt(),
      buildingId: map['buildingId']?.toInt(),
      spaceId: map['spaceId']?.toInt(),
      floorId: map['floorId']?.toInt(),
      requestedFor: map['requestedFor'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Reservation.fromJson(String source) => Reservation.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Reservation(requestId: $requestId, requestNumber: $requestNumber, spaceDisplayName: $spaceDisplayName, status: $status, requestedStartDate: $requestedStartDate, requestedEndDate: $requestedEndDate, propertyId: $propertyId, buildingId: $buildingId, spaceId: $spaceId, floorId: $floorId, requestedFor: $requestedFor)';
  }

  @override
  bool operator ==(Object o) {
    if (identical(this, o)) return true;

    return o is Reservation &&
        o.requestId == requestId &&
        o.requestNumber == requestNumber &&
        o.spaceDisplayName == spaceDisplayName &&
        o.status == status &&
        o.requestedStartDate == requestedStartDate &&
        o.requestedEndDate == requestedEndDate &&
        o.propertyId == propertyId &&
        o.buildingId == buildingId &&
        o.spaceId == spaceId &&
        o.floorId == floorId &&
        o.requestedFor == requestedFor;
  }

  @override
  int get hashCode {
    return requestId.hashCode ^
        requestNumber.hashCode ^
        spaceDisplayName.hashCode ^
        status.hashCode ^
        requestedStartDate.hashCode ^
        requestedEndDate.hashCode ^
        propertyId.hashCode ^
        buildingId.hashCode ^
        spaceId.hashCode ^
        floorId.hashCode ^
        requestedFor.hashCode;
  }
}
