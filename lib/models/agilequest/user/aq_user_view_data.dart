
import 'package:json_annotation/json_annotation.dart';

part 'aq_user_view_data.g.dart';

@JsonSerializable()
class AqUserViewData {
  int? sysidUser;
  int? sysidStatus;
  String? firstName;
  String? lastName;
  String? emailAddress;
  String? middleInitial;

  AqUserViewData({
    this.sysidStatus,
    this.sysidUser,
    this.firstName,
    this.lastName,
    this.emailAddress,
    this.middleInitial,
  });

  factory AqUserViewData.fromJson(Map<String, dynamic> json) =>
      _$AqUserViewDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqUserViewDataToJson(this);

  String getUserName() {
    if(firstName == null && lastName == null) {
      return 'External User';
    }
    return '${firstName?.trim() ?? ''} ${middleInitial?.trim() ?? ''} ${lastName?.trim() ?? ''}'.trim();
  }
}