import 'package:json_annotation/json_annotation.dart';

part 'aq_resource_images_data.g.dart';

@JsonSerializable()
class AqResourceImagesData {
  int? sysidResourceImage;
  String? name;
  String? description;
  String? imageSource;
  String? thumbnailSource;
  bool? defaultImage;
  int? height;
  int? width;
  int? displayOrder;

  AqResourceImagesData({
    this.defaultImage,
    this.description,
    this.displayOrder,
    this.height,
    this.imageSource,
    this.name,
    this.sysidResourceImage,
    this.thumbnailSource,
    this.width,
  });

  factory AqResourceImagesData.fromJson(Map<String, dynamic> json) => _$AqResourceImagesDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqResourceImagesDataToJson(this);
}
