import 'package:json_annotation/json_annotation.dart';

part 'aq_resource_attribute_values_data.g.dart';

@JsonSerializable()
class AqResourceAttributeValuesData {
  int? sysidResourceAttributeValue;
  int? sysidResourceAttribute;
  bool? booleanValue;
  int? sysidAttributeDataType;

  AqResourceAttributeValuesData({
    this.booleanValue,
    this.sysidAttributeDataType,
    this.sysidResourceAttribute,
    this.sysidResourceAttributeValue,
  });

  factory AqResourceAttributeValuesData.fromJson(Map<String, dynamic> json) => _$AqResourceAttributeValuesDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqResourceAttributeValuesDataToJson(this);
}
