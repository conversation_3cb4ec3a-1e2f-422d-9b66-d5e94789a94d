// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_resource_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqResourceData _$AqResourceDataFromJson(Map<String, dynamic> json) =>
    AqResourceData(
      discriminator: json['discriminator'] as String?,
      sysidResource: json['sysidResource'] as int?,
      sysidCategory: json['sysidCategory'] as int?,
      sysidResourceType: json['sysidResourceType'] as int?,
      sysidAccount: json['sysidAccount'] as int?,
      sysidResourcePricings: json['sysidResourcePricings'] as int?,
      sysidAccountResource: json['sysidAccountResource'] as int?,
      originalResource: json['originalResource'] as bool?,
      name: json['name'] as String?,
      sysidResourceStatus: json['sysidResourceStatus'] as int?,
      description: json['description'] as String?,
      cancelLeadTimeMinutes: json['cancelLeadTimeMinutes'] as int?,
      leadTimeMinutes: json['leadTimeMinutes'] as int?,
      maxFutureReservationDays: json['maxFutureReservationDays'] as int?,
      comments: json['comments'] as String?,
      setupMinutes: json['setupMinutes'] as int?,
      breakDownMinutes: json['breakDownMinutes'] as int?,
      sysidChargeCodeType: json['sysidChargeCodeType'] as int?,
      minChargeCodesRequired: json['minChargeCodesRequired'] as int?,
      publiclyViewable: json['publiclyViewable'] as bool?,
      shortTermNotification: json['shortTermNotification'] as bool?,
      shortTermMinutes: json['shortTermMinutes'] as String?,
      minDurationMinutes: json['minDurationMinutes'] as int?,
      maxDurationMinutes: json['maxDurationMinutes'] as int?,
      sysidDepartment: json['sysidDepartment'] as int?,
      locations: (json['locations'] as List<dynamic>?)
          ?.map((e) => AqResourceLocation.fromJson(e as Map<String, dynamic>))
          .toList(),
      resourceAttributeValues: (json['resourceAttributeValues']
              as List<dynamic>?)
          ?.map((e) =>
              AqResourceAttributeValuesData.fromJson(e as Map<String, dynamic>))
          .toList(),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => AqResourceImagesData.fromJson(e as Map<String, dynamic>))
          .toList(),
      isWaitlistable: json['isWaitlistable'] as bool?,
      backToBackMinutes: json['backToBackMinutes'] as int?,
    );

Map<String, dynamic> _$AqResourceDataToJson(AqResourceData instance) =>
    <String, dynamic>{
      'discriminator': instance.discriminator,
      'sysidResource': instance.sysidResource,
      'sysidCategory': instance.sysidCategory,
      'sysidResourceType': instance.sysidResourceType,
      'sysidAccount': instance.sysidAccount,
      'sysidResourcePricings': instance.sysidResourcePricings,
      'sysidAccountResource': instance.sysidAccountResource,
      'originalResource': instance.originalResource,
      'name': instance.name,
      'sysidResourceStatus': instance.sysidResourceStatus,
      'description': instance.description,
      'cancelLeadTimeMinutes': instance.cancelLeadTimeMinutes,
      'leadTimeMinutes': instance.leadTimeMinutes,
      'maxFutureReservationDays': instance.maxFutureReservationDays,
      'comments': instance.comments,
      'setupMinutes': instance.setupMinutes,
      'breakDownMinutes': instance.breakDownMinutes,
      'sysidChargeCodeType': instance.sysidChargeCodeType,
      'minChargeCodesRequired': instance.minChargeCodesRequired,
      'publiclyViewable': instance.publiclyViewable,
      'shortTermNotification': instance.shortTermNotification,
      'shortTermMinutes': instance.shortTermMinutes,
      'minDurationMinutes': instance.minDurationMinutes,
      'maxDurationMinutes': instance.maxDurationMinutes,
      'sysidDepartment': instance.sysidDepartment,
      'locations': instance.locations,
      'resourceAttributeValues': instance.resourceAttributeValues,
      'images': instance.images,
      'isWaitlistable': instance.isWaitlistable,
      'backToBackMinutes': instance.backToBackMinutes,
    };
