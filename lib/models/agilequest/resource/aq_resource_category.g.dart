// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_resource_category.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqResourceCategory _$AqResourceCategoryFromJson(Map<String, dynamic> json) =>
    AqResourceCategory(
      sysidCategory: json['sysidCategory'] as int,
      discriminator:
          $enumDecode(_$ReserveCategoryTypeEnumMap, json['discriminator']),
      name: json['name'] as String,
      languageTagKey: json['languageTagKey'] as String,
      iconPropKey: json['iconPropKey'] as String?,
      sysidRoomType: json['sysidRoomType'] as int?,
      hasCapacity: json['hasCapacity'] as bool?,
      allowMultiDay: json['allowMultiDay'] as bool?,
      canBeAChild: json['canBeAChild'] as bool?,
      canHaveChildren: json['canHaveChildren'] as bool?,
    );

Map<String, dynamic> _$AqResourceCategoryToJson(AqResourceCategory instance) =>
    <String, dynamic>{
      'sysidCategory': instance.sysidCategory,
      'discriminator': _$ReserveCategoryTypeEnumMap[instance.discriminator]!,
      'name': instance.name,
      'languageTagKey': instance.languageTagKey,
      'iconPropKey': instance.iconPropKey,
      'sysidRoomType': instance.sysidRoomType,
      'hasCapacity': instance.hasCapacity,
      'allowMultiDay': instance.allowMultiDay,
      'canBeAChild': instance.canBeAChild,
      'canHaveChildren': instance.canHaveChildren,
    };

const _$ReserveCategoryTypeEnumMap = {
  ReserveCategoryType.ROOM: 'ROOM',
  ReserveCategoryType.XHST: 'XHST',
  ReserveCategoryType.POOL: 'POOL',
  ReserveCategoryType.VNDR: 'VNDR',
  ReserveCategoryType.SRVC: 'SRVC',
  ReserveCategoryType.ALL: 'ALL',
  ReserveCategoryType.NONE: 'NONE',
};
