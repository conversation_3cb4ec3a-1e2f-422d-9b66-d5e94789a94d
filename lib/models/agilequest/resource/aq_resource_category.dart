import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/types/aq_reserve_const.dart';

part 'aq_resource_category.g.dart';

@JsonSerializable()
class AqResourceCategory {
  int sysidCategory;
  ReserveCategoryType discriminator;
  String name;
  String languageTagKey;
  String? iconPropKey;
  int? sysidRoomType;
  bool? hasCapacity;
  bool? allowMultiDay;
  bool? canBeAChild;
  bool? canHaveChildren;

  AqResourceCategory(
      {required this.sysidCategory,
      required this.discriminator,
      required this.name,
      required this.languageTagKey,
      this.iconPropKey,
      this.sysidRoomType,
      required this.hasCapacity,
      required this.allowMultiDay,
      required this.canBeAChild,
      required this.canHaveChildren});

  factory AqResourceCategory.fromJson(Map<String, dynamic> json) => _$AqResourceCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$AqResourceCategoryToJson(this);

  factory AqResourceCategory.none() => AqResourceCategory(
        sysidCategory: -1,
        discriminator: ReserveCategoryType.NONE,
        name: "None",
        languageTagKey: "",
        hasCapacity: false,
        allowMultiDay: false,
        canBeAChild: false,
        canHaveChildren: false,
      );

  factory AqResourceCategory.all() => AqResourceCategory(
        sysidCategory: -1,
        discriminator: ReserveCategoryType.ALL,
        name: "All",
        languageTagKey: "",
        hasCapacity: false,
        allowMultiDay: false,
        canBeAChild: false,
        canHaveChildren: false,
      );
}
