// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_resource_images_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqResourceImagesData _$AqResourceImagesDataFromJson(
        Map<String, dynamic> json) =>
    AqResourceImagesData(
      defaultImage: json['defaultImage'] as bool?,
      description: json['description'] as String?,
      displayOrder: json['displayOrder'] as int?,
      height: json['height'] as int?,
      imageSource: json['imageSource'] as String?,
      name: json['name'] as String?,
      sysidResourceImage: json['sysidResourceImage'] as int?,
      thumbnailSource: json['thumbnailSource'] as String?,
      width: json['width'] as int?,
    );

Map<String, dynamic> _$AqResourceImagesDataToJson(
        AqResourceImagesData instance) =>
    <String, dynamic>{
      'sysidResourceImage': instance.sysidResourceImage,
      'name': instance.name,
      'description': instance.description,
      'imageSource': instance.imageSource,
      'thumbnailSource': instance.thumbnailSource,
      'defaultImage': instance.defaultImage,
      'height': instance.height,
      'width': instance.width,
      'displayOrder': instance.displayOrder,
    };
