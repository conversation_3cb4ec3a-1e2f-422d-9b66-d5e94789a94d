import '../location/aq_resource_location.dart';
import 'aq_resource_attribute_values_data.dart';
import 'aq_resource_images_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'aq_resource_data.g.dart';

@JsonSerializable()
class AqResourceData {
  String? discriminator;
  int? sysidResource;
  int? sysidCategory;
  int? sysidResourceType;
  int? sysidAccount;
  int? sysidResourcePricings;
  int? sysidAccountResource;
  bool? originalResource;
  String? name;
  int? sysidResourceStatus;
  String? description;
  int? cancelLeadTimeMinutes;
  int? leadTimeMinutes;
  int? maxFutureReservationDays;
  String? comments;
  int? setupMinutes;
  int? breakDownMinutes;
  int? sysidChargeCodeType;
  int? minChargeCodesRequired;
  bool? publiclyViewable;
  bool? shortTermNotification;
  String? shortTermMinutes;
  int? minDurationMinutes;
  int? maxDurationMinutes;
  int? sysidDepartment;
  List<AqResourceLocation>? locations;
  List<AqResourceAttributeValuesData>? resourceAttributeValues;
  // List<Notifications?>? notifications;
  List<AqResourceImagesData>? images;
  //List<Forums?>? forums;
  // List<Schedules?>? resourceSchedules;
//  List<InactiveBlocks?>? inactiveBlocks;
  // Pricings? pricings;
  // List<ApprovalFlows?>? approvalFlows;
  bool? isWaitlistable;
  // List<MapLocators?>? mapLocators;
  // List<DependentResources?>? dependentResources;
  int? backToBackMinutes;
  // RoomData? roomData;
  //List<Identities?>? identities;

  AqResourceData({
    this.discriminator,
    this.sysidResource,
    this.sysidCategory,
    this.sysidResourceType,
    this.sysidAccount,
    this.sysidResourcePricings,
    this.sysidAccountResource,
    this.originalResource,
    this.name,
    this.sysidResourceStatus,
    this.description,
    this.cancelLeadTimeMinutes,
    this.leadTimeMinutes,
    this.maxFutureReservationDays,
    this.comments,
    this.setupMinutes,
    this.breakDownMinutes,
    this.sysidChargeCodeType,
    this.minChargeCodesRequired,
    this.publiclyViewable,
    this.shortTermNotification,
    this.shortTermMinutes,
    this.minDurationMinutes,
    this.maxDurationMinutes,
    this.sysidDepartment,
    this.locations,
    this.resourceAttributeValues,
    // this.notifications,
    this.images,
    // this.forums,
    // this.resourceSchedules,
    // this.inactiveBlocks,
    //  this.pricings,
    // this.approvalFlows,
    this.isWaitlistable,
    // this.mapLocators,
    //  this.dependentResources,
    this.backToBackMinutes,
    // this.roomData,
    // this.identities,
  });
  factory AqResourceData.fromJson(Map<String, dynamic> json) => _$AqResourceDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqResourceDataToJson(this);
}
