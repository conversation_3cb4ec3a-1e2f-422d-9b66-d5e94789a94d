// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_review_view.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReviewView _$AqReviewViewFromJson(Map<String, dynamic> json) => AqReviewView(
      sysidReview: json['sysidReview'] as int?,
      rating: json['rating'] as int,
      sysidResource: json['sysidResource'] as int,
      comment: json['comment'] as String?,
      aqCreateTime: json['aqCreateTime'] == null
          ? null
          : AqDate.fromJson(json['aqCreateTime'] as Map<String, dynamic>),
      aqStartTime: json['aqStartTime'] == null
          ? null
          : AqDate.fromJson(json['aqStartTime'] as Map<String, dynamic>),
      sysidUser: json['sysidUser'] as int,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      preferredName: json['preferredName'] as String?,
    );

Map<String, dynamic> _$AqReviewViewToJson(AqReviewView instance) =>
    <String, dynamic>{
      'sysidReview': instance.sysidReview,
      'rating': instance.rating,
      'sysidResource': instance.sysidResource,
      'comment': instance.comment,
      'aqStartTime': instance.aqStartTime,
      'aqCreateTime': instance.aqCreateTime,
      'sysidUser': instance.sysidUser,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'preferredName': instance.preferredName,
    };
