

import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';

part 'aq_review_view.g.dart';

@JsonSerializable()
class AqReviewView {
  int? sysidReview;
  int rating;
  int sysidResource;
  String? comment;
  AqDate? aqStartTime;
  AqDate? aqCreateTime;
  int sysidUser;
  String firstName;
  String lastName;
  String? preferredName;
  

  AqReviewView({
    this.sysidReview,
    required this.rating,
    required this.sysidResource,
    this.comment,
    this.aqCreateTime,
    this.aqStartTime,
    required this.sysidUser,
    required this.firstName,
    required this.lastName,
    this.preferredName
  });

  factory AqReviewView.fromJson(Map<String, dynamic> json) => _$AqReviewViewFromJson(json);

  Map<String, dynamic> toJson() => _$AqReviewViewToJson(this);
}
