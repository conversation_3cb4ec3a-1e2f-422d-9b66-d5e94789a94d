import 'package:json_annotation/json_annotation.dart';

part 'aq_date.g.dart';

@JsonSerializable(includeIfNull: false)
class AqDate {
  int? year;
  int? month;
  int? dayOfMonth;
  int? hour;
  int? minute;
  String tzIdValue;
  bool? noEndDate;
  String? discriminator;

  AqDate({
    this.year,
    this.month,
    this.dayOfMonth,
    this.hour,
    this.minute,
    required this.tzIdValue,
    this.noEndDate,
    this.discriminator,
  });

  factory AqDate.fromJson(Map<String, dynamic> json) => _$AqDateFromJson(json);

  Map<String, dynamic> toJson() => _$AqDateToJson(this);

  factory AqDate.fromDate(DateTime date, String tzValue, {String? discriminator})=> AqDate(
        year: date.year,
        month: date.month,
        dayOfMonth: date.day,
        hour: date.hour,
        minute: date.minute,
        discriminator: discriminator ?? 'AqDate',
        tzIdValue: tzValue,
      );

  factory AqDate.noEndDate(String tzValue) => AqDate(
        discriminator: 'AqNoEndDate',
        tzIdValue: tzValue,
      );
 
}
