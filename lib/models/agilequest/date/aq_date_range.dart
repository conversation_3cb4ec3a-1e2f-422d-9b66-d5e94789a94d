import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';

part 'aq_date_range.g.dart';

@JsonSerializable()
class AqDateRange {
  AqDate startTime;
  AqDate endTime;

  AqDateRange(this.startTime, this.endTime,);


  factory AqDateRange.fromJson(Map<String, dynamic> json) => _$AqDateRangeFromJson(json);

  Map<String, dynamic> toJson() => _$AqDateRangeToJson(this);
}