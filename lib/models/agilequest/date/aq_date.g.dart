// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_date.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqDate _$AqDateFromJson(Map<String, dynamic> json) => AqDate(
      year: json['year'] as int?,
      month: json['month'] as int?,
      dayOfMonth: json['dayOfMonth'] as int?,
      hour: json['hour'] as int?,
      minute: json['minute'] as int?,
      tzIdValue: json['tzIdValue'] as String,
      noEndDate: json['noEndDate'] as bool?,
      discriminator: json['discriminator'] as String?,
    );

Map<String, dynamic> _$AqDateToJson(AqDate instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('year', instance.year);
  writeNotNull('month', instance.month);
  writeNotNull('dayOfMonth', instance.dayOfMonth);
  writeNotNull('hour', instance.hour);
  writeNotNull('minute', instance.minute);
  val['tzIdValue'] = instance.tzIdValue;
  writeNotNull('noEndDate', instance.noEndDate);
  writeNotNull('discriminator', instance.discriminator);
  return val;
}
