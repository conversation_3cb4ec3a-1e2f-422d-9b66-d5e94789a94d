// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_recurrence_rule.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqRecurrenceRule _$AqRecurrenceRuleFromJson(Map<String, dynamic> json) =>
    AqRecurrenceRule(
      sysidRecurrenceRule: json['sysidRecurrenceRule'] as int?,
      sysidMonthlyDayType: json['sysidMonthlyDayType'] as int?,
      sysidRecurrencePatternType: json['sysidRecurrencePatternType'] as int?,
      aqStartDate: json['aqStartDate'] == null
          ? null
          : AqDate.fromJson(json['aqStartDate'] as Map<String, dynamic>),
      aqEndDate: json['aqEndDate'] == null
          ? null
          : AqDate.fromJson(json['aqEndDate'] as Map<String, dynamic>),
      occurrences: json['occurrences'] as int?,
      sysidMonthlyDayOccurrenceType:
          json['sysidMonthlyDayOccurrenceType'] as int?,
      interval: json['interval'] as int?,
      theDate: json['theDate'] as int?,
      onSunday: json['onSunday'] as bool?,
      onMonday: json['onMonday'] as bool?,
      onTuesday: json['onTuesday'] as bool?,
      onWednesday: json['onWednesday'] as bool?,
      onThursday: json['onThursday'] as bool?,
      onFriday: json['onFriday'] as bool?,
      onSaturday: json['onSaturday'] as bool?,
    );

Map<String, dynamic> _$AqRecurrenceRuleToJson(AqRecurrenceRule instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('sysidRecurrenceRule', instance.sysidRecurrenceRule);
  writeNotNull('sysidMonthlyDayType', instance.sysidMonthlyDayType);
  writeNotNull(
      'sysidRecurrencePatternType', instance.sysidRecurrencePatternType);
  writeNotNull('aqStartDate', instance.aqStartDate);
  writeNotNull('aqEndDate', instance.aqEndDate);
  writeNotNull('occurrences', instance.occurrences);
  writeNotNull(
      'sysidMonthlyDayOccurrenceType', instance.sysidMonthlyDayOccurrenceType);
  writeNotNull('interval', instance.interval);
  writeNotNull('theDate', instance.theDate);
  writeNotNull('onSunday', instance.onSunday);
  writeNotNull('onMonday', instance.onMonday);
  writeNotNull('onTuesday', instance.onTuesday);
  writeNotNull('onWednesday', instance.onWednesday);
  writeNotNull('onThursday', instance.onThursday);
  writeNotNull('onFriday', instance.onFriday);
  writeNotNull('onSaturday', instance.onSaturday);
  return val;
}
