import 'package:json_annotation/json_annotation.dart';

part 'aq_resource_location.g.dart';

@JsonSerializable()
class AqResourceLocation {
  int? sysidLocatorNode;
  int? sysidVenueNode;
  int? sysidLocation;
  String? venueName;
  String? locationName;
  String? address1;
  String? address2;
  String? city;
  String? stateProvince;
  String? postalCode;
  String? country;

  AqResourceLocation({
    this.sysidLocatorNode,
    this.sysidVenueNode,
    this.sysidLocation,
    this.venueName,
    this.locationName,
    this.address1,
    this.address2,
    this.city,
    this.stateProvince,
    this.postalCode,
    this.country,
  });

  factory AqResourceLocation.fromJson(Map<String, dynamic> json) => _$AqResourceLocationFromJson(json);

  Map<String, dynamic> toJson() => _$AqResourceLocationToJson(this);
}
