import 'package:json_annotation/json_annotation.dart';

part 'aq_location_data.g.dart';

@JsonSerializable()
class AqLocationData {
  int sysidLocatorNode;
  int? sysidParentNode;
  bool isVenue;
  String name;
  int locationLevel;
  String? tzvalue;
  double? latitude;
  double? longitude;

  AqLocationData({
    required this.sysidLocatorNode,
    this.sysidParentNode,
    required this.isVenue,
    required this.name,
    required this.locationLevel,
    this.tzvalue,
    this.latitude,
    this.longitude,
  });

  factory AqLocationData.fromJson(Map<String, dynamic> json) => _$AqLocationDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqLocationDataToJson(this);

  factory AqLocationData.all() => AqLocationData(
        sysidLocatorNode: -1,
        isVenue: true,
        name: "All",
        locationLevel: 0,
      );

  factory AqLocationData.alina() => AqLocationData(
    sysidLocatorNode: -1,
    isVenue: true,
    name: "<PERSON><PERSON>",
    locationLevel: 0,
  );
}
