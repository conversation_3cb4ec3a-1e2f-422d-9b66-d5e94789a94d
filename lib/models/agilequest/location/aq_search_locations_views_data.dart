

import 'package:json_annotation/json_annotation.dart';
import 'aq_location_data.dart';

part 'aq_search_locations_views_data.g.dart';

@JsonSerializable()
class AqSearchLocationsViewsData {

  List<AqLocationData>? locations;
  List<int>? sysidCategories;

  AqSearchLocationsViewsData(
    this.locations,
    this.sysidCategories,
  );

  factory AqSearchLocationsViewsData.fromJson(Map<String, dynamic> json) => _$AqSearchLocationsViewsDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqSearchLocationsViewsDataToJson(this);
}
