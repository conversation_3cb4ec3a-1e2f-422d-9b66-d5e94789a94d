// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_search_locations_views_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqSearchLocationsViewsData _$AqSearchLocationsViewsDataFromJson(
        Map<String, dynamic> json) =>
    AqSearchLocationsViewsData(
      (json['locations'] as List<dynamic>?)
          ?.map((e) => AqLocationData.fromJson(e as Map<String, dynamic>))
          .toList(),
      (json['sysidCategories'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
    );

Map<String, dynamic> _$AqSearchLocationsViewsDataToJson(
        AqSearchLocationsViewsData instance) =>
    <String, dynamic>{
      'locations': instance.locations,
      'sysidCategories': instance.sysidCategories,
    };
