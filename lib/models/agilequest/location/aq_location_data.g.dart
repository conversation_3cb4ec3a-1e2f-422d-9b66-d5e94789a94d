// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_location_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqLocationData _$AqLocationDataFromJson(Map<String, dynamic> json) =>
    AqLocationData(
      sysidLocatorNode: json['sysidLocatorNode'] as int,
      sysidParentNode: json['sysidParentNode'] as int?,
      isVenue: json['isVenue'] as bool,
      name: json['name'] as String,
      locationLevel: json['locationLevel'] as int,
      tzvalue: json['tzvalue'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$AqLocationDataToJson(AqLocationData instance) =>
    <String, dynamic>{
      'sysidLocatorNode': instance.sysidLocatorNode,
      'sysidParentNode': instance.sysidParentNode,
      'isVenue': instance.isVenue,
      'name': instance.name,
      'locationLevel': instance.locationLevel,
      'tzvalue': instance.tzvalue,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
