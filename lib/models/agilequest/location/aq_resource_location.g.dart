// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_resource_location.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqResourceLocation _$AqResourceLocationFromJson(Map<String, dynamic> json) =>
    AqResourceLocation(
      sysidLocatorNode: json['sysidLocatorNode'] as int?,
      sysidVenueNode: json['sysidVenueNode'] as int?,
      sysidLocation: json['sysidLocation'] as int?,
      venueName: json['venueName'] as String?,
      locationName: json['locationName'] as String?,
      address1: json['address1'] as String?,
      address2: json['address2'] as String?,
      city: json['city'] as String?,
      stateProvince: json['stateProvince'] as String?,
      postalCode: json['postalCode'] as String?,
      country: json['country'] as String?,
    );

Map<String, dynamic> _$AqResourceLocationToJson(AqResourceLocation instance) =>
    <String, dynamic>{
      'sysidLocatorNode': instance.sysidLocatorNode,
      'sysidVenueNode': instance.sysidVenueNode,
      'sysidLocation': instance.sysidLocation,
      'venueName': instance.venueName,
      'locationName': instance.locationName,
      'address1': instance.address1,
      'address2': instance.address2,
      'city': instance.city,
      'stateProvince': instance.stateProvince,
      'postalCode': instance.postalCode,
      'country': instance.country,
    };
