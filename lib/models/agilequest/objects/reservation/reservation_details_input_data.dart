import 'package:json_annotation/json_annotation.dart';

import '../../types/aq_reserve_const.dart';
part 'reservation_details_input_data.g.dart';

@JsonSerializable(includeIfNull: false)
class ReservationDetailsInput {
  int? confirmationNumber;
  int? sysidReservation;
  String? modeType = ReservationFormMode.modify.type;
  bool? isForDelegates;

  ReservationDetailsInput({
    this.confirmationNumber,
    this.isForDelegates,
    this.modeType,
    this.sysidReservation,
  });
  factory ReservationDetailsInput.fromJson(Map<String, dynamic> json) => _$ReservationDetailsInputFromJson(json);

  Map<String, dynamic> toJson() => _$ReservationDetailsInputToJson(this);
}
