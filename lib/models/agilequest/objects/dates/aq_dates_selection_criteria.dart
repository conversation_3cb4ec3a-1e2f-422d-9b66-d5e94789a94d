import 'package:tangoworkplace/models/agilequest/objects/dates/aq_dates.dart';
import 'package:tangoworkplace/models/agilequest/types/dates/aq_calendar_type.dart';
import 'package:tangoworkplace/models/agilequest/types/dates/aq_time_mode.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_date_extension.dart';
import 'package:tangoworkplace/providers/agilequest/date/utils/aq_recurrence_rule_extension.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';

import '../../date/aq_recurrence_rule.dart';
import '../../types/dates/aq_date_selection_mode.dart';

class AqDatesSelectionCriteria {
  AqCalendarType calendarType;
  AqDateSelectionMode selectionMode;
  AqTimeMode timeMode;
  AqRecurrenceRule?
      recurrenceRule; //This should be null, when dateAndTime selected
  AqDates?
      dateAndTime; //Note should be null, if user selected recurring pattern
  AqDates?
      recurringStartAndEnd; //This should be null, when dateAndTime selected
  bool isAvailableOnlyForSelectedMode;
  AqDates? borderDates;
  AqDates? preferredDates;

  AqDatesSelectionCriteria(
      {required this.calendarType,
      required this.selectionMode,
      this.timeMode = AqTimeMode.none,
      this.recurrenceRule,
      this.dateAndTime,
      this.recurringStartAndEnd,
      this.borderDates,
      this.preferredDates,
      this.isAvailableOnlyForSelectedMode = false});

  String tzValue() {
    return dateAndTime?.startDateTime?.tzIdValue ??
        recurringStartAndEnd?.startDateTime?.tzIdValue ??
        preferredDates?.startDateTime?.tzIdValue ??
        DateTime.now().timeZoneName;
  }

  AqDatesSelectionCriteria copyWith({
    AqCalendarType? calendarType,
    AqDateSelectionMode? selectionMode,
    AqTimeMode? timeMode,
    AqRecurrenceRule? recurrenceRule,
    AqDates? dateAndTime,
    AqDates? recurringStartAndEnd,
    bool? isAvailableOnlyForSelectedMode,
    AqDates? borderDates,
    AqDates? preferredDates,
  }) {
    return AqDatesSelectionCriteria(
      calendarType: calendarType ?? this.calendarType, 
      selectionMode: selectionMode ?? this.selectionMode,
      timeMode: timeMode ?? this.timeMode,
      recurrenceRule: recurrenceRule ?? this.recurrenceRule,
      dateAndTime: dateAndTime ?? this.dateAndTime,
      recurringStartAndEnd: recurringStartAndEnd ?? this.recurringStartAndEnd,
      isAvailableOnlyForSelectedMode: isAvailableOnlyForSelectedMode ?? this.isAvailableOnlyForSelectedMode,
      borderDates: borderDates ?? this.borderDates,
      preferredDates: preferredDates ?? this.preferredDates
    );
  }

  String displayDates(LabelController talabel) {
    if(dateAndTime != null) {
      final startDate = dateAndTime?.startDateTime?.displayDateString(talabel, withTimeZone: false) ?? '';
      final endDate = dateAndTime?.endDateTime?.displayDateString(talabel, withTimeZone: true) ?? '';
      return '$startDate - $endDate';
    } else {
      return recurrenceRule?.formatted(talabel) ?? '';
    }
   }
}
