import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';
import 'package:tangoworkplace/models/agilequest/location/aq_resource_location.dart';

part 'aq_reserve_view_data.g.dart';

@JsonSerializable()
class AqReserveViewData {
  int? sysidReservation;
  int? confirmationNumber;
  int? sysidParentReservation;
  int? sysidDependentReservation;
  String? reservationName;
  int? sysidEventType;
  int? sysidColorCode;
  int? sysidDurationType;
  int? sysidOrgDurationType;
  int? sysidReservationState;
  int? sysidPurposeType;
  bool? privateReservation;
  String? reservationComments;
  String? reservationEmailComments;
  int? sysidResource;
  int? sysidAccountResource;
  String? resourceName;
  int? sysidResourceAccount;
  int? sysidCategory;
  String? resourceCategoryName;
  int? sysidResourceType;
  String? resourceTypeName;
  String? resourceImage;
  String? resourceThumbnail;
  int? leadTimeMinutes;
  int? setupMinutes;
  int? breakDownMinutes;
  int? sysidCreator;
  int? sysidOwner;
  String? ownerFirstName;
  String? ownerMiddleInitial;
  String? ownerLastName;
  bool? privateProfile;
  String? ownerPhone;
  String? checkInStatus;
  List<AqResourceLocation>? resourceLocations;
  AqDate? aqStartTime;
  AqDate? aqEndTime;
  AqDate? aqActiveStartTime;
  AqDate? aqActiveEndTime;
  AqDate? aqSetupTime;
  AqDate? aqBreakDownTime;
  AqDate? aqCreatedTime;
  String? reservationResourceType;
  String? discriminator;
  bool? reqCheckIn;
  String? tempStatus;
  String? tempAddress;
  String? aqTempStartTime;
  DateTime? aqStartDateTime;

  AqReserveViewData({
    this.sysidReservation,
    this.reqCheckIn,
    this.confirmationNumber,
    this.sysidParentReservation,
    this.sysidDependentReservation,
    this.reservationName,
    this.sysidEventType,
    this.sysidColorCode,
    this.sysidDurationType,
    this.sysidOrgDurationType,
    this.sysidReservationState,
    this.sysidPurposeType,
    this.privateReservation,
    this.reservationComments,
    this.reservationEmailComments,
    this.sysidResource,
    this.sysidAccountResource,
    this.resourceName,
    this.sysidResourceAccount,
    this.sysidCategory,
    this.resourceCategoryName,
    this.sysidResourceType,
    this.resourceTypeName,
    this.resourceImage,
    this.resourceThumbnail,
    this.leadTimeMinutes,
    this.setupMinutes,
    this.breakDownMinutes,
    this.sysidCreator,
    this.sysidOwner,
    this.ownerFirstName,
    this.ownerMiddleInitial,
    this.ownerLastName,
    this.privateProfile,
    this.ownerPhone,
    this.resourceLocations,
    this.aqStartTime,
    this.aqEndTime,
    this.aqActiveStartTime,
    this.aqActiveEndTime,
    this.aqSetupTime,
    this.aqBreakDownTime,
    this.aqCreatedTime,
    this.reservationResourceType,
    this.discriminator,
    this.checkInStatus,
    this.tempStatus,
    this.tempAddress,
    this.aqTempStartTime,
    this.aqStartDateTime,
  });

  factory AqReserveViewData.fromJson(Map<String, dynamic> json) => _$AqReserveViewDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqReserveViewDataToJson(this);
}
