// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_reservation_group.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReservationGroup _$AqReservationGroupFromJson(Map<String, dynamic> json) =>
    AqReservationGroup(
      discriminator:
          $enumDecode(_$ReservationGroupTypeEnumMap, json['discriminator']),
      name: json['name'] as String,
      sysidConfirmationNumber: json['sysidConfirmationNumber'] as int?,
      comment: json['comment'] as String?,
      sysidEventOwner: json['sysidEventOwner'] as int?,
      sysidEventCreator: json['sysidEventCreator'] as int?,
      totalCost: (json['totalCost'] as num?)?.toDouble(),
      aqEventStartTime: json['aqEventStartTime'] == null
          ? null
          : AqDate.fromJson(json['aqEventStartTime'] as Map<String, dynamic>),
      aqEventEndTime: json['aqEventEndTime'] == null
          ? null
          : AqDate.fromJson(json['aqEventEndTime'] as Map<String, dynamic>),
      recurrenceRule: json['recurrenceRule'] == null
          ? null
          : AqRecurrenceRule.fromJson(
              json['recurrenceRule'] as Map<String, dynamic>),
      reservations: (json['reservations'] as List<dynamic>?)
          ?.map((e) => AqReservation.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AqReservationGroupToJson(AqReservationGroup instance) =>
    <String, dynamic>{
      'discriminator': _$ReservationGroupTypeEnumMap[instance.discriminator]!,
      'name': instance.name,
      'sysidConfirmationNumber': instance.sysidConfirmationNumber,
      'comment': instance.comment,
      'sysidEventOwner': instance.sysidEventOwner,
      'sysidEventCreator': instance.sysidEventCreator,
      'totalCost': instance.totalCost,
      'aqEventStartTime': instance.aqEventStartTime,
      'aqEventEndTime': instance.aqEventEndTime,
      'recurrenceRule': instance.recurrenceRule,
      'reservations': instance.reservations,
    };

const _$ReservationGroupTypeEnumMap = {
  ReservationGroupType.STND: 'STND',
  ReservationGroupType.MLTP: 'MLTP',
};
