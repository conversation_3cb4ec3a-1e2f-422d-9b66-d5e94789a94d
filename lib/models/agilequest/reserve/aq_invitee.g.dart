// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_invitee.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqInvitee _$AqInviteeFromJson(Map<String, dynamic> json) => AqInvitee(
      sysidHost: json['sysidHost'] as int,
      sysidReservationInvitee: json['sysidReservationInvitee'] as int?,
      isOwner: json['isOwner'] as bool?,
      roleId: json['roleId'] as int?,
      emailAddress: json['emailAddress'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      middleInitial: json['middleInitial'] as String?,
    );

Map<String, dynamic> _$AqInviteeToJson(AqInvitee instance) => <String, dynamic>{
      'sysidHost': instance.sysidHost,
      'sysidReservationInvitee': instance.sysidReservationInvitee,
      'isOwner': instance.isOwner,
      'roleId': instance.roleId,
      'emailAddress': instance.emailAddress,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'middleInitial': instance.middleInitial,
    };
