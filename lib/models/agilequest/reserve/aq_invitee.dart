
import 'package:json_annotation/json_annotation.dart';

part 'aq_invitee.g.dart';

@JsonSerializable()
class AqInvitee {
  int sysidHost;
  int? sysidReservationInvitee;
  bool? isOwner;
  int? roleId;
  String emailAddress;
  String? firstName;
  String? lastName;
  String? middleInitial;


  AqInvitee({
    required this.sysidHost,
    this.sysidReservationInvitee,
    this.isOwner,
    this.roleId,
    required this.emailAddress,
    this.firstName,
    this.lastName,
    this.middleInitial
  });

  factory AqInvitee.fromJson(Map<String, dynamic> json) => _$AqInviteeFromJson(json);

  Map<String, dynamic> toJson() => _$AqInviteeToJson(this);

}