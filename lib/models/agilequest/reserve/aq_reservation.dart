import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_recurrence_rule.dart';

import '../types/aq_reserve_const.dart';
import 'aq_invitation.dart';

part 'aq_reservation.g.dart';

@JsonSerializable()
class AqReservation {
  int? sysidReservation;
  ReserveCategoryType discriminator;
  int sysidResource;
  int? sysidCategory;
  int? sysidStateType;
  int? sysidPurposeType;
  int? sysidOrgDurationType;
  int? sysidDurationType;
  int sysidUserOwner;
  bool? privateResv;
  int? sysidParentReservation;
  ReservationCheckInStatus? checkInStatus;
  String name;
  AqDate aqStartTime;
  AqDate aqEndTime;
  String? emailComments;
  int? numberOfAttendees;
  String? deliveryLocation;
  List<AqReservation>? children;
  int? quantity;
  String? specialInstructions;
  AqRecurrenceRule? recurrenceRule;
  int? sysidConfirmationNumber;
  AqInvitation? invitation;


  AqReservation({
    this.sysidReservation,
    required this.discriminator,
    required this.sysidResource,
    required this.name,
    required this.sysidCategory,
    this.sysidStateType,
    this.sysidPurposeType,
    this.sysidOrgDurationType,
    this.sysidDurationType,
    required this.sysidUserOwner,
    this.privateResv,
    this.sysidParentReservation,
    this.checkInStatus,
    required this.aqStartTime,
    required this.aqEndTime,
    this.emailComments,
    this.numberOfAttendees,
    this.deliveryLocation,
    this.children,
    this.quantity,
    this.specialInstructions,
    this.recurrenceRule,
    this.sysidConfirmationNumber,
    this.invitation
  });

  factory AqReservation.fromJson(Map<String, dynamic> json) => _$AqReservationFromJson(json);

  Map<String, dynamic> toJson() => _$AqReservationToJson(this);

}