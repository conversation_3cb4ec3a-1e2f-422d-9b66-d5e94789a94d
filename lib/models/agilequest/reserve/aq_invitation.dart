
import 'package:json_annotation/json_annotation.dart';

import 'aq_invitee.dart';

part 'aq_invitation.g.dart';

@JsonSerializable()
class AqInvitation {
  bool isBusy;
  List<AqInvitee>? reservationInvitees;


  AqInvitation({
    required this.isBusy,
    required this.reservationInvitees,
  });

  factory AqInvitation.fromJson(Map<String, dynamic> json) => _$AqInvitationFromJson(json);

  Map<String, dynamic> toJson() => _$AqInvitationToJson(this);

}