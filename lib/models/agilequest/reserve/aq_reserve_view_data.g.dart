// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_reserve_view_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReserveViewData _$AqReserveViewDataFromJson(Map<String, dynamic> json) =>
    AqReserveViewData(
      sysidReservation: json['sysidReservation'] as int?,
      reqCheckIn: json['reqCheckIn'] as bool?,
      confirmationNumber: json['confirmationNumber'] as int?,
      sysidParentReservation: json['sysidParentReservation'] as int?,
      sysidDependentReservation: json['sysidDependentReservation'] as int?,
      reservationName: json['reservationName'] as String?,
      sysidEventType: json['sysidEventType'] as int?,
      sysidColorCode: json['sysidColorCode'] as int?,
      sysidDurationType: json['sysidDurationType'] as int?,
      sysidOrgDurationType: json['sysidOrgDurationType'] as int?,
      sysidReservationState: json['sysidReservationState'] as int?,
      sysidPurposeType: json['sysidPurposeType'] as int?,
      privateReservation: json['privateReservation'] as bool?,
      reservationComments: json['reservationComments'] as String?,
      reservationEmailComments: json['reservationEmailComments'] as String?,
      sysidResource: json['sysidResource'] as int?,
      sysidAccountResource: json['sysidAccountResource'] as int?,
      resourceName: json['resourceName'] as String?,
      sysidResourceAccount: json['sysidResourceAccount'] as int?,
      sysidCategory: json['sysidCategory'] as int?,
      resourceCategoryName: json['resourceCategoryName'] as String?,
      sysidResourceType: json['sysidResourceType'] as int?,
      resourceTypeName: json['resourceTypeName'] as String?,
      resourceImage: json['resourceImage'] as String?,
      resourceThumbnail: json['resourceThumbnail'] as String?,
      leadTimeMinutes: json['leadTimeMinutes'] as int?,
      setupMinutes: json['setupMinutes'] as int?,
      breakDownMinutes: json['breakDownMinutes'] as int?,
      sysidCreator: json['sysidCreator'] as int?,
      sysidOwner: json['sysidOwner'] as int?,
      ownerFirstName: json['ownerFirstName'] as String?,
      ownerMiddleInitial: json['ownerMiddleInitial'] as String?,
      ownerLastName: json['ownerLastName'] as String?,
      privateProfile: json['privateProfile'] as bool?,
      ownerPhone: json['ownerPhone'] as String?,
      resourceLocations: (json['resourceLocations'] as List<dynamic>?)
          ?.map((e) => AqResourceLocation.fromJson(e as Map<String, dynamic>))
          .toList(),
      aqStartTime: json['aqStartTime'] == null
          ? null
          : AqDate.fromJson(json['aqStartTime'] as Map<String, dynamic>),
      aqEndTime: json['aqEndTime'] == null
          ? null
          : AqDate.fromJson(json['aqEndTime'] as Map<String, dynamic>),
      aqActiveStartTime: json['aqActiveStartTime'] == null
          ? null
          : AqDate.fromJson(json['aqActiveStartTime'] as Map<String, dynamic>),
      aqActiveEndTime: json['aqActiveEndTime'] == null
          ? null
          : AqDate.fromJson(json['aqActiveEndTime'] as Map<String, dynamic>),
      aqSetupTime: json['aqSetupTime'] == null
          ? null
          : AqDate.fromJson(json['aqSetupTime'] as Map<String, dynamic>),
      aqBreakDownTime: json['aqBreakDownTime'] == null
          ? null
          : AqDate.fromJson(json['aqBreakDownTime'] as Map<String, dynamic>),
      aqCreatedTime: json['aqCreatedTime'] == null
          ? null
          : AqDate.fromJson(json['aqCreatedTime'] as Map<String, dynamic>),
      reservationResourceType: json['reservationResourceType'] as String?,
      discriminator: json['discriminator'] as String?,
      checkInStatus: json['checkInStatus'] as String?,
      tempStatus: json['tempStatus'] as String?,
      tempAddress: json['tempAddress'] as String?,
      aqTempStartTime: json['aqTempStartTime'] as String?,
      aqStartDateTime: json['aqStartDateTime'] == null
          ? null
          : DateTime.parse(json['aqStartDateTime'] as String),
    );

Map<String, dynamic> _$AqReserveViewDataToJson(AqReserveViewData instance) =>
    <String, dynamic>{
      'sysidReservation': instance.sysidReservation,
      'confirmationNumber': instance.confirmationNumber,
      'sysidParentReservation': instance.sysidParentReservation,
      'sysidDependentReservation': instance.sysidDependentReservation,
      'reservationName': instance.reservationName,
      'sysidEventType': instance.sysidEventType,
      'sysidColorCode': instance.sysidColorCode,
      'sysidDurationType': instance.sysidDurationType,
      'sysidOrgDurationType': instance.sysidOrgDurationType,
      'sysidReservationState': instance.sysidReservationState,
      'sysidPurposeType': instance.sysidPurposeType,
      'privateReservation': instance.privateReservation,
      'reservationComments': instance.reservationComments,
      'reservationEmailComments': instance.reservationEmailComments,
      'sysidResource': instance.sysidResource,
      'sysidAccountResource': instance.sysidAccountResource,
      'resourceName': instance.resourceName,
      'sysidResourceAccount': instance.sysidResourceAccount,
      'sysidCategory': instance.sysidCategory,
      'resourceCategoryName': instance.resourceCategoryName,
      'sysidResourceType': instance.sysidResourceType,
      'resourceTypeName': instance.resourceTypeName,
      'resourceImage': instance.resourceImage,
      'resourceThumbnail': instance.resourceThumbnail,
      'leadTimeMinutes': instance.leadTimeMinutes,
      'setupMinutes': instance.setupMinutes,
      'breakDownMinutes': instance.breakDownMinutes,
      'sysidCreator': instance.sysidCreator,
      'sysidOwner': instance.sysidOwner,
      'ownerFirstName': instance.ownerFirstName,
      'ownerMiddleInitial': instance.ownerMiddleInitial,
      'ownerLastName': instance.ownerLastName,
      'privateProfile': instance.privateProfile,
      'ownerPhone': instance.ownerPhone,
      'checkInStatus': instance.checkInStatus,
      'resourceLocations': instance.resourceLocations,
      'aqStartTime': instance.aqStartTime,
      'aqEndTime': instance.aqEndTime,
      'aqActiveStartTime': instance.aqActiveStartTime,
      'aqActiveEndTime': instance.aqActiveEndTime,
      'aqSetupTime': instance.aqSetupTime,
      'aqBreakDownTime': instance.aqBreakDownTime,
      'aqCreatedTime': instance.aqCreatedTime,
      'reservationResourceType': instance.reservationResourceType,
      'discriminator': instance.discriminator,
      'reqCheckIn': instance.reqCheckIn,
      'tempStatus': instance.tempStatus,
      'tempAddress': instance.tempAddress,
      'aqTempStartTime': instance.aqTempStartTime,
      'aqStartDateTime': instance.aqStartDateTime?.toIso8601String(),
    };
