// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_reservation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqReservation _$AqReservationFromJson(Map<String, dynamic> json) =>
    AqReservation(
      sysidReservation: json['sysidReservation'] as int?,
      discriminator:
          $enumDecode(_$ReserveCategoryTypeEnumMap, json['discriminator']),
      sysidResource: json['sysidResource'] as int,
      name: json['name'] as String,
      sysidCategory: json['sysidCategory'] as int?,
      sysidStateType: json['sysidStateType'] as int?,
      sysidPurposeType: json['sysidPurposeType'] as int?,
      sysidOrgDurationType: json['sysidOrgDurationType'] as int?,
      sysidDurationType: json['sysidDurationType'] as int?,
      sysidUserOwner: json['sysidUserOwner'] as int,
      privateResv: json['privateResv'] as bool?,
      sysidParentReservation: json['sysidParentReservation'] as int?,
      checkInStatus: $enumDecodeNullable(
          _$ReservationCheckInStatusEnumMap, json['checkInStatus']),
      aqStartTime: AqDate.fromJson(json['aqStartTime'] as Map<String, dynamic>),
      aqEndTime: AqDate.fromJson(json['aqEndTime'] as Map<String, dynamic>),
      emailComments: json['emailComments'] as String?,
      numberOfAttendees: json['numberOfAttendees'] as int?,
      deliveryLocation: json['deliveryLocation'] as String?,
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => AqReservation.fromJson(e as Map<String, dynamic>))
          .toList(),
      quantity: json['quantity'] as int?,
      specialInstructions: json['specialInstructions'] as String?,
      recurrenceRule: json['recurrenceRule'] == null
          ? null
          : AqRecurrenceRule.fromJson(
              json['recurrenceRule'] as Map<String, dynamic>),
      sysidConfirmationNumber: json['sysidConfirmationNumber'] as int?,
      invitation: json['invitation'] == null
          ? null
          : AqInvitation.fromJson(json['invitation'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AqReservationToJson(AqReservation instance) =>
    <String, dynamic>{
      'sysidReservation': instance.sysidReservation,
      'discriminator': _$ReserveCategoryTypeEnumMap[instance.discriminator]!,
      'sysidResource': instance.sysidResource,
      'sysidCategory': instance.sysidCategory,
      'sysidStateType': instance.sysidStateType,
      'sysidPurposeType': instance.sysidPurposeType,
      'sysidOrgDurationType': instance.sysidOrgDurationType,
      'sysidDurationType': instance.sysidDurationType,
      'sysidUserOwner': instance.sysidUserOwner,
      'privateResv': instance.privateResv,
      'sysidParentReservation': instance.sysidParentReservation,
      'checkInStatus':
          _$ReservationCheckInStatusEnumMap[instance.checkInStatus],
      'name': instance.name,
      'aqStartTime': instance.aqStartTime,
      'aqEndTime': instance.aqEndTime,
      'emailComments': instance.emailComments,
      'numberOfAttendees': instance.numberOfAttendees,
      'deliveryLocation': instance.deliveryLocation,
      'children': instance.children,
      'quantity': instance.quantity,
      'specialInstructions': instance.specialInstructions,
      'recurrenceRule': instance.recurrenceRule,
      'sysidConfirmationNumber': instance.sysidConfirmationNumber,
      'invitation': instance.invitation,
    };

const _$ReserveCategoryTypeEnumMap = {
  ReserveCategoryType.ROOM: 'ROOM',
  ReserveCategoryType.XHST: 'XHST',
  ReserveCategoryType.POOL: 'POOL',
  ReserveCategoryType.VNDR: 'VNDR',
  ReserveCategoryType.SRVC: 'SRVC',
  ReserveCategoryType.ALL: 'ALL',
  ReserveCategoryType.NONE: 'NONE',
};

const _$ReservationCheckInStatusEnumMap = {
  ReservationCheckInStatus.CHECK_IN: 'CHECK_IN',
  ReservationCheckInStatus.CHECK_IN_NO_ROLL: 'CHECK_IN_NO_ROLL',
  ReservationCheckInStatus.CHECK_OUT: 'CHECK_OUT',
  ReservationCheckInStatus.NONE: 'NONE',
};
