import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_date.dart';
import 'package:tangoworkplace/models/agilequest/date/aq_recurrence_rule.dart';

import '../types/aq_reserve_const.dart';
import 'aq_reservation.dart';

part 'aq_reservation_group.g.dart';

@JsonSerializable()
class AqReservationGroup {
  ReservationGroupType discriminator;
  String name;
  int? sysidConfirmationNumber;
  String? comment;
  int? sysidEventOwner;
  int? sysidEventCreator;
  double? totalCost;
  AqDate? aqEventStartTime;
  AqDate? aqEventEndTime;
  AqRecurrenceRule? recurrenceRule;
  List<AqReservation>? reservations;

  AqReservationGroup({
    required this.discriminator,
    required this.name,
    this.sysidConfirmationNumber,
    this.comment,
    this.sysidEventOwner,
    this.sysidEventCreator,
    this.totalCost,
    this.aqEventStartTime,
    this.aqEventEndTime,
    this.recurrenceRule,
    this.reservations,
  });

  factory AqReservationGroup.fromJson(Map<String, dynamic> json) => _$AqReservationGroupFromJson(json);

  Map<String, dynamic> toJson() => _$AqReservationGroupToJson(this);

}