// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_user_login_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqUserLoginData _$AqUserLoginDataFromJson(Map<String, dynamic> json) =>
    AqUserLoginData(
      sysidApplication: json['sysidApplication'] as int,
      sysidAccount: json['sysidAccount'] as int,
      appName: json['appName'] as String,
      accountName: json['accountName'] as String,
      userData: AqUserData.fromJson(json['userData'] as Map<String, dynamic>),
      accountOwner: json['accountOwner'] as bool,
      allowICS: json['allowICS'] as bool,
      forumList: (json['forumList'] as List<dynamic>)
          .map((e) => AqForumList.fromJson(e as Map<String, dynamic>))
          .toList(),
      customRules: json['customRules'] as List<dynamic>,
    );

Map<String, dynamic> _$AqUserLoginDataToJson(AqUserLoginData instance) =>
    <String, dynamic>{
      'sysidApplication': instance.sysidApplication,
      'sysidAccount': instance.sysidAccount,
      'appName': instance.appName,
      'accountName': instance.accountName,
      'userData': instance.userData,
      'accountOwner': instance.accountOwner,
      'allowICS': instance.allowICS,
      'forumList': instance.forumList,
      'customRules': instance.customRules,
    };
