class AqAuthRulesConst {
  static int NOT_DEFINED = -1;
  static int DELEGATE_ANOTHERS_ALLOCATIONS = 1;
  static int DELEGATE_OWN_ALLOCATIONS = 2;
  static int MAKE_AND_MANAGE_LONG_TERM_RESV = 3;
  static int MODIFY_FINAL_RESERVATIONS = 4;
  static int MAKE_RECURRING_RESERVATIONS = 5;
  static int MANAGE_ACCOUNT = 9;
  static int MANAGE_FORUMS = 10;
  static int MANAGE_OWN_USER_PROFILE = 20;
  static int MANAGE_OWN_PASSWORD = 21;
  static int MANAGE_OWN_ALLOCATIONS = 22;
  static int MANAGE_OWN_BUSINESS_RULES = 23;
  static int MANAGE_OWN_BUSINESS_ATTRIBUTES = 24;
  static int MANAGE_OWN_CREDENTIALS = 25;
  static int MANAGE_OWN_DELEGATES = 26;
  static int MANAGE_OWN_EMAIL = 27;
  static int MANAGE_OWN_NAME = 28;
  static int MANAGE_OWN_RIGHTS = 29;
  static int MANAGE_OWN_ROLE = 30;
  static int PUBLISH_OWN_USER = 31;
  static int NO_PASSWORD_EXPIRATION = 32;
  static int MANAGE_OWN_PHONE_SETTINGS = 33;
  static int MANAGE_OWN_PRIMARY_WORKSPACE = 34;
  static int VIEW_ANOTHERS_USER_PROFILE = 40;
  static int MANAGE_ANOTHERS_PASSWORD = 41;
  static int MANAGE_ANOTHERS_ALLOCATIONS = 42;
  static int MANAGE_ANOTHERS_BUSINESS_RULES = 43;
  static int MANAGE_ANOTHERS_BUSINESS_ATTRIBUTES = 44;
  static int MANAGE_ANOTHERS_CREDENTIALS = 45;
  static int MANAGE_ANOTHERS_DELEGATES = 46;
  static int MANAGE_ANOTHERS_EMAIL = 47;
  static int MANAGE_ANOTHERS_NAME = 48;
  static int MANAGE_ANOTHERS_RIGHTS = 49;
  static int MANAGE_ANOTHERS_ROLE = 50;
  static int MANAGE_ANOTHERS_REQUESTS = 51;
  static int MANAGE_ANOTHERS_RESERVATIONS = 52;
  static int PUBLISH_OTHER_USER = 53;
  static int MANAGE_ANOTHERS_PHONE_SETTINGS = 54;
  static int MANAGE_ANOTHERS_PRIMARY_WORKSPACE = 55;
  static int CREATE_USER = 60;
  static int MANAGE_USERS = 61;
  static int DELETE_USER = 62;
  static int OVERRIDE_MAX_RESERVATION_DAYS = 70;
  static int OVERRIDE_RESERVATION_LEADTIME = 71;
  static int OVERRIDE_COST_LIMIT = 72;
  static int OVERRIDE_WORKSPACE_LIMIT = 73;
  static int ALLOW_RESERVATIONS_ON_CLOSED_TIME = 74;
  static int OVERRIDE_SEND_EMAIL = 75;
  static int CANCEL_NO_END_DATE_RESERVATION = 76;
  static int OVERRIDE_MAX_DURATION = 77;
  static int OVERRIDE_MIN_DURATION = 78;
  static int OVERRIDE_BACK_TO_BACK_RESERVATIONS = 79;
  static int CREATE_LOCATIONS = 89;
  static int MANAGE_LOCATIONS = 89;
  static int DELETE_LOCATIONS = 89;
  static int CREATE_VENUES = 89;
  static int MANAGE_VENUES = 89;
  static int MANAGE_VENUES_STATUS = 89;
  static int DELETE_VENUES = 89;
  static int PUBLISH_VENUES = 89;
  static int CREATE_RESOURCES = 90;
  static int MANAGE_RESOURCES = 91;
  static int MANAGE_RESOURCES_NON_ALLOC = 92;
  static int DELETE_RESOURCES = 93;
  static int MANAGE_ALLOCATIONS = 94;
  static int MANAGE_CHARGE_CODES = 95;
  static int PUBLISH_RESOURCES = 96;
  static int MANAGE_APPROVAL_GROUPS = 97;
  static int MANAGE_RESOURCE_REVIEWS = 98;
  static int CLEAR_CACHE = 100;
  static int MANAGE_SYSTEM = 101;
  static int MANAGE_KIOSK_SETTINGS = 102;
  static int CAN_CHECKIN_WS_WITH_MOBILE_APP = 113;
  static int CAN_CHECKIN_CR_WITH_MOBILE_APP = 119;
  static int MANAGE_REPORTS = 200;
  static int CREATE_DELEGATE_RESERVATION = 500;
  static int MANAGE_REVIEWS = -1;
}
