import 'package:json_annotation/json_annotation.dart';

part 'app_auth_data.g.dart';

@JsonSerializable()
class AppAuthData {
  int? sysidApplication;
  String? sysidAccount;
  String? authToken;
  String? appName;
  String? accountName;

  AppAuthData({
    this.sysidApplication,
    this.sysidAccount,
    this.authToken,
    this.appName,
    this.accountName,
  });

  factory AppAuthData.fromJson(Map<String, dynamic> json) => _$AppAuthDataFromJson(json);

  Map<String, dynamic> toJson() => _$AppAuthDataToJson(this);

}
