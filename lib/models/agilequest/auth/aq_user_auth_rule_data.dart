
import 'package:json_annotation/json_annotation.dart';

part 'aq_user_auth_rule_data.g.dart';

@JsonSerializable()
class AqUserAuthRulesData {
  int? sysidAuthorizationRule;

  AqUserAuthRulesData({
    this.sysidAuthorizationRule,
  });


  factory AqUserAuthRulesData.fromJson(Map<String, dynamic> json) => _$AqUserAuthRulesDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqUserAuthRulesDataToJson(this);
}
