
import 'package:json_annotation/json_annotation.dart';

part 'aq_currency_type_data.g.dart';

@JsonSerializable()
class AqCurrencyTypeData {
  int? sysidCurrencyType;
  String? name;
  String? code;
  String? symbol;

  AqCurrencyTypeData({
    this.sysidCurrencyType,
    this.name,
    this.code,
    this.symbol,
  });

  factory AqCurrencyTypeData.fromJson(Map<String, dynamic> json) =>
      _$AqCurrencyTypeDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqCurrencyTypeDataToJson(this);
}
