import 'package:json_annotation/json_annotation.dart';

import 'aq_user_auth_rule_data.dart';

part 'aq_user_data.g.dart';

@JsonSerializable()
class AqUserData {
  int? sysidApplicationUser;
  String? authToken;
  String? firstName;
  String? middleInitial;
  String? lastName;
  String? preferredName;
  int? sysidDefaultLocation;
  int? sysidDefaultResCategory;
  int? sysidDefaultResType;
  int? sysidHomeLocation;
  String? defaultChargeCode;
  int? sysidRole;
  int? sysidLanguage;
  int? reservationSpendingLimit;
  int? maxFutureReservationDays;
  List<AqUserAuthRulesData>? userAuthRules;

  AqUserData({
    this.sysidApplicationUser,
    this.authToken,
    this.firstName,
    this.middleInitial,
    this.lastName,
    this.preferredName,
    this.sysidDefaultLocation,
    this.sysidDefaultResCategory,
    this.sysidDefaultResType,
    this.sysidHomeLocation,
    this.defaultChargeCode,
    this.sysidRole,
    this.sysidLanguage,
    this.reservationSpendingLimit,
    this.maxFutureReservationDays,
    this.userAuthRules,
  });

  factory AqUserData.fromJson(Map<String, dynamic> json) => _$AqUserDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqUserDataToJson(this);
}
