// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aq_user_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AqUserData _$AqUserDataFromJson(Map<String, dynamic> json) => AqUserData(
      sysidApplicationUser: json['sysidApplicationUser'] as int?,
      authToken: json['authToken'] as String?,
      firstName: json['firstName'] as String?,
      middleInitial: json['middleInitial'] as String?,
      lastName: json['lastName'] as String?,
      preferredName: json['preferredName'] as String?,
      sysidDefaultLocation: json['sysidDefaultLocation'] as int?,
      sysidDefaultResCategory: json['sysidDefaultResCategory'] as int?,
      sysidDefaultResType: json['sysidDefaultResType'] as int?,
      sysidHomeLocation: json['sysidHomeLocation'] as int?,
      defaultChargeCode: json['defaultChargeCode'] as String?,
      sysidRole: json['sysidRole'] as int?,
      sysidLanguage: json['sysidLanguage'] as int?,
      reservationSpendingLimit: json['reservationSpendingLimit'] as int?,
      maxFutureReservationDays: json['maxFutureReservationDays'] as int?,
      userAuthRules: (json['userAuthRules'] as List<dynamic>?)
          ?.map((e) => AqUserAuthRulesData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AqUserDataToJson(AqUserData instance) =>
    <String, dynamic>{
      'sysidApplicationUser': instance.sysidApplicationUser,
      'authToken': instance.authToken,
      'firstName': instance.firstName,
      'middleInitial': instance.middleInitial,
      'lastName': instance.lastName,
      'preferredName': instance.preferredName,
      'sysidDefaultLocation': instance.sysidDefaultLocation,
      'sysidDefaultResCategory': instance.sysidDefaultResCategory,
      'sysidDefaultResType': instance.sysidDefaultResType,
      'sysidHomeLocation': instance.sysidHomeLocation,
      'defaultChargeCode': instance.defaultChargeCode,
      'sysidRole': instance.sysidRole,
      'sysidLanguage': instance.sysidLanguage,
      'reservationSpendingLimit': instance.reservationSpendingLimit,
      'maxFutureReservationDays': instance.maxFutureReservationDays,
      'userAuthRules': instance.userAuthRules,
    };
