
import 'package:json_annotation/json_annotation.dart';

part 'aq_languagecodes_data.g.dart';

@JsonSerializable()
class AqLanguageCodesData {
  String key;
  String tag;

  AqLanguageCodesData(
    this.key,
    this.tag,
  );


  factory AqLanguageCodesData.fromJson(Map<String, dynamic> json) =>
      _$AqLanguageCodesDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqLanguageCodesDataToJson(this);
}
