import 'package:json_annotation/json_annotation.dart';

part 'aq_app_properties_data.g.dart';

@JsonSerializable()
class AqAppPropertiesData {
  String key;
  String value;

  AqAppPropertiesData(
    this.key,
    this.value,
  );

  factory AqAppPropertiesData.fromJson(Map<String, dynamic> json) =>
      _$AqAppPropertiesDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqAppPropertiesDataToJson(this);
}
