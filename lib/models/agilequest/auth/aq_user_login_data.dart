import 'package:json_annotation/json_annotation.dart';
import 'package:tangoworkplace/models/agilequest/auth/aq_forum_list.dart';
import 'package:tangoworkplace/models/agilequest/auth/aq_user_data.dart';

part 'aq_user_login_data.g.dart';

@JsonSerializable()
class AqUserLoginData {
  AqUserLoginData({
    required this.sysidApplication,
    required this.sysidAccount,
    required this.appName,
    required this.accountName,
    required this.userData,
    required this.accountOwner,
    required this.allowICS,
    required this.forumList,
    required this.customRules,
  });

  late final int sysidApplication;
  late final int sysidAccount;
  late final String appName;
  late final String accountName;
  late final AqUserData userData;
  late final bool accountOwner;
  late final bool allowICS;
  late final List<AqForumList> forumList;
  late final List<dynamic> customRules;


  factory AqUserLoginData.fromJson(Map<String, dynamic> json) => _$AqUserLoginDataFromJson(json);

  Map<String, dynamic> toJson() => _$AqUserLoginDataToJson(this);
}

