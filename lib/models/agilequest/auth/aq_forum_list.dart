import 'package:json_annotation/json_annotation.dart';

part 'aq_forum_list.g.dart';

@JsonSerializable()
class AqForumList {
  AqForumList({
    required this.sysid,
    required this.name,
    required this.homeForum,
    required this.visited,
  });
  late final int sysid;
  late final String name;
  late final bool homeForum;
  late final bool visited;

  factory AqForumList.fromJson(Map<String, dynamic> json) => _$AqForumListFromJson(json);

  Map<String, dynamic> toJson() => _$AqForumListToJson(this);
}