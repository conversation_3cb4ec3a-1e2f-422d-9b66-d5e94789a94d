enum EventType {
  STANDARD(1),
  MULTIPOINT(2);

  final int type;
  const EventType(this.type);
}

enum ReservationGroupType {
  STND('STND'),
  MLTP('MLTP');

  final String type;
  const ReservationGroupType(this.type);
}

enum ReservationFormMode {
  modify('MODIFY'),
  viewrecurringseries('VIEW_RECURRING_SERIES'),
  viewmltp('VIEW_MLTP'),
  copy('COPY');

  final String type;
  const ReservationFormMode(this.type);
}

enum ReservationCheckInStatus {
  CHECK_IN('CHECK_IN'),
  CHECK_IN_NO_ROLL('CHECK_IN_NO_ROLL'),
  CHECK_OUT('CHECK_OUT'),
  NONE('NONE');

  final String type;
  const ReservationCheckInStatus(this.type);
}

enum ReserveCategoryType {
  ROOM('ROOM'),
  XHST('XHST'),
  POOL('POOL'),
  VNDR('VNDR'),
  SRVC('SRVC'),
  ALL('ALL'),
  NONE('NONE');

  final String name;
  const ReserveCategoryType(this.name);
}

enum ReservationOrgDurationType {
  SINGLE_DAY(1),
  CONTINUOUS(2),
  OPEN_ENDED(3),
  RECURRING(4);

  final int type;
  const ReservationOrgDurationType(this.type);
}

enum AqLocationDataType {
  topLocationsActiveNonAllocated(1),
  topLocationsActiveAllocated(2),
  topLocationsAllNonAllocated(3),
  topLocationsAllAllocated(4),
  nodeLocationsActiveNonAllocated(5),
  nodeLocationsActiveAllocated(6),
  nodeLocationsAllNonAllocated(7),
  nodeLocationsAllAllocated(8);

  final int id;
  const AqLocationDataType(this.id);
}

enum RoomType {
  CONFERENCE(1),
  WORKSPACE(2),
  OTHER(3);

  final int type;
  const RoomType(this.type);
}

enum AqManageReservePageType {
  active(0),
  delegates(1),
  draft(2),
  requests(3),
  completed(4);

  final int id;

  const AqManageReservePageType(this.id);
}

enum AqReservationPurposeType {
  reservation(1),
  dependency(2),
  resourceUnavailable(3),
  draft(4);

  final int id;

  const AqReservationPurposeType(this.id);
}

enum ReservationActionType {
  CHECK_IN(5),
  CHECK_IN_NO_ROLL(16),
  CHECK_OUT(6),
  CANCEL(3),
  END(6),
  CANCEL_SERIES(101),
  CANCEL_INSTANCE(102),
  CANCEL_TODAY(201),
  CANCEL_RESERVATION(202),
  CANCEL_RANGE(203);

  final int id;

  const ReservationActionType(this.id);
}
