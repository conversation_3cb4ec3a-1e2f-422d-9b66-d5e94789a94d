class AqReserveStates {
  static int INITIAL = 0;
  static int AWAITING_DECISION = 1;
  static int AWAITING_CHECKIN = 2;
  static int ACTIVE_PENDING = 3;
  static int DENIED = 4;
  static int EXPIRED = 5;
  static int BUMPED = 6;
  static int CANCELED = 7;
  static int CHECKEDOUT = 8;
  static int CANCELED_WITHIN_LEAD_TIME = 9;
  static int AWAITING_PARENT_DECISION_RESERVE = 10;
  static int AWAITING_PARENT_DECISION_REQUEST = 11;
  static int WAITLIST_AWAITING_DECISION = 12;

  static List<int> active = [AWAITING_CHECKIN, ACTIVE_PENDING];
  static List<int> pendingRequest = [
    AWAITING_DECISION,
    AWAITING_PARENT_DECISION_RESERVE,
    AWAITING_PARENT_DECISION_REQUEST,
  ];
  static List<int> completed = [
    DENIED,
    EXPIRED,
    BUMPED,
    CANCELED,
    CHECKEDOUT,
    CANC<PERSON>ED_WITHIN_LEAD_TIME,
  ];
  static List<int> activeOrPending = [
    AWAITING_CHECKIN,
    ACTIVE_PENDING,
    AWAITING_DECISION,
    AWAITING_PARENT_DECISION_RESERVE,
    AWAITING_PARENT_DECISION_REQUEST,
  ];
  static List<int> delegates = [
    AWAITING_CHECKIN,
    ACTIVE_PENDING,
    AWAITING_DECISION,
    //AWAITING_PARENT_DECISION_RESERVE,
    //AWAITING_PARENT_DECISION_REQUEST
  ];
}
