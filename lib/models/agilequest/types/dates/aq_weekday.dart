enum AqWeekday {
  MONDAY(2),
  TUESDAY(3),
  WEDNESDAY(4),
  THURSDAY(5),
  FRIDAY(6),
  SATURDAY(7),
  SUNDAY(1);

  final int id;
  const AqWeekday(this.id);
}

extension AqWeekdayExtension on AqWeekday {
  String get displayName {
    switch (this) {
      case AqWeekday.SUNDAY:
        return "Sunday";
      case AqWeekday.MONDAY:
        return "Monday";
      case AqWeekday.TUESDAY:
        return "Tuesday";
      case AqWeekday.WEDNESDAY:
        return "Wednesday";
      case AqWeekday.THURSDAY:
        return "Thursday";
      case AqWeekday.FRIDAY:
        return "Friday";
      case AqWeekday.SATURDAY:
        return "Saturday";
      default:
        return "";
    }
  }

  int get id {
    switch (this) {
      case AqWeekday.SUNDAY:
        return 1;
      case AqWeekday.MONDAY:
        return 2;
      case AqWeekday.TUESDAY:
        return 3;
      case AqWeekday.WEDNESDAY:
        return 4;
      case AqWeekday.THURSDAY:
        return 5;
      case AqWeekday.FRIDAY:
        return 6;
      case AqWeekday.SATURDAY:
        return 7;
      default:
        return 0;
    }
  }

  String get languageTagKey {
    switch (this) {
      case AqWeekday.SUNDAY:
        return "aq.week.sunday";
      case AqWeekday.MONDAY:
        return "aq.week.monday";
      case AqWeekday.TUESDAY:
        return "aq.week.tuesday";
      case AqWeekday.WEDNESDAY:
        return "aq.week.wednesday";
      case AqWeekday.THURSDAY:
        return "aq.week.thursday";
      case AqWeekday.FRIDAY:
        return "aq.week.friday";
      case AqWeekday.SATURDAY:
        return "aq.week.saturday";
      default:
        return "";
    }
  }

  String get getWeekDaysShortenTagKey {
    switch (this) {
      case AqWeekday.SUNDAY:
        return "aq.week.sunday.short";
      case AqWeekday.MONDAY:
        return "aq.week.monday.short";
      case AqWeekday.TUESDAY:
        return "aq.week.tuesday.short";
      case AqWeekday.WEDNESDAY:
        return "aq.week.wednesday.short";
      case AqWeekday.THURSDAY:
        return "aq.week.thursday.short";
      case AqWeekday.FRIDAY:
        return "aq.week.friday.short";
      case AqWeekday.SATURDAY:
        return "aq.week.saturday.short";
      default:
        return "";
    }
  }
}



