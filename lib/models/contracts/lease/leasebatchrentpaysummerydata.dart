class LeaseBatchRentPaysummeryData {
  int? leasePaymentBatchId;
  String? currencyCode;
  int? distinctLeasesCount;
  int? totalPaymentsCount;
  double? sumNetAmount;
  double? sumGrossAmount;
  int? sumTaxAmount;

  LeaseBatchRentPaysummeryData({
    this.leasePaymentBatchId,
    this.currencyCode,
    this.distinctLeasesCount,
    this.totalPaymentsCount,
    this.sumNetAmount,
    this.sumGrossAmount,
    this.sumTaxAmount,
  });
  LeaseBatchRentPaysummeryData.fromJson(Map<String, dynamic> json) {
    leasePaymentBatchId = json['lease_payment_batch_id']?.toInt();
    currencyCode = json['currency_code']?.toString() ?? '';
    distinctLeasesCount = json['distinct_leases_count']?.toInt();
    totalPaymentsCount = json['total_payments_count']?.toInt();
    sumNetAmount = json['sum_net_amount']?.toDouble();
    sumGrossAmount = json['sum_gross_amount']?.toDouble();
    sumTaxAmount = json['sum_tax_amount']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['lease_payment_batch_id'] = leasePaymentBatchId;
    data['currency_code'] = currencyCode;
    data['distinct_leases_count'] = distinctLeasesCount;
    data['total_payments_count'] = totalPaymentsCount;
    data['sum_net_amount'] = sumNetAmount;
    data['sum_gross_amount'] = sumGrossAmount;
    data['sum_tax_amount'] = sumTaxAmount;
    return data;
  }
}
