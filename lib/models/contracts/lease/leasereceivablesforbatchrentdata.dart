class LeaseReceivablesforBatchRentData {
  String? accountingString;
  String? appliesFromDate;
  String? appliesToDate;
  String? brandId;
  int? clientId;
  String? comments;
  String? createdBy;
  String? creationDate;
  String? currencyCode;
  String? dueDate;
  String? effectiveDate;
  double? grossAmount;
  String? lastUpdateDate;
  String? lastUpdateLogin;
  String? lastUpdatedBy;
  String? leaseFinancialId;
  int? leaseId;
  int? leaseBillingId;
  double? netAmount;
  int? objectVersionNumber;
  String? orgId;
  String? chargeStatus;
  String? paymentType;
  String? remitMessage;
  int? taxAmount;
  String? taxRate;
  String? vendorId;
  String? vendorName;
  String? vendorStatus;
  String? leaseIncludedBilling;
  String? leaseIncluded;
  String? leaseReceivableBatchId;
  String? oneTime;
  String? oneTimeBilling;
  String? taxApplies;
  String? taxAppliesBilling;
  String? leaseNumber;
  String? storeNumber;
  String? storeName;
  String? prevNet;
  String? invoiceId;
  String? country;
  int? changeAmount;
  int? changePercent;
  double? bNetAmountLocal;
  double? bNetAmountGlobal;
  int? bTaxAmountLocal;
  int? bTaxAmountGlobal;
  double? bGrossAmountLocal;
  double? bGrossAmountGlobal;
  String? errorCountBilling;
  String? errorDescriptionBilling;
  String? paymentCategory;

  LeaseReceivablesforBatchRentData({
    this.accountingString,
    this.appliesFromDate,
    this.appliesToDate,
    this.brandId,
    this.clientId,
    this.comments,
    this.createdBy,
    this.creationDate,
    this.currencyCode,
    this.dueDate,
    this.effectiveDate,
    this.grossAmount,
    this.lastUpdateDate,
    this.lastUpdateLogin,
    this.lastUpdatedBy,
    this.leaseFinancialId,
    this.leaseId,
    this.leaseBillingId,
    this.netAmount,
    this.objectVersionNumber,
    this.orgId,
    this.chargeStatus,
    this.paymentType,
    this.remitMessage,
    this.taxAmount,
    this.taxRate,
    this.vendorId,
    this.vendorName,
    this.vendorStatus,
    this.leaseIncludedBilling,
    this.leaseIncluded,
    this.leaseReceivableBatchId,
    this.oneTime,
    this.oneTimeBilling,
    this.taxApplies,
    this.taxAppliesBilling,
    this.leaseNumber,
    this.storeNumber,
    this.storeName,
    this.prevNet,
    this.invoiceId,
    this.country,
    this.changeAmount,
    this.changePercent,
    this.bNetAmountLocal,
    this.bNetAmountGlobal,
    this.bTaxAmountLocal,
    this.bTaxAmountGlobal,
    this.bGrossAmountLocal,
    this.bGrossAmountGlobal,
    this.errorCountBilling,
    this.errorDescriptionBilling,
    this.paymentCategory,
  });
  LeaseReceivablesforBatchRentData.fromJson(Map<String, dynamic> json) {
    accountingString = json['accounting_string']?.toString();
    appliesFromDate = json['applies_from_date']?.toString();
    appliesToDate = json['applies_to_date']?.toString();
    brandId = json['brand_id']?.toString();
    clientId = json['client_id']?.toInt();
    comments = json['comments']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    currencyCode = json['currency_code']?.toString();
    dueDate = json['due_date']?.toString();
    effectiveDate = json['effective_date']?.toString();
    grossAmount = json['gross_amount']?.toDouble();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdateLogin = json['last_update_login']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    leaseFinancialId = json['lease_financial_id']?.toString();
    leaseId = json['lease_id']?.toInt();
    leaseBillingId = json['lease_billing_id']?.toInt();
    netAmount = json['net_amount']?.toDouble();
    objectVersionNumber = json['object_version_number']?.toInt();
    orgId = json['org_id']?.toString();
    chargeStatus = json['charge_status']?.toString();
    paymentType = json['payment_type']?.toString();
    remitMessage = json['remit_message']?.toString();
    taxAmount = json['tax_amount']?.toInt();
    taxRate = json['tax_rate']?.toString();
    vendorId = json['vendor_id']?.toString();
    vendorName = json['vendor_name']?.toString();
    vendorStatus = json['vendor_status']?.toString();
    leaseIncludedBilling = json['lease_included_billing']?.toString();
    leaseIncluded = json['lease_included']?.toString();
    leaseReceivableBatchId = json['lease_receivable_batch_id']?.toString();
    oneTime = json['one_time']?.toString();
    oneTimeBilling = json['one_time_billing']?.toString();
    taxApplies = json['tax_applies']?.toString();
    taxAppliesBilling = json['tax_applies_billing']?.toString();
    leaseNumber = json['lease_number']?.toString();
    storeNumber = json['store_number']?.toString();
    storeName = json['store_name']?.toString();
    prevNet = json['prev_net']?.toString();
    invoiceId = json['invoice_id']?.toString();
    country = json['country']?.toString();
    changeAmount = json['change_amount']?.toInt();
    changePercent = json['change_percent']?.toInt();
    bNetAmountLocal = json['b_net_amount_local']?.toDouble();
    bNetAmountGlobal = json['b_net_amount_global']?.toDouble();
    bTaxAmountLocal = json['b_tax_amount_local']?.toInt();
    bTaxAmountGlobal = json['b_tax_amount_global']?.toInt();
    bGrossAmountLocal = json['b_gross_amount_local']?.toDouble();
    bGrossAmountGlobal = json['b_gross_amount_global']?.toDouble();
    errorCountBilling = json['error_count_billing']?.toString();
    errorDescriptionBilling = json['error_description_billing']?.toString();
    paymentCategory = json['payment_category']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['accounting_string'] = accountingString;
    data['applies_from_date'] = appliesFromDate;
    data['applies_to_date'] = appliesToDate;
    data['brand_id'] = brandId;
    data['client_id'] = clientId;
    data['comments'] = comments;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['currency_code'] = currencyCode;
    data['due_date'] = dueDate;
    data['effective_date'] = effectiveDate;
    data['gross_amount'] = grossAmount;
    data['last_update_date'] = lastUpdateDate;
    data['last_update_login'] = lastUpdateLogin;
    data['last_updated_by'] = lastUpdatedBy;
    data['lease_financial_id'] = leaseFinancialId;
    data['lease_id'] = leaseId;
    data['lease_billing_id'] = leaseBillingId;
    data['net_amount'] = netAmount;
    data['object_version_number'] = objectVersionNumber;
    data['org_id'] = orgId;
    data['charge_status'] = chargeStatus;
    data['payment_type'] = paymentType;
    data['remit_message'] = remitMessage;
    data['tax_amount'] = taxAmount;
    data['tax_rate'] = taxRate;
    data['vendor_id'] = vendorId;
    data['vendor_name'] = vendorName;
    data['vendor_status'] = vendorStatus;
    data['lease_included_billing'] = leaseIncludedBilling;
    data['lease_included'] = leaseIncluded;
    data['lease_receivable_batch_id'] = leaseReceivableBatchId;
    data['one_time'] = oneTime;
    data['one_time_billing'] = oneTimeBilling;
    data['tax_applies'] = taxApplies;
    data['tax_applies_billing'] = taxAppliesBilling;
    data['lease_number'] = leaseNumber;
    data['store_number'] = storeNumber;
    data['store_name'] = storeName;
    data['prev_net'] = prevNet;
    data['invoice_id'] = invoiceId;
    data['country'] = country;
    data['change_amount'] = changeAmount;
    data['change_percent'] = changePercent;
    data['b_net_amount_local'] = bNetAmountLocal;
    data['b_net_amount_global'] = bNetAmountGlobal;
    data['b_tax_amount_local'] = bTaxAmountLocal;
    data['b_tax_amount_global'] = bTaxAmountGlobal;
    data['b_gross_amount_local'] = bGrossAmountLocal;
    data['b_gross_amount_global'] = bGrossAmountGlobal;
    data['error_count_billing'] = errorCountBilling;
    data['error_description_billing'] = errorDescriptionBilling;
    data['payment_category'] = paymentCategory;
    return data;
  }
}

class LeaseReceivablesforBatchRentRec {
  List<LeaseReceivablesforBatchRentData?>? records;

  LeaseReceivablesforBatchRentRec({
    this.records,
  });
  LeaseReceivablesforBatchRentRec.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      final v = json['records'];
      final arr0 = <LeaseReceivablesforBatchRentData>[];
      v.forEach((v) {
        arr0.add(LeaseReceivablesforBatchRentData.fromJson(v));
      });
      records = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (records != null) {
      final v = records;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['records'] = arr0;
    }
    return data;
  }
}
