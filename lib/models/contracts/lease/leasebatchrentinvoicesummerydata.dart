class LeaseBatchRentInvoiceSummeryData {
  int? leaseReceivableBatchId;
  String? currencyCode;
  int? distinctLeasesCount;
  int? totalPaymentsCount;
  int? totalInvoicesCount;
  double? sumNetAmount;
  double? sumGrossAmount;
  double? sumTaxAmount;

  LeaseBatchRentInvoiceSummeryData({
    this.leaseReceivableBatchId,
    this.currencyCode,
    this.distinctLeasesCount,
    this.totalPaymentsCount,
    this.sumNetAmount,
    this.sumGrossAmount,
    this.sumTaxAmount,
    this.totalInvoicesCount,
  });
  LeaseBatchRentInvoiceSummeryData.fromJson(Map<String, dynamic> json) {
    leaseReceivableBatchId = json['lease_receivable_batch_id']?.toInt();
    currencyCode = json['currency_code']?.toString() ?? '';
    distinctLeasesCount = json['distinct_leases_count']?.toInt();
    totalPaymentsCount = json['total_payments_count']?.toInt();
    totalInvoicesCount = json['total_invoices_count']?.toInt();
    sumNetAmount = json['sum_net_amount']?.toDouble();
    sumGrossAmount = json['sum_gross_amount']?.toDouble();
    sumTaxAmount = json['sum_tax_amount']?.toDouble();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['lease_receivable_batch_id'] = leaseReceivableBatchId;
    data['currency_code'] = currencyCode;
    data['distinct_leases_count'] = distinctLeasesCount;
    data['total_payments_count'] = totalPaymentsCount;
    data['sum_net_amount'] = sumNetAmount;
    data['sum_gross_amount'] = sumGrossAmount;
    data['sum_tax_amount'] = sumTaxAmount;
    data['total_invoices_count'] = totalInvoicesCount;
    return data;
  }
}
