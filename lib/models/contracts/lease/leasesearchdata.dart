class LeaseSearchData {
  String? acctTypeCode;
  String? allColumns;
  String? approvalStatus;
  String? approvedToPay;
  int? brandId;
  String? cExtAttr1;
  String? cExtAttr10;
  String? cExtAttr2;
  String? cExtAttr3;
  String? cExtAttr4;
  String? cExtAttr5;
  String? cExtAttr6;
  String? cExtAttr7;
  String? cExtAttr8;
  String? cExtAttr9;
  int? clientId;
  String? createdBy;
  String? creationDate;
  String? dExtAttr1;
  String? dExtAttr10;
  String? dExtAttr2;
  String? dExtAttr3;
  String? dExtAttr4;
  String? dExtAttr5;
  String? dExtAttr6;
  String? dExtAttr7;
  String? dExtAttr8;
  String? dExtAttr9;
  String? dispositionDate;
  String? dispositionType;
  String? effectiveDate;
  String? lExtAttr1;
  String? lExtAttr10;
  String? lExtAttr2;
  String? lExtAttr3;
  String? lExtAttr4;
  String? lExtAttr5;
  String? lExtAttr6;
  String? lExtAttr7;
  String? lExtAttr8;
  String? lExtAttr9;
  String? landlord;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? leaseCommencementDate;
  String? leaseDate;
  String? leaseExecutionDate;
  int? leaseId;
  String? leaseNum;
  String? leasePortfolioType;
  String? leaseStatus;
  String? leaseTerminationDate;
  String? leaseTypeCode;
  String? legalEntity;
  int? nExtAttr1;
  int? nExtAttr10;
  int? nExtAttr2;
  int? nExtAttr3;
  int? nExtAttr4;
  int? nExtAttr5;
  int? nExtAttr6;
  int? nExtAttr7;
  int? nExtAttr8;
  int? nExtAttr9;
  String? name;
  int? objectVersionNumber;
  int? orgId;
  int? parentLeaseId;
  int? prorataShare;
  String? rentProrateMethod;
  int? rentableArea;
  int? sellingArea;
  int? srcLeaseId;
  int? storageArea;
  String? tenancyType;
  String? termStatus;
  int? totalAreaGla;
  int? usableArea;
  int? versionNumber;
  String? city;
  String? currencyCode;
  String? state;
  int? annualrent;
  String? country;
  String? storeName;
  String? storeNumber;
  String? landlordName;
  String? brandName;
  String? censusRegion;
  String? assetType;
  String? address;
  String? calenderType;

  LeaseSearchData({
    this.acctTypeCode,
    this.allColumns,
    this.approvalStatus,
    this.approvedToPay,
    this.brandId,
    this.cExtAttr1,
    this.cExtAttr10,
    this.cExtAttr2,
    this.cExtAttr3,
    this.cExtAttr4,
    this.cExtAttr5,
    this.cExtAttr6,
    this.cExtAttr7,
    this.cExtAttr8,
    this.cExtAttr9,
    this.clientId,
    this.createdBy,
    this.creationDate,
    this.dExtAttr1,
    this.dExtAttr10,
    this.dExtAttr2,
    this.dExtAttr3,
    this.dExtAttr4,
    this.dExtAttr5,
    this.dExtAttr6,
    this.dExtAttr7,
    this.dExtAttr8,
    this.dExtAttr9,
    this.dispositionDate,
    this.dispositionType,
    this.effectiveDate,
    this.lExtAttr1,
    this.lExtAttr10,
    this.lExtAttr2,
    this.lExtAttr3,
    this.lExtAttr4,
    this.lExtAttr5,
    this.lExtAttr6,
    this.lExtAttr7,
    this.lExtAttr8,
    this.lExtAttr9,
    this.landlord,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.leaseCommencementDate,
    this.leaseDate,
    this.leaseExecutionDate,
    this.leaseId,
    this.leaseNum,
    this.leasePortfolioType,
    this.leaseStatus,
    this.leaseTerminationDate,
    this.leaseTypeCode,
    this.legalEntity,
    this.nExtAttr1,
    this.nExtAttr10,
    this.nExtAttr2,
    this.nExtAttr3,
    this.nExtAttr4,
    this.nExtAttr5,
    this.nExtAttr6,
    this.nExtAttr7,
    this.nExtAttr8,
    this.nExtAttr9,
    this.name,
    this.objectVersionNumber,
    this.orgId,
    this.parentLeaseId,
    this.prorataShare,
    this.rentProrateMethod,
    this.rentableArea,
    this.sellingArea,
    this.srcLeaseId,
    this.storageArea,
    this.tenancyType,
    this.termStatus,
    this.totalAreaGla,
    this.usableArea,
    this.versionNumber,
    this.city,
    this.currencyCode,
    this.state,
    this.annualrent,
    this.country,
    this.storeName,
    this.storeNumber,
    this.landlordName,
    this.brandName,
    this.censusRegion,
    this.assetType,
    this.address,
    this.calenderType,
  });
  LeaseSearchData.fromJson(Map<String, dynamic> json) {
    acctTypeCode = json['acct_type_code']?.toString();
    allColumns = json['all_columns']?.toString();
    approvalStatus = json['approval_status']?.toString();
    approvedToPay = json['approved_to_pay']?.toString();
    brandId = json['brand_id']?.toInt();
    cExtAttr1 = json['c_ext_attr1']?.toString();
    cExtAttr10 = json['c_ext_attr10']?.toString();
    cExtAttr2 = json['c_ext_attr2']?.toString();
    cExtAttr3 = json['c_ext_attr3']?.toString();
    cExtAttr4 = json['c_ext_attr4']?.toString();
    cExtAttr5 = json['c_ext_attr5']?.toString();
    cExtAttr6 = json['c_ext_attr6']?.toString();
    cExtAttr7 = json['c_ext_attr7']?.toString();
    cExtAttr8 = json['c_ext_attr8']?.toString();
    cExtAttr9 = json['c_ext_attr9']?.toString();
    clientId = json['client_id']?.toInt();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    dExtAttr1 = json['d_ext_attr1']?.toString();
    dExtAttr10 = json['d_ext_attr10']?.toString();
    dExtAttr2 = json['d_ext_attr2']?.toString();
    dExtAttr3 = json['d_ext_attr3']?.toString();
    dExtAttr4 = json['d_ext_attr4']?.toString();
    dExtAttr5 = json['d_ext_attr5']?.toString();
    dExtAttr6 = json['d_ext_attr6']?.toString();
    dExtAttr7 = json['d_ext_attr7']?.toString();
    dExtAttr8 = json['d_ext_attr8']?.toString();
    dExtAttr9 = json['d_ext_attr9']?.toString();
    dispositionDate = json['disposition_date']?.toString();
    dispositionType = json['disposition_type']?.toString();
    effectiveDate = json['effective_date']?.toString();
    lExtAttr1 = json['l_ext_attr1']?.toString();
    lExtAttr10 = json['l_ext_attr10']?.toString();
    lExtAttr2 = json['l_ext_attr2']?.toString();
    lExtAttr3 = json['l_ext_attr3']?.toString();
    lExtAttr4 = json['l_ext_attr4']?.toString();
    lExtAttr5 = json['l_ext_attr5']?.toString();
    lExtAttr6 = json['l_ext_attr6']?.toString();
    lExtAttr7 = json['l_ext_attr7']?.toString();
    lExtAttr8 = json['l_ext_attr8']?.toString();
    lExtAttr9 = json['l_ext_attr9']?.toString();
    landlord = json['landlord']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    leaseCommencementDate = json['lease_commencement_date']?.toString();
    leaseDate = json['lease_date']?.toString();
    leaseExecutionDate = json['lease_execution_date']?.toString();
    leaseId = json['lease_id']?.toInt();
    leaseNum = json['lease_num']?.toString();
    leasePortfolioType = json['lease_portfolio_type']?.toString();
    leaseStatus = json['lease_status']?.toString();
    leaseTerminationDate = json['lease_termination_date']?.toString();
    leaseTypeCode = json['lease_type_code']?.toString();
    legalEntity = json['legal_entity']?.toString();
    nExtAttr1 = json['n_ext_attr1']?.toInt();
    nExtAttr10 = json['n_ext_attr10']?.toInt();
    nExtAttr2 = json['n_ext_attr2']?.toInt();
    nExtAttr3 = json['n_ext_attr3']?.toInt();
    nExtAttr4 = json['n_ext_attr4']?.toInt();
    nExtAttr5 = json['n_ext_attr5']?.toInt();
    nExtAttr6 = json['n_ext_attr6']?.toInt();
    nExtAttr7 = json['n_ext_attr7']?.toInt();
    nExtAttr8 = json['n_ext_attr8']?.toInt();
    nExtAttr9 = json['n_ext_attr9']?.toInt();
    name = json['name']?.toString();
    objectVersionNumber = json['object_version_number']?.toInt();
    orgId = json['org_id']?.toInt();
    parentLeaseId = json['parent_lease_id']?.toInt();
    prorataShare = json['prorata_share']?.toInt();
    rentProrateMethod = json['rent_prorate_method']?.toString();
    rentableArea = json['rentable_area']?.toInt();
    sellingArea = json['selling_area']?.toInt();
    srcLeaseId = json['src_lease_id']?.toInt();
    storageArea = json['storage_area']?.toInt();
    tenancyType = json['tenancy_type']?.toString();
    termStatus = json['term_status']?.toString();
    totalAreaGla = json['total_area_gla']?.toInt();
    usableArea = json['usable_area']?.toInt();
    versionNumber = json['version_number']?.toInt();
    city = json['city']?.toString();
    currencyCode = json['currency_code']?.toString();
    state = json['state']?.toString();
    annualrent = json['annualrent']?.toInt();
    country = json['country']?.toString();
    storeName = json['store_name']?.toString();
    storeNumber = json['store_number']?.toString();
    landlordName = json['landlord_name']?.toString();
    brandName = json['brand_name']?.toString();
    censusRegion = json['census_region']?.toString();
    assetType = json['asset_type']?.toString();
    address = json['address']?.toString();
    calenderType = json['calender_type']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['acct_type_code'] = acctTypeCode;
    data['all_columns'] = allColumns;
    data['approval_status'] = approvalStatus;
    data['approved_to_pay'] = approvedToPay;
    data['brand_id'] = brandId;
    data['c_ext_attr1'] = cExtAttr1;
    data['c_ext_attr10'] = cExtAttr10;
    data['c_ext_attr2'] = cExtAttr2;
    data['c_ext_attr3'] = cExtAttr3;
    data['c_ext_attr4'] = cExtAttr4;
    data['c_ext_attr5'] = cExtAttr5;
    data['c_ext_attr6'] = cExtAttr6;
    data['c_ext_attr7'] = cExtAttr7;
    data['c_ext_attr8'] = cExtAttr8;
    data['c_ext_attr9'] = cExtAttr9;
    data['client_id'] = clientId;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['d_ext_attr1'] = dExtAttr1;
    data['d_ext_attr10'] = dExtAttr10;
    data['d_ext_attr2'] = dExtAttr2;
    data['d_ext_attr3'] = dExtAttr3;
    data['d_ext_attr4'] = dExtAttr4;
    data['d_ext_attr5'] = dExtAttr5;
    data['d_ext_attr6'] = dExtAttr6;
    data['d_ext_attr7'] = dExtAttr7;
    data['d_ext_attr8'] = dExtAttr8;
    data['d_ext_attr9'] = dExtAttr9;
    data['disposition_date'] = dispositionDate;
    data['disposition_type'] = dispositionType;
    data['effective_date'] = effectiveDate;
    data['l_ext_attr1'] = lExtAttr1;
    data['l_ext_attr10'] = lExtAttr10;
    data['l_ext_attr2'] = lExtAttr2;
    data['l_ext_attr3'] = lExtAttr3;
    data['l_ext_attr4'] = lExtAttr4;
    data['l_ext_attr5'] = lExtAttr5;
    data['l_ext_attr6'] = lExtAttr6;
    data['l_ext_attr7'] = lExtAttr7;
    data['l_ext_attr8'] = lExtAttr8;
    data['l_ext_attr9'] = lExtAttr9;
    data['landlord'] = landlord;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['lease_commencement_date'] = leaseCommencementDate;
    data['lease_date'] = leaseDate;
    data['lease_execution_date'] = leaseExecutionDate;
    data['lease_id'] = leaseId;
    data['lease_num'] = leaseNum;
    data['lease_portfolio_type'] = leasePortfolioType;
    data['lease_status'] = leaseStatus;
    data['lease_termination_date'] = leaseTerminationDate;
    data['lease_type_code'] = leaseTypeCode;
    data['legal_entity'] = legalEntity;
    data['n_ext_attr1'] = nExtAttr1;
    data['n_ext_attr10'] = nExtAttr10;
    data['n_ext_attr2'] = nExtAttr2;
    data['n_ext_attr3'] = nExtAttr3;
    data['n_ext_attr4'] = nExtAttr4;
    data['n_ext_attr5'] = nExtAttr5;
    data['n_ext_attr6'] = nExtAttr6;
    data['n_ext_attr7'] = nExtAttr7;
    data['n_ext_attr8'] = nExtAttr8;
    data['n_ext_attr9'] = nExtAttr9;
    data['name'] = name;
    data['object_version_number'] = objectVersionNumber;
    data['org_id'] = orgId;
    data['parent_lease_id'] = parentLeaseId;
    data['prorata_share'] = prorataShare;
    data['rent_prorate_method'] = rentProrateMethod;
    data['rentable_area'] = rentableArea;
    data['selling_area'] = sellingArea;
    data['src_lease_id'] = srcLeaseId;
    data['storage_area'] = storageArea;
    data['tenancy_type'] = tenancyType;
    data['term_status'] = termStatus;
    data['total_area_gla'] = totalAreaGla;
    data['usable_area'] = usableArea;
    data['version_number'] = versionNumber;
    data['city'] = city;
    data['currency_code'] = currencyCode;
    data['state'] = state;
    data['annualrent'] = annualrent;
    data['country'] = country;
    data['store_name'] = storeName;
    data['store_number'] = storeNumber;
    data['landlord_name'] = landlordName;
    data['brand_name'] = brandName;
    data['census_region'] = censusRegion;
    data['asset_type'] = assetType;
    data['address'] = address;
    data['calender_type'] = calenderType;
    return data;
  }
}
