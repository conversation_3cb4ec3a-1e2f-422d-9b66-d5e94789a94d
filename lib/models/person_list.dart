class PersonList {
  final int? personId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? departmentName;
  final String? location;
  final String? allcolumns;

  PersonList(
      {this.personId,
      this.firstName,
      this.lastName,
      this.email,
      this.departmentName,
      this.location,
      this.allcolumns});

  factory PersonList.fromJson(Map<String, dynamic> json) {
    return PersonList(
        personId: json['personId'] ?? 0,
        firstName: json['firstName'] ?? "",
        lastName: json['lastName'] ?? "",
        email: json['email'] ?? "",
        departmentName: json['departmentName'] ?? "",
        location: json['location'] ?? "",
        allcolumns: json['allcolumns'] ?? "");
  }
}
