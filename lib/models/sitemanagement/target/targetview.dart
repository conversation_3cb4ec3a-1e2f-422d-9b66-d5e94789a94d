class TargetView {
  String? address;
  int? annualPlan;
  int? brandId;
  int? capitalExpense;
  int? cbsaClass;
  String? cbsaName;
  String? censusDivision;
  String? censusRegion;
  String? city;
  int? clientId;
  String? constructionManager;
  String? country;
  String? county;
  String? countyFips;
  String? createdBy;
  String? creationDate;
  String? devPhasing;
  String? devPriority;
  String? devTargetYear;
  String? division;
  String? dma;
  String? dmaName;
  String? facilityType;
  int? fiscalPeriod;
  int? fiscalQuarter;
  int? fiscalYear;
  String? intersection;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  double? latitude;
  double? longitude;
  String? miniMarket;
  String? msaName;
  String? neighborhood;
  String? ownershipType;
  String? proposedStartDate;
  String? realEstateManager;
  int? siteRating;
  String? siteType;
  String? state;
  String? stateManager;
  String? status;
  String? statusDesc;
  int? storeClass;
  String? strategy;
  int? targetId;
  String? targetName;
  String? targetNumber;
  String? targetSource;
  String? teir;
  String? timezone;
  String? zipCode;
  String? allColumns;
  String? assetType;
  int? rootFolderId;

  TargetView({
    this.address,
    this.annualPlan,
    this.brandId,
    this.capitalExpense,
    this.cbsaClass,
    this.cbsaName,
    this.censusDivision,
    this.censusRegion,
    this.city,
    this.clientId,
    this.constructionManager,
    this.country,
    this.county,
    this.countyFips,
    this.createdBy,
    this.creationDate,
    this.devPhasing,
    this.devPriority,
    this.devTargetYear,
    this.division,
    this.dma,
    this.dmaName,
    this.facilityType,
    this.fiscalPeriod,
    this.fiscalQuarter,
    this.fiscalYear,
    this.intersection,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.latitude,
    this.longitude,
    this.miniMarket,
    this.msaName,
    this.neighborhood,
    this.ownershipType,
    this.proposedStartDate,
    this.realEstateManager,
    this.siteRating,
    this.siteType,
    this.state,
    this.stateManager,
    this.status,
    this.statusDesc,
    this.storeClass,
    this.strategy,
    this.targetId,
    this.targetName,
    this.targetNumber,
    this.targetSource,
    this.teir,
    this.timezone,
    this.zipCode,
    this.allColumns,
    this.assetType,
    this.rootFolderId,
  });
  TargetView.fromJson(Map<String, dynamic> json) {
    address = json['address']?.toString();
    annualPlan = json['annual_plan']?.toInt();
    brandId = json['brand_id']?.toInt();
    capitalExpense = json['capital_expense']?.toInt();
    cbsaClass = json['cbsa_class']?.toInt();
    cbsaName = json['cbsa_name']?.toString();
    censusDivision = json['census_division']?.toString();
    censusRegion = json['census_region']?.toString();
    city = json['city']?.toString();
    clientId = json['client_id']?.toInt();
    constructionManager = json['construction_manager']?.toString();
    country = json['country']?.toString();
    county = json['county']?.toString();
    countyFips = json['county_fips']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    devPhasing = json['dev_phasing']?.toString();
    devPriority = json['dev_priority']?.toString();
    devTargetYear = json['dev_target_year']?.toString();
    division = json['division']?.toString();
    dma = json['dma']?.toString();
    dmaName = json['dma_name']?.toString();
    facilityType = json['facility_type']?.toString();
    fiscalPeriod = json['fiscal_period']?.toInt();
    fiscalQuarter = json['fiscal_quarter']?.toInt();
    fiscalYear = json['fiscal_year']?.toInt();
    intersection = json['intersection']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    miniMarket = json['mini_market']?.toString();
    msaName = json['msa_name']?.toString();
    neighborhood = json['neighborhood']?.toString();
    ownershipType = json['ownership_type']?.toString();
    proposedStartDate = json['proposed_start_date']?.toString();
    realEstateManager = json['real_estate_manager']?.toString();
    siteRating = json['site_rating']?.toInt();
    siteType = json['site_type']?.toString();
    state = json['state']?.toString();
    stateManager = json['state_manager']?.toString();
    status = json['status']?.toString();
    statusDesc = json['status_desc']?.toString();
    storeClass = json['store_class']?.toInt();
    strategy = json['strategy']?.toString();
    targetId = json['target_id']?.toInt();
    targetName = json['target_name']?.toString();
    targetNumber = json['target_number']?.toString();
    targetSource = json['target_source']?.toString();
    teir = json['teir']?.toString();
    timezone = json['timezone']?.toString();
    zipCode = json['zip_code']?.toString();
    allColumns = json['all_columns']?.toString();
    assetType = json['asset_type']?.toString();
    rootFolderId = json["root_folder_id"]?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    data['annual_plan'] = annualPlan;
    data['brand_id'] = brandId;
    data['capital_expense'] = capitalExpense;
    data['cbsa_class'] = cbsaClass;
    data['cbsa_name'] = cbsaName;
    data['census_division'] = censusDivision;
    data['census_region'] = censusRegion;
    data['city'] = city;
    data['client_id'] = clientId;
    data['construction_manager'] = constructionManager;
    data['country'] = country;
    data['county'] = county;
    data['county_fips'] = countyFips;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['dev_phasing'] = devPhasing;
    data['dev_priority'] = devPriority;
    data['dev_target_year'] = devTargetYear;
    data['division'] = division;
    data['dma'] = dma;
    data['dma_name'] = dmaName;
    data['facility_type'] = facilityType;
    data['fiscal_period'] = fiscalPeriod;
    data['fiscal_quarter'] = fiscalQuarter;
    data['fiscal_year'] = fiscalYear;
    data['intersection'] = intersection;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['mini_market'] = miniMarket;
    data['msa_name'] = msaName;
    data['neighborhood'] = neighborhood;
    data['ownership_type'] = ownershipType;
    data['proposed_start_date'] = proposedStartDate;
    data['real_estate_manager'] = realEstateManager;
    data['site_rating'] = siteRating;
    data['site_type'] = siteType;
    data['state'] = state;
    data['state_manager'] = stateManager;
    data['status'] = status;
    data['status_desc'] = statusDesc;
    data['store_class'] = storeClass;
    data['strategy'] = strategy;
    data['target_id'] = targetId;
    data['target_name'] = targetName;
    data['target_number'] = targetNumber;
    data['target_source'] = targetSource;
    data['teir'] = teir;
    data['timezone'] = timezone;
    data['zip_code'] = zipCode;
    data['all_columns'] = allColumns;
    data['asset_type'] = assetType;
    return data;
  }
}
