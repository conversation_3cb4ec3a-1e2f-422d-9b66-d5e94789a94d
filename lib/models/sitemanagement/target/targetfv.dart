class TargetFV {
  int? targetId;
  String? devPhasing;
  String? devPriority;
  String? reCostInTa;
  String? difficultyEntry;
  String? abilityImage;
  String? trafficRating;
  String? devTargetYear;
  String? comments;
  String? cextLov21;

  TargetFV({
    this.targetId,
    this.devPhasing,
    this.devPriority,
    this.reCostInTa,
    this.difficultyEntry,
    this.abilityImage,
    this.trafficRating,
    this.devTargetYear,
    this.comments,
    this.cextLov21,
  });
  TargetFV.fromJson(Map<String, dynamic> json) {
    targetId = json['targetId']?.toInt();
    devPhasing = json['devPhasing']?.toString();
    devPriority = json['devPriority']?.toString();
    reCostInTa = json['reCostInTa']?.toString();
    difficultyEntry = json['difficultyEntry']?.toString();
    abilityImage = json['abilityImage']?.toString();
    trafficRating = json['trafficRating']?.toString();
    devTargetYear = json['devTargetYear']?.toString();
    comments = json['comments']?.toString();
    cextLov21 = json['cextLov21']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['targetId'] = targetId;
    data['devPhasing'] = devPhasing;
    data['devPriority'] = devPriority;
    data['reCostInTa'] = reCostInTa;
    data['difficultyEntry'] = difficultyEntry;
    data['abilityImage'] = abilityImage;
    data['trafficRating'] = trafficRating;
    data['devTargetYear'] = devTargetYear;
    data['comments'] = comments;
    data['cextLov21'] = cextLov21;
    return data;
  }
}
