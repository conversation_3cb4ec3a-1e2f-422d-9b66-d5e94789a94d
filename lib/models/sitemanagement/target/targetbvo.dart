class Targetbvo {
  String? targetName;
  String? targetType;
  String? targetNumber;
  int? targetId;
  String? targetSource;
  String? intersection;
  double? longitude;
  double? latitude;
  String? dma;
  String? address;
  String? city;
  String? county;
  String? state;
  String? zipCode;
  String? country;
  String? realEstateManager;
  String? proposedStartDate;
  double? annualPlan;
  double? capitalExpense;
  String? status;
  String? statusDesc;
  String? gas;
  String? gasOther;
  String? strategy;
  int? stateManager;
  String? siteType;
  String? neighborhood;
  String? division;
  String? miniMarket;
  String? devPhasing;
  String? devPriority;
  String? reCostInTa;
  String? difficultyEntry;
  String? abilityImage;
  String? trafficRating;
  String? devTargetYear;
  String? comments;
  String? teir;
  String? territoryId;
  int? orgId;
  int? clientId;
  int? brandId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? productList;
  String? profitCenter;
  String? costCenter;
  String? internalOrder;
  String? indexGroup;
  String? timezone;
  String? cExtAttr1;
  String? cExtAttr2;
  String? cExtAttr3;
  String? cExtAttr4;
  String? cExtAttr5;
  double? nExtAttr1;
  double? nExtAttr2;
  double? nExtAttr3;
  double? nExtAttr4;
  double? nExtAttr5;
  String? dExtAttr1;
  String? dExtAttr2;
  String? dExtAttr3;
  String? dExtAttr4;
  String? dExtAttr5;
  String? cExtAttr6;
  String? cExtAttr7;
  String? cExtAttr8;
  String? cExtAttr9;
  double? nExtAttr6;
  double? nExtAttr7;
  double? nExtAttr8;
  double? nExtAttr9;
  String? cExtAttr10;
  double? nExtAttr10;
  double? nExtAttr11;
  double? nExtAttr12;
  double? nExtAttr13;
  double? nExtAttr14;
  double? nExtAttr15;
  double? nExtAttr16;
  double? nExtAttr17;
  double? nExtAttr18;
  double? nExtAttr19;
  double? nExtAttr20;
  double? nExtAttr21;
  double? nExtAttr22;
  double? nExtAttr23;
  double? nExtAttr24;
  double? nExtAttr25;
  double? nExtAttr26;
  String? cExtAttr11;
  int? cbsaClass;
  int? storeClass;
  String? cExtAttr12;
  String? cExtAttr13;
  String? cExtAttr14;
  String? cExtAttr15;
  String? cExtAttr16;
  String? cExtAttr17;
  String? cExtAttr18;
  String? cExtAttr19;
  String? cExtLov1;
  String? cExtLov10;
  String? cExtLov2;
  String? cExtLov3;
  String? cExtLov4;
  String? cExtLov5;
  String? cExtLov6;
  String? cExtLov7;
  String? cExtLov8;
  String? cExtLov9;
  int? assosciatedSites;
  String? countryName;
  double? nExtAttr27;
  double? nExtAttr28;
  double? nExtAttr29;
  double? nExtAttr30;
  double? nExtAttr31;
  double? nExtAttr32;
  double? nExtAttr33;
  double? nExtAttr34;
  double? nExtAttr35;
  double? nExtAttr36;
  double? nExtAttr37;
  double? nExtAttr38;
  double? nExtAttr39;
  double? nExtAttr40;
  String? dExtAttr6;
  String? dExtAttr7;
  String? dExtAttr8;
  String? dExtAttr9;
  String? dExtAttr10;
  String? dExtAttr11;
  String? dExtAttr12;
  String? dExtAttr13;
  String? dExtAttr14;
  String? dExtAttr15;
  String? cExtAttr20;
  String? cExtAttr21;
  String? cExtAttr22;
  String? cExtAttr23;
  String? cExtAttr24;
  int? cbsaId;
  String? cbsaName;
  String? countyFips;
  int? dmaId;
  String? dmaName;
  int? msaId;
  String? msaName;
  String? cExtLov11;
  String? cExtLov12;
  String? cExtLov13;
  String? cExtLov14;
  String? cExtLov15;
  String? realEstateManagerLov;
  int? sdaId;
  String? sdaName;
  String? sdaNameFilter;
  String? globalGroupName;
  String? censusRegion;
  String? censusDivision;
  String? franchiseType;
  String? ownershipType;
  double? salesForecast;
  String? facilityType;
  String? xxstate;
  int? fiscalYear;
  int? fiscalPeriod;
  int? fiscalQuarter;
  String? miniMktFilter;
  String? risk;
  double? nExtAttr41;
  double? nExtAttr42;
  String? cExtLov16;
  String? cExtLov17;
  String? cExtLov18;
  String? cExtLov19;
  String? cExtLov20;
  String? cExtLov21;
  String? comments1;
  double? salesForecast2;
  double? salesForecast3;
  double? salesForecast4;
  double? salesForecast5;
  String? assetType;
  String? secAttr1;
  String? secAttr2;
  String? parentSdaName;
  String? uom;
  int? rootFolderId;

  Targetbvo({
    this.targetName,
    this.rootFolderId,
    this.targetType,
    this.targetNumber,
    this.targetId,
    this.targetSource,
    this.intersection,
    this.longitude,
    this.latitude,
    this.dma,
    this.address,
    this.city,
    this.county,
    this.state,
    this.zipCode,
    this.country,
    this.realEstateManager,
    this.proposedStartDate,
    this.annualPlan,
    this.capitalExpense,
    this.status,
    this.statusDesc,
    this.gas,
    this.gasOther,
    this.strategy,
    this.stateManager,
    this.siteType,
    this.neighborhood,
    this.division,
    this.miniMarket,
    this.devPhasing,
    this.devPriority,
    this.reCostInTa,
    this.difficultyEntry,
    this.abilityImage,
    this.trafficRating,
    this.devTargetYear,
    this.comments,
    this.teir,
    this.territoryId,
    this.orgId,
    this.clientId,
    this.brandId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.productList,
    this.profitCenter,
    this.costCenter,
    this.internalOrder,
    this.indexGroup,
    this.timezone,
    this.cExtAttr1,
    this.cExtAttr2,
    this.cExtAttr3,
    this.cExtAttr4,
    this.cExtAttr5,
    this.nExtAttr1,
    this.nExtAttr2,
    this.nExtAttr3,
    this.nExtAttr4,
    this.nExtAttr5,
    this.dExtAttr1,
    this.dExtAttr2,
    this.dExtAttr3,
    this.dExtAttr4,
    this.dExtAttr5,
    this.cExtAttr6,
    this.cExtAttr7,
    this.cExtAttr8,
    this.cExtAttr9,
    this.nExtAttr6,
    this.nExtAttr7,
    this.nExtAttr8,
    this.nExtAttr9,
    this.cExtAttr10,
    this.nExtAttr10,
    this.nExtAttr11,
    this.nExtAttr12,
    this.nExtAttr13,
    this.nExtAttr14,
    this.nExtAttr15,
    this.nExtAttr16,
    this.nExtAttr17,
    this.nExtAttr18,
    this.nExtAttr19,
    this.nExtAttr20,
    this.nExtAttr21,
    this.nExtAttr22,
    this.nExtAttr23,
    this.nExtAttr24,
    this.nExtAttr25,
    this.nExtAttr26,
    this.cExtAttr11,
    this.cbsaClass,
    this.storeClass,
    this.cExtAttr12,
    this.cExtAttr13,
    this.cExtAttr14,
    this.cExtAttr15,
    this.cExtAttr16,
    this.cExtAttr17,
    this.cExtAttr18,
    this.cExtAttr19,
    this.cExtLov1,
    this.cExtLov10,
    this.cExtLov2,
    this.cExtLov3,
    this.cExtLov4,
    this.cExtLov5,
    this.cExtLov6,
    this.cExtLov7,
    this.cExtLov8,
    this.cExtLov9,
    this.assosciatedSites,
    this.countryName,
    this.nExtAttr27,
    this.nExtAttr28,
    this.nExtAttr29,
    this.nExtAttr30,
    this.nExtAttr31,
    this.nExtAttr32,
    this.nExtAttr33,
    this.nExtAttr34,
    this.nExtAttr35,
    this.nExtAttr36,
    this.nExtAttr37,
    this.nExtAttr38,
    this.nExtAttr39,
    this.nExtAttr40,
    this.dExtAttr6,
    this.dExtAttr7,
    this.dExtAttr8,
    this.dExtAttr9,
    this.dExtAttr10,
    this.dExtAttr11,
    this.dExtAttr12,
    this.dExtAttr13,
    this.dExtAttr14,
    this.dExtAttr15,
    this.cExtAttr20,
    this.cExtAttr21,
    this.cExtAttr22,
    this.cExtAttr23,
    this.cExtAttr24,
    this.cbsaId,
    this.cbsaName,
    this.countyFips,
    this.dmaId,
    this.dmaName,
    this.msaId,
    this.msaName,
    this.cExtLov11,
    this.cExtLov12,
    this.cExtLov13,
    this.cExtLov14,
    this.cExtLov15,
    this.realEstateManagerLov,
    this.sdaId,
    this.sdaName,
    this.sdaNameFilter,
    this.globalGroupName,
    this.censusRegion,
    this.censusDivision,
    this.franchiseType,
    this.ownershipType,
    this.salesForecast,
    this.facilityType,
    this.xxstate,
    this.fiscalYear,
    this.fiscalPeriod,
    this.fiscalQuarter,
    this.miniMktFilter,
    this.risk,
    this.nExtAttr41,
    this.nExtAttr42,
    this.cExtLov16,
    this.cExtLov17,
    this.cExtLov18,
    this.cExtLov19,
    this.cExtLov20,
    this.cExtLov21,
    this.comments1,
    this.salesForecast2,
    this.salesForecast3,
    this.salesForecast4,
    this.salesForecast5,
    this.assetType,
    this.secAttr1,
    this.secAttr2,
    this.parentSdaName,
    this.uom,
  });
  Targetbvo.fromJson(Map<String, dynamic> json) {
    targetName = json['target_name']?.toString();
    targetType = json['target_type']?.toString();
    targetNumber = json['target_number']?.toString();
    targetId = json['target_id']?.toInt();
    targetSource = json['target_source']?.toString();
    intersection = json['intersection']?.toString();
    longitude = json['longitude']?.toDouble();
    latitude = json['latitude']?.toDouble();
    dma = json['dma']?.toString();
    address = json['address']?.toString();
    city = json['city']?.toString();
    county = json['county']?.toString();
    state = json['state']?.toString();
    zipCode = json['zip_code']?.toString();
    country = json['country']?.toString();
    realEstateManager = json['real_estate_manager']?.toString();
    proposedStartDate = json['proposed_start_date']?.toString();
    annualPlan = json['annual_plan']?.toDouble();
    capitalExpense = json['capital_expense']?.toDouble();
    status = json['status']?.toString();
    statusDesc = json['status_desc']?.toString();
    gas = json['gas']?.toString();
    gasOther = json['gas_other']?.toString();
    strategy = json['strategy']?.toString();
    stateManager = json['state_manager']?.toInt();
    siteType = json['site_type']?.toString();
    neighborhood = json['neighborhood']?.toString();
    division = json['division']?.toString();
    miniMarket = json['mini_market']?.toString();
    devPhasing = json['dev_phasing']?.toString();
    devPriority = json['dev_priority']?.toString();
    reCostInTa = json['re_cost_in_ta']?.toString();
    difficultyEntry = json['difficulty_entry']?.toString();
    abilityImage = json['ability_image']?.toString();
    trafficRating = json['traffic_rating']?.toString();
    devTargetYear = json['dev_target_year']?.toString();
    comments = json['comments']?.toString();
    teir = json['teir']?.toString();
    territoryId = json['territory_id']?.toString();
    orgId = json['org_id']?.toInt();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    productList = json['product_list']?.toString();
    profitCenter = json['profit_center']?.toString();
    costCenter = json['cost_center']?.toString();
    internalOrder = json['internal_order']?.toString();
    indexGroup = json['index_group']?.toString();
    timezone = json['timezone']?.toString();
    cExtAttr1 = json['c_ext_attr1']?.toString();
    cExtAttr2 = json['c_ext_attr2']?.toString();
    cExtAttr3 = json['c_ext_attr3']?.toString();
    cExtAttr4 = json['c_ext_attr4']?.toString();
    cExtAttr5 = json['c_ext_attr5']?.toString();
    nExtAttr1 = json['n_ext_attr1']?.toDouble();
    nExtAttr2 = json['n_ext_attr2']?.toDouble();
    nExtAttr3 = json['n_ext_attr3']?.toDouble();
    nExtAttr4 = json['n_ext_attr4']?.toDouble();
    nExtAttr5 = json['n_ext_attr5']?.toDouble();
    dExtAttr1 = json['d_ext_attr1']?.toString();
    dExtAttr2 = json['d_ext_attr2']?.toString();
    dExtAttr3 = json['d_ext_attr3']?.toString();
    dExtAttr4 = json['d_ext_attr4']?.toString();
    dExtAttr5 = json['d_ext_attr5']?.toString();
    cExtAttr6 = json['c_ext_attr6']?.toString();
    cExtAttr7 = json['c_ext_attr7']?.toString();
    cExtAttr8 = json['c_ext_attr8']?.toString();
    cExtAttr9 = json['c_ext_attr9']?.toString();
    nExtAttr6 = json['n_ext_attr6']?.toDouble();
    nExtAttr7 = json['n_ext_attr7']?.toDouble();
    nExtAttr8 = json['n_ext_attr8']?.toDouble();
    nExtAttr9 = json['n_ext_attr9']?.toDouble();
    cExtAttr10 = json['c_ext_attr10']?.toString();
    nExtAttr10 = json['n_ext_attr10']?.toDouble();
    nExtAttr11 = json['n_ext_attr11']?.toDouble();
    nExtAttr12 = json['n_ext_attr12']?.toDouble();
    nExtAttr13 = json['n_ext_attr13']?.toDouble();
    nExtAttr14 = json['n_ext_attr14']?.toDouble();
    nExtAttr15 = json['n_ext_attr15']?.toDouble();
    nExtAttr16 = json['n_ext_attr16']?.toDouble();
    nExtAttr17 = json['n_ext_attr17']?.toDouble();
    nExtAttr18 = json['n_ext_attr18']?.toDouble();
    nExtAttr19 = json['n_ext_attr19']?.toDouble();
    nExtAttr20 = json['n_ext_attr20']?.toDouble();
    nExtAttr21 = json['n_ext_attr21']?.toDouble();
    nExtAttr22 = json['n_ext_attr22']?.toDouble();
    nExtAttr23 = json['n_ext_attr23']?.toDouble();
    nExtAttr24 = json['n_ext_attr24']?.toDouble();
    nExtAttr25 = json['n_ext_attr25']?.toDouble();
    nExtAttr26 = json['n_ext_attr26']?.toDouble();
    cExtAttr11 = json['c_ext_attr11']?.toString();
    cbsaClass = json['cbsa_class']?.toInt();
    storeClass = json['store_class']?.toInt();
    cExtAttr12 = json['c_ext_attr12']?.toString();
    cExtAttr13 = json['c_ext_attr13']?.toString();
    cExtAttr14 = json['c_ext_attr14']?.toString();
    cExtAttr15 = json['c_ext_attr15']?.toString();
    cExtAttr16 = json['c_ext_attr16']?.toString();
    cExtAttr17 = json['c_ext_attr17']?.toString();
    cExtAttr18 = json['c_ext_attr18']?.toString();
    cExtAttr19 = json['c_ext_attr19']?.toString();
    cExtLov1 = json['c_ext_lov1']?.toString();
    cExtLov10 = json['c_ext_lov10']?.toString();
    cExtLov2 = json['c_ext_lov2']?.toString();
    cExtLov3 = json['c_ext_lov3']?.toString();
    cExtLov4 = json['c_ext_lov4']?.toString();
    cExtLov5 = json['c_ext_lov5']?.toString();
    cExtLov6 = json['c_ext_lov6']?.toString();
    cExtLov7 = json['c_ext_lov7']?.toString();
    cExtLov8 = json['c_ext_lov8']?.toString();
    cExtLov9 = json['c_ext_lov9']?.toString();
    assosciatedSites = json['assosciated_sites']?.toInt();
    countryName = json['country_name']?.toString();
    nExtAttr27 = json['n_ext_attr27']?.toDouble();
    nExtAttr28 = json['n_ext_attr28']?.toDouble();
    nExtAttr29 = json['n_ext_attr29']?.toDouble();
    nExtAttr30 = json['n_ext_attr30']?.toDouble();
    nExtAttr31 = json['n_ext_attr31']?.toDouble();
    nExtAttr32 = json['n_ext_attr32']?.toDouble();
    nExtAttr33 = json['n_ext_attr33']?.toDouble();
    nExtAttr34 = json['n_ext_attr34']?.toDouble();
    nExtAttr35 = json['n_ext_attr35']?.toDouble();
    nExtAttr36 = json['n_ext_attr36']?.toDouble();
    nExtAttr37 = json['n_ext_attr37']?.toDouble();
    nExtAttr38 = json['n_ext_attr38']?.toDouble();
    nExtAttr39 = json['n_ext_attr39']?.toDouble();
    nExtAttr40 = json['n_ext_attr40']?.toDouble();
    dExtAttr6 = json['d_ext_attr6']?.toString();
    dExtAttr7 = json['d_ext_attr7']?.toString();
    dExtAttr8 = json['d_ext_attr8']?.toString();
    dExtAttr9 = json['d_ext_attr9']?.toString();
    dExtAttr10 = json['d_ext_attr10']?.toString();
    dExtAttr11 = json['d_ext_attr11']?.toString();
    dExtAttr12 = json['d_ext_attr12']?.toString();
    dExtAttr13 = json['d_ext_attr13']?.toString();
    dExtAttr14 = json['d_ext_attr14']?.toString();
    dExtAttr15 = json['d_ext_attr15']?.toString();
    cExtAttr20 = json['c_ext_attr20']?.toString();
    cExtAttr21 = json['c_ext_attr21']?.toString();
    cExtAttr22 = json['c_ext_attr22']?.toString();
    cExtAttr23 = json['c_ext_attr23']?.toString();
    cExtAttr24 = json['c_ext_attr24']?.toString();
    cbsaId = json['cbsa_id']?.toInt();
    cbsaName = json['cbsa_name']?.toString();
    countyFips = json['county_fips']?.toString();
    dmaId = json['dma_id']?.toInt();
    dmaName = json['dma_name']?.toString();
    msaId = json['msa_id']?.toInt();
    msaName = json['msa_name']?.toString();
    cExtLov11 = json['c_ext_lov11']?.toString();
    cExtLov12 = json['c_ext_lov12']?.toString();
    cExtLov13 = json['c_ext_lov13']?.toString();
    cExtLov14 = json['c_ext_lov14']?.toString();
    cExtLov15 = json['c_ext_lov15']?.toString();
    realEstateManagerLov = json['real_estate_manager_lov']?.toString();
    sdaId = json['sda_id']?.toInt();
    sdaName = json['sda_name']?.toString();
    sdaNameFilter = json['sda_name_filter']?.toString();
    globalGroupName = json['global_group_name']?.toString();
    censusRegion = json['census_region']?.toString();
    censusDivision = json['census_division']?.toString();
    franchiseType = json['franchise_type']?.toString();
    ownershipType = json['ownership_type']?.toString();
    salesForecast = json['sales_forecast']?.toDouble();
    facilityType = json['facility_type']?.toString();
    xxstate = json['xxstate']?.toString();
    fiscalYear = json['fiscal_year']?.toInt();
    fiscalPeriod = json['fiscal_period']?.toInt();
    fiscalQuarter = json['fiscal_quarter']?.toInt();
    miniMktFilter = json['mini_mkt_filter']?.toString();
    risk = json['risk']?.toString();
    nExtAttr41 = json['n_ext_attr41']?.toDouble();
    nExtAttr42 = json['n_ext_attr42']?.toDouble();
    cExtLov16 = json['c_ext_lov16']?.toString();
    cExtLov17 = json['c_ext_lov17']?.toString();
    cExtLov18 = json['c_ext_lov18']?.toString();
    cExtLov19 = json['c_ext_lov19']?.toString();
    cExtLov20 = json['c_ext_lov20']?.toString();
    cExtLov21 = json['c_ext_lov21']?.toString();
    comments1 = json['comments1']?.toString();
    salesForecast2 = json['sales_forecast2']?.toDouble();
    salesForecast3 = json['sales_forecast3']?.toDouble();
    salesForecast4 = json['sales_forecast4']?.toDouble();
    salesForecast5 = json['sales_forecast5']?.toDouble();
    assetType = json['asset_type']?.toString();
    secAttr1 = json['sec_attr1']?.toString();
    secAttr2 = json['sec_attr2']?.toString();
    parentSdaName = json['parent_sda_name']?.toString();
    uom = json['uom']?.toString();
    rootFolderId = json["root_folder_id"]?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['target_name'] = targetName;
    data['target_type'] = targetType;
    data['target_number'] = targetNumber;
    data['target_id'] = targetId;
    data['target_source'] = targetSource;
    data['intersection'] = intersection;
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['dma'] = dma;
    data['address'] = address;
    data['city'] = city;
    data['county'] = county;
    data['state'] = state;
    data['zip_code'] = zipCode;
    data['country'] = country;
    data['real_estate_manager'] = realEstateManager;
    data['proposed_start_date'] = proposedStartDate;
    data['annual_plan'] = annualPlan;
    data['capital_expense'] = capitalExpense;
    data['status'] = status;
    data['status_desc'] = statusDesc;
    data['gas'] = gas;
    data['gas_other'] = gasOther;
    data['strategy'] = strategy;
    data['state_manager'] = stateManager;
    data['site_type'] = siteType;
    data['neighborhood'] = neighborhood;
    data['division'] = division;
    data['mini_market'] = miniMarket;
    data['dev_phasing'] = devPhasing;
    data['dev_priority'] = devPriority;
    data['re_cost_in_ta'] = reCostInTa;
    data['difficulty_entry'] = difficultyEntry;
    data['ability_image'] = abilityImage;
    data['traffic_rating'] = trafficRating;
    data['dev_target_year'] = devTargetYear;
    data['comments'] = comments;
    data['teir'] = teir;
    data['territory_id'] = territoryId;
    data['org_id'] = orgId;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['product_list'] = productList;
    data['profit_center'] = profitCenter;
    data['cost_center'] = costCenter;
    data['internal_order'] = internalOrder;
    data['index_group'] = indexGroup;
    data['timezone'] = timezone;
    data['c_ext_attr1'] = cExtAttr1;
    data['c_ext_attr2'] = cExtAttr2;
    data['c_ext_attr3'] = cExtAttr3;
    data['c_ext_attr4'] = cExtAttr4;
    data['c_ext_attr5'] = cExtAttr5;
    data['n_ext_attr1'] = nExtAttr1;
    data['n_ext_attr2'] = nExtAttr2;
    data['n_ext_attr3'] = nExtAttr3;
    data['n_ext_attr4'] = nExtAttr4;
    data['n_ext_attr5'] = nExtAttr5;
    data['d_ext_attr1'] = dExtAttr1;
    data['d_ext_attr2'] = dExtAttr2;
    data['d_ext_attr3'] = dExtAttr3;
    data['d_ext_attr4'] = dExtAttr4;
    data['d_ext_attr5'] = dExtAttr5;
    data['c_ext_attr6'] = cExtAttr6;
    data['c_ext_attr7'] = cExtAttr7;
    data['c_ext_attr8'] = cExtAttr8;
    data['c_ext_attr9'] = cExtAttr9;
    data['n_ext_attr6'] = nExtAttr6;
    data['n_ext_attr7'] = nExtAttr7;
    data['n_ext_attr8'] = nExtAttr8;
    data['n_ext_attr9'] = nExtAttr9;
    data['c_ext_attr10'] = cExtAttr10;
    data['n_ext_attr10'] = nExtAttr10;
    data['n_ext_attr11'] = nExtAttr11;
    data['n_ext_attr12'] = nExtAttr12;
    data['n_ext_attr13'] = nExtAttr13;
    data['n_ext_attr14'] = nExtAttr14;
    data['n_ext_attr15'] = nExtAttr15;
    data['n_ext_attr16'] = nExtAttr16;
    data['n_ext_attr17'] = nExtAttr17;
    data['n_ext_attr18'] = nExtAttr18;
    data['n_ext_attr19'] = nExtAttr19;
    data['n_ext_attr20'] = nExtAttr20;
    data['n_ext_attr21'] = nExtAttr21;
    data['n_ext_attr22'] = nExtAttr22;
    data['n_ext_attr23'] = nExtAttr23;
    data['n_ext_attr24'] = nExtAttr24;
    data['n_ext_attr25'] = nExtAttr25;
    data['n_ext_attr26'] = nExtAttr26;
    data['c_ext_attr11'] = cExtAttr11;
    data['cbsa_class'] = cbsaClass;
    data['store_class'] = storeClass;
    data['c_ext_attr12'] = cExtAttr12;
    data['c_ext_attr13'] = cExtAttr13;
    data['c_ext_attr14'] = cExtAttr14;
    data['c_ext_attr15'] = cExtAttr15;
    data['c_ext_attr16'] = cExtAttr16;
    data['c_ext_attr17'] = cExtAttr17;
    data['c_ext_attr18'] = cExtAttr18;
    data['c_ext_attr19'] = cExtAttr19;
    data['c_ext_lov1'] = cExtLov1;
    data['c_ext_lov10'] = cExtLov10;
    data['c_ext_lov2'] = cExtLov2;
    data['c_ext_lov3'] = cExtLov3;
    data['c_ext_lov4'] = cExtLov4;
    data['c_ext_lov5'] = cExtLov5;
    data['c_ext_lov6'] = cExtLov6;
    data['c_ext_lov7'] = cExtLov7;
    data['c_ext_lov8'] = cExtLov8;
    data['c_ext_lov9'] = cExtLov9;
    data['assosciated_sites'] = assosciatedSites;
    data['country_name'] = countryName;
    data['n_ext_attr27'] = nExtAttr27;
    data['n_ext_attr28'] = nExtAttr28;
    data['n_ext_attr29'] = nExtAttr29;
    data['n_ext_attr30'] = nExtAttr30;
    data['n_ext_attr31'] = nExtAttr31;
    data['n_ext_attr32'] = nExtAttr32;
    data['n_ext_attr33'] = nExtAttr33;
    data['n_ext_attr34'] = nExtAttr34;
    data['n_ext_attr35'] = nExtAttr35;
    data['n_ext_attr36'] = nExtAttr36;
    data['n_ext_attr37'] = nExtAttr37;
    data['n_ext_attr38'] = nExtAttr38;
    data['n_ext_attr39'] = nExtAttr39;
    data['n_ext_attr40'] = nExtAttr40;
    data['d_ext_attr6'] = dExtAttr6;
    data['d_ext_attr7'] = dExtAttr7;
    data['d_ext_attr8'] = dExtAttr8;
    data['d_ext_attr9'] = dExtAttr9;
    data['d_ext_attr10'] = dExtAttr10;
    data['d_ext_attr11'] = dExtAttr11;
    data['d_ext_attr12'] = dExtAttr12;
    data['d_ext_attr13'] = dExtAttr13;
    data['d_ext_attr14'] = dExtAttr14;
    data['d_ext_attr15'] = dExtAttr15;
    data['c_ext_attr20'] = cExtAttr20;
    data['c_ext_attr21'] = cExtAttr21;
    data['c_ext_attr22'] = cExtAttr22;
    data['c_ext_attr23'] = cExtAttr23;
    data['c_ext_attr24'] = cExtAttr24;
    data['cbsa_id'] = cbsaId;
    data['cbsa_name'] = cbsaName;
    data['county_fips'] = countyFips;
    data['dma_id'] = dmaId;
    data['dma_name'] = dmaName;
    data['msa_id'] = msaId;
    data['msa_name'] = msaName;
    data['c_ext_lov11'] = cExtLov11;
    data['c_ext_lov12'] = cExtLov12;
    data['c_ext_lov13'] = cExtLov13;
    data['c_ext_lov14'] = cExtLov14;
    data['c_ext_lov15'] = cExtLov15;
    data['real_estate_manager_lov'] = realEstateManagerLov;
    data['sda_id'] = sdaId;
    data['sda_name'] = sdaName;
    data['sda_name_filter'] = sdaNameFilter;
    data['global_group_name'] = globalGroupName;
    data['census_region'] = censusRegion;
    data['census_division'] = censusDivision;
    data['franchise_type'] = franchiseType;
    data['ownership_type'] = ownershipType;
    data['sales_forecast'] = salesForecast;
    data['facility_type'] = facilityType;
    data['xxstate'] = xxstate;
    data['fiscal_year'] = fiscalYear;
    data['fiscal_period'] = fiscalPeriod;
    data['fiscal_quarter'] = fiscalQuarter;
    data['mini_mkt_filter'] = miniMktFilter;
    data['risk'] = risk;
    data['n_ext_attr41'] = nExtAttr41;
    data['n_ext_attr42'] = nExtAttr42;
    data['c_ext_lov16'] = cExtLov16;
    data['c_ext_lov17'] = cExtLov17;
    data['c_ext_lov18'] = cExtLov18;
    data['c_ext_lov19'] = cExtLov19;
    data['c_ext_lov20'] = cExtLov20;
    data['c_ext_lov21'] = cExtLov21;
    data['comments1'] = comments1;
    data['sales_forecast2'] = salesForecast2;
    data['sales_forecast3'] = salesForecast3;
    data['sales_forecast4'] = salesForecast4;
    data['sales_forecast5'] = salesForecast5;
    data['asset_type'] = assetType;
    data['sec_attr1'] = secAttr1;
    data['sec_attr2'] = secAttr2;
    data['parent_sda_name'] = parentSdaName;
    data['uom'] = uom;
    return data;
  }
}
