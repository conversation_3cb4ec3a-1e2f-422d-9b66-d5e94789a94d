class LocateData {
  String? entityType;
  int? entityId;
  String? entityName;
  int? brand;
  double? distance;
  String? address;
  String? city;
  String? state;
  double? longitude;
  double? latitude;
  String? fullAddress;
  String? entityNumber;

  LocateData({
    this.entityType,
    this.entityId,
    this.entityName,
    this.brand,
    this.distance,
    this.address,
    this.city,
    this.state,
    this.longitude,
    this.latitude,
    this.fullAddress,
  });
  LocateData.fromJson(Map<String, dynamic> json) {
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    entityName = json['entity_name']?.toString();
    brand = json['brand']?.toInt();
    distance = json['distance']?.toDouble();
    address = json['address']?.toString();
    city = json['city']?.toString();
    state = json['state']?.toString();
    longitude = json['longitude']?.toDouble();
    latitude = json['latitude']?.toDouble();
    fullAddress = json['full_address']?.toString();
    entityNumber = json['entity_number']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['entity_name'] = entityName;
    data['brand'] = brand;
    data['distance'] = distance;
    data['address'] = address;
    data['city'] = city;
    data['state'] = state;
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['full_address'] = fullAddress;
    data['entity_number'] = entityNumber;
    return data;
  }
}
