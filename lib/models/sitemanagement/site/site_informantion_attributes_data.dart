class SiteInformationAttributes {
  int? siteId;
  String? cExtAttr19;
  String? cExtAttr19Desc;
  String? cExtLov22;
  String? cExtLov22Desc;
  String? cExtLov72;
  String? cExtLov72Desc;
  String? cExtLov74;
  String? cExtLov74Desc;
  String? cExtLov16;
  String? cExtLov16Desc;
  String? cExtLov2;
  String? cExtLov2Desc;
  String? cExtLov27;
  String? cExtLov27Desc;
  String? cExtLov30;
  String? cExtLov30Desc;
  String? cExtLov6;
  String? cExtLov6Desc;
  int? nExtAttr110;
  int? nExtAttr111;
  int? nExtAttr112;
  int? nExtAttr115;
  String? proposedStartDate;
  String? cExtAttr27;
  String? cExtAttr29;
  String? cExtAttr30;
  String? cExtAttr62;
  String? dExtAttr33;
  String? dExtAttr34;
  String? timezone;

  SiteInformationAttributes({
    this.siteId,
    this.cExtAttr19,
    this.cExtAttr19Desc,
    this.cExtLov22,
    this.cExtLov22Desc,
    this.cExtLov72,
    this.cExtLov72Desc,
    this.cExtLov74,
    this.cExtLov74Desc,
    this.cExtLov16,
    this.cExtLov16Desc,
    this.cExtLov2,
    this.cExtLov2Desc,
    this.cExtLov27,
    this.cExtLov27Desc,
    this.cExtLov30,
    this.cExtLov30Desc,
    this.cExtLov6,
    this.cExtLov6Desc,
    this.nExtAttr110,
    this.nExtAttr111,
    this.nExtAttr112,
    this.nExtAttr115,
    this.proposedStartDate,
    this.cExtAttr27,
    this.cExtAttr29,
    this.cExtAttr30,
    this.cExtAttr62,
    this.dExtAttr33,
    this.dExtAttr34,
    this.timezone,
  });
  SiteInformationAttributes.fromJson(Map<String, dynamic> json) {
    siteId = json['site_id']?.toInt();
    cExtAttr19 = json['c_ext_attr19']?.toString();
    cExtAttr19Desc = json['c_ext_attr19_desc']?.toString();
    cExtLov22 = json['c_ext_lov22']?.toString();
    cExtLov22Desc = json['c_ext_lov22_desc']?.toString();
    cExtLov72 = json['c_ext_lov72']?.toString();
    cExtLov72Desc = json['c_ext_lov72_desc']?.toString();
    cExtLov74 = json['c_ext_lov74']?.toString();
    cExtLov74Desc = json['c_ext_lov74_desc']?.toString();
    cExtLov16 = json['c_ext_lov16']?.toString();
    cExtLov16Desc = json['c_ext_lov16_desc']?.toString();
    cExtLov2 = json['c_ext_lov2']?.toString();
    cExtLov2Desc = json['c_ext_lov2_desc']?.toString();
    cExtLov27 = json['c_ext_lov27']?.toString();
    cExtLov27Desc = json['c_ext_lov27_desc']?.toString();
    cExtLov30 = json['c_ext_lov30']?.toString();
    cExtLov30Desc = json['c_ext_lov30_desc']?.toString();
    cExtLov6 = json['c_ext_lov6']?.toString();
    cExtLov6Desc = json['c_ext_lov6_desc']?.toString();
    nExtAttr110 = json['n_ext_attr110']?.toInt();
    nExtAttr111 = json['n_ext_attr111']?.toInt();
    nExtAttr112 = json['n_ext_attr112']?.toInt();
    nExtAttr115 = json['n_ext_attr115']?.toInt();
    proposedStartDate = json['proposed_start_date']?.toString();
    cExtAttr27 = json['c_ext_attr27']?.toString();
    cExtAttr29 = json['c_ext_attr29']?.toString();
    cExtAttr30 = json['c_ext_attr30']?.toString();
    cExtAttr62 = json['c_ext_attr62']?.toString();
    dExtAttr33 = json['d_ext_attr33']?.toString();
    dExtAttr34 = json['d_ext_attr34']?.toString();
    timezone = json['timezone']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['site_id'] = siteId;
    data['c_ext_attr19'] = cExtAttr19;
    data['c_ext_attr19_desc'] = cExtAttr19Desc;
    data['c_ext_lov22'] = cExtLov22;
    data['c_ext_lov22_desc'] = cExtLov22Desc;
    data['c_ext_lov72'] = cExtLov72;
    data['c_ext_lov72_desc'] = cExtLov72Desc;
    data['c_ext_lov74'] = cExtLov74;
    data['c_ext_lov74_desc'] = cExtLov74Desc;
    data['c_ext_lov16'] = cExtLov16;
    data['c_ext_lov16_desc'] = cExtLov16Desc;
    data['c_ext_lov2'] = cExtLov2;
    data['c_ext_lov2_desc'] = cExtLov2Desc;
    data['c_ext_lov27'] = cExtLov27;
    data['c_ext_lov27_desc'] = cExtLov27Desc;
    data['c_ext_lov30'] = cExtLov30;
    data['c_ext_lov30_desc'] = cExtLov30Desc;
    data['c_ext_lov6'] = cExtLov6;
    data['c_ext_lov6_desc'] = cExtLov6Desc;
    data['n_ext_attr110'] = nExtAttr110;
    data['n_ext_attr111'] = nExtAttr111;
    data['n_ext_attr112'] = nExtAttr112;
    data['n_ext_attr115'] = nExtAttr115;
    data['proposed_start_date'] = proposedStartDate;
    data['c_ext_attr27'] = cExtAttr27;
    data['c_ext_attr29'] = cExtAttr29;
    data['c_ext_attr30'] = cExtAttr30;
    data['c_ext_attr62'] = cExtAttr62;
    data['d_ext_attr33'] = dExtAttr33;
    data['d_ext_attr34'] = dExtAttr34;
    data['timezone'] = timezone;
    return data;
  }
}
