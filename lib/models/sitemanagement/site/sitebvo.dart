class SiteBvo {
  int? siteId;
  String? siteName;
  int? targetId;
  double? longitude;
  double? latitude;
  String? address;
  String? city;
  String? county;
  String? state;
  String? zipCode;
  String? country;
  int? projectId;
  String? siteType;
  String? creationDate;
  String? createdBy;
  String? targetNumber;
  String? siteNumber;
  int? sfCount;
  String? cbsaName;
  int? cbsaId;
  String? countyFips;
  int? dmaId;
  String? dmaName;
  int? msaId;
  String? msaName;
  String? censusRegion;
  String? censusDivision;
  String? readOnlyFlag;
  String? projectName;
  String? targetName;
  int? budgetTemplateId;
  String? franchiseType;
  String? countryName;
  int? storeId;
  String? storeName;
  String? dealSummary;
  String? strength;
  String? weakness;
  String? opportunity;
  String? threat;
  String? swotDescription;
  String? constructionAnalysis;
  String? statusDesc;
  String? category;

  SiteBvo({
    this.siteId,
    this.siteName,
    this.targetId,
    this.longitude,
    this.latitude,
    this.address,
    this.city,
    this.county,
    this.state,
    this.zipCode,
    this.country,
    this.projectId,
    this.siteType,
    this.creationDate,
    this.createdBy,
    this.targetNumber,
    this.siteNumber,
    this.sfCount,
    this.cbsaName,
    this.cbsaId,
    this.countyFips,
    this.dmaId,
    this.dmaName,
    this.msaId,
    this.msaName,
    this.censusRegion,
    this.censusDivision,
    this.readOnlyFlag,
    this.projectName,
    this.targetName,
    this.budgetTemplateId,
    this.franchiseType,
    this.countryName,
    this.storeId,
    this.storeName,
    this.dealSummary,
    this.strength,
    this.weakness,
    this.opportunity,
    this.threat,
    this.swotDescription,
    this.constructionAnalysis,
    this.statusDesc,
    this.category,
  });
  SiteBvo.fromJson(Map<String, dynamic> json) {
    siteId = json['site_id']?.toInt();
    siteName = json['site_name']?.toString();
    targetId = json['target_id']?.toInt();
    longitude = json['longitude']?.toDouble();
    latitude = json['latitude']?.toDouble();
    address = json['address']?.toString();
    city = json['city']?.toString();
    county = json['county']?.toString();
    state = json['state']?.toString();
    zipCode = json['zip_code']?.toString();
    country = json['country']?.toString();
    projectId = json['project_id']?.toInt();
    siteType = json['site_type']?.toString();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    targetNumber = json['target_number']?.toString();
    siteNumber = json['site_number']?.toString();
    sfCount = json['sf_count']?.toInt();
    cbsaName = json['cbsa_name']?.toString();
    cbsaId = json['cbsa_id']?.toInt();
    countyFips = json['county_fips']?.toString();
    dmaId = json['dma_id']?.toInt();
    dmaName = json['dma_name']?.toString();
    msaId = json['msa_id']?.toInt();
    msaName = json['msa_name']?.toString();
    censusRegion = json['census_region']?.toString();
    censusDivision = json['census_division']?.toString();
    readOnlyFlag = json['read_only_flag']?.toString();
    projectName = json['project_name']?.toString();
    targetName = json['target_name']?.toString();
    budgetTemplateId = json['budget_template_id']?.toInt();
    franchiseType = json['franchise_type']?.toString();
    countryName = json['country_name']?.toString();
    storeId = json['store_id']?.toInt();
    storeName = json['store_name']?.toString();
    constructionAnalysis = json['Construction_Analysis']?.toString();
    dealSummary = json['deal_summary']?.toString();
    strength = json['strength']?.toString();
    weakness = json['weakness']?.toString();
    opportunity = json['opportunity']?.toString();
    threat = json['threat']?.toString();
    swotDescription = json['swot_description']?.toString();
    statusDesc = json['status_desc']?.toString();
    category = json['category']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['site_id'] = siteId;
    data['site_name'] = siteName;
    data['target_id'] = targetId;
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['address'] = address;
    data['city'] = city;
    data['county'] = county;
    data['state'] = state;
    data['zip_code'] = zipCode;
    data['country'] = country;
    data['project_id'] = projectId;
    data['site_type'] = siteType;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['target_number'] = targetNumber;
    data['site_number'] = siteNumber;
    data['sf_count'] = sfCount;
    data['cbsa_name'] = cbsaName;
    data['cbsa_id'] = cbsaId;
    data['county_fips'] = countyFips;
    data['dma_id'] = dmaId;
    data['dma_name'] = dmaName;
    data['msa_id'] = msaId;
    data['msa_name'] = msaName;
    data['census_region'] = censusRegion;
    data['census_division'] = censusDivision;
    data['read_only_flag'] = readOnlyFlag;
    data['project_name'] = projectName;
    data['target_name'] = targetName;
    data['budget_template_id'] = budgetTemplateId;
    data['franchise_type'] = franchiseType;
    data['country_name'] = countryName;
    data['store_id'] = storeId;
    data['store_name'] = storeName;
    data['Construction_Analysis'] = constructionAnalysis;
    data['deal_summary'] = dealSummary;
    data['STRENGTH'] = strength;
    data['WEAKNESS'] = weakness;
    data['OPPORTUNITY'] = opportunity;
    data['THREAT'] = threat;
    data['SWOT_DESCRIPTION'] = swotDescription;
    data['status_desc'] = statusDesc;
    data['category'] = category;

    return data;
  }
}
