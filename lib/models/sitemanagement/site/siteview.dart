class SiteView {
  int? siteId;
  String? siteName;
  String? siteNumber;
  int? storeId;
  String? storeNumber;
  int? targetId;
  String? proposedStartDate;
  String? status;
  String? statusDesc;
  String? wfStatus;
  double? longitude;
  double? latitude;
  String? dma;
  String? address;
  String? address3;
  String? address2;
  String? city;
  String? county;
  String? state;
  String? zipCode;
  String? country;
  String? prototype;
  String? storeType;
  int? totalArea;
  int? projectCost;
  String? category;
  String? siteType;
  String? ownershipType;
  String? facilityType;
  String? propertyType;
  int? sqFt;
  int? cbsaClass;
  int? storeClass;
  String? cbsaName;
  String? msaName;
  String? dmaName;
  String? realEstateManager;
  String? constructionManager;
  String? managerOfDev;
  String? managerOfCons;
  String? realEstateVp;
  String? operationsManager;
  String? operationsVp;
  String? operationsRegion;
  int? fiscalPeriod;
  int? fiscalYear;
  int? fiscalQuarter;
  String? timezone;
  int? clientId;
  int? brandId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? allColumns;
  int? analogCount;
  String? readOnlyFlag;
  String? cExtAttr19;
  String? mallName;
  int? rootFolderId;

  SiteView({
    this.siteId,
    this.siteName,
    this.siteNumber,
    this.storeId,
    this.storeNumber,
    this.targetId,
    this.proposedStartDate,
    this.status,
    this.statusDesc,
    this.wfStatus,
    this.longitude,
    this.latitude,
    this.dma,
    this.address,
    this.address3,
    this.address2,
    this.city,
    this.county,
    this.state,
    this.zipCode,
    this.country,
    this.prototype,
    this.storeType,
    this.totalArea,
    this.projectCost,
    this.category,
    this.siteType,
    this.ownershipType,
    this.facilityType,
    this.propertyType,
    this.sqFt,
    this.cbsaClass,
    this.storeClass,
    this.cbsaName,
    this.msaName,
    this.dmaName,
    this.realEstateManager,
    this.constructionManager,
    this.managerOfDev,
    this.managerOfCons,
    this.realEstateVp,
    this.operationsManager,
    this.operationsVp,
    this.operationsRegion,
    this.fiscalPeriod,
    this.fiscalYear,
    this.fiscalQuarter,
    this.timezone,
    this.clientId,
    this.brandId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.allColumns,
    this.analogCount,
    this.readOnlyFlag,
    this.cExtAttr19,
    this.mallName,
    this.rootFolderId,
  });
  SiteView.fromJson(Map<String, dynamic> json) {
    siteId = json['site_id']?.toInt();
    siteName = json['site_name']?.toString();
    siteNumber = json['site_number']?.toString();
    storeId = json['store_id']?.toInt();
    storeNumber = json['store_number']?.toString();
    targetId = json['target_id']?.toInt();
    proposedStartDate = json['proposed_start_date']?.toString();
    status = json['status']?.toString();
    statusDesc = json['status_desc']?.toString();
    wfStatus = json['wf_status']?.toString();
    longitude = json['longitude']?.toDouble();
    latitude = json['latitude']?.toDouble();
    dma = json['dma']?.toString();
    address = json['address']?.toString();
    address3 = json['address3']?.toString();
    address2 = json['address2']?.toString();
    city = json['city']?.toString();
    county = json['county']?.toString();
    state = json['state']?.toString();
    zipCode = json['zip_code']?.toString();
    country = json['country']?.toString();
    prototype = json['prototype']?.toString();
    storeType = json['store_type']?.toString();
    totalArea = json['total_area']?.toInt();
    projectCost = json['project_cost']?.toInt();
    category = json['category']?.toString();
    siteType = json['site_type']?.toString();
    ownershipType = json['ownership_type']?.toString();
    facilityType = json['facility_type']?.toString();
    propertyType = json['property_type']?.toString();
    sqFt = json['sq_ft']?.toInt();
    cbsaClass = json['cbsa_class']?.toInt();
    storeClass = json['store_class']?.toInt();
    cbsaName = json['cbsa_name']?.toString();
    msaName = json['msa_name']?.toString();
    dmaName = json['dma_name']?.toString();
    realEstateManager = json['real_estate_manager']?.toString();
    constructionManager = json['construction_manager']?.toString();
    managerOfDev = json['manager_of_dev']?.toString();
    managerOfCons = json['manager_of_cons']?.toString();
    realEstateVp = json['real_estate_vp']?.toString();
    operationsManager = json['operations_manager']?.toString();
    operationsVp = json['operations_vp']?.toString();
    operationsRegion = json['operations_region']?.toString();
    fiscalPeriod = json['fiscal_period']?.toInt();
    fiscalYear = json['fiscal_year']?.toInt();
    fiscalQuarter = json['fiscal_quarter']?.toInt();
    timezone = json['timezone']?.toString();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    allColumns = json['all_columns']?.toString();
    analogCount = json['analog_count']?.toInt();
    readOnlyFlag = json['read_only_flag']?.toString();
    cExtAttr19 = json['c_ext_attr19']?.toString();
    mallName = json['mall_name']?.toString();
    rootFolderId = json["root_folder_id"]?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['site_id'] = siteId;
    data['site_name'] = siteName;
    data['site_number'] = siteNumber;
    data['store_id'] = storeId;
    data['store_number'] = storeNumber;
    data['target_id'] = targetId;
    data['proposed_start_date'] = proposedStartDate;
    data['status'] = status;
    data['status_desc'] = statusDesc;
    data['wf_status'] = wfStatus;
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['dma'] = dma;
    data['address'] = address;
    data['address3'] = address3;
    data['address2'] = address2;
    data['city'] = city;
    data['county'] = county;
    data['state'] = state;
    data['zip_code'] = zipCode;
    data['country'] = country;
    data['prototype'] = prototype;
    data['store_type'] = storeType;
    data['total_area'] = totalArea;
    data['project_cost'] = projectCost;
    data['category'] = category;
    data['site_type'] = siteType;
    data['ownership_type'] = ownershipType;
    data['facility_type'] = facilityType;
    data['property_type'] = propertyType;
    data['sq_ft'] = sqFt;
    data['cbsa_class'] = cbsaClass;
    data['store_class'] = storeClass;
    data['cbsa_name'] = cbsaName;
    data['msa_name'] = msaName;
    data['dma_name'] = dmaName;
    data['real_estate_manager'] = realEstateManager;
    data['construction_manager'] = constructionManager;
    data['manager_of_dev'] = managerOfDev;
    data['manager_of_cons'] = managerOfCons;
    data['real_estate_vp'] = realEstateVp;
    data['operations_manager'] = operationsManager;
    data['operations_vp'] = operationsVp;
    data['operations_region'] = operationsRegion;
    data['fiscal_period'] = fiscalPeriod;
    data['fiscal_year'] = fiscalYear;
    data['fiscal_quarter'] = fiscalQuarter;
    data['timezone'] = timezone;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['all_columns'] = allColumns;
    data['analog_count'] = analogCount;
    data['read_only_flag'] = readOnlyFlag;
    data['c_ext_attr19'] = cExtAttr19;
    data['mall_name'] = mallName;
    return data;
  }
}
