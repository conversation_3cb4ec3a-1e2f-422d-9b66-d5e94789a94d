class SiteSwot {
  int? siteId;
  String? siteName;
  String? dealSummary;
  String? strength;
  String? weakness;
  String? opportunity;
  String? threat;
  String? swotDescription;
  String? constructionAnalysis;

  SiteSwot({
    this.siteId,
    this.siteName,
    this.dealSummary,
    this.strength,
    this.weakness,
    this.opportunity,
    this.threat,
    this.swotDescription,
    this.constructionAnalysis,
  });
  SiteSwot.fromJson(Map<String, dynamic> json) {
    siteId = json['site_id']?.toInt();
    siteName = json['site_name']?.toString();
    constructionAnalysis = json['construction_analysis']?.toString();
    dealSummary = json['deal_summary']?.toString();
    strength = json['strength']?.toString();
    weakness = json['weakness']?.toString();
    opportunity = json['opportunity']?.toString();
    threat = json['threat']?.toString();
    swotDescription = json['swot_description']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['site_id'] = siteId;
    data['site_name'] = siteName;
    data['construction_analysis'] = constructionAnalysis;
    data['deal_summary'] = dealSummary;
    data['strength'] = strength;
    data['weakness'] = weakness;
    data['opportunity'] = opportunity;
    data['threat'] = threat;
    data['swot_description'] = swotDescription;
    return data;
  }
}
