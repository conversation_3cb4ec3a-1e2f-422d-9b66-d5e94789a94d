class MacRequestSearchRec {
  int? requestId;
  String? requestNumber;
  String? requestType;
  int? personId;
  String? name;
  String? status;
  int? fromSpaceId;
  String? fromSpace;
  int? toSpaceId;
  String? toSpace;
  String? requestedStartDate;
  String? approvedDate;
  String? scheduledDate;
  String? assginedTo;
  String? projectNumber;
  String? assignedToName;
  int? groupId;
  String? groupName;
  String? createdBy;
  String? updateOnly;
  String? updateOnlyFlag;
  String? fromArea;
  String? toArea;
  int? fromAreaId;
  int? toAreaId;
  String? movee;
  String? macGroupName;
  int? macGroupId;
  String? supervisor;
  String? notifyMovee;
  String? notifyRequestor;
  String? notifyApprover;
  String? notifyMovers;
  String? notifySecurity;
  String? notifyKeyContractor;
  String? notifyFurnitureVendor1;
  String? notifyFurnitureVendor2;
  String? notifyCoordinatormetroarea;
  String? notifyCoordinatorouterarea;
  String? notifyMediaVendor;
  String? notifyJanitorialVendor;
  String? notifyTelecom;
  String? notifyFacilities;
  String? notifySafety;
  String? notifySupplychain;
  String? notifyOthers;
  String? notifyOthersEmail;
  int? fromBuildingId;
  int? toBuildingId;
  String? requestedEndDate;
  String? creationDate;
  String? reservationStatus;
  String? creationDateOnly;
  String? dateCompleted;
  String? assetInstanceName;
  int? assetInstanceId;
  int? toFloorId;
  String? fromLocation;
  String? toLocation;
  String? assignedToUser;
  String? fromMailzoneNum;
  String? toMailzoneNum;
  String? clientRequestNumber;
  String? approvalStatus;
  String? fromBldgName;
  String? toBldgName;
  String? currentLocker;
  String? newLocker;
  String? notifyMacCoordinator;

  MacRequestSearchRec({
    this.requestId,
    this.requestNumber,
    this.requestType,
    this.personId,
    this.name,
    this.status,
    this.fromSpaceId,
    this.fromSpace,
    this.toSpaceId,
    this.toSpace,
    this.requestedStartDate,
    this.approvedDate,
    this.scheduledDate,
    this.assginedTo,
    this.projectNumber,
    this.assignedToName,
    this.groupId,
    this.groupName,
    this.createdBy,
    this.updateOnly,
    this.updateOnlyFlag,
    this.fromArea,
    this.toArea,
    this.fromAreaId,
    this.toAreaId,
    this.movee,
    this.macGroupName,
    this.macGroupId,
    this.supervisor,
    this.notifyMovee,
    this.notifyRequestor,
    this.notifyApprover,
    this.notifyMovers,
    this.notifySecurity,
    this.notifyKeyContractor,
    this.notifyFurnitureVendor1,
    this.notifyFurnitureVendor2,
    this.notifyCoordinatormetroarea,
    this.notifyCoordinatorouterarea,
    this.notifyMediaVendor,
    this.notifyJanitorialVendor,
    this.notifyTelecom,
    this.notifyFacilities,
    this.notifySafety,
    this.notifySupplychain,
    this.notifyOthers,
    this.notifyOthersEmail,
    this.fromBuildingId,
    this.toBuildingId,
    this.requestedEndDate,
    this.creationDate,
    this.reservationStatus,
    this.creationDateOnly,
    this.dateCompleted,
    this.assetInstanceName,
    this.assetInstanceId,
    this.toFloorId,
    this.fromLocation,
    this.toLocation,
    this.assignedToUser,
    this.fromMailzoneNum,
    this.toMailzoneNum,
    this.clientRequestNumber,
    this.approvalStatus,
    this.fromBldgName,
    this.toBldgName,
    this.currentLocker,
    this.newLocker,
    this.notifyMacCoordinator,
  });
  MacRequestSearchRec.fromJson(Map<String, dynamic> json) {
    requestId = json['request_id']?.toInt();
    requestNumber = json['request_number']?.toString();
    requestType = json['request_type']?.toString();
    personId = json['person_id']?.toInt();
    name = json['name']?.toString();
    status = json['status']?.toString();
    fromSpaceId = json['from_space_id']?.toInt();
    fromSpace = json['from_space']?.toString();
    toSpaceId = json['to_space_id']?.toInt();
    toSpace = json['to_space']?.toString();
    requestedStartDate = json['requested_start_date']?.toString();
    approvedDate = json['approved_date']?.toString();
    scheduledDate = json['scheduled_date']?.toString();
    assginedTo = json['assgined_to']?.toString();
    projectNumber = json['project_number']?.toString();
    assignedToName = json['assigned_to_name']?.toString();
    groupId = json['group_id']?.toInt();
    groupName = json['group_name']?.toString();
    createdBy = json['created_by']?.toString();
    updateOnly = json['update_only']?.toString();
    updateOnlyFlag = json['update_only_flag']?.toString();
    fromArea = json['from_area']?.toString();
    toArea = json['to_area']?.toString();
    fromAreaId = json['from_area_id']?.toInt();
    toAreaId = json['to_area_id']?.toInt();
    movee = json['movee']?.toString();
    macGroupName = json['mac_group_name']?.toString();
    macGroupId = json['mac_group_id']?.toInt();
    supervisor = json['supervisor']?.toString();
    notifyMovee = json['notify_movee']?.toString();
    notifyRequestor = json['notify_requestor']?.toString();
    notifyApprover = json['notify_approver']?.toString();
    notifyMovers = json['notify_movers']?.toString();
    notifySecurity = json['notify_security']?.toString();
    notifyKeyContractor = json['notify_key_contractor']?.toString();
    notifyFurnitureVendor1 = json['notify_furniture_vendor1']?.toString();
    notifyFurnitureVendor2 = json['notify_furniture_vendor2']?.toString();
    notifyCoordinatormetroarea = json['notify_coordinatormetroarea']?.toString();
    notifyCoordinatorouterarea = json['notify_coordinatorouterarea']?.toString();
    notifyMediaVendor = json['notify_media_vendor']?.toString();
    notifyJanitorialVendor = json['notify_janitorial_vendor']?.toString();
    notifyTelecom = json['notify_telecom']?.toString();
    notifyFacilities = json['notify_facilities']?.toString();
    notifySafety = json['notify_safety']?.toString();
    notifySupplychain = json['notify_supplychain']?.toString();
    notifyOthers = json['notify_others']?.toString();
    notifyOthersEmail = json['notify_others_email']?.toString();
    fromBuildingId = json['from_building_id']?.toInt();
    toBuildingId = json['to_building_id']?.toInt();
    requestedEndDate = json['requested_end_date']?.toString();
    creationDate = json['creation_date']?.toString();
    reservationStatus = json['reservation_status']?.toString();
    creationDateOnly = json['creation_date_only']?.toString();
    dateCompleted = json['date_completed']?.toString();
    assetInstanceName = json['asset_instance_name']?.toString();
    assetInstanceId = json['asset_instance_id']?.toInt();
    toFloorId = json['to_floor_id']?.toInt();
    fromLocation = json['from_location']?.toString();
    toLocation = json['to_location']?.toString();
    assignedToUser = json['assigned_to_user']?.toString();
    fromMailzoneNum = json['from_mailzone_num']?.toString();
    toMailzoneNum = json['to_mailzone_num']?.toString();
    clientRequestNumber = json['client_request_number']?.toString();
    approvalStatus = json['approval_status']?.toString();
    fromBldgName = json['from_bldg_name']?.toString();
    toBldgName = json['to_bldg_name']?.toString();
    currentLocker = json['current_locker']?.toString();
    newLocker = json['new_locker']?.toString();
    notifyMacCoordinator = json['notify_mac_coordinator']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['request_id'] = requestId;
    data['request_number'] = requestNumber;
    data['request_type'] = requestType;
    data['person_id'] = personId;
    data['name'] = name;
    data['status'] = status;
    data['from_space_id'] = fromSpaceId;
    data['from_space'] = fromSpace;
    data['to_space_id'] = toSpaceId;
    data['to_space'] = toSpace;
    data['requested_start_date'] = requestedStartDate;
    data['approved_date'] = approvedDate;
    data['scheduled_date'] = scheduledDate;
    data['assgined_to'] = assginedTo;
    data['project_number'] = projectNumber;
    data['assigned_to_name'] = assignedToName;
    data['group_id'] = groupId;
    data['group_name'] = groupName;
    data['created_by'] = createdBy;
    data['update_only'] = updateOnly;
    data['update_only_flag'] = updateOnlyFlag;
    data['from_area'] = fromArea;
    data['to_area'] = toArea;
    data['from_area_id'] = fromAreaId;
    data['to_area_id'] = toAreaId;
    data['movee'] = movee;
    data['mac_group_name'] = macGroupName;
    data['mac_group_id'] = macGroupId;
    data['supervisor'] = supervisor;
    data['notify_movee'] = notifyMovee;
    data['notify_requestor'] = notifyRequestor;
    data['notify_approver'] = notifyApprover;
    data['notify_movers'] = notifyMovers;
    data['notify_security'] = notifySecurity;
    data['notify_key_contractor'] = notifyKeyContractor;
    data['notify_furniture_vendor1'] = notifyFurnitureVendor1;
    data['notify_furniture_vendor2'] = notifyFurnitureVendor2;
    data['notify_coordinatormetroarea'] = notifyCoordinatormetroarea;
    data['notify_coordinatorouterarea'] = notifyCoordinatorouterarea;
    data['notify_media_vendor'] = notifyMediaVendor;
    data['notify_janitorial_vendor'] = notifyJanitorialVendor;
    data['notify_telecom'] = notifyTelecom;
    data['notify_facilities'] = notifyFacilities;
    data['notify_safety'] = notifySafety;
    data['notify_supplychain'] = notifySupplychain;
    data['notify_others'] = notifyOthers;
    data['notify_others_email'] = notifyOthersEmail;
    data['from_building_id'] = fromBuildingId;
    data['to_building_id'] = toBuildingId;
    data['requested_end_date'] = requestedEndDate;
    data['creation_date'] = creationDate;
    data['reservation_status'] = reservationStatus;
    data['creation_date_only'] = creationDateOnly;
    data['date_completed'] = dateCompleted;
    data['asset_instance_name'] = assetInstanceName;
    data['asset_instance_id'] = assetInstanceId;
    data['to_floor_id'] = toFloorId;
    data['from_location'] = fromLocation;
    data['to_location'] = toLocation;
    data['assigned_to_user'] = assignedToUser;
    data['from_mailzone_num'] = fromMailzoneNum;
    data['to_mailzone_num'] = toMailzoneNum;
    data['client_request_number'] = clientRequestNumber;
    data['approval_status'] = approvalStatus;
    data['from_bldg_name'] = fromBldgName;
    data['to_bldg_name'] = toBldgName;
    data['current_locker'] = currentLocker;
    data['new_locker'] = newLocker;
    data['notify_mac_coordinator'] = notifyMacCoordinator;
    return data;
  }
}
