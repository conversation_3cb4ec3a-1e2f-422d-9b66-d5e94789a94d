class RecentReservations {
  final int? propertyid;
  final int? buildingid;
  final int? floorid;
  final int? spaceid;
  final String? spaceDisplayName;
  final String? propertyname;
  final String? buildingname;
  final String? floorname;
  final String? spaceCategory;
  final String? spaceUse;  
  final int? picId;
  final int? noOfReviews;
  final String? allColumns;
  final List? spaceAttributes;

  RecentReservations({
    this.propertyid,
    this.buildingid,
    this.floorid,
    this.spaceid,
    this.spaceDisplayName,
    this.propertyname,
    this.buildingname,
    this.floorname,
    this.spaceCategory,
    this.spaceUse,
    this.picId,
    this.noOfReviews,
    this.allColumns,
    this.spaceAttributes
  });

  factory RecentReservations.fromJson(Map<String, dynamic> json) {

    return RecentReservations(
        propertyid: json['propertyid'] ?? 0,
        buildingid: json['buildingid'] ?? 0,
        floorid: json['floorid'] ?? 0,
        spaceid: json['spaceid'] ?? 0,
        spaceDisplayName: json['spaceDisplayName'] ?? "",
        propertyname: json['propertyname'] ?? "",
        buildingname: json['buildingname'] ?? "",
        floorname: json['floorname'] ?? "",
        spaceUse: json['spaceUse'] ?? "",
        picId: json['picId'] ?? "" as int?,
        noOfReviews: json['noOfReviews'] ?? 0,
        allColumns: json['allColumns'] ?? "",
        spaceAttributes: json['spaceAttributes']);
  }
}
