class SitePendingTask {
  String? name_;
  String? assignee_;
  String? id_;
  String? procInstId_;
  String? startTime_;
  String? initiator;
  String? entityid;
  String? entitytype;
  String? siteName;
  String? cExtAttr19;
  String? aqm;
  double? latitude;
  double? longitude;
  String? address;
  double? forecast;
  String? dealStatusDesc;

  SitePendingTask({
    this.name_,
    this.assignee_,
    this.id_,
    this.procInstId_,
    this.startTime_,
    this.initiator,
    this.entityid,
    this.entitytype,
    this.siteName,
    this.cExtAttr19,
    this.aqm,
    this.latitude,
    this.longitude,
    this.address,
    this.forecast,
    this.dealStatusDesc,
  });
  SitePendingTask.fromJson(Map<String, dynamic> json) {
    name_ = json['name_']?.toString();
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    initiator = json['initiator']?.toString();
    entityid = json['entityid']?.toString();
    entitytype = json['entitytype']?.toString();
    siteName = json['site_name']?.toString();
    cExtAttr19 = json['c_ext_attr19']?.toString();
    aqm = json['aqm']?.toString();
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    address = json['address']?.toString();
    forecast = json['forecast']?.toDouble();
    dealStatusDesc = json['deal_status_desc']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name_'] = name_;
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['proc_inst_id_'] = procInstId_;
    data['start_time_'] = startTime_;
    data['initiator'] = initiator;
    data['entityid'] = entityid;
    data['entitytype'] = entitytype;
    data['site_name'] = siteName;
    data['c_ext_attr19'] = cExtAttr19;
    data['aqm'] = aqm;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['address'] = address;
    data['forecast'] = forecast;
    data['deal_status_desc'] = dealStatusDesc;
    return data;
  }
}
