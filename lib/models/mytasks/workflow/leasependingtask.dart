class LeasePendingTask {
  String? id_;
  String? startTime_;
  String? name;
  int? leaseId;
  String? leaseNum;
  String? citystate;
  String? leaseCommencementDate;
  String? leaseTerminationDate;
  String? effectiveDate;
  String? leasePortfolioType;
  String? createdBy;
  String? creationDate;
  String? creationDateAttr;
  String? assignee;

  LeasePendingTask({
    this.id_,
    this.startTime_,
    this.name,
    this.leaseId,
    this.leaseNum,
    this.citystate,
    this.leaseCommencementDate,
    this.leaseTerminationDate,
    this.effectiveDate,
    this.leasePortfolioType,
    this.createdBy,
    this.creationDate,
    this.creationDateAttr,
    this.assignee,
  });
  LeasePendingTask.fromJson(Map<String, dynamic> json) {
    id_ = json['id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    name = json['name']?.toString();
    leaseId = json['lease_id']?.toInt();
    leaseNum = json['lease_num']?.toString();
    citystate = json['citystate']?.toString();
    leaseCommencementDate = json['lease_commencement_date']?.toString();
    leaseTerminationDate = json['lease_termination_date']?.toString();
    effectiveDate = json['effective_date']?.toString();
    leasePortfolioType = json['lease_portfolio_type']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    creationDateAttr = json['creation_date_attr']?.toString();
    assignee = json['assignee_']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id_'] = id_;
    data['start_time_'] = startTime_;
    data['name'] = name;
    data['lease_id'] = leaseId;
    data['lease_num'] = leaseNum;
    data['citystate'] = citystate;
    data['lease_commencement_date'] = leaseCommencementDate;
    data['lease_termination_date'] = leaseTerminationDate;
    data['effective_date'] = effectiveDate;
    data['lease_portfolio_type'] = leasePortfolioType;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['creation_date_attr'] = creationDateAttr;
    data['assignee_'] = assignee;
    return data;
  }
}
