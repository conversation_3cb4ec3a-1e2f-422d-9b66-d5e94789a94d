class LeaseRecurringCostTask {
  String? assignee_;
  String? id_;
  String? procInstId_;
  String? startTime_;
  String? initiator;
  String? entityid;
  String? entitytype;
  String? name;
  int? leaseId;
  String? leaseNum;
  String? citystate;
  String? paymentType;
  String? frequency;
  String? startDate;
  String? endDate;
  double? annualAmout;
  double? amout;
  double? totalObligation;
  String? createdBy;
  String? creationDate;

  LeaseRecurringCostTask({
    this.assignee_,
    this.id_,
    this.procInstId_,
    this.startTime_,
    this.initiator,
    this.entityid,
    this.entitytype,
    this.name,
    this.leaseId,
    this.leaseNum,
    this.citystate,
    this.paymentType,
    this.frequency,
    this.startDate,
    this.endDate,
    this.annualAmout,
    this.amout,
    this.totalObligation,
    this.createdBy,
    this.creationDate,
  });
  LeaseRecurringCostTask.fromJson(Map<String, dynamic> json) {
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    initiator = json['initiator']?.toString();
    entityid = json['entityid']?.toString();
    entitytype = json['entitytype']?.toString();
    name = json['name']?.toString();
    leaseId = json['lease_id']?.toInt();
    leaseNum = json['lease_num']?.toString();
    citystate = json['citystate']?.toString();
    paymentType = json['payment_type']?.toString();
    frequency = json['frequency']?.toString();
    startDate = json['start_date']?.toString();
    endDate = json['end_date']?.toString();
    annualAmout = json['annual_amout']?.toDouble();
    amout = json['amout']?.toDouble();
    totalObligation = json['total_obligation']?.toDouble();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['proc_inst_id_'] = procInstId_;
    data['start_time_'] = startTime_;
    data['initiator'] = initiator;
    data['entityid'] = entityid;
    data['entitytype'] = entitytype;
    data['name'] = name;
    data['lease_id'] = leaseId;
    data['lease_num'] = leaseNum;
    data['citystate'] = citystate;
    data['payment_type'] = paymentType;
    data['frequency'] = frequency;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['annual_amout'] = annualAmout;
    data['amout'] = amout;
    data['total_obligation'] = totalObligation;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    return data;
  }
}
