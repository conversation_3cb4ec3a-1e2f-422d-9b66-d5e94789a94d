class UserPendingTask {
  String? entitylabel;
  int? entitycount;
  String? entityType;
  String? action;
  int? total;
  int? count;

  UserPendingTask({
    this.entitylabel,
    this.entitycount,
    this.entityType,
    this.action,
    this.total,
    this.count,
  });
  UserPendingTask.fromJson(Map<String, dynamic> json) {
    entitylabel = json['entitylabel']?.toString();
    entitycount = json['entitycount']?.toInt();
    entityType = json['entity_type']?.toString();
    action = json['action']?.toString();
    total = json['total']?.toInt();
    count = json['count']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['entitylabel'] = entitylabel;
    data['entitycount'] = entitycount;
    data['entity_type'] = entityType;
    data['action'] = action;
    data['total'] = total;
    data['count'] = count;
    return data;
  }
}
