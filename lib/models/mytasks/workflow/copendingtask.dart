class CoPendingTask {
  String? name_;
  String? assignee_;
  String? id_;
  String? procInstId_;
  String? startTime_;
  String? initiator;
  String? entityid;
  String? entitytype;
  int? projectId;
  int? chgOrderId;
  String? poNumber;
  String? chgOrderNumber;
  String? chgReason;
  String? status;
  String? chgOrderDate;
  String? approvedBy;
  String? approvedDate;
  String? currency;
  String? supplierName;
  String? supplierSiteName;
  double? amount;
  String? projectName;
  String? country;

  CoPendingTask({
    this.name_,
    this.assignee_,
    this.id_,
    this.procInstId_,
    this.startTime_,
    this.initiator,
    this.entityid,
    this.entitytype,
    this.projectId,
    this.chgOrderId,
    this.poNumber,
    this.chgOrderNumber,
    this.chgReason,
    this.status,
    this.chgOrderDate,
    this.approvedBy,
    this.approvedDate,
    this.currency,
    this.supplierName,
    this.supplierSiteName,
    this.amount,
    this.projectName,
    this.country,
  });
  CoPendingTask.fromJson(Map<String, dynamic> json) {
    name_ = json['name_']?.toString();
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    initiator = json['initiator']?.toString();
    entityid = json['entityid']?.toString();
    entitytype = json['entitytype']?.toString();
    projectId = json['project_id']?.toInt();
    chgOrderId = json['chg_order_id']?.toInt();
    poNumber = json['po_number']?.toString();
    chgOrderNumber = json['chg_order_number']?.toString();
    chgReason = json['chg_reason']?.toString();
    status = json['status']?.toString();
    chgOrderDate = json['chg_order_date']?.toString();
    approvedBy = json['approved_by']?.toString();
    approvedDate = json['approved_date']?.toString();
    currency = json['currency']?.toString();
    supplierName = json['supplier_name']?.toString();
    supplierSiteName = json['supplier_site_name']?.toString();
    amount = json['amount']?.toDouble();
    projectName = json['project_name']?.toString();
    country = json['country']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name_'] = name_;
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['proc_inst_id_'] = procInstId_;
    data['start_time_'] = startTime_;
    data['initiator'] = initiator;
    data['entityid'] = entityid;
    data['entitytype'] = entitytype;
    data['project_id'] = projectId;
    data['chg_order_id'] = chgOrderId;
    data['po_number'] = poNumber;
    data['chg_order_number'] = chgOrderNumber;
    data['chg_reason'] = chgReason;
    data['status'] = status;
    data['chg_order_date'] = chgOrderDate;
    data['approved_by'] = approvedBy;
    data['approved_date'] = approvedDate;
    data['currency'] = currency;
    data['supplier_name'] = supplierName;
    data['supplier_site_name'] = supplierSiteName;
    data['amount'] = amount;
    data['project_name'] = projectName;
    data['country'] = country;
    return data;
  }
}
