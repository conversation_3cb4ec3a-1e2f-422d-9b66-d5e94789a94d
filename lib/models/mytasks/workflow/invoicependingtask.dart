class InvoicePendingTask {
  String? name_;
  String? assignee_;
  String? id_;
  String? procInstId_;
  String? startTime_;
  String? initiator;
  String? entityid;
  String? entitytype;
  int? projectId;
  String? invoiceNumber;
  String? poNumber;
  String? status;
  String? approvedBy;
  int? invoiceId;
  String? currency;
  String? supplierName;
  double? amount;
  double? netAmount;
  String? projectName;
  String? country;

  InvoicePendingTask({
    this.name_,
    this.assignee_,
    this.id_,
    this.procInstId_,
    this.startTime_,
    this.initiator,
    this.entityid,
    this.entitytype,
    this.projectId,
    this.invoiceNumber,
    this.poNumber,
    this.status,
    this.approvedBy,
    this.invoiceId,
    this.currency,
    this.supplierName,
    this.amount,
    this.netAmount,
    this.projectName,
    this.country,
  });
  InvoicePendingTask.fromJson(Map<String, dynamic> json) {
    name_ = json['name_']?.toString();
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    initiator = json['initiator']?.toString();
    entityid = json['entityid']?.toString();
    entitytype = json['entitytype']?.toString();
    projectId = json['project_id']?.toInt();
    invoiceNumber = json['invoice_number']?.toString();
    poNumber = json['po_number']?.toString();
    status = json['status']?.toString();
    approvedBy = json['approved_by']?.toString();
    invoiceId = json['invoice_id']?.toInt();
    currency = json['currency']?.toString();
    supplierName = json['supplier_name']?.toString();
    amount = json['amount']?.toDouble();
    netAmount = json['net_amount']?.toDouble();
    projectName = json['project_name']?.toString();
    country = json['country']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name_'] = name_;
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['proc_inst_id_'] = procInstId_;
    data['start_time_'] = startTime_;
    data['initiator'] = initiator;
    data['entityid'] = entityid;
    data['entitytype'] = entitytype;
    data['project_id'] = projectId;
    data['invoice_number'] = invoiceNumber;
    data['po_number'] = poNumber;
    data['status'] = status;
    data['approved_by'] = approvedBy;
    data['invoice_id'] = invoiceId;
    data['currency'] = currency;
    data['supplier_name'] = supplierName;
    data['amount'] = amount;
    data['net_amount'] = netAmount;
    data['project_name'] = projectName;
    data['country'] = country;
    return data;
  }
}
