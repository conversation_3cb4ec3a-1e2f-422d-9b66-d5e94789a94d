class Wfhistory {
  int? workflowId;
  String? entityType;
  int? entityId;
  String? userName;
  String? status;
  String? notificationDate;
  String? comments;
  String? action;
  String? entityStatus;
  String? processInstanceId;
  String? role;
  int? approvalLimit;
  String? name;
  String? stepName;
  String? stepStatus;

  Wfhistory({
    this.workflowId,
    this.entityType,
    this.entityId,
    this.userName,
    this.status,
    this.notificationDate,
    this.comments,
    this.action,
    this.entityStatus,
    this.processInstanceId,
    this.role,
    this.approvalLimit,
    this.name,
    this.stepName,
    this.stepStatus,
  });
  Wfhistory.fromJson(Map<String, dynamic> json) {
    workflowId = json['workflow_id']?.toInt();
    entityType = json['entity_type']?.toString() ?? '';
    entityId = json['entity_id']?.toInt() ?? 0;
    userName = json['user_name']?.toString() ?? '';
    status = json['status']?.toString() ?? '';
    notificationDate = json['notification_date']?.toString() ?? '';
    comments = json['comments']?.toString() ?? '';
    action = json['action']?.toString() ?? '';
    entityStatus = json['entity_status']?.toString() ?? '';
    processInstanceId = json['process_instance_id']?.toString() ?? '';
    role = json['role']?.toString() ?? '';
    approvalLimit = json['approval_limit']?.toInt() ?? 0;
    name = json['name']?.toString() ?? '';
    stepName = json['step_name']?.toString() ?? '';
    stepStatus = json['step_status']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['workflow_id'] = workflowId;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['user_name'] = userName;
    data['status'] = status;
    data['notification_date'] = notificationDate;
    data['comments'] = comments;
    data['action'] = action;
    data['entity_status'] = entityStatus;
    data['process_instance_id'] = processInstanceId;
    data['role'] = role;
    data['approval_limit'] = approvalLimit;
    data['name'] = name;
    data['step_name'] = stepName;
    data['step_status'] = stepStatus;
    return data;
  }
}
