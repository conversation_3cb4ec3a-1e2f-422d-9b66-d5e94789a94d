class LeaseOtpPendingTask {
  String? leaseName;
  int? leaseId;
  String? leaseNum;
  String? citystate;
  String? paymentType;
  String? paymentTypeDesc;
  double? grossAmount;
  String? effectiveDate;
  String? initiator;
  int? procInstId_;
  int? id_;
  String? assignee_;
  int? entityId;
  String? creationDate;
  String? entityType;
  String? creationDateAttr;
  String? createdBy;
  String? starttime;
  String? vendor;

  LeaseOtpPendingTask({
    this.leaseName,
    this.leaseId,
    this.leaseNum,
    this.citystate,
    this.paymentType,
    this.grossAmount,
    this.effectiveDate,
    this.initiator,
    this.procInstId_,
    this.id_,
    this.assignee_,
    this.entityId,
    this.creationDate,
    this.entityType,
    this.creationDateAttr,
    this.createdBy,
    this.starttime,
    this.paymentTypeDesc,
    this.vendor,
  });
  LeaseOtpPendingTask.fromJson(Map<String, dynamic> json) {
    leaseName = json['lease_name']?.toString() ?? '';
    leaseId = json['lease_id']?.toInt();
    leaseNum = json['lease_num']?.toString() ?? '';
    citystate = json['citystate']?.toString() ?? '';
    paymentType = json['payment_type']?.toString() ?? '';
    paymentTypeDesc = json['payment_type_desc']?.toString() ?? '';
    grossAmount = json['gross_amount']?.toDouble();
    effectiveDate = json['effective_date']?.toString() ?? '';
    initiator = json['initiator']?.toString() ?? '';
    procInstId_ = json['proc_inst_id_']?.toInt();
    id_ = json['id_']?.toInt();
    assignee_ = json['assignee_']?.toString() ?? '';
    entityId = json['entity_id']?.toInt();
    creationDate = json['creation_date']?.toString() ?? '';
    entityType = json['entity_type']?.toString() ?? '';
    creationDateAttr = json['creation_date_attr']?.toString() ?? '';
    createdBy = json['created_by']?.toString() ?? '';
    starttime = json['starttime']?.toString() ?? '';
    vendor = json['vendor']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['lease_name'] = leaseName;
    data['lease_id'] = leaseId;
    data['lease_num'] = leaseNum;
    data['citystate'] = citystate;
    data['payment_type'] = paymentType;
    data['payment_type_desc'] = paymentTypeDesc;
    data['gross_amount'] = grossAmount;
    data['effective_date'] = effectiveDate;
    data['initiator'] = initiator;
    data['proc_inst_id_'] = procInstId_;
    data['id_'] = id_;
    data['assignee_'] = assignee_;
    data['entity_id'] = entityId;
    data['creation_date'] = creationDate;
    data['entity_type'] = entityType;
    data['creation_date_attr'] = creationDateAttr;
    data['created_by'] = createdBy;
    data['starttime'] = starttime;
    data['vendor'] = starttime;
    return data;
  }
}
