class BudgetPendingTask {
  String? name_;
  String? assignee_;
  String? id_;
  String? procInstId_;
  String? startTime_;
  String? initiator;
  String? baseid;
  String? entitytype;
  String? projectName;
  int? projectId;
  String? entityType;
  int? entityId;
  String? currency;
  String? realEstateManager;
  String? constructionManager;
  double? forecast;
  double? approvedbudget;
  double? totalapprovedbudget;

  BudgetPendingTask({
    this.name_,
    this.assignee_,
    this.id_,
    this.procInstId_,
    this.startTime_,
    this.initiator,
    this.baseid,
    this.entitytype,
    this.projectName,
    this.projectId,
    this.entityType,
    this.entityId,
    this.currency,
    this.realEstateManager,
    this.constructionManager,
    this.forecast,
    this.approvedbudget,
    this.totalapprovedbudget,
  });
  BudgetPendingTask.fromJson(Map<String, dynamic> json) {
    name_ = json['name_']?.toString();
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    initiator = json['initiator']?.toString();
    baseid = json['entityid']?.toString();
    entitytype = json['entitytype']?.toString();
    projectName = json['project_name']?.toString();
    projectId = json['project_id']?.toInt();
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    currency = json['currency']?.toString();
    realEstateManager = json['real_estate_manager']?.toString();
    constructionManager = json['construction_manager']?.toString();
    forecast = json['forecast']?.toDouble();
    approvedbudget = json['approvedbudget']?.toDouble();
    totalapprovedbudget = json['totalapprovedbudget']?.toDouble();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name_'] = name_;
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['proc_inst_id_'] = procInstId_;
    data['start_time_'] = startTime_;
    data['initiator'] = initiator;
    data['entityid'] = baseid;
    data['entitytype'] = entitytype;
    data['project_name'] = projectName;
    data['project_id'] = projectId;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['currency'] = currency;
    data['real_estate_manager'] = realEstateManager;
    data['construction_manager'] = constructionManager;
    data['forecast'] = forecast;
    data['approvedbudget'] = approvedbudget;
    data['totalapprovedbudget'] = totalapprovedbudget;
    return data;
  }
}
