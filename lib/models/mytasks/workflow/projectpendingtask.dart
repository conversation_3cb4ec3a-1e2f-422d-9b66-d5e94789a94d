class ProjectPendingTask {
  String? name_;
  String? assignee_;
  String? id_;
  String? procInstId_;
  String? startTime_;
  String? initiator;
  String? entityid;
  String? entitytype;
  String? projectName;
  String? entityType;
  int? entityId;
  String? status;
  String? currency;
  int? forecast;
  int? approvedbudget;
  int? projectId;

  ProjectPendingTask({
    this.name_,
    this.assignee_,
    this.id_,
    this.procInstId_,
    this.startTime_,
    this.initiator,
    this.entityid,
    this.entitytype,
    this.projectName,
    this.entityType,
    this.entityId,
    this.status,
    this.currency,
    this.forecast,
    this.approvedbudget,
    this.projectId,
  });
  ProjectPendingTask.fromJson(Map<String, dynamic> json) {
    name_ = json['name_']?.toString();
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    initiator = json['initiator']?.toString();
    entityid = json['entityid']?.toString();
    entitytype = json['entitytype']?.toString();
    projectName = json['project_name']?.toString();
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    status = json['status']?.toString();
    currency = json['currency']?.toString();
    forecast = json['forecast']?.toInt();
    approvedbudget = json['approvedbudget']?.toInt();
    projectId = json['proj_id']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name_'] = name_;
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['proc_inst_id_'] = procInstId_;
    data['start_time_'] = startTime_;
    data['initiator'] = initiator;
    data['entityid'] = entityid;
    data['entitytype'] = entitytype;
    data['project_name'] = projectName;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['status'] = status;
    data['currency'] = currency;
    data['forecast'] = forecast;
    data['approvedbudget'] = approvedbudget;
    data['proj_id'] = projectId;
    return data;
  }
}
