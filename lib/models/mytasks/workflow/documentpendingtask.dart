class DocumentPendingTask {
  String? name_;
  String? assignee_;
  String? id_;
  String? startTime_;
  String? procInstId_;
  String? entitytype;
  int? entityid;
  int? fileId;
  int? folderId;
  String? folderName;
  String? fileName;
  String? description;
  int? entityId;
  String? entityType;
  String? createdBy;
  String? creationDate;
  String? creationDateAttr;
  String? docTypeDesc;
  String? site_name;
  String? project_name;
  String? lease_name;
  String? store_name;

  DocumentPendingTask({
    this.docTypeDesc,
    this.site_name,
    this.project_name,
    this.lease_name,
    this.store_name,
    this.name_,
    this.assignee_,
    this.id_,
    this.startTime_,
    this.procInstId_,
    this.entitytype,
    this.entityid,
    this.fileId,
    this.folderId,
    this.folderName,
    this.fileName,
    this.description,
    this.entityId,
    this.entityType,
    this.createdBy,
    this.creationDate,
    this.creationDateAttr,
  });
  DocumentPendingTask.fromJson(Map<String, dynamic> json) {
    name_ = json['name_']?.toString();
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    entitytype = json['entitytype']?.toString();
    entityid = json['entityid']?.toInt();
    fileId = json['file_id']?.toInt();
    folderId = json['folder_id']?.toInt();
    folderName = json['folder_name']?.toString();
    fileName = json['file_name']?.toString();
    description = json['description']?.toString();
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    creationDateAttr = json['creation_date_attr']?.toString();
    docTypeDesc = json['doc_type_desc']?.toString();
    project_name = json['project_name']?.toString();
    site_name = json['site_name']?.toString();
    store_name = json['store_name']?.toString();
    lease_name = json['lease_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['doc_type_desc'] = docTypeDesc;
    data['project_name'] = project_name;
    data['site_name'] = site_name;
    data['store_name'] = store_name;
    data['lease_name'] = lease_name;
    data['name_'] = name_;
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['start_time_'] = startTime_;
    data['proc_inst_id_'] = procInstId_;
    data['entitytype'] = entitytype;
    data['entityid'] = entityid;
    data['file_id'] = fileId;
    data['folder_id'] = folderId;
    data['folder_name'] = folderName;
    data['file_name'] = fileName;
    data['description'] = description;
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['creation_date_attr'] = creationDateAttr;
    return data;
  }
}
