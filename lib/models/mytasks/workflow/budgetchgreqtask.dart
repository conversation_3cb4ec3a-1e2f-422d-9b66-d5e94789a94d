class BudgetChgReqTaskData {
  int? wfInstHeaderId;
  String? assignee_;
  int? taskid;
  String? createdBy;
  String? creationDate;
  String? creationDateAttr;
  String? projectName;
  double? releaseAmount;
  int? projectId;
  int? budgetChgId;
  String? budgetChgNumber;
  String? chgReason;
  String? chgOrderDate;
  String? chgReasonDesc;
  String? chgReasonDescription;

  BudgetChgReqTaskData({
    this.wfInstHeaderId,
    this.assignee_,
    this.taskid,
    this.createdBy,
    this.creationDate,
    this.creationDateAttr,
    this.projectName,
    this.releaseAmount,
    this.projectId,
    this.budgetChgId,
    this.budgetChgNumber,
    this.chgReason,
    this.chgOrderDate,
    this.chgReasonDesc,
    this.chgReasonDescription,
  });
  BudgetChgReqTaskData.fromJson(Map<String, dynamic> json) {
    wfInstHeaderId = json['wf_inst_header_id']?.toInt();
    assignee_ = json['assignee_']?.toString();
    taskid = json['taskid']?.toInt();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    creationDateAttr = json['creation_date_attr']?.toString();
    projectName = json['project_name']?.toString();
    releaseAmount = json['release_amount']?.toDouble();
    projectId = json['project_id']?.toInt();
    budgetChgId = json['budget_chg_id']?.toInt();
    budgetChgNumber = json['budget_chg_number']?.toString();
    chgReason = json['chg_reason']?.toString();
    chgOrderDate = json['chg_order_date']?.toString();
    chgReasonDesc = json['chg_reason_desc']?.toString();
    chgReasonDescription = json['chg_reason_description']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['wf_inst_header_id'] = wfInstHeaderId;
    data['assignee_'] = assignee_;
    data['taskid'] = taskid;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['creation_date_attr'] = creationDateAttr;
    data['project_name'] = projectName;
    data['release_amount'] = releaseAmount;
    data['project_id'] = projectId;
    data['budget_chg_id'] = budgetChgId;
    data['budget_chg_number'] = budgetChgNumber;
    data['chg_reason'] = chgReason;
    data['chg_order_date'] = chgOrderDate;
    data['chg_reason_desc'] = chgReasonDesc;
    data['chg_reason_description'] = chgReasonDescription;
    return data;
  }
}
