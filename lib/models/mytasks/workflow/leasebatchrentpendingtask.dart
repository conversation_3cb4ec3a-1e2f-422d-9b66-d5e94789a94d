class LeaseBatchRentPendingTask {
  int? wfInstHeaderId;
  String? assignee;
  int? taskid;
  String? createdBy;
  String? creationDate;
  String? creationDateAttr;
  String? batchNumber;
  String? leaseFinancialsGroup;
  double? batchTotal;
  int? leasePaymentBatchId;
  String? batchType;
  String? statusDesc;
  String? paymentTypeDesc;
  String? appliesFromDate;
  String? appliesToDate;

  LeaseBatchRentPendingTask({
    this.wfInstHeaderId,
    this.assignee,
    this.taskid,
    this.createdBy,
    this.creationDate,
    this.creationDateAttr,
    this.batchNumber,
    this.leaseFinancialsGroup,
    this.batchTotal,
    this.leasePaymentBatchId,
    this.batchType,
    this.appliesFromDate,
    this.appliesToDate,
    this.paymentTypeDesc,
    this.statusDesc,
  });
  LeaseBatchRentPendingTask.fromJson(Map<String, dynamic> json) {
    wfInstHeaderId = json['wf_inst_header_id']?.toInt();
    assignee = json['assignee']?.toString() ?? '';
    taskid = json['taskid']?.toInt();
    createdBy = json['created_by']?.toString() ?? '';
    creationDate = json['creation_date']?.toString() ?? '';
    creationDateAttr = json['creation_date_attr']?.toString() ?? '';
    batchNumber = json['batch_number']?.toString() ?? '';
    leaseFinancialsGroup = json['lease_financials_group']?.toString() ?? '';
    batchTotal = json['batch_total']?.toDouble();
    leasePaymentBatchId = json['lease_payment_batch_id']?.toInt();
    batchType = json['batch_type']?.toString() ?? '';
    statusDesc = json['status_desc']?.toString() ?? '';
    paymentTypeDesc = json['payment_type_desc']?.toString() ?? '';
    appliesFromDate = json['applies_from_date']?.toString() ?? '';
    appliesToDate = json['applies_to_date']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['wf_inst_header_id'] = wfInstHeaderId;
    data['assignee'] = assignee;
    data['taskid'] = taskid;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['creation_date_attr'] = creationDateAttr;
    data['batch_number'] = batchNumber;
    data['lease_financials_group'] = leaseFinancialsGroup;
    data['batch_total'] = batchTotal;
    data['lease_payment_batch_id'] = leasePaymentBatchId;
    data['batch_type'] = batchType;
    data['status_desc'] = statusDesc;
    data['payment_type_desc'] = paymentTypeDesc;
    data['applies_from_date'] = appliesFromDate;
    data['applies_to_date'] = appliesToDate;
    return data;
  }
}
