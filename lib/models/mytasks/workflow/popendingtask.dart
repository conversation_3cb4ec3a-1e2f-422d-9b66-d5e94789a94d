class PoPendingTask {
  String? name_;
  String? assignee_;
  String? id_;
  String? procInstId_;
  String? startTime_;
  String? initiator;
  String? entityid;
  String? entitytype;
  int? projectId;
  int? poId;
  String? poNumber;
  String? status;
  String? issueDate;
  String? patmentTerms;
  String? dueDate;
  String? currency;
  String? approvedBy;
  String? issuedBy;
  String? deliveryDate;
  String? supplierName;
  String? supplierSite;
  String? legalCompany;
  double? amount;
  double? sumTotalAmtWithTax;
  String? projectName;
  String? country;

  PoPendingTask({
    this.name_,
    this.assignee_,
    this.id_,
    this.procInstId_,
    this.startTime_,
    this.initiator,
    this.entityid,
    this.entitytype,
    this.projectId,
    this.poId,
    this.poNumber,
    this.status,
    this.issueDate,
    this.patmentTerms,
    this.dueDate,
    this.currency,
    this.approvedBy,
    this.issuedBy,
    this.deliveryDate,
    this.supplierName,
    this.supplierSite,
    this.legalCompany,
    this.amount,
    this.sumTotalAmtWithTax,
    this.projectName,
    this.country,
  });
  PoPendingTask.fromJson(Map<String, dynamic> json) {
    name_ = json['name_']?.toString();
    assignee_ = json['assignee_']?.toString();
    id_ = json['id_']?.toString();
    procInstId_ = json['proc_inst_id_']?.toString();
    startTime_ = json['start_time_']?.toString();
    initiator = json['initiator']?.toString();
    entityid = json['entityid']?.toString();
    entitytype = json['entitytype']?.toString();
    projectId = json['project_id']?.toInt();
    poId = json['po_id']?.toInt();
    poNumber = json['po_number']?.toString();
    status = json['status']?.toString();
    issueDate = json['issue_date']?.toString();
    patmentTerms = json['patment_terms']?.toString();
    dueDate = json['due_date']?.toString();
    currency = json['currency']?.toString();
    approvedBy = json['approved_by']?.toString();
    issuedBy = json['issued_by']?.toString();
    deliveryDate = json['delivery_date']?.toString();
    supplierName = json['supplier_name']?.toString();
    supplierSite = json['supplier_site']?.toString();
    legalCompany = json['legal_company']?.toString();
    amount = json['amount']?.toDouble();
    sumTotalAmtWithTax = json['sum_total_amt_with_tax']?.toDouble();
    projectName = json['project_name']?.toString();
    country = json['country']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name_'] = name_;
    data['assignee_'] = assignee_;
    data['id_'] = id_;
    data['proc_inst_id_'] = procInstId_;
    data['start_time_'] = startTime_;
    data['initiator'] = initiator;
    data['entityid'] = entityid;
    data['entitytype'] = entitytype;
    data['project_id'] = projectId;
    data['po_id'] = poId;
    data['po_number'] = poNumber;
    data['status'] = status;
    data['issue_date'] = issueDate;
    data['patment_terms'] = patmentTerms;
    data['due_date'] = dueDate;
    data['currency'] = currency;
    data['approved_by'] = approvedBy;
    data['issued_by'] = issuedBy;
    data['delivery_date'] = deliveryDate;
    data['supplier_name'] = supplierName;
    data['supplier_site'] = supplierSite;
    data['legal_company'] = legalCompany;
    data['amount'] = amount;
    data['sum_total_amt_with_tax'] = sumTotalAmtWithTax;
    data['project_name'] = projectName;
    data['country'] = country;
    return data;
  }
}
