class SupplierData {
  int? supplierId;
  String? supplierNumber;
  String? supplierName;
  String? description;
  String? address1;
  String? address2;
  String? city;
  String? state;
  String? zip;
  String? country;
  String? status;
  String? statusDesc;
  int? orgId;
  int? clientId;
  int? brandId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? email;
  String? contactPerson;
  String? phoneNumber;
  String? countryName;
  String? cExtAttr01;
  String? cExtAttr02;
  String? cExtAttr03;
  String? cExtAttr04;
  String? cExtAttr05;
  String? cExtLov1;
  String? cExtLov2;
  String? cExtLov3;
  String? cExtLov4;
  String? cExtLov5;
  double? nExtAttr01;
  double? nExtAttr02;
  double? nExtAttr03;
  double? nExtAttr04;
  double? nExtAttr05;
  String? address3;
  String? supplierType;
  String? paymentTerm;
  String? contactCellPhone;
  String? trades;
  String? fax;
  String? taxId;
  String? partner;
  String? prequalified;
  String? startDate;
  String? endDate;
  String? isEditable;
  String? paymentMethod;
  String? bankAccount;
  String? bankRouting;
  String? recordType;
  String? legalEntityType;
  String? einSsn;
  String? supplierFirstName;
  String? supplierLastName;

  SupplierData({
    this.supplierId,
    this.supplierNumber,
    this.supplierName,
    this.description,
    this.address1,
    this.address2,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.status,
    this.statusDesc,
    this.orgId,
    this.clientId,
    this.brandId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.email,
    this.contactPerson,
    this.phoneNumber,
    this.countryName,
    this.cExtAttr01,
    this.cExtAttr02,
    this.cExtAttr03,
    this.cExtAttr04,
    this.cExtAttr05,
    this.cExtLov1,
    this.cExtLov2,
    this.cExtLov3,
    this.cExtLov4,
    this.cExtLov5,
    this.nExtAttr01,
    this.nExtAttr02,
    this.nExtAttr03,
    this.nExtAttr04,
    this.nExtAttr05,
    this.address3,
    this.supplierType,
    this.paymentTerm,
    this.contactCellPhone,
    this.trades,
    this.fax,
    this.taxId,
    this.partner,
    this.prequalified,
    this.startDate,
    this.endDate,
    this.isEditable,
    this.paymentMethod,
    this.bankAccount,
    this.bankRouting,
    this.einSsn,
    this.legalEntityType,
    this.recordType,
    this.supplierFirstName,
    this.supplierLastName,
  });
  SupplierData.fromJson(Map<String, dynamic> json) {
    supplierId = json['supplier_id']?.toInt();
    supplierNumber = json['supplier_number']?.toString();
    supplierName = json['supplier_name']?.toString();
    description = json['description']?.toString();
    address1 = json['address1']?.toString();
    address2 = json['address2']?.toString();
    city = json['city']?.toString();
    state = json['state']?.toString();
    zip = json['zip']?.toString();
    country = json['country']?.toString();
    status = json['status']?.toString();
    statusDesc = json['status_desc']?.toString();
    orgId = json['org_id']?.toInt();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    email = json['email']?.toString();
    contactPerson = json['contact_person']?.toString();
    phoneNumber = json['phone_number']?.toString();
    countryName = json['country_name']?.toString();
    cExtAttr01 = json['c_ext_attr01']?.toString();
    cExtAttr02 = json['c_ext_attr02']?.toString();
    cExtAttr03 = json['c_ext_attr03']?.toString();
    cExtAttr04 = json['c_ext_attr04']?.toString();
    cExtAttr05 = json['c_ext_attr05']?.toString();
    cExtLov1 = json['c_ext_lov1']?.toString();
    cExtLov2 = json['c_ext_lov2']?.toString();
    cExtLov3 = json['c_ext_lov3']?.toString();
    cExtLov4 = json['c_ext_lov4']?.toString();
    cExtLov5 = json['c_ext_lov5']?.toString();
    nExtAttr01 = json['n_ext_attr01']?.toDouble();
    nExtAttr02 = json['n_ext_attr02']?.toDouble();
    nExtAttr03 = json['n_ext_attr03']?.toDouble();
    nExtAttr04 = json['n_ext_attr04']?.toDouble();
    nExtAttr05 = json['n_ext_attr05']?.toDouble();
    address3 = json['address3']?.toString();
    supplierType = json['supplier_type']?.toString();
    paymentTerm = json['payment_term']?.toString();
    contactCellPhone = json['contact_cell_phone']?.toString();
    trades = json['trades']?.toString();
    fax = json['fax']?.toString();
    taxId = json['tax_id']?.toString();
    partner = json['partner']?.toString();
    prequalified = json['prequalified']?.toString();
    startDate = json['start_date']?.toString();
    endDate = json['end_date']?.toString();
    isEditable = json['is_editable']?.toString();
    paymentMethod = json['payment_method']?.toString();
    bankAccount = json['bank_account']?.toString();
    bankRouting = json['bank_routing']?.toString();

    recordType = json['record_type']?.toString();
    legalEntityType = json['legal_entity_type']?.toString();
    einSsn = json['ein_ssn']?.toString();
    supplierFirstName = json['supplier_first_name']?.toString();
    supplierLastName = json['supplier_last_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['record_type'] = recordType;
    data['legal_entity_type'] = legalEntityType;
    data['ein_ssn'] = einSsn;
    data['supplier_first_name'] = supplierFirstName;
    data['supplier_last_name'] = supplierLastName;

    data['supplier_id'] = supplierId;
    data['supplier_number'] = supplierNumber;
    data['supplier_name'] = supplierName;
    data['description'] = description;
    data['address1'] = address1;
    data['address2'] = address2;
    data['city'] = city;
    data['state'] = state;
    data['zip'] = zip;
    data['country'] = country;
    data['status'] = status;
    data['status_desc'] = statusDesc;
    data['org_id'] = orgId;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['email'] = email;
    data['contact_person'] = contactPerson;
    data['phone_number'] = phoneNumber;
    data['country_name'] = countryName;
    data['c_ext_attr01'] = cExtAttr01;
    data['c_ext_attr02'] = cExtAttr02;
    data['c_ext_attr03'] = cExtAttr03;
    data['c_ext_attr04'] = cExtAttr04;
    data['c_ext_attr05'] = cExtAttr05;
    data['c_ext_lov1'] = cExtLov1;
    data['c_ext_lov2'] = cExtLov2;
    data['c_ext_lov3'] = cExtLov3;
    data['c_ext_lov4'] = cExtLov4;
    data['c_ext_lov5'] = cExtLov5;
    data['n_ext_attr01'] = nExtAttr01;
    data['n_ext_attr02'] = nExtAttr02;
    data['n_ext_attr03'] = nExtAttr03;
    data['n_ext_attr04'] = nExtAttr04;
    data['n_ext_attr05'] = nExtAttr05;
    data['address3'] = address3;
    data['supplier_type'] = supplierType;
    data['payment_term'] = paymentTerm;
    data['contact_cell_phone'] = contactCellPhone;
    data['trades'] = trades;
    data['fax'] = fax;
    data['tax_id'] = taxId;
    data['partner'] = partner;
    data['prequalified'] = prequalified;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['is_editable'] = isEditable;
    data['payment_method'] = paymentMethod;
    data['bank_account'] = bankAccount;
    data['bank_routing'] = bankRouting;
    return data;
  }
}
