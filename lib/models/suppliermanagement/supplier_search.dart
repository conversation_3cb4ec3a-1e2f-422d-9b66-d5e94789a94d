class SupplierSearch {
  int? supplierId;
  String? supplierNumber;
  String? supplierName;
  String? description;
  String? address1;
  String? address2;
  String? city;
  String? state;
  String? zip;
  String? country;
  String? status;
  String? email;
  String? contactPerson;
  String? phoneNumber;
  String? cExtLov1;
  String? supplierType;
  String? address3;
  String? paymentTerm;
  String? allColumns;
  String? statusCode;

  SupplierSearch({
    this.supplierId,
    this.supplierNumber,
    this.supplierName,
    this.description,
    this.address1,
    this.address2,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.status,
    this.email,
    this.contactPerson,
    this.phoneNumber,
    this.cExtLov1,
    this.supplierType,
    this.address3,
    this.paymentTerm,
    this.allColumns,
    this.statusCode,
  });
  SupplierSearch.fromJson(Map<String, dynamic> json) {
    supplierId = json['supplier_id']?.toInt();
    supplierNumber = json['supplier_number']?.toString();
    supplierName = json['supplier_name']?.toString();
    description = json['description']?.toString();
    address1 = json['address1']?.toString();
    address2 = json['address2']?.toString();
    city = json['city']?.toString();
    state = json['state']?.toString();
    zip = json['zip']?.toString();
    country = json['country']?.toString();
    status = json['status']?.toString();
    email = json['email']?.toString();
    contactPerson = json['contact_person']?.toString();
    phoneNumber = json['phone_number']?.toString();
    cExtLov1 = json['c_ext_lov1']?.toString();
    supplierType = json['supplier_type']?.toString();
    address3 = json['address3']?.toString();
    paymentTerm = json['payment_term']?.toString();
    allColumns = json['all_columns']?.toString();
    statusCode = json['status_code']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['supplier_id'] = supplierId;
    data['supplier_number'] = supplierNumber;
    data['supplier_name'] = supplierName;
    data['description'] = description;
    data['address1'] = address1;
    data['address2'] = address2;
    data['city'] = city;
    data['state'] = state;
    data['zip'] = zip;
    data['country'] = country;
    data['status'] = status;
    data['email'] = email;
    data['contact_person'] = contactPerson;
    data['phone_number'] = phoneNumber;
    data['c_ext_lov1'] = cExtLov1;
    data['supplier_type'] = supplierType;
    data['address3'] = address3;
    data['payment_term'] = paymentTerm;
    data['all_columns'] = allColumns;
    data['status_code'] = statusCode;
    return data;
  }
}
