class FindPerson {
  final String? location;
  final String? employeeNumber;
  final String? lastName;
  final String? firstName;
  final String? nickname;
  final String? departmentId;
  final String? departmentName;
  final String? floorNumber;
  final String? allColumns;
  final int? floorId;
  final int? spaceId;
  final int? buildingId;
  final String? managerName;
  final String? primaryEmail;
  final String? assignedBuildingId;
  final String? primaryPhone;
  final String? mobileNumber;
  final String? title;
  final String? gradeLevel;
  final String? shiftWorker;
  final String? employeeType;
  final String? workFromHome;
  final String? status;
  final String? hireDate;
  final String? endDate;
  final int? personId;

  FindPerson(
      {this.location,
      this.employeeNumber,
      this.lastName,
      this.firstName,
      this.nickname,
      this.departmentId,
      this.departmentName,
      this.floorNumber,
      this.allColumns,
      this.floorId,
      this.spaceId,
      this.buildingId,
      this.managerName,
      this.primaryEmail,
      this.assignedBuildingId,
      this.primaryPhone,
      this.mobileNumber,
      this.title,
      this.gradeLevel,
      this.shiftWorker,
      this.employeeType,
      this.workFromHome,
      this.status,
      this.hireDate,
      this.endDate,
      this.personId});

  factory FindPerson.fromJson(Map<String, dynamic> json) {
    return FindPerson(
        location: json['location'] ?? "",
        employeeNumber: json['employeeNumber'] ?? "",
        lastName: json['lastName'] ?? "",
        firstName: json['firstName'] ?? "",
        nickname: json['nickName'] ?? "",
        departmentId: json['departmentId'] ?? "",
        departmentName: json['departmentName'] ?? "",
        floorNumber: json['floorNumber'] ?? "",
        allColumns: json['allColumns'] ?? "",
        floorId: json['floorId'],
        spaceId: json['spaceId'],
        buildingId: json['buildingId'],
        managerName: json['managerName'] ?? "",
        primaryEmail: json['primaryEmail'] ?? "",
        assignedBuildingId: json['assignedBuildingId'] ?? "",
        primaryPhone: json['primaryPhone'] ?? "",
        mobileNumber: json['mobileNumber'] ?? "",
        title: json['title'] ?? "",
        gradeLevel: json['gradeLevel'] ?? "",
        shiftWorker: json['shiftWorker'] ?? "",
        employeeType: json['employeeType'] ?? "",
        workFromHome: json['workFromHome'] ?? "",
        status: json['status'] ?? "",
        hireDate: json['hireDate'] ?? "",
        endDate: json['endDate'] ?? "",
        personId: json['personId'] ?? "" as int?);
  }
}

class FindSpace {
  final int? spaceId;
  final int? allocDeptId;
  final int? floorId;
  final int? cadRentableSf;
  final int? usableSf;
  final String? spaceName;
  final String? spaceNumber;
  final String? buildingId;
  final String? buildingName;
  final String? allocationType;
  final String? spaceType;
  final String? spaceUse;
  final String? floorNumber;
  final String? floorName;
  final String? allColumns;
  final String? chargeType;
  final String? isWkst;
  final String? spaceCategory;
  final String? spaceDisplayName;
  final String? wkstType;
  final String? occupencyStatus;
  final String? actualSf;
  final int? capacityPlanned;
  final int? totalHeadcount;
  final int? capacityAvailable;
  final int? departmentId;
  final String? departmentName;
  final int? noOfReviews;
  final int? picId;

  FindSpace(
      {this.spaceId,
      this.allocDeptId,
      this.floorId,
      this.cadRentableSf,
      this.usableSf,
      this.spaceName,
      this.spaceNumber,
      this.buildingId,
      this.buildingName,
      this.allocationType,
      this.spaceType,
      this.spaceUse,
      this.floorNumber,
      this.floorName,
      this.allColumns,
      this.chargeType,
      this.isWkst,
      this.spaceCategory,
      this.spaceDisplayName,
      this.wkstType,
      this.occupencyStatus,
      this.actualSf,
      this.capacityPlanned,
      this.totalHeadcount,
      this.capacityAvailable,
      this.departmentId,
      this.departmentName,
      this.noOfReviews,
      this.picId});

  factory FindSpace.fromJson(Map<String, dynamic> json) {
    return FindSpace(
        spaceId: json['spaceId'],
        allocDeptId: json['allocDeptId'],
        floorId: json['floorId'],
        cadRentableSf: json['cadRentableSf'],
        usableSf: json['usableSf'],
        spaceName: json['spaceName'] ?? "",
        spaceNumber: json['spaceNumber'] ?? "",
        buildingId: json['buildingId'] ?? "",
        buildingName: json['buildingName'] ?? "",
        allocationType: json['allocationType'] ?? "",
        spaceType: json['spaceType'] ?? "",
        spaceUse: json['spaceUse'] ?? "",
        floorNumber: json['floorNumber'] ?? "",
        floorName: json['floorName'] ?? "",
        allColumns: json['allColumns'] ?? "",
        chargeType: json['chargeType'] ?? "",
        isWkst: json['isWkst'] ?? "",
        spaceCategory: json['spaceCategory'] ?? "",
        spaceDisplayName: json['spaceDisplayName'] ?? "",
        wkstType: json['wkstType'] ?? "",
        occupencyStatus: json['occupencyStatus'] ?? "",
        actualSf: json['actualSf'] ?? "",
        capacityPlanned: json['capacityPlanned'],
        totalHeadcount: json['totalHeadcount'],
        capacityAvailable: json['capacityAvailable'],
        departmentId: json['departmentId'],
        noOfReviews: json['noOfReviews'] ?? 0,
        picId: json['picId'],
        departmentName: json['departmentName'] ?? "");
  }
}
