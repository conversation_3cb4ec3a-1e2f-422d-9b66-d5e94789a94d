class MacRequest {
  final int? requestId;
  final String? requestNumber;
  final String? tospaceDisplayName;
  final String? fromspaceDisplayName;
  final String? status;
  final String? requestedStartDate;
  final int? propertyId;
  final int? buildingId;
  final int? spaceId;
  final int? floorId;
  final String? requestedFor;
  final String? justification;
  final String? approver;
  final String? noOfBoxes;
  final String? requestType;

  MacRequest(
      {this.requestedFor,
      this.propertyId,
      this.floorId,
      this.spaceId,
      this.requestId,
      this.requestNumber,
      this.tospaceDisplayName,
      this.fromspaceDisplayName,
      this.requestedStartDate,
      this.status,
      this.buildingId,
      this.justification,
      this.approver,
      this.noOfBoxes,
      this.requestType});

  factory MacRequest.fromJson(Map<String, dynamic> json) {
    return MacRequest(
        requestId: json['requestId'],
        requestNumber: json['requestNumber'],
        tospaceDisplayName: json['tospaceDisplayName'] ?? "",
        fromspaceDisplayName: json['fromspaceDisplayName'] ?? "",
        status: json['status'] ?? "",
        requestedStartDate: json['requestedStartDate'] ?? "",
        requestedFor: json['requestedFor'] ?? "",
        propertyId: json['propertyId'] ?? "" as int?,
        floorId: json['floorId'] ?? "" as int?,
        spaceId: json['spaceId'] ?? "" as int?,
        buildingId: json['buildingId'] ?? "" as int?,
        justification: json['justification'] ?? "",
        approver: json['approver'] ?? "",
        noOfBoxes: json['noOfBoxes'] ?? "",
        requestType: json['requestType'] ?? "");
  }
}
