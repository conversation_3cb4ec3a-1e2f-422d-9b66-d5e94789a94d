import 'floor.dart';

class Building {
  int? buildingId;
  String? buildingName;
  List<Floor>? floors;

  Building({
    this.buildingId,
    this.buildingName,
    this.floors,
  });

  factory Building.fromJson(Map<String, dynamic> json) {
    var list = json['floors'] as List;
    List<Floor> floorslist = list.map((i) => Floor.fromJson(i)).toList();

    return Building(
        buildingId: json['buildingId'] ?? '' as int?,
        buildingName: json['buildingName'] ?? '',
        floors: floorslist);
  }
}
