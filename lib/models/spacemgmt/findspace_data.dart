class FindSpaceData {
  int? spaceId;
  String? spaceName;
  String? spaceNumber;
  int? buildingId;
  String? buildingName;
  String? allocationType;
  int? allocDeptId;
  String? spaceType;
  String? spaceUse;
  int? floorId;
  String? floorNumber;
  String? floorName;
  String? allColumns;
  int? cadRentableSf;
  String? chargeType;
  String? isWkst;
  String? spaceCategory;
  String? spaceDisplayName;
  double? usableSf;
  String? wkstType;
  String? buildingNumber;
  int? propertyId;
  String? isResrvable;
  int? spaceAreaId;
  String? areaNumber;
  String? areaName;
  String? isFlexibleSeating;
  String? isShiftSeating;
  double? otherCapacity;
  String? status;

  FindSpaceData({
    this.spaceId,
    this.spaceName,
    this.spaceNumber,
    this.buildingId,
    this.buildingName,
    this.allocationType,
    this.allocDeptId,
    this.spaceType,
    this.spaceUse,
    this.floorId,
    this.floorNumber,
    this.floorName,
    this.allColumns,
    this.cadRentableSf,
    this.chargeType,
    this.isWkst,
    this.spaceCategory,
    this.spaceDisplayName,
    this.usableSf,
    this.wkstType,
    this.buildingNumber,
    this.propertyId,
    this.isResrvable,
    this.spaceAreaId,
    this.areaNumber,
    this.areaName,
    this.isFlexibleSeating,
    this.isShiftSeating,
    this.otherCapacity,
    this.status,
  });
  FindSpaceData.fromJson(Map<String, dynamic> json) {
    spaceId = json['space_id']?.toInt();
    spaceName = json['space_name']?.toString();
    spaceNumber = json['space_number']?.toString();
    buildingId = json['building_id']?.toInt();
    buildingName = json['building_name']?.toString();
    allocationType = json['allocation_type']?.toString();
    allocDeptId = json['alloc_dept_id']?.toInt();
    spaceType = json['space_type']?.toString();
    spaceUse = json['space_use']?.toString();
    floorId = json['floor_id']?.toInt();
    floorNumber = json['floor_number']?.toString();
    floorName = json['floor_name']?.toString();
    allColumns = json['all_columns']?.toString();
    cadRentableSf = json['cad_rentable_sf']?.toInt();
    chargeType = json['charge_type']?.toString();
    isWkst = json['is_wkst']?.toString();
    spaceCategory = json['space_category']?.toString();
    spaceDisplayName = json['space_display_name']?.toString();
    usableSf = json['usable_sf']?.toDouble();
    wkstType = json['wkst_type']?.toString();
    buildingNumber = json['building_number']?.toString();
    propertyId = json['property_id']?.toInt();
    isResrvable = json['is_resrvable']?.toString();
    spaceAreaId = json['space_area_id']?.toInt();
    areaNumber = json['area_number']?.toString();
    areaName = json['area_name']?.toString();
    isFlexibleSeating = json['is_flexible_seating']?.toString();
    isShiftSeating = json['is_shift_seating']?.toString();
    otherCapacity = json['other_capacity']?.toDouble();
    status = json['status']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['space_id'] = spaceId;
    data['space_name'] = spaceName;
    data['space_number'] = spaceNumber;
    data['building_id'] = buildingId;
    data['building_name'] = buildingName;
    data['allocation_type'] = allocationType;
    data['alloc_dept_id'] = allocDeptId;
    data['space_type'] = spaceType;
    data['space_use'] = spaceUse;
    data['floor_id'] = floorId;
    data['floor_number'] = floorNumber;
    data['floor_name'] = floorName;
    data['all_columns'] = allColumns;
    data['cad_rentable_sf'] = cadRentableSf;
    data['charge_type'] = chargeType;
    data['is_wkst'] = isWkst;
    data['space_category'] = spaceCategory;
    data['space_display_name'] = spaceDisplayName;
    data['usable_sf'] = usableSf;
    data['wkst_type'] = wkstType;
    data['building_number'] = buildingNumber;
    data['property_id'] = propertyId;
    data['is_resrvable'] = isResrvable;
    data['space_area_id'] = spaceAreaId;
    data['area_number'] = areaNumber;
    data['area_name'] = areaName;
    data['is_flexible_seating'] = isFlexibleSeating;
    data['is_shift_seating'] = isShiftSeating;
    data['other_capacity'] = otherCapacity;
    data['status'] = status;
    return data;
  }
}
