class FindPersonData {
  String? location;
  String? employeeNumber;
  String? lastName;
  String? firstName;
  String? nickname;
  String? departmentId;
  String? departmentName;
  int? floorId;
  String? floorNumber;
  int? spaceId;
  int? buildingId;
  String? allColumns;
  String? locationType;
  int? personId;

  FindPersonData({
    this.location,
    this.employeeNumber,
    this.lastName,
    this.firstName,
    this.nickname,
    this.departmentId,
    this.departmentName,
    this.floorId,
    this.floorNumber,
    this.spaceId,
    this.buildingId,
    this.allColumns,
    this.locationType,
    this.personId,
  });
  FindPersonData.fromJson(Map<String, dynamic> json) {
    location = json['location']?.toString();
    employeeNumber = json['employee_number']?.toString();
    lastName = json['last_name']?.toString();
    firstName = json['first_name']?.toString();
    nickname = json['nickname']?.toString();
    departmentId = json['department_id']?.toString();
    departmentName = json['department_name']?.toString();
    floorId = json['floor_id']?.toInt();
    floorNumber = json['floor_number']?.toString();
    spaceId = json['space_id']?.toInt();
    buildingId = json['building_id']?.toInt();
    allColumns = json['all_columns']?.toString();
    locationType = json['location_type']?.toString();
    personId = json['person_id']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['location'] = location;
    data['employee_number'] = employeeNumber;
    data['last_name'] = lastName;
    data['first_name'] = firstName;
    data['nickname'] = nickname;
    data['department_id'] = departmentId;
    data['department_name'] = departmentName;
    data['floor_id'] = floorId;
    data['floor_number'] = floorNumber;
    data['space_id'] = spaceId;
    data['building_id'] = buildingId;
    data['all_columns'] = allColumns;
    data['location_type'] = locationType;
    data['person_id'] = personId;
    return data;
  }
}
