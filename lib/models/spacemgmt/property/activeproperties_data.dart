class ActiveProperties {
  int? propertyId;
  String? propertyName;
  String? propertyDesc;
  String? propertyType;
  String? status;
  double? area;
  double? gross;
  double? cadRentable;
  int? leaseRentable;
  double? core;
  double? actual;
  int? propertyCommon;
  double? buildingCommon;
  double? floorCommon;
  double? residual;
  int? owned;
  int? subleased;
  int? totalHeadCount;
  int? workstationHeadCount;
  int? totalCapacity;
  int? workstationCapacity;
  int? clientId;
  int? brandId;
  int? orgId;
  int? buId;
  String? creationDate;
  String? createdBy;
  String? propertyNumber;
  String? propertyStatusType;

  ActiveProperties({
    this.propertyId,
    this.propertyName,
    this.propertyDesc,
    this.propertyType,
    this.status,
    this.area,
    this.gross,
    this.cadRentable,
    this.leaseRentable,
    this.core,
    this.actual,
    this.propertyCommon,
    this.buildingCommon,
    this.floorCommon,
    this.residual,
    this.owned,
    this.subleased,
    this.totalHeadCount,
    this.workstationHeadCount,
    this.totalCapacity,
    this.workstationCapacity,
    this.clientId,
    this.brandId,
    this.orgId,
    this.buId,
    this.creationDate,
    this.createdBy,
    this.propertyNumber,
    this.propertyStatusType,
  });
  ActiveProperties.fromJson(Map<String, dynamic> json) {
    propertyId = json['property_id']?.toInt();
    propertyName = json['property_name']?.toString();
    propertyDesc = json['property_desc']?.toString();
    propertyType = json['property_type']?.toString();
    status = json['status']?.toString();
    area = json['area']?.toDouble();
    gross = json['gross']?.toDouble();
    cadRentable = json['cad_rentable']?.toDouble();
    leaseRentable = json['lease_rentable']?.toInt();
    core = json['core']?.toDouble();
    actual = json['actual']?.toDouble();
    propertyCommon = json['property_common']?.toInt();
    buildingCommon = json['building_common']?.toDouble();
    floorCommon = json['floor_common']?.toDouble();
    residual = json['residual']?.toDouble();
    owned = json['owned']?.toInt();
    subleased = json['subleased']?.toInt();
    totalHeadCount = json['total_head_count']?.toInt();
    workstationHeadCount = json['workstation_head_count']?.toInt();
    totalCapacity = json['total_capacity']?.toInt();
    workstationCapacity = json['workstation_capacity']?.toInt();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    orgId = json['org_id']?.toInt();
    buId = json['bu_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    propertyNumber = json['property_number']?.toString();
    propertyStatusType = json['property_status_type']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['property_id'] = propertyId;
    data['property_name'] = propertyName;
    data['property_desc'] = propertyDesc;
    data['property_type'] = propertyType;
    data['status'] = status;
    data['area'] = area;
    data['gross'] = gross;
    data['cad_rentable'] = cadRentable;
    data['lease_rentable'] = leaseRentable;
    data['core'] = core;
    data['actual'] = actual;
    data['property_common'] = propertyCommon;
    data['building_common'] = buildingCommon;
    data['floor_common'] = floorCommon;
    data['residual'] = residual;
    data['owned'] = owned;
    data['subleased'] = subleased;
    data['total_head_count'] = totalHeadCount;
    data['workstation_head_count'] = workstationHeadCount;
    data['total_capacity'] = totalCapacity;
    data['workstation_capacity'] = workstationCapacity;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['org_id'] = orgId;
    data['bu_id'] = buId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['property_number'] = propertyNumber;
    data['property_status_type'] = propertyStatusType;
    return data;
  }
}
