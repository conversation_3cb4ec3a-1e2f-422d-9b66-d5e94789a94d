class PropertyViewData {
  String? locEntityType;
  String? locSubEntityType;
  int? storeId;
  String? storeNumber;
  String? storeName;
  String? address;
  String? city;
  String? state;
  String? zipCode;
  String? status;
  String? shoppingCenter;
  String? landlord;
  String? openDate;
  String? ownershipType;
  String? allColumns;
  int? clientId;
  int? brandId;
  int? regionId;
  String? regionName;
  int? subRegionId;
  String? locationTypeCode;
  double? latitude;
  double? longitude;
  String? subRegionName;
  String? facilityType;
  String? statusCode;
  String? locTypeCodeDesc;
  String? facilityTypeDesc;
  String? ownershipTypeDesc;

  PropertyViewData({
    this.locEntityType,
    this.locSubEntityType,
    this.storeId,
    this.storeNumber,
    this.storeName,
    this.address,
    this.city,
    this.state,
    this.zipCode,
    this.status,
    this.shoppingCenter,
    this.landlord,
    this.openDate,
    this.ownershipType,
    this.allColumns,
    this.clientId,
    this.brandId,
    this.regionId,
    this.regionName,
    this.subRegionId,
    this.locationTypeCode,
    this.latitude,
    this.longitude,
    this.subRegionName,
    this.facilityType,
    this.statusCode,
    this.facilityTypeDesc,
    this.locTypeCodeDesc,
    this.ownershipTypeDesc,
  });
  PropertyViewData.fromJson(Map<String, dynamic> json) {
    locEntityType = json['loc_entity_type']?.toString();
    locSubEntityType = json['loc_sub_entity_type']?.toString();
    storeId = json['store_id']?.toInt();
    storeNumber = json['store_number']?.toString();
    storeName = json['store_name']?.toString();
    address = json['address']?.toString();
    city = json['city']?.toString();
    state = json['state']?.toString();
    zipCode = json['zip_code']?.toString();
    status = json['status']?.toString();
    shoppingCenter = json['shopping_center']?.toString();
    landlord = json['landlord']?.toString();
    openDate = json['open_date']?.toString();
    ownershipType = json['ownership_type']?.toString();
    allColumns = json['all_columns']?.toString();
    clientId = json['client_id']?.toInt();
    brandId = json['brand_id']?.toInt();
    regionId = json['region_id']?.toInt();
    regionName = json['region_name']?.toString();
    subRegionId = json['sub_region_id']?.toInt();
    locationTypeCode = json['location_type_code']?.toString();
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    subRegionName = json['sub_region_name']?.toString();
    facilityType = json['facility_type']?.toString();
    statusCode = json['status_code']?.toString();
    locTypeCodeDesc = json['location_type_code_desc']?.toString();
    ownershipTypeDesc = json['ownership_type_desc']?.toString();
    facilityTypeDesc = json['facility_type_desc']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['loc_entity_type'] = locEntityType;
    data['loc_sub_entity_type'] = locSubEntityType;
    data['store_id'] = storeId;
    data['store_number'] = storeNumber;
    data['store_name'] = storeName;
    data['address'] = address;
    data['city'] = city;
    data['state'] = state;
    data['zip_code'] = zipCode;
    data['status'] = status;
    data['shopping_center'] = shoppingCenter;
    data['landlord'] = landlord;
    data['open_date'] = openDate;
    data['ownership_type'] = ownershipType;
    data['all_columns'] = allColumns;
    data['client_id'] = clientId;
    data['brand_id'] = brandId;
    data['region_id'] = regionId;
    data['region_name'] = regionName;
    data['sub_region_id'] = subRegionId;
    data['location_type_code'] = locationTypeCode;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['sub_region_name'] = subRegionName;
    data['facility_type'] = facilityType;
    data['status_code'] = statusCode;
    data['location_type_code_desc'] = locTypeCodeDesc;
    data['ownership_type_desc'] = facilityTypeDesc;
    data['facility_type_desc'] = ownershipTypeDesc;
    return data;
  }
}
