class ReservationListData {
  String? allDay;
  String? brandId;
  int? buildingId;
  int? clientId;
  String? country;
  String? createdBy;
  String? creationDate;
  String? description;
  String? endDate;
  int? floorId;
  String? isRecurring;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  int? objectVersionNumber;
  int? orgId;
  int? parentReservationId;
  int? propertyId;
  int? rating;
  String? recurringEndDate;
  int? recurringOccurences;
  String? recurringPattern;
  String? recurringStartDate;
  int? reservationId;
  String? reservationNumber;
  String? reservationTitle;
  String? reservationType;
  String? selfReserve;
  String? serviceItems;
  int? spaceId;
  String? startDate;
  String? status;
  int? personId;
  String? personUserName;
  String? spaceDisplayName;
  String? spaceNum;
  String? spaceCategory;
  String? spaceUse;
  String? spaceAttributes;
  int? spaceRsf;
  String? propertyName;
  String? buildingName;
  String? floorName;
  String? assignee;
  String? startDateTime;
  String? endDateTime;

  ReservationListData({
    this.allDay,
    this.brandId,
    this.buildingId,
    this.clientId,
    this.country,
    this.createdBy,
    this.creationDate,
    this.description,
    this.endDate,
    this.floorId,
    this.isRecurring,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.objectVersionNumber,
    this.orgId,
    this.parentReservationId,
    this.propertyId,
    this.rating,
    this.recurringEndDate,
    this.recurringOccurences,
    this.recurringPattern,
    this.recurringStartDate,
    this.reservationId,
    this.reservationNumber,
    this.reservationTitle,
    this.reservationType,
    this.selfReserve,
    this.serviceItems,
    this.spaceId,
    this.startDate,
    this.status,
    this.personId,
    this.personUserName,
    this.spaceDisplayName,
    this.spaceNum,
    this.spaceCategory,
    this.spaceUse,
    this.spaceAttributes,
    this.spaceRsf,
    this.propertyName,
    this.buildingName,
    this.floorName,
    this.assignee,
    this.startDateTime,
    this.endDateTime,
  });
  ReservationListData.fromJson(Map<String, dynamic> json) {
    allDay = json['all_day']?.toString();
    brandId = json['brand_id']?.toString();
    buildingId = json['building_id']?.toInt();
    clientId = json['client_id']?.toInt();
    country = json['country']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    description = json['description']?.toString();
    endDate = json['end_date']?.toString();
    floorId = json['floor_id']?.toInt();
    isRecurring = json['is_recurring']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    objectVersionNumber = json['object_version_number']?.toInt();
    orgId = json['org_id']?.toInt();
    parentReservationId = json['parent_reservation_id']?.toInt();
    propertyId = json['property_id']?.toInt();
    rating = json['rating']?.toInt();
    recurringEndDate = json['recurring_end_date']?.toString();
    recurringOccurences = json['recurring_occurences']?.toInt();
    recurringPattern = json['recurring_pattern']?.toString();
    recurringStartDate = json['recurring_start_date']?.toString();
    reservationId = json['reservation_id']?.toInt();
    reservationNumber = json['reservation_number']?.toString();
    reservationTitle = json['reservation_title']?.toString();
    reservationType = json['reservation_type']?.toString();
    selfReserve = json['self_reserve']?.toString();
    serviceItems = json['service_items']?.toString();
    spaceId = json['space_id']?.toInt();
    startDate = json['start_date']?.toString();
    status = json['status']?.toString();
    personId = json['person_id']?.toInt();
    personUserName = json['person_user_name']?.toString();
    spaceDisplayName = json['space_display_name']?.toString();
    spaceNum = json['space_num']?.toString();
    spaceCategory = json['space_category']?.toString();
    spaceUse = json['space_use']?.toString();
    spaceAttributes = json['space_attributes']?.toString();
    spaceRsf = json['space_rsf']?.toInt();
    propertyName = json['property_name']?.toString();
    buildingName = json['building_name']?.toString();
    floorName = json['floor_name']?.toString();
    assignee = json['assignee']?.toString();
    startDateTime = json['start_date_time']?.toString();
    endDateTime = json['end_date_time']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['all_day'] = allDay;
    data['brand_id'] = brandId;
    data['building_id'] = buildingId;
    data['client_id'] = clientId;
    data['country'] = country;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['description'] = description;
    data['end_date'] = endDate;
    data['floor_id'] = floorId;
    data['is_recurring'] = isRecurring;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['object_version_number'] = objectVersionNumber;
    data['org_id'] = orgId;
    data['parent_reservation_id'] = parentReservationId;
    data['property_id'] = propertyId;
    data['rating'] = rating;
    data['recurring_end_date'] = recurringEndDate;
    data['recurring_occurences'] = recurringOccurences;
    data['recurring_pattern'] = recurringPattern;
    data['recurring_start_date'] = recurringStartDate;
    data['reservation_id'] = reservationId;
    data['reservation_number'] = reservationNumber;
    data['reservation_title'] = reservationTitle;
    data['reservation_type'] = reservationType;
    data['self_reserve'] = selfReserve;
    data['service_items'] = serviceItems;
    data['space_id'] = spaceId;
    data['start_date'] = startDate;
    data['status'] = status;
    data['person_id'] = personId;
    data['person_user_name'] = personUserName;
    data['space_display_name'] = spaceDisplayName;
    data['space_num'] = spaceNum;
    data['space_category'] = spaceCategory;
    data['space_use'] = spaceUse;
    data['space_attributes'] = spaceAttributes;
    data['space_rsf'] = spaceRsf;
    data['property_name'] = propertyName;
    data['building_name'] = buildingName;
    data['floor_name'] = floorName;
    data['assignee'] = assignee;
    data['start_date_time'] = startDateTime;
    data['end_date_time'] = endDateTime;

    return data;
  }
}
