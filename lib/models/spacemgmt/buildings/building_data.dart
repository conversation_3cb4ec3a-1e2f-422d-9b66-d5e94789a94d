class BuildingData {
  String? address;
  String? address2;
  String? address3;
  String? assetType;
  double? baseRentPerSize;
  int? brandId;
  String? cExtAttr1;
  String? cExtAttr10;
  String? cExtAttr11;
  String? cExtAttr12;
  String? cExtAttr13;
  String? cExtAttr14;
  String? cExtAttr15;
  String? cExtAttr16;
  String? cExtAttr17;
  String? cExtAttr18;
  String? cExtAttr19;
  String? cExtAttr2;
  String? cExtAttr20;
  String? cExtAttr21;
  String? cExtAttr22;
  String? cExtAttr23;
  String? cExtAttr24;
  String? cExtAttr25;
  String? cExtAttr26;
  String? cExtAttr27;
  String? cExtAttr28;
  String? cExtAttr29;
  String? cExtAttr3;
  String? cExtAttr30;
  String? cExtAttr31;
  String? cExtAttr32;
  String? cExtAttr33;
  String? cExtAttr34;
  String? cExtAttr35;
  String? cExtAttr36;
  String? cExtAttr37;
  String? cExtAttr38;
  String? cExtAttr39;
  String? cExtAttr4;
  String? cExtAttr40;
  String? cExtAttr41;
  String? cExtAttr42;
  String? cExtAttr43;
  String? cExtAttr44;
  String? cExtAttr45;
  String? cExtAttr46;
  String? cExtAttr47;
  String? cExtAttr48;
  String? cExtAttr49;
  String? cExtAttr5;
  String? cExtAttr50;
  String? cExtAttr51;
  String? cExtAttr52;
  String? cExtAttr53;
  String? cExtAttr54;
  String? cExtAttr55;
  String? cExtAttr56;
  String? cExtAttr57;
  String? cExtAttr58;
  String? cExtAttr59;
  String? cExtAttr6;
  String? cExtAttr60;
  String? cExtAttr7;
  String? cExtAttr8;
  String? cExtAttr9;
  String? cExtLov1;
  String? cExtLov2;
  String? cExtLov3;
  String? cExtLov4;
  String? cExtLov5;
  String? cbsaClass;
  int? cbsaId;
  String? cbsaName;
  String? censusDivision;
  String? censusRegion;
  String? changeReasonCode;
  String? changeReasonDesc;
  String? city;
  int? clientId;
  String? closeDate;
  String? country;
  String? county;
  String? countyFips;
  String? createdBy;
  String? creationDate;
  double? cyAnnualSales;
  double? cyEbitda;
  double? cyTrl_12MoSales;
  String? dExtAttr1;
  String? dExtAttr10;
  String? dExtAttr11;
  String? dExtAttr12;
  String? dExtAttr13;
  String? dExtAttr14;
  String? dExtAttr15;
  String? dExtAttr2;
  String? dExtAttr3;
  String? dExtAttr4;
  String? dExtAttr5;
  String? dExtAttr6;
  String? dExtAttr7;
  String? dExtAttr8;
  String? dExtAttr9;
  String? delivery;
  double? demoScore;
  String? diningArea;
  int? dmaId;
  String? dmaName;
  String? effectiveDate;
  String? facilityType;
  int? fiscalPeriod;
  int? fiscalQuarter;
  int? fiscalYear;
  String? fullAddress;
  int? landId;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  double? latitude;
  String? leaseExpDate;
  String? leaseOption;
  String? locEntityType;
  String? locSubEntityType;
  String? locationTypeCode;
  double? longitude;
  String? marketName;
  double? miPrinx;
  int? msaId;
  String? msaName;
  double? nExtAttr1;
  double? nExtAttr10;
  double? nExtAttr11;
  double? nExtAttr12;
  double? nExtAttr13;
  double? nExtAttr14;
  double? nExtAttr15;
  double? nExtAttr16;
  double? nExtAttr17;
  double? nExtAttr18;
  double? nExtAttr19;
  double? nExtAttr2;
  double? nExtAttr20;
  double? nExtAttr21;
  double? nExtAttr22;
  double? nExtAttr23;
  double? nExtAttr24;
  double? nExtAttr25;
  double? nExtAttr26;
  double? nExtAttr27;
  double? nExtAttr28;
  double? nExtAttr29;
  double? nExtAttr3;
  double? nExtAttr30;
  double? nExtAttr31;
  double? nExtAttr32;
  double? nExtAttr33;
  double? nExtAttr34;
  double? nExtAttr35;
  double? nExtAttr36;
  double? nExtAttr37;
  double? nExtAttr4;
  double? nExtAttr5;
  double? nExtAttr6;
  double? nExtAttr7;
  double? nExtAttr8;
  double? nExtAttr9;
  String? neighborhood;
  int? objectVersionNumber;
  double? openDate;
  int? operationHirerarchyId;
  String? operationsManager;
  int? operationsMarketId;
  String? operationsRegion;
  int? operationsSubMarktetId;
  String? operationsVp;
  int? orgId;
  String? outboundIntegFlag;
  String? ownershipType;
  int? parentLocId;
  double? pctDiffEbitda;
  double? pctDiffSales;
  double? periodEnding;
  String? phone;
  String? propertyType;
  double? pyEbitda;
  double? pyTrl_12MoSales;
  int? realEstateHierarchyId;
  String? realEstateManager;
  String? realEstateVp;
  int? relocStoreId;
  int? relocToSiteId;
  String? scId;
  int? sdaId;
  String? secAttr1;
  String? secAttr2;
  String? sisTypeCode;
  int? siteId;
  int? siteScore;
  String? siteScoreDate;
  String? state;
  String? status;
  String? storeClass;
  int? storeId;
  String? storeName;
  String? storeNumber;
  String? storeNumberErp;
  String? storeNumberLegacy;
  double? storeSize;
  double? totalAnnualRent;
  String? zip;
  int? dwgFileId;
  String? contractualType;
  String? usingTangoLease;
  String? measurementType;
  String? isSubleased;
  String? chargeType;
  String? occupancyStatus;
  String? allocationType;
  double? departmentIdCharge;
  double? departmentIdOverride;
  double? departmentIdDefault;
  double? departmentIdTemp;
  double? totalAreaSf;
  int? totalAreaSm;
  double? externalGrossSf;
  double? externalGrossSm;
  double? internalGrossSf;
  double? internalGrossSm;
  double? cadRentableSf;
  int? cadRentableSm;
  double? leaseRentableSf;
  int? leaseRentableSm;
  int? leaseRentableManualSf;
  double? leaseRentableManualSm;
  int? usableSf;
  int? usableSm;
  double? actualSf;
  int? actualSm;
  double? verticalPenetrationSf;
  int? verticalPenetrationSm;
  double? propertyCommonSf;
  int? propertyCommonSm;
  int? buildingCommonSf;
  int? buildingCommonSm;
  double? floorCommonSf;
  int? floorCommonSm;
  double? floorSharedSf;
  int? floorSharedSm;
  double? buildingSharedSf;
  int? buildingSharedSm;
  int? residualSf;
  int? residualSm;
  double? subleasedSf;
  int? subleasedSm;
  double? totalCapacity;
  double? capacityPlanned;
  double? capacityAvailable;
  int? totalHeadcount;
  int? workstationHeadcount;
  int? workstationCapacity;
  int? pctBuilding;
  String? buildingName;
  String? buildingNumber;
  String? propertyName;
  double? occupancyRate;
  double? workstationUtilization;
  String? cadLocked;
  String? lastUpdatedDate;
  String? timezone;
  double? vacantSf;
  double? vacancypercent;
  String? strategy;
  String? bldgPropName;
  String? cadLockedBy;
  String? cadCreatedBy;
  String? cadCreationDate;
  String? cadUpdatedBy;
  String? cadUpdatedDate;
  String? brandName;
  String? statusType;
  String? tempCadFile;
  String? spaceUom;
  int? vacantSm;
  String? excludeFromReserve;
  String? isManualSync;
  int? floorSeq;

  BuildingData({
    this.address,
    this.address2,
    this.address3,
    this.assetType,
    this.baseRentPerSize,
    this.brandId,
    this.cExtAttr1,
    this.cExtAttr10,
    this.cExtAttr11,
    this.cExtAttr12,
    this.cExtAttr13,
    this.cExtAttr14,
    this.cExtAttr15,
    this.cExtAttr16,
    this.cExtAttr17,
    this.cExtAttr18,
    this.cExtAttr19,
    this.cExtAttr2,
    this.cExtAttr20,
    this.cExtAttr21,
    this.cExtAttr22,
    this.cExtAttr23,
    this.cExtAttr24,
    this.cExtAttr25,
    this.cExtAttr26,
    this.cExtAttr27,
    this.cExtAttr28,
    this.cExtAttr29,
    this.cExtAttr3,
    this.cExtAttr30,
    this.cExtAttr31,
    this.cExtAttr32,
    this.cExtAttr33,
    this.cExtAttr34,
    this.cExtAttr35,
    this.cExtAttr36,
    this.cExtAttr37,
    this.cExtAttr38,
    this.cExtAttr39,
    this.cExtAttr4,
    this.cExtAttr40,
    this.cExtAttr41,
    this.cExtAttr42,
    this.cExtAttr43,
    this.cExtAttr44,
    this.cExtAttr45,
    this.cExtAttr46,
    this.cExtAttr47,
    this.cExtAttr48,
    this.cExtAttr49,
    this.cExtAttr5,
    this.cExtAttr50,
    this.cExtAttr51,
    this.cExtAttr52,
    this.cExtAttr53,
    this.cExtAttr54,
    this.cExtAttr55,
    this.cExtAttr56,
    this.cExtAttr57,
    this.cExtAttr58,
    this.cExtAttr59,
    this.cExtAttr6,
    this.cExtAttr60,
    this.cExtAttr7,
    this.cExtAttr8,
    this.cExtAttr9,
    this.cExtLov1,
    this.cExtLov2,
    this.cExtLov3,
    this.cExtLov4,
    this.cExtLov5,
    this.cbsaClass,
    this.cbsaId,
    this.cbsaName,
    this.censusDivision,
    this.censusRegion,
    this.changeReasonCode,
    this.changeReasonDesc,
    this.city,
    this.clientId,
    this.closeDate,
    this.country,
    this.county,
    this.countyFips,
    this.createdBy,
    this.creationDate,
    this.cyAnnualSales,
    this.cyEbitda,
    this.cyTrl_12MoSales,
    this.dExtAttr1,
    this.dExtAttr10,
    this.dExtAttr11,
    this.dExtAttr12,
    this.dExtAttr13,
    this.dExtAttr14,
    this.dExtAttr15,
    this.dExtAttr2,
    this.dExtAttr3,
    this.dExtAttr4,
    this.dExtAttr5,
    this.dExtAttr6,
    this.dExtAttr7,
    this.dExtAttr8,
    this.dExtAttr9,
    this.delivery,
    this.demoScore,
    this.diningArea,
    this.dmaId,
    this.dmaName,
    this.effectiveDate,
    this.facilityType,
    this.fiscalPeriod,
    this.fiscalQuarter,
    this.fiscalYear,
    this.fullAddress,
    this.landId,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.latitude,
    this.leaseExpDate,
    this.leaseOption,
    this.locEntityType,
    this.locSubEntityType,
    this.locationTypeCode,
    this.longitude,
    this.marketName,
    this.miPrinx,
    this.msaId,
    this.msaName,
    this.nExtAttr1,
    this.nExtAttr10,
    this.nExtAttr11,
    this.nExtAttr12,
    this.nExtAttr13,
    this.nExtAttr14,
    this.nExtAttr15,
    this.nExtAttr16,
    this.nExtAttr17,
    this.nExtAttr18,
    this.nExtAttr19,
    this.nExtAttr2,
    this.nExtAttr20,
    this.nExtAttr21,
    this.nExtAttr22,
    this.nExtAttr23,
    this.nExtAttr24,
    this.nExtAttr25,
    this.nExtAttr26,
    this.nExtAttr27,
    this.nExtAttr28,
    this.nExtAttr29,
    this.nExtAttr3,
    this.nExtAttr30,
    this.nExtAttr31,
    this.nExtAttr32,
    this.nExtAttr33,
    this.nExtAttr34,
    this.nExtAttr35,
    this.nExtAttr36,
    this.nExtAttr37,
    this.nExtAttr4,
    this.nExtAttr5,
    this.nExtAttr6,
    this.nExtAttr7,
    this.nExtAttr8,
    this.nExtAttr9,
    this.neighborhood,
    this.objectVersionNumber,
    this.openDate,
    this.operationHirerarchyId,
    this.operationsManager,
    this.operationsMarketId,
    this.operationsRegion,
    this.operationsSubMarktetId,
    this.operationsVp,
    this.orgId,
    this.outboundIntegFlag,
    this.ownershipType,
    this.parentLocId,
    this.pctDiffEbitda,
    this.pctDiffSales,
    this.periodEnding,
    this.phone,
    this.propertyType,
    this.pyEbitda,
    this.pyTrl_12MoSales,
    this.realEstateHierarchyId,
    this.realEstateManager,
    this.realEstateVp,
    this.relocStoreId,
    this.relocToSiteId,
    this.scId,
    this.sdaId,
    this.secAttr1,
    this.secAttr2,
    this.sisTypeCode,
    this.siteId,
    this.siteScore,
    this.siteScoreDate,
    this.state,
    this.status,
    this.storeClass,
    this.storeId,
    this.storeName,
    this.storeNumber,
    this.storeNumberErp,
    this.storeNumberLegacy,
    this.storeSize,
    this.totalAnnualRent,
    this.zip,
    this.dwgFileId,
    this.contractualType,
    this.usingTangoLease,
    this.measurementType,
    this.isSubleased,
    this.chargeType,
    this.occupancyStatus,
    this.allocationType,
    this.departmentIdCharge,
    this.departmentIdOverride,
    this.departmentIdDefault,
    this.departmentIdTemp,
    this.totalAreaSf,
    this.totalAreaSm,
    this.externalGrossSf,
    this.externalGrossSm,
    this.internalGrossSf,
    this.internalGrossSm,
    this.cadRentableSf,
    this.cadRentableSm,
    this.leaseRentableSf,
    this.leaseRentableSm,
    this.leaseRentableManualSf,
    this.leaseRentableManualSm,
    this.usableSf,
    this.usableSm,
    this.actualSf,
    this.actualSm,
    this.verticalPenetrationSf,
    this.verticalPenetrationSm,
    this.propertyCommonSf,
    this.propertyCommonSm,
    this.buildingCommonSf,
    this.buildingCommonSm,
    this.floorCommonSf,
    this.floorCommonSm,
    this.floorSharedSf,
    this.floorSharedSm,
    this.buildingSharedSf,
    this.buildingSharedSm,
    this.residualSf,
    this.residualSm,
    this.subleasedSf,
    this.subleasedSm,
    this.totalCapacity,
    this.capacityPlanned,
    this.capacityAvailable,
    this.totalHeadcount,
    this.workstationHeadcount,
    this.workstationCapacity,
    this.pctBuilding,
    this.buildingName,
    this.buildingNumber,
    this.propertyName,
    this.occupancyRate,
    this.workstationUtilization,
    this.cadLocked,
    this.lastUpdatedDate,
    this.timezone,
    this.vacantSf,
    this.vacancypercent,
    this.strategy,
    this.bldgPropName,
    this.cadLockedBy,
    this.cadCreatedBy,
    this.cadCreationDate,
    this.cadUpdatedBy,
    this.cadUpdatedDate,
    this.brandName,
    this.statusType,
    this.tempCadFile,
    this.spaceUom,
    this.vacantSm,
    this.excludeFromReserve,
    this.isManualSync,
    this.floorSeq,
  });
  BuildingData.fromJson(Map<String, dynamic> json) {
    address = json['address']?.toString();
    address2 = json['address2']?.toString();
    address3 = json['address3']?.toString();
    assetType = json['asset_type']?.toString();
    baseRentPerSize = json['base_rent_per_size']?.toDouble();
    brandId = json['brand_id']?.toInt();
    cExtAttr1 = json['c_ext_attr1']?.toString();
    cExtAttr10 = json['c_ext_attr10']?.toString();
    cExtAttr11 = json['c_ext_attr11']?.toString();
    cExtAttr12 = json['c_ext_attr12']?.toString();
    cExtAttr13 = json['c_ext_attr13']?.toString();
    cExtAttr14 = json['c_ext_attr14']?.toString();
    cExtAttr15 = json['c_ext_attr15']?.toString();
    cExtAttr16 = json['c_ext_attr16']?.toString();
    cExtAttr17 = json['c_ext_attr17']?.toString();
    cExtAttr18 = json['c_ext_attr18']?.toString();
    cExtAttr19 = json['c_ext_attr19']?.toString();
    cExtAttr2 = json['c_ext_attr2']?.toString();
    cExtAttr20 = json['c_ext_attr20']?.toString();
    cExtAttr21 = json['c_ext_attr21']?.toString();
    cExtAttr22 = json['c_ext_attr22']?.toString();
    cExtAttr23 = json['c_ext_attr23']?.toString();
    cExtAttr24 = json['c_ext_attr24']?.toString();
    cExtAttr25 = json['c_ext_attr25']?.toString();
    cExtAttr26 = json['c_ext_attr26']?.toString();
    cExtAttr27 = json['c_ext_attr27']?.toString();
    cExtAttr28 = json['c_ext_attr28']?.toString();
    cExtAttr29 = json['c_ext_attr29']?.toString();
    cExtAttr3 = json['c_ext_attr3']?.toString();
    cExtAttr30 = json['c_ext_attr30']?.toString();
    cExtAttr31 = json['c_ext_attr31']?.toString();
    cExtAttr32 = json['c_ext_attr32']?.toString();
    cExtAttr33 = json['c_ext_attr33']?.toString();
    cExtAttr34 = json['c_ext_attr34']?.toString();
    cExtAttr35 = json['c_ext_attr35']?.toString();
    cExtAttr36 = json['c_ext_attr36']?.toString();
    cExtAttr37 = json['c_ext_attr37']?.toString();
    cExtAttr38 = json['c_ext_attr38']?.toString();
    cExtAttr39 = json['c_ext_attr39']?.toString();
    cExtAttr4 = json['c_ext_attr4']?.toString();
    cExtAttr40 = json['c_ext_attr40']?.toString();
    cExtAttr41 = json['c_ext_attr41']?.toString();
    cExtAttr42 = json['c_ext_attr42']?.toString();
    cExtAttr43 = json['c_ext_attr43']?.toString();
    cExtAttr44 = json['c_ext_attr44']?.toString();
    cExtAttr45 = json['c_ext_attr45']?.toString();
    cExtAttr46 = json['c_ext_attr46']?.toString();
    cExtAttr47 = json['c_ext_attr47']?.toString();
    cExtAttr48 = json['c_ext_attr48']?.toString();
    cExtAttr49 = json['c_ext_attr49']?.toString();
    cExtAttr5 = json['c_ext_attr5']?.toString();
    cExtAttr50 = json['c_ext_attr50']?.toString();
    cExtAttr51 = json['c_ext_attr51']?.toString();
    cExtAttr52 = json['c_ext_attr52']?.toString();
    cExtAttr53 = json['c_ext_attr53']?.toString();
    cExtAttr54 = json['c_ext_attr54']?.toString();
    cExtAttr55 = json['c_ext_attr55']?.toString();
    cExtAttr56 = json['c_ext_attr56']?.toString();
    cExtAttr57 = json['c_ext_attr57']?.toString();
    cExtAttr58 = json['c_ext_attr58']?.toString();
    cExtAttr59 = json['c_ext_attr59']?.toString();
    cExtAttr6 = json['c_ext_attr6']?.toString();
    cExtAttr60 = json['c_ext_attr60']?.toString();
    cExtAttr7 = json['c_ext_attr7']?.toString();
    cExtAttr8 = json['c_ext_attr8']?.toString();
    cExtAttr9 = json['c_ext_attr9']?.toString();
    cExtLov1 = json['c_ext_lov1']?.toString();
    cExtLov2 = json['c_ext_lov2']?.toString();
    cExtLov3 = json['c_ext_lov3']?.toString();
    cExtLov4 = json['c_ext_lov4']?.toString();
    cExtLov5 = json['c_ext_lov5']?.toString();
    cbsaClass = json['cbsa_class']?.toString();
    cbsaId = json['cbsa_id']?.toInt();
    cbsaName = json['cbsa_name']?.toString();
    censusDivision = json['census_division']?.toString();
    censusRegion = json['census_region']?.toString();
    changeReasonCode = json['change_reason_code']?.toString();
    changeReasonDesc = json['change_reason_desc']?.toString();
    city = json['city']?.toString();
    clientId = json['client_id']?.toInt();
    closeDate = json['close_date']?.toString();
    country = json['country']?.toString();
    county = json['county']?.toString();
    countyFips = json['county_fips']?.toString();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    cyAnnualSales = json['cy_annual_sales']?.toDouble();
    cyEbitda = json['cy_ebitda']?.toDouble();
    cyTrl_12MoSales = json['cy_trl_12_mo_sales']?.toDouble();
    dExtAttr1 = json['d_ext_attr1']?.toString();
    dExtAttr10 = json['d_ext_attr10']?.toString();
    dExtAttr11 = json['d_ext_attr11']?.toString();
    dExtAttr12 = json['d_ext_attr12']?.toString();
    dExtAttr13 = json['d_ext_attr13']?.toString();
    dExtAttr14 = json['d_ext_attr14']?.toString();
    dExtAttr15 = json['d_ext_attr15']?.toString();
    dExtAttr2 = json['d_ext_attr2']?.toString();
    dExtAttr3 = json['d_ext_attr3']?.toString();
    dExtAttr4 = json['d_ext_attr4']?.toString();
    dExtAttr5 = json['d_ext_attr5']?.toString();
    dExtAttr6 = json['d_ext_attr6']?.toString();
    dExtAttr7 = json['d_ext_attr7']?.toString();
    dExtAttr8 = json['d_ext_attr8']?.toString();
    dExtAttr9 = json['d_ext_attr9']?.toString();
    delivery = json['delivery']?.toString();
    demoScore = json['demo_score']?.toDouble();
    diningArea = json['dining_area']?.toString();
    dmaId = json['dma_id']?.toInt();
    dmaName = json['dma_name']?.toString();
    effectiveDate = json['effective_date']?.toString();
    facilityType = json['facility_type']?.toString();
    fiscalPeriod = json['fiscal_period']?.toInt();
    fiscalQuarter = json['fiscal_quarter']?.toInt();
    fiscalYear = json['fiscal_year']?.toInt();
    fullAddress = json['full_address']?.toString();
    landId = json['land_id']?.toInt();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    latitude = json['latitude']?.toDouble();
    leaseExpDate = json['lease_exp_date']?.toString();
    leaseOption = json['lease_option']?.toString();
    locEntityType = json['loc_entity_type']?.toString();
    locSubEntityType = json['loc_sub_entity_type']?.toString();
    locationTypeCode = json['location_type_code']?.toString();
    longitude = json['longitude']?.toDouble();
    marketName = json['market_name']?.toString();
    miPrinx = json['mi_prinx']?.toDouble();
    msaId = json['msa_id']?.toInt();
    msaName = json['msa_name']?.toString();
    nExtAttr1 = json['n_ext_attr1']?.toDouble();
    nExtAttr10 = json['n_ext_attr10']?.toDouble();
    nExtAttr11 = json['n_ext_attr11']?.toDouble();
    nExtAttr12 = json['n_ext_attr12']?.toDouble();
    nExtAttr13 = json['n_ext_attr13']?.toDouble();
    nExtAttr14 = json['n_ext_attr14']?.toDouble();
    nExtAttr15 = json['n_ext_attr15']?.toDouble();
    nExtAttr16 = json['n_ext_attr16']?.toDouble();
    nExtAttr17 = json['n_ext_attr17']?.toDouble();
    nExtAttr18 = json['n_ext_attr18']?.toDouble();
    nExtAttr19 = json['n_ext_attr19']?.toDouble();
    nExtAttr2 = json['n_ext_attr2']?.toDouble();
    nExtAttr20 = json['n_ext_attr20']?.toDouble();
    nExtAttr21 = json['n_ext_attr21']?.toDouble();
    nExtAttr22 = json['n_ext_attr22']?.toDouble();
    nExtAttr23 = json['n_ext_attr23']?.toDouble();
    nExtAttr24 = json['n_ext_attr24']?.toDouble();
    nExtAttr25 = json['n_ext_attr25']?.toDouble();
    nExtAttr26 = json['n_ext_attr26']?.toDouble();
    nExtAttr27 = json['n_ext_attr27']?.toDouble();
    nExtAttr28 = json['n_ext_attr28']?.toDouble();
    nExtAttr29 = json['n_ext_attr29']?.toDouble();
    nExtAttr3 = json['n_ext_attr3']?.toDouble();
    nExtAttr30 = json['n_ext_attr30']?.toDouble();
    nExtAttr31 = json['n_ext_attr31']?.toDouble();
    nExtAttr32 = json['n_ext_attr32']?.toDouble();
    nExtAttr33 = json['n_ext_attr33']?.toDouble();
    nExtAttr34 = json['n_ext_attr34']?.toDouble();
    nExtAttr35 = json['n_ext_attr35']?.toDouble();
    nExtAttr36 = json['n_ext_attr36']?.toDouble();
    nExtAttr37 = json['n_ext_attr37']?.toDouble();
    nExtAttr4 = json['n_ext_attr4']?.toDouble();
    nExtAttr5 = json['n_ext_attr5']?.toDouble();
    nExtAttr6 = json['n_ext_attr6']?.toDouble();
    nExtAttr7 = json['n_ext_attr7']?.toDouble();
    nExtAttr8 = json['n_ext_attr8']?.toDouble();
    nExtAttr9 = json['n_ext_attr9']?.toDouble();
    neighborhood = json['neighborhood']?.toString();
    objectVersionNumber = json['object_version_number']?.toInt();
    openDate = json['open_date']?.toDouble();
    operationHirerarchyId = json['operation_hirerarchy_id']?.toInt();
    operationsManager = json['operations_manager']?.toString();
    operationsMarketId = json['operations_market_id']?.toInt();
    operationsRegion = json['operations_region']?.toString();
    operationsSubMarktetId = json['operations_sub_marktet_id']?.toInt();
    operationsVp = json['operations_vp']?.toString();
    orgId = json['org_id']?.toInt();
    outboundIntegFlag = json['outbound_integ_flag']?.toString();
    ownershipType = json['ownership_type']?.toString();
    parentLocId = json['parent_loc_id']?.toInt();
    pctDiffEbitda = json['pct_diff_ebitda']?.toDouble();
    pctDiffSales = json['pct_diff_sales']?.toDouble();
    periodEnding = json['period_ending']?.toDouble();
    phone = json['phone']?.toString();
    propertyType = json['property_type']?.toString();
    pyEbitda = json['py_ebitda']?.toDouble();
    pyTrl_12MoSales = json['py_trl_12_mo_sales']?.toDouble();
    realEstateHierarchyId = json['real_estate_hierarchy_id']?.toInt();
    realEstateManager = json['real_estate_manager']?.toString();
    realEstateVp = json['real_estate_vp']?.toString();
    relocStoreId = json['reloc_store_id']?.toInt();
    relocToSiteId = json['reloc_to_site_id']?.toInt();
    scId = json['sc_id']?.toString();
    sdaId = json['sda_id']?.toInt();
    secAttr1 = json['sec_attr1']?.toString();
    secAttr2 = json['sec_attr2']?.toString();
    sisTypeCode = json['sis_type_code']?.toString();
    siteId = json['site_id']?.toInt();
    siteScore = json['site_score']?.toInt();
    siteScoreDate = json['site_score_date']?.toString();
    state = json['state']?.toString();
    status = json['status']?.toString();
    storeClass = json['store_class']?.toString();
    storeId = json['store_id']?.toInt();
    storeName = json['store_name']?.toString();
    storeNumber = json['store_number']?.toString();
    storeNumberErp = json['store_number_erp']?.toString();
    storeNumberLegacy = json['store_number_legacy']?.toString();
    storeSize = json['store_size']?.toDouble();
    totalAnnualRent = json['total_annual_rent']?.toDouble();
    zip = json['zip']?.toString();
    dwgFileId = json['dwg_file_id']?.toInt();
    contractualType = json['contractual_type']?.toString();
    usingTangoLease = json['using_tango_lease']?.toString();
    measurementType = json['measurement_type']?.toString();
    isSubleased = json['is_subleased']?.toString();
    chargeType = json['charge_type']?.toString();
    occupancyStatus = json['occupancy_status']?.toString();
    allocationType = json['allocation_type']?.toString();
    departmentIdCharge = json['department_id_charge']?.toDouble();
    departmentIdOverride = json['department_id_override']?.toDouble();
    departmentIdDefault = json['department_id_default']?.toDouble();
    departmentIdTemp = json['department_id_temp']?.toDouble();
    totalAreaSf = json['total_area_sf']?.toDouble();
    totalAreaSm = json['total_area_sm']?.toInt();
    externalGrossSf = json['external_gross_sf']?.toDouble();
    externalGrossSm = json['external_gross_sm']?.toDouble();
    internalGrossSf = json['internal_gross_sf']?.toDouble();
    internalGrossSm = json['internal_gross_sm']?.toDouble();
    cadRentableSf = json['cad_rentable_sf']?.toDouble();
    cadRentableSm = json['cad_rentable_sm']?.toInt();
    leaseRentableSf = json['lease_rentable_sf']?.toDouble();
    leaseRentableSm = json['lease_rentable_sm']?.toInt();
    leaseRentableManualSf = json['lease_rentable_manual_sf']?.toInt();
    leaseRentableManualSm = json['lease_rentable_manual_sm']?.toDouble();
    usableSf = json['usable_sf']?.toInt();
    usableSm = json['usable_sm']?.toInt();
    actualSf = json['actual_sf']?.toDouble();
    actualSm = json['actual_sm']?.toInt();
    verticalPenetrationSf = json['vertical_penetration_sf']?.toDouble();
    verticalPenetrationSm = json['vertical_penetration_sm']?.toInt();
    propertyCommonSf = json['property_common_sf']?.toDouble();
    propertyCommonSm = json['property_common_sm']?.toInt();
    buildingCommonSf = json['building_common_sf']?.toInt();
    buildingCommonSm = json['building_common_sm']?.toInt();
    floorCommonSf = json['floor_common_sf']?.toDouble();
    floorCommonSm = json['floor_common_sm']?.toInt();
    floorSharedSf = json['floor_shared_sf']?.toDouble();
    floorSharedSm = json['floor_shared_sm']?.toInt();
    buildingSharedSf = json['building_shared_sf']?.toDouble();
    buildingSharedSm = json['building_shared_sm']?.toInt();
    residualSf = json['residual_sf']?.toInt();
    residualSm = json['residual_sm']?.toInt();
    subleasedSf = json['subleased_sf']?.toDouble();
    subleasedSm = json['subleased_sm']?.toInt();
    totalCapacity = json['total_capacity']?.toDouble();
    capacityPlanned = json['capacity_planned']?.toDouble();
    capacityAvailable = json['capacity_available']?.toDouble();
    totalHeadcount = json['total_headcount']?.toInt();
    workstationHeadcount = json['workstation_headcount']?.toInt();
    workstationCapacity = json['workstation_capacity']?.toInt();
    pctBuilding = json['pct_building']?.toInt();
    buildingName = json['building_name']?.toString();
    buildingNumber = json['building_number']?.toString();
    propertyName = json['property_name']?.toString();
    occupancyRate = json['occupancy_rate']?.toDouble();
    workstationUtilization = json['workstation_utilization']?.toDouble();
    cadLocked = json['cad_locked']?.toString();
    lastUpdatedDate = json['last_updated_date']?.toString();
    timezone = json['timezone']?.toString();
    vacantSf = json['vacant_sf']?.toDouble();
    vacancypercent = json['vacancypercent']?.toDouble();
    strategy = json['strategy']?.toString();
    bldgPropName = json['bldg_prop_name']?.toString();
    cadLockedBy = json['cad_locked_by']?.toString();
    cadCreatedBy = json['cad_created_by']?.toString();
    cadCreationDate = json['cad_creation_date']?.toString();
    cadUpdatedBy = json['cad_updated_by']?.toString();
    cadUpdatedDate = json['cad_updated_date']?.toString();
    brandName = json['brand_name']?.toString();
    statusType = json['status_type']?.toString();
    tempCadFile = json['temp_cad_file']?.toString();
    spaceUom = json['space_uom']?.toString();
    vacantSm = json['vacant_sm']?.toInt();
    excludeFromReserve = json['exclude_from_reserve']?.toString();
    isManualSync = json['is_manual_sync']?.toString();
    floorSeq = json['floor_seq']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    data['address2'] = address2;
    data['address3'] = address3;
    data['asset_type'] = assetType;
    data['base_rent_per_size'] = baseRentPerSize;
    data['brand_id'] = brandId;
    data['c_ext_attr1'] = cExtAttr1;
    data['c_ext_attr10'] = cExtAttr10;
    data['c_ext_attr11'] = cExtAttr11;
    data['c_ext_attr12'] = cExtAttr12;
    data['c_ext_attr13'] = cExtAttr13;
    data['c_ext_attr14'] = cExtAttr14;
    data['c_ext_attr15'] = cExtAttr15;
    data['c_ext_attr16'] = cExtAttr16;
    data['c_ext_attr17'] = cExtAttr17;
    data['c_ext_attr18'] = cExtAttr18;
    data['c_ext_attr19'] = cExtAttr19;
    data['c_ext_attr2'] = cExtAttr2;
    data['c_ext_attr20'] = cExtAttr20;
    data['c_ext_attr21'] = cExtAttr21;
    data['c_ext_attr22'] = cExtAttr22;
    data['c_ext_attr23'] = cExtAttr23;
    data['c_ext_attr24'] = cExtAttr24;
    data['c_ext_attr25'] = cExtAttr25;
    data['c_ext_attr26'] = cExtAttr26;
    data['c_ext_attr27'] = cExtAttr27;
    data['c_ext_attr28'] = cExtAttr28;
    data['c_ext_attr29'] = cExtAttr29;
    data['c_ext_attr3'] = cExtAttr3;
    data['c_ext_attr30'] = cExtAttr30;
    data['c_ext_attr31'] = cExtAttr31;
    data['c_ext_attr32'] = cExtAttr32;
    data['c_ext_attr33'] = cExtAttr33;
    data['c_ext_attr34'] = cExtAttr34;
    data['c_ext_attr35'] = cExtAttr35;
    data['c_ext_attr36'] = cExtAttr36;
    data['c_ext_attr37'] = cExtAttr37;
    data['c_ext_attr38'] = cExtAttr38;
    data['c_ext_attr39'] = cExtAttr39;
    data['c_ext_attr4'] = cExtAttr4;
    data['c_ext_attr40'] = cExtAttr40;
    data['c_ext_attr41'] = cExtAttr41;
    data['c_ext_attr42'] = cExtAttr42;
    data['c_ext_attr43'] = cExtAttr43;
    data['c_ext_attr44'] = cExtAttr44;
    data['c_ext_attr45'] = cExtAttr45;
    data['c_ext_attr46'] = cExtAttr46;
    data['c_ext_attr47'] = cExtAttr47;
    data['c_ext_attr48'] = cExtAttr48;
    data['c_ext_attr49'] = cExtAttr49;
    data['c_ext_attr5'] = cExtAttr5;
    data['c_ext_attr50'] = cExtAttr50;
    data['c_ext_attr51'] = cExtAttr51;
    data['c_ext_attr52'] = cExtAttr52;
    data['c_ext_attr53'] = cExtAttr53;
    data['c_ext_attr54'] = cExtAttr54;
    data['c_ext_attr55'] = cExtAttr55;
    data['c_ext_attr56'] = cExtAttr56;
    data['c_ext_attr57'] = cExtAttr57;
    data['c_ext_attr58'] = cExtAttr58;
    data['c_ext_attr59'] = cExtAttr59;
    data['c_ext_attr6'] = cExtAttr6;
    data['c_ext_attr60'] = cExtAttr60;
    data['c_ext_attr7'] = cExtAttr7;
    data['c_ext_attr8'] = cExtAttr8;
    data['c_ext_attr9'] = cExtAttr9;
    data['c_ext_lov1'] = cExtLov1;
    data['c_ext_lov2'] = cExtLov2;
    data['c_ext_lov3'] = cExtLov3;
    data['c_ext_lov4'] = cExtLov4;
    data['c_ext_lov5'] = cExtLov5;
    data['cbsa_class'] = cbsaClass;
    data['cbsa_id'] = cbsaId;
    data['cbsa_name'] = cbsaName;
    data['census_division'] = censusDivision;
    data['census_region'] = censusRegion;
    data['change_reason_code'] = changeReasonCode;
    data['change_reason_desc'] = changeReasonDesc;
    data['city'] = city;
    data['client_id'] = clientId;
    data['close_date'] = closeDate;
    data['country'] = country;
    data['county'] = county;
    data['county_fips'] = countyFips;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['cy_annual_sales'] = cyAnnualSales;
    data['cy_ebitda'] = cyEbitda;
    data['cy_trl_12_mo_sales'] = cyTrl_12MoSales;
    data['d_ext_attr1'] = dExtAttr1;
    data['d_ext_attr10'] = dExtAttr10;
    data['d_ext_attr11'] = dExtAttr11;
    data['d_ext_attr12'] = dExtAttr12;
    data['d_ext_attr13'] = dExtAttr13;
    data['d_ext_attr14'] = dExtAttr14;
    data['d_ext_attr15'] = dExtAttr15;
    data['d_ext_attr2'] = dExtAttr2;
    data['d_ext_attr3'] = dExtAttr3;
    data['d_ext_attr4'] = dExtAttr4;
    data['d_ext_attr5'] = dExtAttr5;
    data['d_ext_attr6'] = dExtAttr6;
    data['d_ext_attr7'] = dExtAttr7;
    data['d_ext_attr8'] = dExtAttr8;
    data['d_ext_attr9'] = dExtAttr9;
    data['delivery'] = delivery;
    data['demo_score'] = demoScore;
    data['dining_area'] = diningArea;
    data['dma_id'] = dmaId;
    data['dma_name'] = dmaName;
    data['effective_date'] = effectiveDate;
    data['facility_type'] = facilityType;
    data['fiscal_period'] = fiscalPeriod;
    data['fiscal_quarter'] = fiscalQuarter;
    data['fiscal_year'] = fiscalYear;
    data['full_address'] = fullAddress;
    data['land_id'] = landId;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['latitude'] = latitude;
    data['lease_exp_date'] = leaseExpDate;
    data['lease_option'] = leaseOption;
    data['loc_entity_type'] = locEntityType;
    data['loc_sub_entity_type'] = locSubEntityType;
    data['location_type_code'] = locationTypeCode;
    data['longitude'] = longitude;
    data['market_name'] = marketName;
    data['mi_prinx'] = miPrinx;
    data['msa_id'] = msaId;
    data['msa_name'] = msaName;
    data['n_ext_attr1'] = nExtAttr1;
    data['n_ext_attr10'] = nExtAttr10;
    data['n_ext_attr11'] = nExtAttr11;
    data['n_ext_attr12'] = nExtAttr12;
    data['n_ext_attr13'] = nExtAttr13;
    data['n_ext_attr14'] = nExtAttr14;
    data['n_ext_attr15'] = nExtAttr15;
    data['n_ext_attr16'] = nExtAttr16;
    data['n_ext_attr17'] = nExtAttr17;
    data['n_ext_attr18'] = nExtAttr18;
    data['n_ext_attr19'] = nExtAttr19;
    data['n_ext_attr2'] = nExtAttr2;
    data['n_ext_attr20'] = nExtAttr20;
    data['n_ext_attr21'] = nExtAttr21;
    data['n_ext_attr22'] = nExtAttr22;
    data['n_ext_attr23'] = nExtAttr23;
    data['n_ext_attr24'] = nExtAttr24;
    data['n_ext_attr25'] = nExtAttr25;
    data['n_ext_attr26'] = nExtAttr26;
    data['n_ext_attr27'] = nExtAttr27;
    data['n_ext_attr28'] = nExtAttr28;
    data['n_ext_attr29'] = nExtAttr29;
    data['n_ext_attr3'] = nExtAttr3;
    data['n_ext_attr30'] = nExtAttr30;
    data['n_ext_attr31'] = nExtAttr31;
    data['n_ext_attr32'] = nExtAttr32;
    data['n_ext_attr33'] = nExtAttr33;
    data['n_ext_attr34'] = nExtAttr34;
    data['n_ext_attr35'] = nExtAttr35;
    data['n_ext_attr36'] = nExtAttr36;
    data['n_ext_attr37'] = nExtAttr37;
    data['n_ext_attr4'] = nExtAttr4;
    data['n_ext_attr5'] = nExtAttr5;
    data['n_ext_attr6'] = nExtAttr6;
    data['n_ext_attr7'] = nExtAttr7;
    data['n_ext_attr8'] = nExtAttr8;
    data['n_ext_attr9'] = nExtAttr9;
    data['neighborhood'] = neighborhood;
    data['object_version_number'] = objectVersionNumber;
    data['open_date'] = openDate;
    data['operation_hirerarchy_id'] = operationHirerarchyId;
    data['operations_manager'] = operationsManager;
    data['operations_market_id'] = operationsMarketId;
    data['operations_region'] = operationsRegion;
    data['operations_sub_marktet_id'] = operationsSubMarktetId;
    data['operations_vp'] = operationsVp;
    data['org_id'] = orgId;
    data['outbound_integ_flag'] = outboundIntegFlag;
    data['ownership_type'] = ownershipType;
    data['parent_loc_id'] = parentLocId;
    data['pct_diff_ebitda'] = pctDiffEbitda;
    data['pct_diff_sales'] = pctDiffSales;
    data['period_ending'] = periodEnding;
    data['phone'] = phone;
    data['property_type'] = propertyType;
    data['py_ebitda'] = pyEbitda;
    data['py_trl_12_mo_sales'] = pyTrl_12MoSales;
    data['real_estate_hierarchy_id'] = realEstateHierarchyId;
    data['real_estate_manager'] = realEstateManager;
    data['real_estate_vp'] = realEstateVp;
    data['reloc_store_id'] = relocStoreId;
    data['reloc_to_site_id'] = relocToSiteId;
    data['sc_id'] = scId;
    data['sda_id'] = sdaId;
    data['sec_attr1'] = secAttr1;
    data['sec_attr2'] = secAttr2;
    data['sis_type_code'] = sisTypeCode;
    data['site_id'] = siteId;
    data['site_score'] = siteScore;
    data['site_score_date'] = siteScoreDate;
    data['state'] = state;
    data['status'] = status;
    data['store_class'] = storeClass;
    data['store_id'] = storeId;
    data['store_name'] = storeName;
    data['store_number'] = storeNumber;
    data['store_number_erp'] = storeNumberErp;
    data['store_number_legacy'] = storeNumberLegacy;
    data['store_size'] = storeSize;
    data['total_annual_rent'] = totalAnnualRent;
    data['zip'] = zip;
    data['dwg_file_id'] = dwgFileId;
    data['contractual_type'] = contractualType;
    data['using_tango_lease'] = usingTangoLease;
    data['measurement_type'] = measurementType;
    data['is_subleased'] = isSubleased;
    data['charge_type'] = chargeType;
    data['occupancy_status'] = occupancyStatus;
    data['allocation_type'] = allocationType;
    data['department_id_charge'] = departmentIdCharge;
    data['department_id_override'] = departmentIdOverride;
    data['department_id_default'] = departmentIdDefault;
    data['department_id_temp'] = departmentIdTemp;
    data['total_area_sf'] = totalAreaSf;
    data['total_area_sm'] = totalAreaSm;
    data['external_gross_sf'] = externalGrossSf;
    data['external_gross_sm'] = externalGrossSm;
    data['internal_gross_sf'] = internalGrossSf;
    data['internal_gross_sm'] = internalGrossSm;
    data['cad_rentable_sf'] = cadRentableSf;
    data['cad_rentable_sm'] = cadRentableSm;
    data['lease_rentable_sf'] = leaseRentableSf;
    data['lease_rentable_sm'] = leaseRentableSm;
    data['lease_rentable_manual_sf'] = leaseRentableManualSf;
    data['lease_rentable_manual_sm'] = leaseRentableManualSm;
    data['usable_sf'] = usableSf;
    data['usable_sm'] = usableSm;
    data['actual_sf'] = actualSf;
    data['actual_sm'] = actualSm;
    data['vertical_penetration_sf'] = verticalPenetrationSf;
    data['vertical_penetration_sm'] = verticalPenetrationSm;
    data['property_common_sf'] = propertyCommonSf;
    data['property_common_sm'] = propertyCommonSm;
    data['building_common_sf'] = buildingCommonSf;
    data['building_common_sm'] = buildingCommonSm;
    data['floor_common_sf'] = floorCommonSf;
    data['floor_common_sm'] = floorCommonSm;
    data['floor_shared_sf'] = floorSharedSf;
    data['floor_shared_sm'] = floorSharedSm;
    data['building_shared_sf'] = buildingSharedSf;
    data['building_shared_sm'] = buildingSharedSm;
    data['residual_sf'] = residualSf;
    data['residual_sm'] = residualSm;
    data['subleased_sf'] = subleasedSf;
    data['subleased_sm'] = subleasedSm;
    data['total_capacity'] = totalCapacity;
    data['capacity_planned'] = capacityPlanned;
    data['capacity_available'] = capacityAvailable;
    data['total_headcount'] = totalHeadcount;
    data['workstation_headcount'] = workstationHeadcount;
    data['workstation_capacity'] = workstationCapacity;
    data['pct_building'] = pctBuilding;
    data['building_name'] = buildingName;
    data['building_number'] = buildingNumber;
    data['property_name'] = propertyName;
    data['occupancy_rate'] = occupancyRate;
    data['workstation_utilization'] = workstationUtilization;
    data['cad_locked'] = cadLocked;
    data['last_updated_date'] = lastUpdatedDate;
    data['timezone'] = timezone;
    data['vacant_sf'] = vacantSf;
    data['vacancypercent'] = vacancypercent;
    data['strategy'] = strategy;
    data['bldg_prop_name'] = bldgPropName;
    data['cad_locked_by'] = cadLockedBy;
    data['cad_created_by'] = cadCreatedBy;
    data['cad_creation_date'] = cadCreationDate;
    data['cad_updated_by'] = cadUpdatedBy;
    data['cad_updated_date'] = cadUpdatedDate;
    data['brand_name'] = brandName;
    data['status_type'] = statusType;
    data['temp_cad_file'] = tempCadFile;
    data['space_uom'] = spaceUom;
    data['vacant_sm'] = vacantSm;
    data['exclude_from_reserve'] = excludeFromReserve;
    data['is_manual_sync'] = isManualSync;
    data['floor_seq'] = floorSeq;
    return data;
  }
}
