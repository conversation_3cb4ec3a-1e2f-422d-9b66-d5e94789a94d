class ActiveReserveBuilding {
  int? storeId;
  String? storeNumber;
  String? storeName;
  String? locEntityType;
  String? locSubEntityType;
  String? parentLocId;
  int? properityId;
  String? status;
  int? dwgFileId;
  String? buildingStatusType;

  ActiveReserveBuilding({
    this.storeId,
    this.storeNumber,
    this.storeName,
    this.locEntityType,
    this.locSubEntityType,
    this.parentLocId,
    this.properityId,
    this.status,
    this.dwgFileId,
    this.buildingStatusType,
  });
  ActiveReserveBuilding.fromJson(Map<String, dynamic> json) {
    storeId = json['store_id']?.toInt();
    storeNumber = json['store_number']?.toString();
    storeName = json['store_name']?.toString();
    locEntityType = json['loc_entity_type']?.toString();
    locSubEntityType = json['loc_sub_entity_type']?.toString();
    parentLocId = json['parent_loc_id']?.toString();
    properityId = json['properity_id']?.toInt();
    status = json['status']?.toString();
    dwgFileId = json['dwg_file_id']?.toInt();
    buildingStatusType = json['building_status_type']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['store_id'] = storeId;
    data['store_number'] = storeNumber;
    data['store_name'] = storeName;
    data['loc_entity_type'] = locEntityType;
    data['loc_sub_entity_type'] = locSubEntityType;
    data['parent_loc_id'] = parentLocId;
    data['properity_id'] = properityId;
    data['status'] = status;
    data['dwg_file_id'] = dwgFileId;
    data['building_status_type'] = buildingStatusType;
    return data;
  }
}
