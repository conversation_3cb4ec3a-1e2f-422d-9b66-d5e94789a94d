class BuildingViewData {
  String? address;
  String? allColumns;
  int? brandId;
  String? city;
  int? clientId;
  String? locEntityType;
  String? locSubEntityType;
  String? locationTypeCode;
  String? openDate;
  String? ownershipType;
  String? state;
  String? status;
  int? storeId;
  String? storeName;
  String? storeNumber;
  String? zipCode;
  String? bomaType;
  double? buildingActualArea;
  double? buildingArea;
  double? buildingCommonArea;
  double? buildingCoreArea;
  double? buildingOwnedArea;
  double? buildingResidualArea;
  double? buildingSubleasedArea;
  double? buildingUsableArea;
  double? cadRentableArea;
  double? capacityAvailable;
  double? capacityPlanned;
  int? departmentId;
  String? departmentOverride;
  double? externalGrossArea;
  double? floorCommonArea;
  double? internalGrossArea;
  String? isShiftSpace;
  String? isSubLeased;
  double? leaseRentable;
  String? measurementType;
  int? occupancyCount;
  String? occupancyStatus;
  int? parentLocId;
  String? portfolio;
  double? propertyCommonArea;
  String? spaceCategory;
  String? spaceType;
  String? spaceUse;
  String? timeZone;
  double? totalCapacity;
  int? totalHeadCount;
  String? usingTangoLease;
  int? workstationCapacity;
  int? workstationHeadCount;
  double? utilization;
  double? occupancyrate;
  double? wkstoccupancyrate;
  int? floorcount;
  double? rsfperwkst;
  double? rsfperemp;
  double? latitude;
  double? longitude;
  String? parentSpaceName;
  String? parentSpaceNumber;
  double? vacancypercent;
  double? vacantArea;
  String? leaseExpiryDate;
  String? strategy;
  String? createdBy;
  String? propertyName;
  int? dwgFileId;
  String? tempCadFile;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? lastUpdatedDate;
  String? country;
  String? buildingStatusType;
  String? floorWithoutCurrentcad;
  int? propertyId;
  int? buildingActualAreaSm;
  double? buildingUsableAreaSm;
  double? cadRentableAreaSm;
  double? internalGrossAreaSm;
  String? isManualSync;
  double? floorSeq;

  BuildingViewData({
    this.address,
    this.allColumns,
    this.brandId,
    this.city,
    this.clientId,
    this.locEntityType,
    this.locSubEntityType,
    this.locationTypeCode,
    this.openDate,
    this.ownershipType,
    this.state,
    this.status,
    this.storeId,
    this.storeName,
    this.storeNumber,
    this.zipCode,
    this.bomaType,
    this.buildingActualArea,
    this.buildingArea,
    this.buildingCommonArea,
    this.buildingCoreArea,
    this.buildingOwnedArea,
    this.buildingResidualArea,
    this.buildingSubleasedArea,
    this.buildingUsableArea,
    this.cadRentableArea,
    this.capacityAvailable,
    this.capacityPlanned,
    this.departmentId,
    this.departmentOverride,
    this.externalGrossArea,
    this.floorCommonArea,
    this.internalGrossArea,
    this.isShiftSpace,
    this.isSubLeased,
    this.leaseRentable,
    this.measurementType,
    this.occupancyCount,
    this.occupancyStatus,
    this.parentLocId,
    this.portfolio,
    this.propertyCommonArea,
    this.spaceCategory,
    this.spaceType,
    this.spaceUse,
    this.timeZone,
    this.totalCapacity,
    this.totalHeadCount,
    this.usingTangoLease,
    this.workstationCapacity,
    this.workstationHeadCount,
    this.utilization,
    this.occupancyrate,
    this.wkstoccupancyrate,
    this.floorcount,
    this.rsfperwkst,
    this.rsfperemp,
    this.latitude,
    this.longitude,
    this.parentSpaceName,
    this.parentSpaceNumber,
    this.vacancypercent,
    this.vacantArea,
    this.leaseExpiryDate,
    this.strategy,
    this.createdBy,
    this.propertyName,
    this.dwgFileId,
    this.tempCadFile,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.lastUpdatedDate,
    this.country,
    this.buildingStatusType,
    this.floorWithoutCurrentcad,
    this.propertyId,
    this.buildingActualAreaSm,
    this.buildingUsableAreaSm,
    this.cadRentableAreaSm,
    this.internalGrossAreaSm,
    this.isManualSync,
    this.floorSeq,
  });
  BuildingViewData.fromJson(Map<String, dynamic> json) {
    address = json['address']?.toString();
    allColumns = json['all_columns']?.toString();
    brandId = json['brand_id']?.toInt();
    city = json['city']?.toString();
    clientId = json['client_id']?.toInt();
    locEntityType = json['loc_entity_type']?.toString();
    locSubEntityType = json['loc_sub_entity_type']?.toString();
    locationTypeCode = json['location_type_code']?.toString();
    openDate = json['open_date']?.toString();
    ownershipType = json['ownership_type']?.toString();
    state = json['state']?.toString();
    status = json['status']?.toString();
    storeId = json['store_id']?.toInt();
    storeName = json['store_name']?.toString();
    storeNumber = json['store_number']?.toString();
    zipCode = json['zip_code']?.toString();
    bomaType = json['boma_type']?.toString();
    buildingActualArea = json['building_actual_area']?.toDouble();
    buildingArea = json['building_area']?.toDouble();
    buildingCommonArea = json['building_common_area']?.toDouble();
    buildingCoreArea = json['building_core_area']?.toDouble();
    buildingOwnedArea = json['building_owned_area']?.toDouble();
    buildingResidualArea = json['building_residual_area']?.toDouble();
    buildingSubleasedArea = json['building_subleased_area']?.toDouble();
    buildingUsableArea = json['building_usable_area']?.toDouble();
    cadRentableArea = json['cad_rentable_area']?.toDouble();
    capacityAvailable = json['capacity_available']?.toDouble();
    capacityPlanned = json['capacity_planned']?.toDouble();
    departmentId = json['department_id']?.toInt();
    departmentOverride = json['department_override']?.toString();
    externalGrossArea = json['external_gross_area']?.toDouble();
    floorCommonArea = json['floor_common_area']?.toDouble();
    internalGrossArea = json['internal_gross_area']?.toDouble();
    isShiftSpace = json['is_shift_space']?.toString();
    isSubLeased = json['is_sub_leased']?.toString();
    leaseRentable = json['lease_rentable']?.toDouble();
    measurementType = json['measurement_type']?.toString();
    occupancyCount = json['occupancy_count']?.toInt();
    occupancyStatus = json['occupancy_status']?.toString();
    parentLocId = json['parent_loc_id']?.toInt();
    portfolio = json['portfolio']?.toString();
    propertyCommonArea = json['property_common_area']?.toDouble();
    spaceCategory = json['space_category']?.toString();
    spaceType = json['space_type']?.toString();
    spaceUse = json['space_use']?.toString();
    timeZone = json['time_zone']?.toString();
    totalCapacity = json['total_capacity']?.toDouble();
    totalHeadCount = json['total_head_count']?.toInt();
    usingTangoLease = json['using_tango_lease']?.toString();
    workstationCapacity = json['workstation_capacity']?.toInt();
    workstationHeadCount = json['workstation_head_count']?.toInt();
    utilization = json['utilization']?.toDouble();
    occupancyrate = json['occupancyrate']?.toDouble();
    wkstoccupancyrate = json['wkstoccupancyrate']?.toDouble();
    floorcount = json['floorcount']?.toInt();
    rsfperwkst = json['rsfperwkst']?.toDouble();
    rsfperemp = json['rsfperemp']?.toDouble();
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    parentSpaceName = json['parent_space_name']?.toString();
    parentSpaceNumber = json['parent_space_number']?.toString();
    vacancypercent = json['vacancypercent']?.toDouble();
    vacantArea = json['vacant_area']?.toDouble();
    leaseExpiryDate = json['lease_expiry_date']?.toString();
    strategy = json['strategy']?.toString();
    createdBy = json['created_by']?.toString();
    propertyName = json['property_name']?.toString();
    dwgFileId = json['dwg_file_id']?.toInt();
    tempCadFile = json['temp_cad_file']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    lastUpdatedDate = json['last_updated_date']?.toString();
    country = json['country']?.toString();
    buildingStatusType = json['building_status_type']?.toString();
    floorWithoutCurrentcad = json['floor_without_currentcad']?.toString();
    propertyId = json['property_id']?.toInt();
    buildingActualAreaSm = json['building_actual_area_sm']?.toInt();
    buildingUsableAreaSm = json['building_usable_area_sm']?.toDouble();
    cadRentableAreaSm = json['cad_rentable_area_sm']?.toDouble();
    internalGrossAreaSm = json['internal_gross_area_sm']?.toDouble();
    isManualSync = json['is_manual_sync']?.toString();
    floorSeq = json['floor_seq']?.toDouble();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    data['all_columns'] = allColumns;
    data['brand_id'] = brandId;
    data['city'] = city;
    data['client_id'] = clientId;
    data['loc_entity_type'] = locEntityType;
    data['loc_sub_entity_type'] = locSubEntityType;
    data['location_type_code'] = locationTypeCode;
    data['open_date'] = openDate;
    data['ownership_type'] = ownershipType;
    data['state'] = state;
    data['status'] = status;
    data['store_id'] = storeId;
    data['store_name'] = storeName;
    data['store_number'] = storeNumber;
    data['zip_code'] = zipCode;
    data['boma_type'] = bomaType;
    data['building_actual_area'] = buildingActualArea;
    data['building_area'] = buildingArea;
    data['building_common_area'] = buildingCommonArea;
    data['building_core_area'] = buildingCoreArea;
    data['building_owned_area'] = buildingOwnedArea;
    data['building_residual_area'] = buildingResidualArea;
    data['building_subleased_area'] = buildingSubleasedArea;
    data['building_usable_area'] = buildingUsableArea;
    data['cad_rentable_area'] = cadRentableArea;
    data['capacity_available'] = capacityAvailable;
    data['capacity_planned'] = capacityPlanned;
    data['department_id'] = departmentId;
    data['department_override'] = departmentOverride;
    data['external_gross_area'] = externalGrossArea;
    data['floor_common_area'] = floorCommonArea;
    data['internal_gross_area'] = internalGrossArea;
    data['is_shift_space'] = isShiftSpace;
    data['is_sub_leased'] = isSubLeased;
    data['lease_rentable'] = leaseRentable;
    data['measurement_type'] = measurementType;
    data['occupancy_count'] = occupancyCount;
    data['occupancy_status'] = occupancyStatus;
    data['parent_loc_id'] = parentLocId;
    data['portfolio'] = portfolio;
    data['property_common_area'] = propertyCommonArea;
    data['space_category'] = spaceCategory;
    data['space_type'] = spaceType;
    data['space_use'] = spaceUse;
    data['time_zone'] = timeZone;
    data['total_capacity'] = totalCapacity;
    data['total_head_count'] = totalHeadCount;
    data['using_tango_lease'] = usingTangoLease;
    data['workstation_capacity'] = workstationCapacity;
    data['workstation_head_count'] = workstationHeadCount;
    data['utilization'] = utilization;
    data['occupancyrate'] = occupancyrate;
    data['wkstoccupancyrate'] = wkstoccupancyrate;
    data['floorcount'] = floorcount;
    data['rsfperwkst'] = rsfperwkst;
    data['rsfperemp'] = rsfperemp;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['parent_space_name'] = parentSpaceName;
    data['parent_space_number'] = parentSpaceNumber;
    data['vacancypercent'] = vacancypercent;
    data['vacant_area'] = vacantArea;
    data['lease_expiry_date'] = leaseExpiryDate;
    data['strategy'] = strategy;
    data['created_by'] = createdBy;
    data['property_name'] = propertyName;
    data['dwg_file_id'] = dwgFileId;
    data['temp_cad_file'] = tempCadFile;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['last_updated_date'] = lastUpdatedDate;
    data['country'] = country;
    data['building_status_type'] = buildingStatusType;
    data['floor_without_currentcad'] = floorWithoutCurrentcad;
    data['property_id'] = propertyId;
    data['building_actual_area_sm'] = buildingActualAreaSm;
    data['building_usable_area_sm'] = buildingUsableAreaSm;
    data['cad_rentable_area_sm'] = cadRentableAreaSm;
    data['internal_gross_area_sm'] = internalGrossAreaSm;
    data['is_manual_sync'] = isManualSync;
    data['floor_seq'] = floorSeq;
    return data;
  }
}
