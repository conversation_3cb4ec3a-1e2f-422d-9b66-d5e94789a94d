class ActiveReserveSpaces {
  int? spaceId;
  int? buildingId;
  int? floorId;
  String? spaceName;
  String? spaceNumber;
  int? propertyId;
  String? propertyName;
  String? buildingName;
  String? floorName;
  String? occupancyStatus;
  String? spaceCategory;
  String? spaceUse;
  String? chargeType;
  String? isReservable;
  String? isWkst;
  String? wkstType;
  String? spaceDisplayName;
  int? cadRentableSf;
  String? spaceAttributes;

  ActiveReserveSpaces({
    this.spaceId,
    this.buildingId,
    this.floorId,
    this.spaceName,
    this.spaceNumber,
    this.propertyId,
    this.propertyName,
    this.buildingName,
    this.floorName,
    this.occupancyStatus,
    this.spaceCategory,
    this.spaceUse,
    this.chargeType,
    this.isReservable,
    this.isWkst,
    this.wkstType,
    this.spaceDisplayName,
    this.cadRentableSf,
    this.spaceAttributes,
  });
  ActiveReserveSpaces.fromJson(Map<String, dynamic> json) {
    spaceId = json['space_id']?.toInt();
    buildingId = json['building_id']?.toInt();
    floorId = json['floor_id']?.toInt();
    spaceName = json['space_name']?.toString();
    spaceNumber = json['space_number']?.toString();
    propertyId = json['property_id']?.toInt();
    propertyName = json['property_name']?.toString();
    buildingName = json['building_name']?.toString();
    floorName = json['floor_name']?.toString();
    occupancyStatus = json['occupancy_status']?.toString();
    spaceCategory = json['space_category']?.toString();
    spaceUse = json['space_use']?.toString();
    chargeType = json['charge_type']?.toString();
    isReservable = json['is_reservable']?.toString();
    isWkst = json['is_wkst']?.toString();
    wkstType = json['wkst_type']?.toString();
    spaceDisplayName = json['space_display_name']?.toString();
    cadRentableSf = json['cad_rentable_sf']?.toInt();
    spaceAttributes = json['space_attributes']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['space_id'] = spaceId;
    data['building_id'] = buildingId;
    data['floor_id'] = floorId;
    data['space_name'] = spaceName;
    data['space_number'] = spaceNumber;
    data['property_id'] = propertyId;
    data['property_name'] = propertyName;
    data['building_name'] = buildingName;
    data['floor_name'] = floorName;
    data['occupancy_status'] = occupancyStatus;
    data['space_category'] = spaceCategory;
    data['space_use'] = spaceUse;
    data['charge_type'] = chargeType;
    data['is_reservable'] = isReservable;
    data['is_wkst'] = isWkst;
    data['wkst_type'] = wkstType;
    data['space_display_name'] = spaceDisplayName;
    data['cad_rentable_sf'] = cadRentableSf;
    data['space_attributes'] = spaceAttributes;
    return data;
  }
}
