class Space {
  int? spaceId;
  String? spaceNumber;
  String? chargeType;
  String? spaceDisplayName;
  List? spaceAttributes;
  int? floorId;
  String? floorName;

  Space({this.spaceId, this.spaceNumber, this.chargeType, this.spaceDisplayName, this.spaceAttributes, this.floorId, this.floorName});

  factory Space.fromJson(Map<String, dynamic> json) {
    return Space(
      spaceId: json['spaceId'] ?? '' as int?,
      spaceNumber: json['spaceNumber'] ?? '',
      chargeType: json['chargeType'] ?? '',
      spaceDisplayName: json['spaceDisplayName'] ?? '',
      spaceAttributes: json['spaceAttributes'],
      floorId: json['floorId'] ?? '' as int?,
      floorName: json['floorName'] ?? '',
    );
  }
}
