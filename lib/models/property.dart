import 'building.dart';

class Property {
  int? propertyId;
  String? propertyName;
  List<Building>? buildings;

  Property({
    this.propertyId,
    this.propertyName,
    this.buildings,
  });

  factory Property.fromJson(Map<String, dynamic> json) {
    var list = json['buildings'] as List;
    List<Building> buildingsList =
        list.map((i) => Building.fromJson(i)).toList();

    return Property(
        propertyId: json['propertyId'] ?? 0,
        propertyName: json['propertyName'] ?? "",
        buildings: buildingsList);
  }
}
