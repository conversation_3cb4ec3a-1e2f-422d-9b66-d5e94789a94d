class LookupValues {
  final String? lookupValue;
  final String? lookupCode;
  final String? typeLookupCode;
  final String? tag;

  LookupValues({this.lookupValue, this.lookupCode, this.typeLookupCode, this.tag});

  factory LookupValues.fromJson(Map<String, dynamic> json) {
    return LookupValues(
        lookupValue: json['lookupValue'] ?? '',
        lookupCode: json['lookupCode'] ?? '',
        typeLookupCode: json['typeLookupCode'] ?? '',
        tag: json['tag'] ?? '');
  }
}
