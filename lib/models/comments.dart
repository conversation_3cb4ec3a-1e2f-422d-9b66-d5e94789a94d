class Comments {
  String? comments;
  String? reviewDate;
  String? reviewedBy;
  int? commentId;

  Comments({this.comments, this.reviewDate, this.reviewedBy, this.commentId});

  factory Comments.fromJson(Map<String, dynamic> json) {
    return Comments(
        comments: json['comments'] ?? '',
        reviewDate: json['creation_date'] ?? '',
        reviewedBy: json['created_by'] ?? '',
        commentId: json['comment_id'] ?? '' as int?);
  }
}
