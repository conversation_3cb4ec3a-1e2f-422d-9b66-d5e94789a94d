class UserInfo {
  bool? active;
  String? userType;
  String? userName;
  String? firstName;
  String? lastName;
  int? userId;
  int? personId;
  String? isContractor;
  String? userTermsStatus;
  String? userTerms;
  String? userTermsVersion;
  String? phoneNumber;
  String? userPrefLocale;
  String? fullname;

  UserInfo({
    this.active,
    this.userType,
    this.userName,
    this.firstName,
    this.lastName,
    this.userId,
    this.personId,
    this.isContractor,
    this.userTermsStatus,
    this.userTerms,
    this.userTermsVersion,
    this.phoneNumber,
    this.userPrefLocale,
    this.fullname,
  });
  UserInfo.fromJson(Map<String, dynamic> json) {
    active = json['active'];
    userType = json['userType']?.toString();
    userName = json['userName']?.toString();
    firstName = json['firstName']?.toString();
    lastName = json['lastName']?.toString();
    userId = json['userId']?.toInt();
    personId = json['personId']?.toInt();
    isContractor = json['isContractor']?.toString();
    userTermsStatus = json['userTermsStatus']?.toString();
    userTerms = json['userTerms']?.toString();
    userTermsVersion = json['userTermsVersion']?.toString();
    phoneNumber = json['phoneNumber']?.toString();
    userPrefLocale = json['userPrefLocale']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['active'] = active;
    data['userType'] = userType;
    data['userName'] = userName;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['userId'] = userId;
    data['personId'] = personId;
    data['isContractor'] = isContractor;
    data['userTermsStatus'] = userTermsStatus;
    data['userTerms'] = userTerms;
    data['userTermsVersion'] = userTermsVersion;
    data['userPrefLocale'] = userPrefLocale;
    return data;
  }
}
