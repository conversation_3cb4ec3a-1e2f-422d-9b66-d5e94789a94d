class EntityStatus {
  int? statusId;
  String? entityType;
  String? statusCode;
  String? status;
  String? statusType;
  String? enabledFlag;
  int? displaySeq;
  String? defaultStatus;
  String? displayFlag;

  EntityStatus({
    this.statusId,
    this.entityType,
    this.statusCode,
    this.status,
    this.statusType,
    this.enabledFlag,
    this.displaySeq,
    this.defaultStatus,
    this.displayFlag,
  });
  EntityStatus.fromJson(Map<String, dynamic> json) {
    statusId = json['status_id']?.toInt();
    entityType = json['entity_type']?.toString();
    statusCode = json['status_code']?.toString();
    status = json['status']?.toString();
    statusType = json['status_type']?.toString();
    enabledFlag = json['enabled_flag']?.toString();
    displaySeq = json['display_seq']?.toInt();
    defaultStatus = json['default_status']?.toString();
    displayFlag = json['display_flag']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['status_id'] = statusId;
    data['entity_type'] = entityType;
    data['status_code'] = statusCode;
    data['status'] = status;
    data['status_type'] = statusType;
    data['enabled_flag'] = enabledFlag;
    data['display_seq'] = displaySeq;
    data['default_status'] = defaultStatus;
    data['display_flag'] = displayFlag;
    return data;
  }
}
