class Applabel {
  int? resourceId;
  String? key;
  String? value;
  String? parentkey;
  String? edit;
  String? lookupCode;
  String? req;
  String? defaultValue;
  String? formatting;
  bool? ro;

  Applabel({
    this.resourceId,
    this.key,
    this.value,
    this.parentkey,
    this.edit,
    this.lookupCode,
    this.req,
    this.defaultValue,
    this.formatting,
    this.ro,
  });
  Applabel.fromJson(Map<String, dynamic> json) {
    resourceId = json['resource_id'] != null ? int.tryParse(json['resource_id']) : 0;
    key = json['component_key']?.toString() ?? '';
    value = json['label_value']?.toString() ?? '';
    parentkey = json['parent_key']?.toString() ?? '';
    edit = json['editable']?.toString() ?? '';
    //lookupCode = json['lookup_code']?.toString();
    req = json['mandatory']?.toString() ?? '';
    defaultValue = json['default_value']?.toString() ?? '';
    formatting = json['formatting']?.toString() ?? '';
  }
  Applabel.forJson(Map<String, dynamic> json) {
    resourceId = json['resource_id'] != null ? int.tryParse(json['resource_id']) : 0;
    key = json['key']?.toString() ?? '';
    value = json['value']?.toString() ?? '';
    parentkey = json['parentkey']?.toString() ?? '';
    edit = json['edit']?.toString() ?? '';
    lookupCode = json['lookup_code']?.toString() ?? '';
    req = json['req']?.toString() ?? '';
    defaultValue = json['default_value']?.toString() ?? '';
    formatting = json['formatting']?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['resource_id'] = resourceId;
    data['component_key'] = key;
    data['label_value'] = value;
    data['parent_key'] = parentkey;
    data['editable'] = edit;
    data['lookup_code'] = lookupCode;
    data['mandatory'] = req;
    data['default_value'] = defaultValue;
    data['formatting'] = formatting;
    return data;
  }
}
