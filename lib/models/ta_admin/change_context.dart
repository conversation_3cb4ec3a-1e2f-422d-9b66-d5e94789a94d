class Client extends ContextChange {
  Client({
    this.clientId,
    this.clientName,
    this.userName,
    this.localizationEnabled,
    this.isBrandSecurityEnable,
    this.applyBuPolicy,
    this.preferredLocale,
    super.value,
    super.text,
  });

  int? clientId;
  String? clientName;
  String? userName;
  String? localizationEnabled;
  String? isBrandSecurityEnable;
  String? applyBuPolicy;
  String? preferredLocale;

  factory Client.fromJson(Map<String, dynamic> json) => Client(
      clientId: json["clientId"],
      clientName: json["clientName"] ?? "",
      userName: json["userName"] ?? "",
      localizationEnabled: json["localizationEnabled"] ?? "",
      isBrandSecurityEnable: json["isBrandSecurityEnable"] ?? "",
      applyBuPolicy: json["applyBuPolicy"] ?? "",
      preferredLocale: json["preferredLocale"] ?? "",
      value: json["clientId"]?.toString() ?? 'xxxx',
      text: json["clientName"] ?? "");

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Client && other.text == text && other.value == value;
  }

  @override
  int get hashCode => value.hashCode ^ text.hashCode;
}

class Brand extends ContextChange {
  Brand({
    this.clientBrandId,
    this.brandName,
    this.brandCode,
    super.value,
    super.text,
  });

  int? clientBrandId;
  String? brandName;
  String? brandCode;

  factory Brand.fromJson(Map<String, dynamic> json) => Brand(
      clientBrandId: json["clientBrandId"],
      brandName: json["brandName"] ?? "",
      brandCode: json["brandCode"] ?? "",
      value: json["clientBrandId"]?.toString() ?? 'xxxx',
      text: json["brandName"] ?? "");

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Brand && other.text == text && other.value == value;
  }

  @override
  int get hashCode => value.hashCode ^ text.hashCode;
}

class Bu extends ContextChange {
  Bu({
    this.buId,
    this.clientId,
    this.brandId,
    this.buName,
    super.value,
    super.text,
  });

  int? buId;
  int? clientId;
  int? brandId;
  String? buName;

  factory Bu.fromJson(Map<String, dynamic> json) => Bu(
      buId: json["buId"],
      clientId: json["clientId"],
      brandId: json["brandId"],
      buName: json["buName"] ?? '',
      value: json["buId"]?.toString() ?? 'xxxx',
      text: json["buName"] ?? '');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bu && other.text == text && other.value == value;
  }

  @override
  int get hashCode => value.hashCode ^ text.hashCode;
}

class Country extends ContextChange {
  Country({
    this.countryCode,
    this.countryName,
    this.timezone,
    this.countrycode2,
    super.value,
    super.text,
  });

  String? countryCode;
  String? countryName;
  String? timezone;
  String? countrycode2;

  factory Country.fromJson(Map<String, dynamic> json) => Country(
      countryCode: json["countryCode"] ?? '',
      countryName: json["countryName"] ?? '',
      timezone: json["timezone"] ?? '',
      countrycode2: json["countrycode2"] ?? '',
      value: json["countryCode"] ?? 'xxxx',
      text: json["countryName"] ?? '');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Country && other.text == text && other.value == value;
  }

  @override
  int get hashCode => value.hashCode ^ text.hashCode;
}

class Language extends ContextChange {
  Language({
    this.localeId,
    this.clientId,
    this.countryCode,
    this.language,
    this.defaultLocale,
    this.languageLocale,
    super.value,
    super.text,
  });

  int? localeId;
  int? clientId;
  String? countryCode;
  String? language;
  String? defaultLocale;
  String? languageLocale;

  factory Language.fromJson(Map<String, dynamic> json) => Language(
      localeId: json["localeId"],
      clientId: json["clientId"],
      countryCode: json["countryCode"] ?? '',
      language: json["language"] ?? '',
      defaultLocale: json["defaultLocale"] ?? '',
      languageLocale: json["languageLocale"] ?? '',
      value: json["languageLocale"] ?? 'xxxx',
      text: json["language"] ?? '');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Language && other.text == text && other.value == value;
  }

  @override
  int get hashCode => value.hashCode ^ text.hashCode;
}

class ContextChange {
  ContextChange({
    this.value,
    this.text,
  });

  String? value;
  String? text;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Client && other.text == text && other.value == value;
  }

  @override
  int get hashCode => value.hashCode ^ text.hashCode;
}
