class ReservationRules {
/*
{
  "advance_allowed_days": 60,
  "allday_start_hour": 8,
  "allday_start_min": 0,
  "allday_end_hour": 20,
  "allday_end_min": 0
  "days_limit_between_start_end_date":6
} 
*/

  int? advanceAllowedDays;
  int? alldayStartHour;
  int? alldayStartMin;
  int? alldayEndHour;
  int? alldayEndMin;
  int? bwStartEndDate;

  ReservationRules({
    this.advanceAllowedDays,
    this.alldayStartHour,
    this.alldayStartMin,
    this.alldayEndHour,
    this.alldayEndMin,
    this.bwStartEndDate,
  });
  ReservationRules.fromJson(Map<String, dynamic> json) {
    advanceAllowedDays = json['advance_allowed_days']?.toInt();
    alldayStartHour = json['allday_start_hour']?.toInt();
    alldayStartMin = json['allday_start_min']?.toInt();
    alldayEndHour = json['allday_end_hour']?.toInt();
    alldayEndMin = json['allday_end_min']?.toInt();
    bwStartEndDate = json['days_limit_between_start_end_date']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['advance_allowed_days'] = advanceAllowedDays;
    data['allday_start_hour'] = alldayStartHour;
    data['allday_start_min'] = alldayStartMin;
    data['allday_end_hour'] = alldayEndHour;
    data['allday_end_min'] = alldayEndMin;
    data['days_limit_between_start_end_date'] = bwStartEndDate;

    return data;
  }
}
