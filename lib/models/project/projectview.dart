class ProjectsView {
  int? projectId;
  String? projectNumber;
  String? projectName;
  String? projectType;
  String? projectTypeDesc;
  String? description;
  String? ownershipType;
  String? statusDesc;
  String? country;
  String? entityType;
  String? entityTypeLov;
  int? entityId;
  String? entityNumber;
  String? entityAddress;
  String? city;
  String? state;
  String? isMasterProject;
  int? masterProjectId;
  String? programName;
  int? rootFolderId;
  int? parentRootFolderId;
  String? storeNumber;
  String? realEstateManager;
  String? constructionManager;
  String? constructionManagerDesc;
  String? readOnlyFlag;

  ProjectsView({
    this.projectId,
    this.projectNumber,
    this.projectName,
    this.projectType,
    this.projectTypeDesc,
    this.description,
    this.ownershipType,
    this.statusDesc,
    this.country,
    this.entityType,
    this.entityTypeLov,
    this.entityId,
    this.entityNumber,
    this.entityAddress,
    this.city,
    this.state,
    this.isMasterProject,
    this.masterProjectId,
    this.programName,
    this.rootFolderId,
    this.parentRootFolderId,
    this.storeNumber,
    this.realEstateManager,
    this.constructionManager,
    this.constructionManagerDesc,
    this.readOnlyFlag,
  });
  ProjectsView.fromJson(Map<String, dynamic> json) {
    projectId = json["project_id"]?.toInt();
    projectNumber = json["project_number"]?.toString() ?? '';
    projectName = json["project_name"]?.toString() ?? '';
    projectType = json["project_type"]?.toString() ?? '';
    projectTypeDesc = json["project_type_desc"]?.toString() ?? '';
    description = json["description"]?.toString() ?? '';
    ownershipType = json["ownership_type"]?.toString() ?? '';
    statusDesc = json["status_desc"]?.toString() ?? '';
    country = json["country"]?.toString() ?? '';
    entityType = json["entity_type"]?.toString() ?? '';
    entityTypeLov = json["entity_type_lov"]?.toString() ?? '';
    entityId = json["entity_id"]?.toInt();
    entityNumber = json["entity_number"]?.toString() ?? '';
    entityAddress = json["entity_address"]?.toString() ?? '';
    city = json["city"]?.toString() ?? '';
    state = json["state"]?.toString() ?? '';
    isMasterProject = json["is_master_project"]?.toString() ?? '';
    masterProjectId = json["master_project_id"]?.toInt();
    programName = json["progr_name"]?.toString() ?? '';
    rootFolderId = json["root_folder_id"]?.toInt();
    parentRootFolderId = json["parent_root_folder_id"]?.toInt();
    storeNumber = json["store_number"]?.toString() ?? '';

    realEstateManager = json["real_estate_manager"]?.toString() ?? '';
    constructionManager = json["construction_manager"]?.toString() ?? '';
    constructionManagerDesc = json["construction_manager_desc"]?.toString() ?? '';
    readOnlyFlag = json["read_only_flag"]?.toString() ?? '';
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["project_id"] = projectId;
    data["project_number"] = projectNumber;
    data["project_name"] = projectName;
    data["project_type"] = projectType;
    data["project_type_desc"] = projectTypeDesc;
    data["description"] = description;
    data["ownership_type"] = ownershipType;
    data["status_desc"] = statusDesc;
    data["country"] = country;
    data["entity_type"] = entityType;
    data["entity_type_lov"] = entityTypeLov;
    data["entity_id"] = entityId;
    data["entity_number"] = entityNumber;
    data["entity_address"] = entityAddress;
    data["city"] = city;
    data["is_master_project"] = isMasterProject;
    data["master_project_id"] = masterProjectId;
    data["progr_name"] = programName;
    data["read_only_flag"] = readOnlyFlag;
    return data;
  }
}
