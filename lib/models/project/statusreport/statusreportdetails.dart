class StatusReportDetailsData {
  String? actualCompletionDate;
  int? clientId;
  String? createdBy;
  String? creationDate;
  int? detailStatusId;
  String? milestoneName;
  String? origianlCompletionDate;
  int? projectId;
  String? projectedCompletionDate;
  int? statusId;
  int? taskNumber;
  int? templateId;
  String? milestoneRisk;
  String? actiontype;

  StatusReportDetailsData({
    this.actualCompletionDate,
    this.clientId,
    this.createdBy,
    this.creationDate,
    this.detailStatusId,
    this.milestoneName,
    this.origianlCompletionDate,
    this.projectId,
    this.projectedCompletionDate,
    this.statusId,
    this.taskNumber,
    this.templateId,
    this.milestoneRisk,
    this.actiontype,
  });
  StatusReportDetailsData.fromJson(Map<String, dynamic> json) {
    actualCompletionDate = json['actual_completion_date']?.toString();
    clientId = json['client_id']?.toInt();
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    detailStatusId = json['detail_status_id']?.toInt();
    milestoneName = json['milestone_name']?.toString();
    origianlCompletionDate = json['origianl_completion_date']?.toString();
    projectId = json['project_id']?.toInt();
    projectedCompletionDate = json['projected_completion_date']?.toString();
    statusId = json['status_id']?.toInt();
    taskNumber = json['task_number']?.toInt();
    templateId = json['template_id']?.toInt();
    milestoneRisk = json['milestone_risk']?.toString();
    actiontype = null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['actual_completion_date'] = actualCompletionDate;
    data['client_id'] = clientId;
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['detail_status_id'] = detailStatusId;
    data['milestone_name'] = milestoneName;
    data['origianl_completion_date'] = origianlCompletionDate;
    data['project_id'] = projectId;
    data['projected_completion_date'] = projectedCompletionDate;
    data['status_id'] = statusId;
    data['task_number'] = taskNumber;
    data['template_id'] = templateId;
    data['milestone_risk'] = milestoneRisk;
    data['action_type'] = actiontype;
    return data;
  }
}
