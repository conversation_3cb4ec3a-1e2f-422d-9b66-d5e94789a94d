class StatusReport {
  String? createdBy;
  String? creationDate;
  String? generalContractor;
  String? jobSuper;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? phoneNumber;
  int? projectId;
  int? projectInWeek;
  String? projectManager;
  String? reasonNotCompleted;
  int? statusId;
  String? weatherNextWeek;
  String? weatherThisWeek;
  String? weekEnding;
  String? workNotCompleted;
  String? workScheduleNextWeek;
  String? workScheduleThisWeek;
  String? generalComments;
  String? scheduleComments;
  String? budgetComments;
  String? riskComments;
  String? actionItems;
  String? perReportComments;
  String? scheduleHealth;
  String? riskHealth;
  String? financialHealth;
  String? projectName;
  String? storeNumber;
  String? realEstateManager;
  String? constructionManager;

  StatusReport({
    this.createdBy,
    this.creationDate,
    this.generalContractor,
    this.jobSuper,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.phoneNumber,
    this.projectId,
    this.projectInWeek,
    this.projectManager,
    this.reasonNotCompleted,
    this.statusId,
    this.weatherNextWeek,
    this.weatherThisWeek,
    this.weekEnding,
    this.workNotCompleted,
    this.workScheduleNextWeek,
    this.workScheduleThisWeek,
    this.generalComments,
    this.scheduleComments,
    this.budgetComments,
    this.riskComments,
    this.actionItems,
    this.perReportComments,
    this.scheduleHealth,
    this.riskHealth,
    this.financialHealth,
    this.projectName,
    this.storeNumber,
    this.constructionManager,
    this.realEstateManager,
  });
  StatusReport.fromJson(Map<String, dynamic> json) {
    createdBy = json['created_by']?.toString();
    creationDate = json['creation_date']?.toString();
    generalContractor = json['general_contractor']?.toString();
    jobSuper = json['job_super']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    phoneNumber = json['phone_number']?.toString();
    projectId = json['project_id']?.toInt();
    projectInWeek = json['project_in_week']?.toInt();
    projectManager = json['project_manager']?.toString();
    reasonNotCompleted = json['reason_not_completed']?.toString();
    statusId = json['status_id']?.toInt();
    weatherNextWeek = json['weather_next_week']?.toString();
    weatherThisWeek = json['weather_this_week']?.toString();
    weekEnding = json['week_ending']?.toString();
    workNotCompleted = json['work_not_completed']?.toString();
    workScheduleNextWeek = json['work_schedule_next_week']?.toString();
    workScheduleThisWeek = json['work_schedule_this_week']?.toString();
    generalComments = json['general_comments']?.toString();
    scheduleComments = json['schedule_comments']?.toString();
    budgetComments = json['budget_comments']?.toString();
    riskComments = json['risk_comments']?.toString();
    actionItems = json['action_items']?.toString();
    perReportComments = json['per_report_comments']?.toString();
    scheduleHealth = json['schedule_health']?.toString();
    riskHealth = json['risk_health']?.toString();
    financialHealth = json['financial_health']?.toString();
    projectName = json['project_name']?.toString();
    storeNumber = json['store_number']?.toString();
    constructionManager = json['construction_manager']?.toString();
    realEstateManager = json['real_estate_manager']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['created_by'] = createdBy;
    data['creation_date'] = creationDate;
    data['general_contractor'] = generalContractor;
    data['job_super'] = jobSuper;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['phone_number'] = phoneNumber;
    data['project_id'] = projectId;
    data['project_in_week'] = projectInWeek;
    data['project_manager'] = projectManager;
    data['reason_not_completed'] = reasonNotCompleted;
    data['status_id'] = statusId;
    data['weather_next_week'] = weatherNextWeek;
    data['weather_this_week'] = weatherThisWeek;
    data['week_ending'] = weekEnding;
    data['work_not_completed'] = workNotCompleted;
    data['work_schedule_next_week'] = workScheduleNextWeek;
    data['work_schedule_this_week'] = workScheduleThisWeek;
    data['general_comments'] = generalComments;
    data['schedule_comments'] = scheduleComments;
    data['budget_comments'] = budgetComments;
    data['risk_comments'] = riskComments;
    data['action_items'] = actionItems;
    data['per_report_comments'] = perReportComments;
    data['schedule_health'] = scheduleHealth;
    data['risk_health'] = riskHealth;
    data['financial_health'] = financialHealth;
    return data;
  }
}
