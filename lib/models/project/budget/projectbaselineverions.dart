class ProjBaselineVersions {
  int? baselineId;
  int? projectId;
  int? versionNumber;
  int? clientId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? countryCode;
  String? comments;
  String? wfStatus;
  String? status;
  String? category;
  String? phase;
  String? cExtAttr01;
  String? cExtAttr02;
  String? cExtAttr03;
  String? cExtAttr04;
  String? cExtAttr05;
  String? cExtAttr06;
  String? cExtAttr07;
  String? cExtAttr08;
  String? cExtAttr09;
  String? cExtAttr10;
  String? cExtAttr11;
  String? cExtAttr12;
  String? cExtAttr13;
  String? cExtAttr14;
  String? cExtAttr15;
  String? cExtLov1;
  String? cExtLov10;
  String? cExtLov11;
  String? cExtLov12;
  String? cExtLov13;
  String? cExtLov14;
  String? cExtLov15;
  String? cExtLov2;
  String? cExtLov3;
  String? cExtLov4;
  String? cExtLov5;
  String? cExtLov7;
  String? cExtLov6;
  String? cExtLov8;
  String? cExtLov9;
  String? dExtAttr01;
  String? dExtAttr02;
  String? dExtAttr03;
  String? dExtAttr04;
  String? dExtAttr05;
  String? dExtAttr06;
  String? dExtAttr07;
  String? dExtAttr08;
  String? dExtAttr09;
  String? dExtAttr10;
  String? dExtAttr11;
  String? dExtAttr12;
  String? dExtAttr13;
  String? dExtAttr14;
  String? dExtAttr15;
  int? nExtAttr01;
  int? nExtAttr02;
  int? nExtAttr03;
  int? nExtAttr04;
  int? nExtAttr05;
  int? nExtAttr06;
  int? nExtAttr07;
  int? nExtAttr08;
  int? nExtAttr09;
  int? nExtAttr10;
  int? nExtAttr11;
  int? nExtAttr12;
  int? nExtAttr13;
  int? nExtAttr14;
  int? nExtAttr15;
  int? nExtAttr16;
  int? nExtAttr17;
  int? nExtAttr18;
  int? nExtAttr19;
  int? nExtAttr20;
  String? cExtLov16;
  String? cExtLov17;
  String? cExtLov18;
  String? cExtLov19;
  String? cExtLov20;
  String? cExtAttr17;
  String? cExtAttr18;
  String? cExtAttr19;
  String? cExtAttr20;
  String? cExtAttr61;
  int? approvedBudgetAmount;
  int? totalAprvdBudgetAmt;

  ProjBaselineVersions({
    this.baselineId,
    this.projectId,
    this.versionNumber,
    this.clientId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.countryCode,
    this.comments,
    this.wfStatus,
    this.status,
    this.category,
    this.phase,
    this.cExtAttr01,
    this.cExtAttr02,
    this.cExtAttr03,
    this.cExtAttr04,
    this.cExtAttr05,
    this.cExtAttr06,
    this.cExtAttr07,
    this.cExtAttr08,
    this.cExtAttr09,
    this.cExtAttr10,
    this.cExtAttr11,
    this.cExtAttr12,
    this.cExtAttr13,
    this.cExtAttr14,
    this.cExtAttr15,
    this.cExtLov1,
    this.cExtLov10,
    this.cExtLov11,
    this.cExtLov12,
    this.cExtLov13,
    this.cExtLov14,
    this.cExtLov15,
    this.cExtLov2,
    this.cExtLov3,
    this.cExtLov4,
    this.cExtLov5,
    this.cExtLov7,
    this.cExtLov6,
    this.cExtLov8,
    this.cExtLov9,
    this.dExtAttr01,
    this.dExtAttr02,
    this.dExtAttr03,
    this.dExtAttr04,
    this.dExtAttr05,
    this.dExtAttr06,
    this.dExtAttr07,
    this.dExtAttr08,
    this.dExtAttr09,
    this.dExtAttr10,
    this.dExtAttr11,
    this.dExtAttr12,
    this.dExtAttr13,
    this.dExtAttr14,
    this.dExtAttr15,
    this.nExtAttr01,
    this.nExtAttr02,
    this.nExtAttr03,
    this.nExtAttr04,
    this.nExtAttr05,
    this.nExtAttr06,
    this.nExtAttr07,
    this.nExtAttr08,
    this.nExtAttr09,
    this.nExtAttr10,
    this.nExtAttr11,
    this.nExtAttr12,
    this.nExtAttr13,
    this.nExtAttr14,
    this.nExtAttr15,
    this.nExtAttr16,
    this.nExtAttr17,
    this.nExtAttr18,
    this.nExtAttr19,
    this.nExtAttr20,
    this.cExtLov16,
    this.cExtLov17,
    this.cExtLov18,
    this.cExtLov19,
    this.cExtLov20,
    this.cExtAttr17,
    this.cExtAttr18,
    this.cExtAttr19,
    this.cExtAttr20,
    this.cExtAttr61,
    this.approvedBudgetAmount,
    this.totalAprvdBudgetAmt,
  });
  ProjBaselineVersions.fromJson(Map<String, dynamic> json) {
    baselineId = json['baseline_id']?.toInt();
    projectId = json['project_id']?.toInt();
    versionNumber = json['version_number']?.toInt();
    clientId = json['client_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    countryCode = json['country_code']?.toString();
    comments = json['comments']?.toString();
    wfStatus = json['wf_status']?.toString();
    status = json['status']?.toString();
    category = json['category']?.toString();
    phase = json['phase']?.toString();
    cExtAttr01 = json['c_ext_attr01']?.toString();
    cExtAttr02 = json['c_ext_attr02']?.toString();
    cExtAttr03 = json['c_ext_attr03']?.toString();
    cExtAttr04 = json['c_ext_attr04']?.toString();
    cExtAttr05 = json['c_ext_attr05']?.toString();
    cExtAttr06 = json['c_ext_attr06']?.toString();
    cExtAttr07 = json['c_ext_attr07']?.toString();
    cExtAttr08 = json['c_ext_attr08']?.toString();
    cExtAttr09 = json['c_ext_attr09']?.toString();
    cExtAttr10 = json['c_ext_attr10']?.toString();
    cExtAttr11 = json['c_ext_attr11']?.toString();
    cExtAttr12 = json['c_ext_attr12']?.toString();
    cExtAttr13 = json['c_ext_attr13']?.toString();
    cExtAttr14 = json['c_ext_attr14']?.toString();
    cExtAttr15 = json['c_ext_attr15']?.toString();
    cExtLov1 = json['c_ext_lov1']?.toString();
    cExtLov10 = json['c_ext_lov10']?.toString();
    cExtLov11 = json['c_ext_lov11']?.toString();
    cExtLov12 = json['c_ext_lov12']?.toString();
    cExtLov13 = json['c_ext_lov13']?.toString();
    cExtLov14 = json['c_ext_lov14']?.toString();
    cExtLov15 = json['c_ext_lov15']?.toString();
    cExtLov2 = json['c_ext_lov2']?.toString();
    cExtLov3 = json['c_ext_lov3']?.toString();
    cExtLov4 = json['c_ext_lov4']?.toString();
    cExtLov5 = json['c_ext_lov5']?.toString();
    cExtLov7 = json['c_ext_lov7']?.toString();
    cExtLov6 = json['c_ext_lov6']?.toString();
    cExtLov8 = json['c_ext_lov8']?.toString();
    cExtLov9 = json['c_ext_lov9']?.toString();
    dExtAttr01 = json['d_ext_attr01']?.toString();
    dExtAttr02 = json['d_ext_attr02']?.toString();
    dExtAttr03 = json['d_ext_attr03']?.toString();
    dExtAttr04 = json['d_ext_attr04']?.toString();
    dExtAttr05 = json['d_ext_attr05']?.toString();
    dExtAttr06 = json['d_ext_attr06']?.toString();
    dExtAttr07 = json['d_ext_attr07']?.toString();
    dExtAttr08 = json['d_ext_attr08']?.toString();
    dExtAttr09 = json['d_ext_attr09']?.toString();
    dExtAttr10 = json['d_ext_attr10']?.toString();
    dExtAttr11 = json['d_ext_attr11']?.toString();
    dExtAttr12 = json['d_ext_attr12']?.toString();
    dExtAttr13 = json['d_ext_attr13']?.toString();
    dExtAttr14 = json['d_ext_attr14']?.toString();
    dExtAttr15 = json['d_ext_attr15']?.toString();
    nExtAttr01 = json['n_ext_attr01']?.toInt();
    nExtAttr02 = json['n_ext_attr02']?.toInt();
    nExtAttr03 = json['n_ext_attr03']?.toInt();
    nExtAttr04 = json['n_ext_attr04']?.toInt();
    nExtAttr05 = json['n_ext_attr05']?.toInt();
    nExtAttr06 = json['n_ext_attr06']?.toInt();
    nExtAttr07 = json['n_ext_attr07']?.toInt();
    nExtAttr08 = json['n_ext_attr08']?.toInt();
    nExtAttr09 = json['n_ext_attr09']?.toInt();
    nExtAttr10 = json['n_ext_attr10']?.toInt();
    nExtAttr11 = json['n_ext_attr11']?.toInt();
    nExtAttr12 = json['n_ext_attr12']?.toInt();
    nExtAttr13 = json['n_ext_attr13']?.toInt();
    nExtAttr14 = json['n_ext_attr14']?.toInt();
    nExtAttr15 = json['n_ext_attr15']?.toInt();
    nExtAttr16 = json['n_ext_attr16']?.toInt();
    nExtAttr17 = json['n_ext_attr17']?.toInt();
    nExtAttr18 = json['n_ext_attr18']?.toInt();
    nExtAttr19 = json['n_ext_attr19']?.toInt();
    nExtAttr20 = json['n_ext_attr20']?.toInt();
    cExtLov16 = json['c_ext_lov16']?.toString();
    cExtLov17 = json['c_ext_lov17']?.toString();
    cExtLov18 = json['c_ext_lov18']?.toString();
    cExtLov19 = json['c_ext_lov19']?.toString();
    cExtLov20 = json['c_ext_lov20']?.toString();
    cExtAttr17 = json['c_ext_attr17']?.toString();
    cExtAttr18 = json['c_ext_attr18']?.toString();
    cExtAttr19 = json['c_ext_attr19']?.toString();
    cExtAttr20 = json['c_ext_attr20']?.toString();
    cExtAttr61 = json['c_ext_attr61']?.toString();
    approvedBudgetAmount = json['approved_budget_amount']?.toInt();
    totalAprvdBudgetAmt = json['total_aprvd_budget_amt']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['baseline_id'] = baselineId;
    data['project_id'] = projectId;
    data['version_number'] = versionNumber;
    data['client_id'] = clientId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['country_code'] = countryCode;
    data['comments'] = comments;
    data['wf_status'] = wfStatus;
    data['status'] = status;
    data['category'] = category;
    data['phase'] = phase;
    data['c_ext_attr01'] = cExtAttr01;
    data['c_ext_attr02'] = cExtAttr02;
    data['c_ext_attr03'] = cExtAttr03;
    data['c_ext_attr04'] = cExtAttr04;
    data['c_ext_attr05'] = cExtAttr05;
    data['c_ext_attr06'] = cExtAttr06;
    data['c_ext_attr07'] = cExtAttr07;
    data['c_ext_attr08'] = cExtAttr08;
    data['c_ext_attr09'] = cExtAttr09;
    data['c_ext_attr10'] = cExtAttr10;
    data['c_ext_attr11'] = cExtAttr11;
    data['c_ext_attr12'] = cExtAttr12;
    data['c_ext_attr13'] = cExtAttr13;
    data['c_ext_attr14'] = cExtAttr14;
    data['c_ext_attr15'] = cExtAttr15;
    data['c_ext_lov1'] = cExtLov1;
    data['c_ext_lov10'] = cExtLov10;
    data['c_ext_lov11'] = cExtLov11;
    data['c_ext_lov12'] = cExtLov12;
    data['c_ext_lov13'] = cExtLov13;
    data['c_ext_lov14'] = cExtLov14;
    data['c_ext_lov15'] = cExtLov15;
    data['c_ext_lov2'] = cExtLov2;
    data['c_ext_lov3'] = cExtLov3;
    data['c_ext_lov4'] = cExtLov4;
    data['c_ext_lov5'] = cExtLov5;
    data['c_ext_lov7'] = cExtLov7;
    data['c_ext_lov6'] = cExtLov6;
    data['c_ext_lov8'] = cExtLov8;
    data['c_ext_lov9'] = cExtLov9;
    data['d_ext_attr01'] = dExtAttr01;
    data['d_ext_attr02'] = dExtAttr02;
    data['d_ext_attr03'] = dExtAttr03;
    data['d_ext_attr04'] = dExtAttr04;
    data['d_ext_attr05'] = dExtAttr05;
    data['d_ext_attr06'] = dExtAttr06;
    data['d_ext_attr07'] = dExtAttr07;
    data['d_ext_attr08'] = dExtAttr08;
    data['d_ext_attr09'] = dExtAttr09;
    data['d_ext_attr10'] = dExtAttr10;
    data['d_ext_attr11'] = dExtAttr11;
    data['d_ext_attr12'] = dExtAttr12;
    data['d_ext_attr13'] = dExtAttr13;
    data['d_ext_attr14'] = dExtAttr14;
    data['d_ext_attr15'] = dExtAttr15;
    data['n_ext_attr01'] = nExtAttr01;
    data['n_ext_attr02'] = nExtAttr02;
    data['n_ext_attr03'] = nExtAttr03;
    data['n_ext_attr04'] = nExtAttr04;
    data['n_ext_attr05'] = nExtAttr05;
    data['n_ext_attr06'] = nExtAttr06;
    data['n_ext_attr07'] = nExtAttr07;
    data['n_ext_attr08'] = nExtAttr08;
    data['n_ext_attr09'] = nExtAttr09;
    data['n_ext_attr10'] = nExtAttr10;
    data['n_ext_attr11'] = nExtAttr11;
    data['n_ext_attr12'] = nExtAttr12;
    data['n_ext_attr13'] = nExtAttr13;
    data['n_ext_attr14'] = nExtAttr14;
    data['n_ext_attr15'] = nExtAttr15;
    data['n_ext_attr16'] = nExtAttr16;
    data['n_ext_attr17'] = nExtAttr17;
    data['n_ext_attr18'] = nExtAttr18;
    data['n_ext_attr19'] = nExtAttr19;
    data['n_ext_attr20'] = nExtAttr20;
    data['c_ext_lov16'] = cExtLov16;
    data['c_ext_lov17'] = cExtLov17;
    data['c_ext_lov18'] = cExtLov18;
    data['c_ext_lov19'] = cExtLov19;
    data['c_ext_lov20'] = cExtLov20;
    data['c_ext_attr17'] = cExtAttr17;
    data['c_ext_attr18'] = cExtAttr18;
    data['c_ext_attr19'] = cExtAttr19;
    data['c_ext_attr20'] = cExtAttr20;
    data['c_ext_attr61'] = cExtAttr61;
    data['approved_budget_amount'] = approvedBudgetAmount;
    data['total_aprvd_budget_amt'] = totalAprvdBudgetAmt;
    return data;
  }
}
