class BudgetOverview {
  int? projectId;
  double? sumTotalApprovedBudget;
  double? sumApprovedBudget;
  double? sumComittedCost;
  double? sumApprovedPo;
  double? sumActualCost;
  double? sumEstimatedCost;
  double? sumActualRcvd;
  double? sumEarnedAmount;
  double? sumAvailableToSpend;
  double? approvedCommitment;
  double? intitialApprovedVsForecast;

  BudgetOverview({
    this.projectId,
    this.sumTotalApprovedBudget,
    this.sumApprovedBudget,
    this.sumComittedCost,
    this.sumApprovedPo,
    this.sumActualCost,
    this.sumEstimatedCost,
    this.sumActualRcvd,
    this.sumEarnedAmount,
    this.sumAvailableToSpend,
    this.approvedCommitment,
    this.intitialApprovedVsForecast,
  });
  BudgetOverview.fromJson(Map<String, dynamic> json) {
    projectId = json['project_id']?.toInt();
    sumTotalApprovedBudget = json['sum_total_approved_budget']?.toDouble();
    sumApprovedBudget = json['sum_approved_budget']?.toDouble();
    sumComittedCost = json['sum_comitted_cost']?.toDouble();
    sumApprovedPo = json['sum_approved_po']?.toDouble();
    sumActualCost = json['sum_actual_cost']?.toDouble();
    sumEstimatedCost = json['sum_estimated_cost']?.toDouble();
    sumActualRcvd = json['sum_actual_rcvd']?.toDouble();
    sumEarnedAmount = json['sum_earned_amount']?.toDouble();
    sumAvailableToSpend = json['sum_available_to_spend']?.toDouble();
    approvedCommitment = json['approved_commitment']?.toDouble();
    intitialApprovedVsForecast = json['intitial_approved_vs_forecast']?.toDouble();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['project_id'] = projectId;
    data['sum_total_approved_budget'] = sumTotalApprovedBudget;
    data['sum_approved_budget'] = sumApprovedBudget;
    data['sum_comitted_cost'] = sumComittedCost;
    data['sum_approved_po'] = sumApprovedPo;
    data['sum_actual_cost'] = sumActualCost;
    data['sum_estimated_cost'] = sumEstimatedCost;
    data['sum_actual_rcvd'] = sumActualRcvd;
    data['sum_earned_amount'] = sumEarnedAmount;
    data['sum_available_to_spend'] = sumAvailableToSpend;
    data['approved_commitment'] = approvedCommitment;
    data['intitial_approved_vs_forecast'] = intitialApprovedVsForecast;
    return data;
  }
}
