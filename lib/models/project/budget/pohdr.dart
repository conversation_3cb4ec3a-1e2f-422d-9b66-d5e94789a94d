class PoHdr {
  int? poId;
  String? poNumber;
  int? projectId;
  String? issueDate;
  String? dueDate;
  String? status;
  String? patmentTerms;
  String? currency;
  double? amount;
  String? issuedBy;
  String? approvedBy;
  String? approvedDate;
  int? supplierId;
  int? supplierSiteId;
  int? clientId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? comments;
  String? wfStatus;
  String? supplierEmail;
  String? deliveryDate;
  String? legalCompany;
  String? poType;
  String? buyerCode;
  String? buyerName;
  String? exportedFlag;
  int? linkFfePoId;
  double? totalAmount;
  double? chgTotAmount;
  String? poReadOnly;
  double? sumTotalAmtWithTax;
  double? sumVendorTaxAmt;
  double? sumAccruedTaxAmt;
  int? sumTotalAmtWithVtax;
  String? supplierName;
  String? supplierSiteType;
  String? supplierSiteName;
  double? sumAllocatedinvAmt;
  String? projectNumber;
  double? totalAmountUsd;
  String? statusDesc;
  double? totalAmountLocal;

  PoHdr({
    this.poId,
    this.poNumber,
    this.projectId,
    this.issueDate,
    this.dueDate,
    this.status,
    this.patmentTerms,
    this.currency,
    this.amount,
    this.issuedBy,
    this.approvedBy,
    this.approvedDate,
    this.supplierId,
    this.supplierSiteId,
    this.clientId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.comments,
    this.wfStatus,
    this.supplierEmail,
    this.deliveryDate,
    this.legalCompany,
    this.poType,
    this.buyerCode,
    this.buyerName,
    this.exportedFlag,
    this.linkFfePoId,
    this.totalAmount,
    this.chgTotAmount,
    this.poReadOnly,
    this.sumTotalAmtWithTax,
    this.sumVendorTaxAmt,
    this.sumAccruedTaxAmt,
    this.sumTotalAmtWithVtax,
    this.supplierName,
    this.supplierSiteType,
    this.supplierSiteName,
    this.sumAllocatedinvAmt,
    this.projectNumber,
    this.totalAmountUsd,
    this.statusDesc,
    this.totalAmountLocal,
  });
  PoHdr.fromJson(Map<String, dynamic> json) {
    poId = json['po_id']?.toInt();
    poNumber = json['po_number']?.toString();
    projectId = json['project_id']?.toInt();
    issueDate = json['issue_date']?.toString();
    dueDate = json['due_date']?.toString();
    status = json['status']?.toString();
    patmentTerms = json['patment_terms']?.toString();
    currency = json['currency']?.toString();
    amount = json['amount']?.toDouble();
    issuedBy = json['issued_by']?.toString();
    approvedBy = json['approved_by']?.toString();
    approvedDate = json['approved_date']?.toString();
    supplierId = json['supplier_id']?.toInt();
    supplierSiteId = json['supplier_site_id']?.toInt();
    clientId = json['client_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    comments = json['comments']?.toString();
    wfStatus = json['wf_status']?.toString();
    supplierEmail = json['supplier_email']?.toString();
    deliveryDate = json['delivery_date']?.toString();
    legalCompany = json['legal_company']?.toString();
    poType = json['po_type']?.toString();
    buyerCode = json['buyer_code']?.toString();
    buyerName = json['buyer_name']?.toString();
    exportedFlag = json['exported_flag']?.toString();
    linkFfePoId = json['link_ffe_po_id']?.toInt();
    totalAmount = json['total_amount']?.toDouble();
    chgTotAmount = json['chg_tot_amount']?.toDouble();
    poReadOnly = json['po_read_only']?.toString();
    sumTotalAmtWithTax = json['sum_total_amt_with_tax']?.toDouble();
    sumVendorTaxAmt = json['sum_vendor_tax_amt']?.toDouble();
    sumAccruedTaxAmt = json['sum_accrued_tax_amt']?.toDouble();
    sumTotalAmtWithVtax = json['sum_total_amt_with_vtax']?.toInt();
    supplierName = json['supplier_name']?.toString();
    supplierSiteType = json['supplier_site_type']?.toString();
    supplierSiteName = json['supplier_site_name']?.toString();
    sumAllocatedinvAmt = json['sum_allocatedinv_amt']?.toDouble();
    projectNumber = json['project_number']?.toString();
    totalAmountUsd = json['total_amount_usd']?.toDouble();
    statusDesc = json['status_desc']?.toString();
    totalAmountLocal = json['total_amount_local']?.toDouble();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['po_id'] = poId;
    data['po_number'] = poNumber;
    data['project_id'] = projectId;
    data['issue_date'] = issueDate;
    data['due_date'] = dueDate;
    data['status'] = status;
    data['patment_terms'] = patmentTerms;
    data['currency'] = currency;
    data['amount'] = amount;
    data['issued_by'] = issuedBy;
    data['approved_by'] = approvedBy;
    data['approved_date'] = approvedDate;
    data['supplier_id'] = supplierId;
    data['supplier_site_id'] = supplierSiteId;
    data['client_id'] = clientId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['comments'] = comments;
    data['wf_status'] = wfStatus;
    data['supplier_email'] = supplierEmail;
    data['delivery_date'] = deliveryDate;
    data['legal_company'] = legalCompany;
    data['po_type'] = poType;
    data['buyer_code'] = buyerCode;
    data['buyer_name'] = buyerName;
    data['exported_flag'] = exportedFlag;
    data['link_ffe_po_id'] = linkFfePoId;
    data['total_amount'] = totalAmount;
    data['chg_tot_amount'] = chgTotAmount;
    data['po_read_only'] = poReadOnly;
    data['sum_total_amt_with_tax'] = sumTotalAmtWithTax;
    data['sum_vendor_tax_amt'] = sumVendorTaxAmt;
    data['sum_accrued_tax_amt'] = sumAccruedTaxAmt;
    data['sum_total_amt_with_vtax'] = sumTotalAmtWithVtax;
    data['supplier_name'] = supplierName;
    data['supplier_site_type'] = supplierSiteType;
    data['supplier_site_name'] = supplierSiteName;
    data['sum_allocatedinv_amt'] = sumAllocatedinvAmt;
    data['project_number'] = projectNumber;
    data['total_amount_usd'] = totalAmountUsd;
    data['status_desc'] = statusDesc;
    data['total_amount_local'] = totalAmountLocal;
    return data;
  }
}
