class CoHdr {
  int? chgOrderId;
  int? poId;
  int? projectId;
  String? chgReason;
  String? status;
  String? chgOrderDate;
  String? approvedBy;
  String? approvedDate;
  int? clientId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? chgOrderNumber;
  String? exportedFlag;
  String? chgReasonDescription;
  String? insuranceReason;
  String? insuranceValid;
  double? totalAmtWithTax;
  int? supplierSiteId;
  int? supplierId;
  String? poCurrency;
  String? poNumber;
  String? coReadOnly;
  String? statusDesc;
  String? wfStatus;
  String? supplierName;

  CoHdr({
    this.chgOrderId,
    this.poId,
    this.projectId,
    this.chgReason,
    this.status,
    this.chgOrderDate,
    this.approvedBy,
    this.approvedDate,
    this.clientId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.chgOrderNumber,
    this.exportedFlag,
    this.chgReasonDescription,
    this.insuranceReason,
    this.insuranceValid,
    this.totalAmtWithTax,
    this.supplierSiteId,
    this.supplierId,
    this.poCurrency,
    this.poNumber,
    this.coReadOnly,
    this.statusDesc,
    this.wfStatus,
    this.supplierName,
  });
  CoHdr.fromJson(Map<String, dynamic> json) {
    chgOrderId = json['chg_order_id']?.toInt();
    poId = json['po_id']?.toInt();
    projectId = json['project_id']?.toInt();
    chgReason = json['chg_reason']?.toString();
    status = json['status']?.toString();
    chgOrderDate = json['chg_order_date']?.toString();
    approvedBy = json['approved_by']?.toString();
    approvedDate = json['approved_date']?.toString();
    clientId = json['client_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    chgOrderNumber = json['chg_order_number']?.toString();
    exportedFlag = json['exported_flag']?.toString();
    chgReasonDescription = json['chg_reason_description']?.toString();
    insuranceReason = json['insurance_reason']?.toString();
    insuranceValid = json['insurance_valid']?.toString();
    totalAmtWithTax = json['total_amt_with_tax']?.toDouble();
    supplierSiteId = json['supplier_site_id']?.toInt();
    supplierId = json['supplier_id']?.toInt();
    poCurrency = json['po_currency']?.toString();
    poNumber = json['po_number']?.toString();
    coReadOnly = json['co_read_only']?.toString();
    statusDesc = json['status_desc']?.toString();
    wfStatus = json['wf_status']?.toString();
    supplierName = json['supplier_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['chg_order_id'] = chgOrderId;
    data['po_id'] = poId;
    data['project_id'] = projectId;
    data['chg_reason'] = chgReason;
    data['status'] = status;
    data['chg_order_date'] = chgOrderDate;
    data['approved_by'] = approvedBy;
    data['approved_date'] = approvedDate;
    data['client_id'] = clientId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['chg_order_number'] = chgOrderNumber;
    data['exported_flag'] = exportedFlag;
    data['chg_reason_description'] = chgReasonDescription;
    data['insurance_reason'] = insuranceReason;
    data['insurance_valid'] = insuranceValid;
    data['total_amt_with_tax'] = totalAmtWithTax;
    data['supplier_site_id'] = supplierSiteId;
    data['supplier_id'] = supplierId;
    data['po_currency'] = poCurrency;
    data['po_number'] = poNumber;
    data['co_read_only'] = coReadOnly;
    data['status_desc'] = statusDesc;
    data['wf_status'] = wfStatus;
    data['supplier_name'] = supplierName;
    return data;
  }
}
