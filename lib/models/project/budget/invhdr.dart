class InvHdr {
  int? invoiceId;
  String? invoiceNumber;
  int? poId;
  int? projectId;
  String? status;
  String? invDate;
  String? submittedBy;
  String? approvedBy;
  int? clientId;
  String? creationDate;
  String? createdBy;
  String? lastUpdateDate;
  String? lastUpdatedBy;
  String? invoiceType;
  String? currency;
  int? supplierId;
  String? wfStatus;
  String? invReadOnly;
  int? supplierSiteId;
  String? poNumber;
  String? creationDateFilter;
  String? lastUpdateDateFilter;
  String? patmentTerms;
  String? comments;
  double? totalAmount;
  double? totalNetAmount;
  String? supplierName;
  String? statusDesc;
  String? projectNumber;
  String? buyerName;

  InvHdr({
    this.invoiceId,
    this.invoiceNumber,
    this.poId,
    this.projectId,
    this.status,
    this.invDate,
    this.submittedBy,
    this.approvedBy,
    this.clientId,
    this.creationDate,
    this.createdBy,
    this.lastUpdateDate,
    this.lastUpdatedBy,
    this.invoiceType,
    this.currency,
    this.supplierId,
    this.wfStatus,
    this.invReadOnly,
    this.supplierSiteId,
    this.poNumber,
    this.creationDateFilter,
    this.lastUpdateDateFilter,
    this.patmentTerms,
    this.comments,
    this.totalAmount,
    this.totalNetAmount,
    this.supplierName,
    this.statusDesc,
    this.projectNumber,
    this.buyerName,
  });
  InvHdr.fromJson(Map<String, dynamic> json) {
    invoiceId = json['invoice_id']?.toInt();
    invoiceNumber = json['invoice_number']?.toString();
    poId = json['po_id']?.toInt();
    projectId = json['project_id']?.toInt();
    status = json['status']?.toString();
    invDate = json['inv_date']?.toString();
    submittedBy = json['submitted_by']?.toString();
    approvedBy = json['approved_by']?.toString();
    clientId = json['client_id']?.toInt();
    creationDate = json['creation_date']?.toString();
    createdBy = json['created_by']?.toString();
    lastUpdateDate = json['last_update_date']?.toString();
    lastUpdatedBy = json['last_updated_by']?.toString();
    invoiceType = json['invoice_type']?.toString();
    currency = json['currency']?.toString();
    supplierId = json['supplier_id']?.toInt();
    wfStatus = json['wf_status']?.toString();
    invReadOnly = json['inv_read_only']?.toString();
    supplierSiteId = json['supplier_site_id']?.toInt();
    poNumber = json['po_number']?.toString();
    creationDateFilter = json['creation_date_filter']?.toString();
    lastUpdateDateFilter = json['last_update_date_filter']?.toString();
    patmentTerms = json['patment_terms']?.toString();
    comments = json['comments']?.toString();
    totalAmount = json['total_amount']?.toDouble();
    totalNetAmount = json['total_net_amount']?.toDouble();
    supplierName = json['supplier_name']?.toString();
    statusDesc = json['status_desc']?.toString();
    projectNumber = json['project_number']?.toString();
    buyerName = json['buyer_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['invoice_id'] = invoiceId;
    data['invoice_number'] = invoiceNumber;
    data['po_id'] = poId;
    data['project_id'] = projectId;
    data['status'] = status;
    data['inv_date'] = invDate;
    data['submitted_by'] = submittedBy;
    data['approved_by'] = approvedBy;
    data['client_id'] = clientId;
    data['creation_date'] = creationDate;
    data['created_by'] = createdBy;
    data['last_update_date'] = lastUpdateDate;
    data['last_updated_by'] = lastUpdatedBy;
    data['invoice_type'] = invoiceType;
    data['currency'] = currency;
    data['supplier_id'] = supplierId;
    data['wf_status'] = wfStatus;
    data['inv_read_only'] = invReadOnly;
    data['supplier_site_id'] = supplierSiteId;
    data['po_number'] = poNumber;
    data['creation_date_filter'] = creationDateFilter;
    data['last_update_date_filter'] = lastUpdateDateFilter;
    data['patment_terms'] = patmentTerms;
    data['comments'] = comments;
    data['total_amount'] = totalAmount;
    data['total_net_amount'] = totalNetAmount;
    data['supplier_name'] = supplierName;
    data['status_desc'] = statusDesc;
    data['project_number'] = projectNumber;
    data['buyer_name'] = buyerName;
    return data;
  }
}
