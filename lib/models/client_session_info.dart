class ClientSessionInfo {  
    
    final int? currentClientId;
    final int? currentBrandId;
    final int? currentBuId;
    final String? currentCountryCode;
    final String? userName;
    final String? firstName;
    final String? lastName;
    final String? clientName;
    final String? langCode;
    final String? isBuEnabled;
    final String? isContractor;
    final String? sessionid;
    final String? projDashboardlink;
    final int? personId;

    ClientSessionInfo({this.currentClientId, this.currentBrandId, this.currentBuId,this.currentCountryCode,
                       this.userName,this.firstName,this.lastName,this.clientName,this.langCode,this.isBuEnabled,
                       this.isContractor,
      this.sessionid,
      this.projDashboardlink,
      this.personId});

    factory ClientSessionInfo.fromJson(Map<String, dynamic> json) {
    return ClientSessionInfo(
      currentClientId: json['currentClientId'],
      currentBrandId: json['currentBrandId'],
      currentBuId: json['currentBuId'],
      currentCountryCode: json['currentCountryCode'],
      userName: json['userName'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      clientName: json['clientName'],
      langCode: json['langCode'],
      isBuEnabled: json['isBuEnabled'],
      isContractor: json['isContractor'],
      sessionid: json['sessionid'],
      projDashboardlink: json['projDashboardlink'],
        personId: json['personId']
    );
  }

}