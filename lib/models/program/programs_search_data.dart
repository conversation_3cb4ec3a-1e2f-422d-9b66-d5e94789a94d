class ProgramsSearchData {
  int? programId;
  String? programName;
  String? programDesc;
  int? programYear;
  int? budgetYear;
  String? status;
  String? sponsor;
  String? startDate;
  String? endDate;
  String? approvalDate;
  int? noOfProjects;
  double? approvedBudget;
  double? remainingBudget;
  double? budgetAllocated;
  double? budgetCommitted;
  double? actualSpent;
  double? cmpltdBudget;
  String? statusDesc;
  double? capitalBudget;
  double? commitments;
  int? rootFolderId;

  ProgramsSearchData({
    this.programId,
    this.programName,
    this.programDesc,
    this.programYear,
    this.budgetYear,
    this.status,
    this.sponsor,
    this.startDate,
    this.endDate,
    this.approvalDate,
    this.noOfProjects,
    this.approvedBudget,
    this.remainingBudget,
    this.budgetAllocated,
    this.budgetCommitted,
    this.actualSpent,
    this.cmpltdBudget,
    this.statusDesc,
    this.capitalBudget,
    this.commitments,
    this.rootFolderId,
  });
  ProgramsSearchData.fromJson(Map<String, dynamic> json) {
    programId = json['program_id']?.toInt();
    programName = json['program_name']?.toString() ?? '';
    programDesc = json['program_desc']?.toString() ?? '';
    programYear = json['program_year']?.toInt();
    budgetYear = json['budget_year']?.toInt();
    status = json['status']?.toString() ?? '';
    sponsor = json['sponsor']?.toString() ?? '';
    startDate = json['start_date']?.toString() ?? '';
    endDate = json['end_date']?.toString() ?? '';
    approvalDate = json['approval_date']?.toString() ?? '';
    noOfProjects = json['no_of_projects']?.toInt();
    approvedBudget = json['approved_budget']?.toDouble() ?? 0.0;
    remainingBudget = json['remaining_budget']?.toDouble() ?? 0.0;
    budgetAllocated = json['budget_allocated']?.toDouble() ?? 0.0;
    budgetCommitted = json['budget_committed']?.toDouble() ?? 0.0;
    actualSpent = json['actual_spent']?.toDouble() ?? 0.0;
    cmpltdBudget = json['cmpltd_budget']?.toDouble() ?? 0;
    statusDesc = json['status_desc']?.toString();
    capitalBudget = json['capital_budget']?.toDouble() ?? 0.0;
    commitments = json['commitments']?.toDouble() ?? 0.0;
    rootFolderId = json['root_folder_id']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['program_id'] = programId;
    data['program_name'] = programName;
    data['program_desc'] = programDesc;
    data['program_year'] = programYear;
    data['budget_year'] = budgetYear;
    data['status'] = status;
    data['sponsor'] = sponsor;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['approval_date'] = approvalDate;
    data['no_of_projects'] = noOfProjects;
    data['approved_budget'] = approvedBudget;
    data['remaining_budget'] = remainingBudget;
    data['budget_allocated'] = budgetAllocated;
    data['budget_committed'] = budgetCommitted;
    data['actual_spent'] = actualSpent;
    data['cmpltd_budget'] = cmpltdBudget;
    data['status_desc'] = statusDesc;
    data['capital_budget'] = capitalBudget;
    data['commitments'] = commitments;
    data['root_folder_id'] = rootFolderId;
    return data;
  }
}
