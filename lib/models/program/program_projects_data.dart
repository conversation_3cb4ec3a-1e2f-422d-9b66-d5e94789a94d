class ProgramProjectsData {
  int? projectId;
  String? projectName;
  String? description;
  int? programId;
  int? entityId;
  String? entityType;
  String? entitynumber;
  String? status;
  String? projectType;
  String? projectTypeValue;
  String? communicatedOpenDate;
  String? projectedOpenDate;
  String? facilityType;
  String? constructionManager;
  String? projectNumber;
  double? approvedbudgetusd;
  double? actualcostusd;
  double? estimatedcostusd;
  double? commitedcostusd;
  double? approvedbudgetbu;
  double? actualcostbu;
  double? estimatedcostbu;
  double? commitedcostbu;
  double? approvedbudgetlocal;
  double? actualcostlocal;
  double? estimatedcostlocal;
  double? commitedcostlocal;
  String? realEstateManager;
  String? statusDesc;

  ProgramProjectsData({
    this.projectId,
    this.projectName,
    this.description,
    this.programId,
    this.entityId,
    this.entityType,
    this.entitynumber,
    this.status,
    this.projectType,
    this.projectTypeValue,
    this.communicatedOpenDate,
    this.projectedOpenDate,
    this.facilityType,
    this.constructionManager,
    this.projectNumber,
    this.approvedbudgetusd,
    this.actualcostusd,
    this.estimatedcostusd,
    this.commitedcostusd,
    this.approvedbudgetbu,
    this.actualcostbu,
    this.estimatedcostbu,
    this.commitedcostbu,
    this.approvedbudgetlocal,
    this.actualcostlocal,
    this.estimatedcostlocal,
    this.commitedcostlocal,
    this.realEstateManager,
    this.statusDesc,
  });
  ProgramProjectsData.fromJson(Map<String, dynamic> json) {
    projectId = json['project_id']?.toInt();
    projectName = json['project_name']?.toString();
    description = json['description']?.toString();
    programId = json['program_id']?.toInt();
    entityId = json['entity_id']?.toInt();
    entityType = json['entity_type']?.toString();
    entitynumber = json['entitynumber']?.toString();
    status = json['status']?.toString();
    projectType = json['project_type']?.toString();
    projectTypeValue = json['project_type_value']?.toString();
    communicatedOpenDate = json['communicated_open_date']?.toString();
    projectedOpenDate = json['projected_open_date']?.toString();
    facilityType = json['facility_type']?.toString();
    constructionManager = json['construction_manager']?.toString();
    projectNumber = json['project_number']?.toString();
    approvedbudgetusd = json['approvedbudgetusd']?.toDouble();
    actualcostusd = json['actualcostusd']?.toDouble();
    estimatedcostusd = json['estimatedcostusd']?.toDouble();
    commitedcostusd = json['commitedcostusd']?.toDouble();
    approvedbudgetbu = json['approvedbudgetbu']?.toDouble();
    actualcostbu = json['actualcostbu']?.toDouble();
    estimatedcostbu = json['estimatedcostbu']?.toDouble();
    commitedcostbu = json['commitedcostbu']?.toDouble();
    approvedbudgetlocal = json['approvedbudgetlocal']?.toDouble();
    actualcostlocal = json['actualcostlocal']?.toDouble();
    estimatedcostlocal = json['estimatedcostlocal']?.toDouble();
    commitedcostlocal = json['commitedcostlocal']?.toDouble();
    realEstateManager = json['real_estate_manager']?.toString();
    statusDesc = json['status_desc']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['project_id'] = projectId;
    data['project_name'] = projectName;
    data['description'] = description;
    data['program_id'] = programId;
    data['entity_id'] = entityId;
    data['entity_type'] = entityType;
    data['entitynumber'] = entitynumber;
    data['status'] = status;
    data['project_type'] = projectType;
    data['project_type_value'] = projectTypeValue;
    data['communicated_open_date'] = communicatedOpenDate;
    data['projected_open_date'] = projectedOpenDate;
    data['facility_type'] = facilityType;
    data['construction_manager'] = constructionManager;
    data['project_number'] = projectNumber;
    data['approvedbudgetusd'] = approvedbudgetusd;
    data['actualcostusd'] = actualcostusd;
    data['estimatedcostusd'] = estimatedcostusd;
    data['commitedcostusd'] = commitedcostusd;
    data['approvedbudgetbu'] = approvedbudgetbu;
    data['actualcostbu'] = actualcostbu;
    data['estimatedcostbu'] = estimatedcostbu;
    data['commitedcostbu'] = commitedcostbu;
    data['approvedbudgetlocal'] = approvedbudgetlocal;
    data['actualcostlocal'] = actualcostlocal;
    data['estimatedcostlocal'] = estimatedcostlocal;
    data['commitedcostlocal'] = commitedcostlocal;
    data['real_estate_manager'] = realEstateManager;
    data['status_desc'] = statusDesc;
    return data;
  }
}
