class SrPersonPrimeLoc {
  int? allocationId;
  int? resourceId;
  int? buildingId;
  int? floorId;
  int? spaceId;
  String? storeName;
  String? spaceDisplayName;

  SrPersonPrimeLoc({
    this.allocationId,
    this.resourceId,
    this.buildingId,
    this.floorId,
    this.spaceId,
    this.storeName,
    this.spaceDisplayName,
  });
  SrPersonPrimeLoc.fromJson(Map<String, dynamic> json) {
    allocationId = json['allocation_id']?.toInt();
    resourceId = json['resource_id']?.toInt();
    buildingId = json['building_id']?.toInt();
    floorId = json['floor_id']?.toInt();
    spaceId = json['space_id']?.toInt();
    storeName = json['store_name']?.toString();
    spaceDisplayName = json['space_display_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['allocation_id'] = allocationId;
    data['resource_id'] = resourceId;
    data['building_id'] = buildingId;
    data['floor_id'] = floorId;
    data['space_id'] = spaceId;
    data['store_name'] = storeName;
    data['space_display_name'] = spaceDisplayName;
    return data;
  }
}
