class SrSpace {
  int? spaceId;
  int? buildingId;
  int? floorId;
  String? spaceName;
  String? spaceNumber;
  String? status;
  String? propertyName;
  String? buildingName;
  String? floorName;
  String? occupancyStatus;
  String? spaceCategory;
  String? spaceUse;
  String? isReservable;
  String? isWkst;
  String? wkstType;
  String? spaceDisplayName;

  SrSpace({
    this.spaceId,
    this.buildingId,
    this.floorId,
    this.spaceName,
    this.spaceNumber,
    this.status,
    this.propertyName,
    this.buildingName,
    this.floorName,
    this.occupancyStatus,
    this.spaceCategory,
    this.spaceUse,
    this.isReservable,
    this.isWkst,
    this.wkstType,
    this.spaceDisplayName,
  });
  SrSpace.fromJson(Map<String, dynamic> json) {
    spaceId = json['space_id']?.toInt();
    buildingId = json['building_id']?.toInt();
    floorId = json['floor_id']?.toInt();
    spaceName = json['space_name']?.toString();
    spaceNumber = json['space_number']?.toString();
    status = json['status']?.toString();
    propertyName = json['property_name']?.toString();
    buildingName = json['building_name']?.toString();
    floorName = json['floor_name']?.toString();
    occupancyStatus = json['occupancy_status']?.toString();
    spaceCategory = json['space_category']?.toString();
    spaceUse = json['space_use']?.toString();
    isReservable = json['is_reservable']?.toString();
    isWkst = json['is_wkst']?.toString();
    wkstType = json['wkst_type']?.toString();
    spaceDisplayName = json['space_display_name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['space_id'] = spaceId;
    data['building_id'] = buildingId;
    data['floor_id'] = floorId;
    data['space_name'] = spaceName;
    data['space_number'] = spaceNumber;
    data['status'] = status;
    data['property_name'] = propertyName;
    data['building_name'] = buildingName;
    data['floor_name'] = floorName;
    data['occupancy_status'] = occupancyStatus;
    data['space_category'] = spaceCategory;
    data['space_use'] = spaceUse;
    data['is_reservable'] = isReservable;
    data['is_wkst'] = isWkst;
    data['wkst_type'] = wkstType;
    data['space_display_name'] = spaceDisplayName;
    return data;
  }
}
