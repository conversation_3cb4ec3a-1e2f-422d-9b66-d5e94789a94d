class SrAssetName {
  String? assetInstanceName;
  int? assetInstanceId;
  String? assetType;
  String? assetmastername;
  String? manufacturer;
  String? assosiatedEntityType;
  int? assosiatedEntityId;
  String? status;
  int? assetIdentifier;

  SrAssetName({
    this.assetInstanceName,
    this.assetInstanceId,
    this.assetType,
    this.assetmastername,
    this.manufacturer,
    this.assosiatedEntityType,
    this.assosiatedEntityId,
    this.status,
    this.assetIdentifier,
  });
  SrAssetName.fromJson(Map<String, dynamic> json) {
    assetInstanceName = json['asset_instance_name']?.toString();
    assetInstanceId = json['asset_instance_id']?.toInt();
    assetType = json['asset_type']?.toString();
    assetmastername = json['assetmastername']?.toString();
    manufacturer = json['manufacturer']?.toString();
    assosiatedEntityType = json['assosiated_entity_type']?.toString();
    assosiatedEntityId = json['assosiated_entity_id']?.toInt();
    status = json['status']?.toString();
    assetIdentifier = json['asset_identifier']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['asset_instance_name'] = assetInstanceName;
    data['asset_instance_id'] = assetInstanceId;
    data['asset_type'] = assetType;
    data['assetmastername'] = assetmastername;
    data['manufacturer'] = manufacturer;
    data['assosiated_entity_type'] = assosiatedEntityType;
    data['assosiated_entity_id'] = assosiatedEntityId;
    data['status'] = status;
    data['asset_identifier'] = assetIdentifier;
    return data;
  }
}
