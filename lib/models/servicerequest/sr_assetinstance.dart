class SrAssetInstance {
  String? assetInstanceName;
  int? assetInstanceId;
  String? assetType;
  String? assetmastername;
  String? manufacturer;
  String? assosiatedEntityType;
  int? assosiatedEntityId;
  String? status;
  int? locationId;
  int? floorId;
  int? spaceId;
  String? entityName;
  String? floorName;
  String? spaceName;
  String? assetInstanceNumber;

  SrAssetInstance({
    this.assetInstanceName,
    this.assetInstanceId,
    this.assetType,
    this.assetmastername,
    this.manufacturer,
    this.assosiatedEntityType,
    this.assosiatedEntityId,
    this.status,
    this.locationId,
    this.floorId,
    this.spaceId,
    this.entityName,
    this.spaceName,
    this.floorName,
    this.assetInstanceNumber,
  });
  SrAssetInstance.fromJson(Map<String, dynamic> json) {
    assetInstanceName = json['asset_instance_name']?.toString();
    assetInstanceId = json['asset_instance_id']?.toInt();
    assetType = json['asset_type']?.toString();
    assetmastername = json['assetmastername']?.toString();
    manufacturer = json['manufacturer']?.toString();
    assosiatedEntityType = json['assosiated_entity_type']?.toString();
    assosiatedEntityId = json['assosiated_entity_id']?.toInt();
    status = json['status']?.toString();
    locationId = json['location_id']?.toInt();
    floorId = json['floor_id']?.toInt();
    spaceId = json['space_id']?.toInt();

    entityName = json['entity_name']?.toString();
    spaceName = json['space_name']?.toString();
    floorName = json['floor_name']?.toString();
    assetInstanceNumber = json['asset_instance_number']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['asset_instance_name'] = assetInstanceName;
    data['asset_instance_id'] = assetInstanceId;
    data['asset_type'] = assetType;
    data['assetmastername'] = assetmastername;
    data['manufacturer'] = manufacturer;
    data['assosiated_entity_type'] = assosiatedEntityType;
    data['assosiated_entity_id'] = assosiatedEntityId;
    data['status'] = status;
    data['location_id'] = locationId;
    data['floor_id'] = floorId;
    data['space_id'] = spaceId;

    data['entity_name'] = entityName;
    data['space_name'] = spaceName;
    data['floor_name'] = floorName;
    data['asset_instance_number'] = assetInstanceNumber;
    return data;
  }
}
