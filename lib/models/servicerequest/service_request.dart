class Servicerequest {
  int? serviceReqId;
  String? serviceReqNumber;
  String? entityType;
  int? entityId;
  String? issueType;
  String? impactedArea;
  String? problemCode;
  String? priority;
  String? requestedFor;
  String? phone;
  String? notifyEmail;
  String? status;
  String? lastUpdatedBy;
  int? floorId;
  int? spaceId;
  String? createdBy;
  String? creationDate;
  String? createdDate;
  String? storeName;
  String? spaceDisplayName;
  String? description;
  String? assetInstanceName;
  int? assetInstanceId;
  String? actionType;

  Servicerequest({
    this.serviceReqId,
    this.lastUpdatedBy,
    this.serviceReqNumber,
    this.entityType,
    this.entityId,
    this.issueType,
    this.impactedArea,
    this.problemCode,
    this.priority,
    this.requestedFor,
    this.phone,
    this.notifyEmail,
    this.status,
    this.floorId,
    this.spaceId,
    this.createdBy,
    this.creationDate,
    this.createdDate,
    this.storeName,
    this.spaceDisplayName,
    this.description,
    this.assetInstanceName,
    this.assetInstanceId,
    this.actionType,
  });
  Servicerequest.fromJson(Map<String, dynamic> json) {
    serviceReqId = json['service_req_id']?.toInt();
    serviceReqNumber = json['service_req_number']?.toString();
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    issueType = json['issue_type']?.toString();
    impactedArea = json['impacted_area']?.toString();
    problemCode = json['problem_code']?.toString();
    priority = json['priority']?.toString();
    requestedFor = json['requested_for']?.toString();
    phone = json['phone']?.toString();
    notifyEmail = json['notify_email']?.toString();
    status = json['status']?.toString();
    floorId = json['floor_id']?.toInt();
    spaceId = json['space_id']?.toInt();
    createdBy = json['created_by']?.toString();
    lastUpdatedBy = json['lastUpdatedBy']?.toString();
    creationDate = json['creation_date']?.toString();
    createdDate = json['created_date']?.toString();
    storeName = json['store_name']?.toString();
    spaceDisplayName = json['space_display_name']?.toString();
    description = json['description']?.toString();
    assetInstanceName = json['asset_instance_name']?.toString();
    assetInstanceId = json['asset_instance_id']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['service_req_id'] = serviceReqId;
    data['service_req_number'] = serviceReqNumber;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['issue_type'] = issueType;
    data['impacted_area'] = impactedArea;
    data['problem_code'] = problemCode;
    data['priority'] = priority;
    data['requested_for'] = requestedFor;
    data['phone'] = phone;
    data['notify_email'] = notifyEmail;
    data['status'] = status;
    data['floor_id'] = floorId;
    data['space_id'] = spaceId;
    data['created_by'] = createdBy;
    data['lastUpdatedBy'] = lastUpdatedBy;
    data['creation_date'] = creationDate;
    data['created_date'] = createdDate;
    data['store_name'] = storeName;
    data['space_display_name'] = spaceDisplayName;
    data['description'] = description;
    data['asset_instance_name'] = assetInstanceName;
    data['asset_instance_id'] = assetInstanceId;
    data['actionType'] = actionType;
    return data;
  }
}
