class SrFloors {
  int? storeId;
  String? storeNumber;
  String? storeName;
  String? locEntityType;
  String? locSubEntityType;
  int? buildingId;
  String? properityId;
  String? status;
  int? dwgFileId;
  String? buildingStatusType;

  SrFloors({
    this.storeId,
    this.storeNumber,
    this.storeName,
    this.locEntityType,
    this.locSubEntityType,
    this.buildingId,
    this.properityId,
    this.status,
    this.dwgFileId,
    this.buildingStatusType,
  });
  SrFloors.fromJson(Map<String, dynamic> json) {
    storeId = json['store_id']?.toInt();
    storeNumber = json['store_number']?.toString();
    storeName = json['store_name']?.toString();
    locEntityType = json['loc_entity_type']?.toString();
    locSubEntityType = json['loc_sub_entity_type']?.toString();
    buildingId = json['building_id']?.toInt();
    properityId = json['properity_id']?.toString();
    status = json['status']?.toString();
    dwgFileId = json['dwg_file_id']?.toInt();
    buildingStatusType = json['building_status_type']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['store_id'] = storeId;
    data['store_number'] = storeNumber;
    data['store_name'] = storeName;
    data['loc_entity_type'] = locEntityType;
    data['loc_sub_entity_type'] = locSubEntityType;
    data['building_id'] = buildingId;
    data['properity_id'] = properityId;
    data['status'] = status;
    data['dwg_file_id'] = dwgFileId;
    data['building_status_type'] = buildingStatusType;
    return data;
  }
}
