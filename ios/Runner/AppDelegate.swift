import UIKit
import Flutter
import GoogleMaps
import flutter_downloader // Add import for flutter_downloader

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("AIzaSyCyoma0qJd0CUTsyF70SJ-trayonRZmGKs") // Existing Google Maps API key setup
    GeneratedPluginRegistrant.register(with: self) // Existing plugin registration
    FlutterDownloaderPlugin.setPluginRegistrantCallback(registerPlugins) // Register flutter_downloader
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}

// Function to register plugins for flutter_downloader
private func registerPlugins(registry: FlutterPluginRegistry) {
    if (!registry.hasPlugin("FlutterDownloaderPlugin")) {
        FlutterDownloaderPlugin.register(with: registry.registrar(forPlugin: "FlutterDownloaderPlugin")!)
    }
}
