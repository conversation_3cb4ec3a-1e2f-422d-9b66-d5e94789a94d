{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a5026ae05d33b7d0696f3f30dec1f88", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc713e17c98d4c728ba82d7c1d7feca1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb2120cb900d8af4bab1bab6ac99e9d5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981fc86f036e3268b6b5e866f601ab4bf6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb2120cb900d8af4bab1bab6ac99e9d5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e865a2907469af1bc7acb09848877d96", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851faa29213dac0f701d68b807a0648d4", "guid": "bfdfe7dc352907fc980b868725387e98975394c9617093a190560d80deb76e02", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986f26e8174ea4a2c56d7dd3dcd78f4431", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f43d2e6fd60b2bb4534493d8ac77e78e", "guid": "bfdfe7dc352907fc980b868725387e983825ece90d8f37fe6e3e838d71a68fed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eda5062b89ee1c5611f9e3d6ef284ba0", "guid": "bfdfe7dc352907fc980b868725387e9846774de0fafea447765d5a08bfbd754e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae806c02799c721cce1254dd439f2613", "guid": "bfdfe7dc352907fc980b868725387e98ba5ecee7775916793588c961b6d2706a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abad59ec87311035c600a7526bf3a746", "guid": "bfdfe7dc352907fc980b868725387e9835ec7eec6156a20ef64db9dbc0e472c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98451d8ae3428c6684f7d3dd469123e7ce", "guid": "bfdfe7dc352907fc980b868725387e98f5955339294744a0b156c368c082911b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d33e85b6072b30dcad9f7de2d17a1d", "guid": "bfdfe7dc352907fc980b868725387e9826794f1089b1126dff52866094ea070e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d94a6a7ef0b5a836ff3827a1238f1ec", "guid": "bfdfe7dc352907fc980b868725387e9810a77168693743044489aa9d760f12db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b9bc425d767dfdbae62aa69d00a653f", "guid": "bfdfe7dc352907fc980b868725387e985c0b7a671aa0c7964c72f192118f8994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818ffec39455c314b2f3e7beeb5a693cf", "guid": "bfdfe7dc352907fc980b868725387e98faf78ab2ef13044e43af474710f2d30b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b80bc26c90991a61f132bbad0da7b0", "guid": "bfdfe7dc352907fc980b868725387e986c2cca8c614fe23159c95e5eabc95f38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f952b82a98085a07a23601dcbf9bfaf2", "guid": "bfdfe7dc352907fc980b868725387e98ac39e292433892f78409a8cfdcff3a3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870da69b0232d73c3b74a570ff30c8326", "guid": "bfdfe7dc352907fc980b868725387e98b0acb116ad1062242fe5b1971c8f49a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860078ff4ba9d7865cddbeb14b3755704", "guid": "bfdfe7dc352907fc980b868725387e98851fa6e88ed0260ca50989a917747ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb704545a2ce16d122039b1500059852", "guid": "bfdfe7dc352907fc980b868725387e98fb68cba71ce0f47b3e4e2db3b1ae8fe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e5c44ea5ab2c25d470a784f37131e4", "guid": "bfdfe7dc352907fc980b868725387e9856e491b7559b1c60fefbb779b4432b0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985293a5cd69c0ae455eb8452ffa2db3c4", "guid": "bfdfe7dc352907fc980b868725387e98e7a5d4438957cfa1da4aa4189bfce430"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b066cf419c3853ab2c71de19b0765306", "guid": "bfdfe7dc352907fc980b868725387e98a33844f9d7151aa6a682337aef1c043d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ff816893cf12fb91221ed83a87fcf9", "guid": "bfdfe7dc352907fc980b868725387e989fafdeec1c050a3eb027cc251f6168b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701988e2feaacb8b83eb10656924d54a", "guid": "bfdfe7dc352907fc980b868725387e9882637a7bdde7c686615ce09ede74d275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814b2c932c984cfd070c592daedf39657", "guid": "bfdfe7dc352907fc980b868725387e98707a84e6ae8a0c2c9c7c8b7bf8467fed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd93ce744c11fd4942eddc3b7c5ebb2b", "guid": "bfdfe7dc352907fc980b868725387e9825f21f2ad3ba551d648e28f6687d6017"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c675afa3cbfb4c78dbc1cc20bbba5c", "guid": "bfdfe7dc352907fc980b868725387e988d6bbc32206aaaf337d629048d0293a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832476b5103984fd05427264db0f71a82", "guid": "bfdfe7dc352907fc980b868725387e980067c6e1e0fa7a3c1028038b5ea5bda8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c617c1763800cc84250ac783c5ea36ac", "guid": "bfdfe7dc352907fc980b868725387e9837c6c4ef6e92bbf2a2ac77e776c2007d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f129e4d06159cde3ddc9b33d5371bf0", "guid": "bfdfe7dc352907fc980b868725387e9805e58120c3a9b02b2750f0db330db158"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868e7f84ee383c3e5315da4dd3f655a30", "guid": "bfdfe7dc352907fc980b868725387e981e248da3e615d261fdab7fac0e8058f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347b13753e835e308beb2c8b2891f8ea", "guid": "bfdfe7dc352907fc980b868725387e98bd65960421d5d96db99750dd33ec100f"}], "guid": "bfdfe7dc352907fc980b868725387e9899239d11704e12983975d7a90b88f785", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981e734e2f21f8b53bfcfc90a84d85fe3e", "guid": "bfdfe7dc352907fc980b868725387e984152d0a86e752f240ea813e2127bda26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c821778e0140b12be669d582cafeb4af", "guid": "bfdfe7dc352907fc980b868725387e9860571afc251b98abf452ba428f55ae95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e98373d91e10557bffb318e98c4071e64b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efe88d914a31660f0a3cdfa55dbb3135", "guid": "bfdfe7dc352907fc980b868725387e986fceb84b71fddd395bfcdd0997e56703"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f0d9556536b9a1c4c34f63a34277dbb", "guid": "bfdfe7dc352907fc980b868725387e9840886e0594c88b644d83f05a112507cd"}], "guid": "bfdfe7dc352907fc980b868725387e9836b949269518709ccd7c7141843bb547", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981f5b4c19d72a79bf9450f80a66ad6196", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e9856ee2a046c4ef29eb3aded8f8813c19d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}