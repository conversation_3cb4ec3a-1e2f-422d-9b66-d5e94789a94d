{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980573bbfece4afb1bf5a76ddd765311d0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b44da7a35eb8b26a79fdc765bc663b27", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988be877de4ab86d4e80aedfa4cbf8ae32", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d66f7e42b49fabb2050b4dc67c313861", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988be877de4ab86d4e80aedfa4cbf8ae32", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5c129b7854a20b39565f0a45fe4530b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984104cde65c1795a747d2d75522618708", "guid": "bfdfe7dc352907fc980b868725387e98e8fbc74e024f2dc908a9098eb0048402", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981867283f415acf4566c1e43d538b12f2", "guid": "bfdfe7dc352907fc980b868725387e98f8d0139db9b00d44498115a4b266b142", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6eeae3a3077cb849d934826dd6bbe4d", "guid": "bfdfe7dc352907fc980b868725387e987dc5dcea8da14697cab584e4a4e01eff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ef3334f1df54f391aa10e382bbf5b4f", "guid": "bfdfe7dc352907fc980b868725387e980bc96acb04227567a22259d69342bfd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837d5075fd244823e35936e027f8e07e2", "guid": "bfdfe7dc352907fc980b868725387e98504a5ebc00b58ce4ea363b58db6021b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106dc6f461b1576f2fe0dd866c0bd7b8", "guid": "bfdfe7dc352907fc980b868725387e9815a8eaf12a9798e3aa29ca9c1760b887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f05e4473d39fe36a426c2ab925f7b6c", "guid": "bfdfe7dc352907fc980b868725387e98aa3c4f17c1000ec4c5d55a810faf7bac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872ba70815de5e88d745ddeadb395b3c2", "guid": "bfdfe7dc352907fc980b868725387e9880af7588be9b62efe2526823750f6c73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808fcbfff9f2dad48e8e7c3f87e39b32c", "guid": "bfdfe7dc352907fc980b868725387e987f22e104cb86ae4f85e629d9039d5b8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c206ac8f596c48f9187b9a6d329c6063", "guid": "bfdfe7dc352907fc980b868725387e982964f8170eecb2ef2a41e41606257531", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855caf15d7d502632ee98c0e84b124a26", "guid": "bfdfe7dc352907fc980b868725387e98586b5540c04e7956bd8a265a7a3627b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee1671b1b8b6947ec2dd7f60f32296c2", "guid": "bfdfe7dc352907fc980b868725387e980e6d0d2512bdb231977398667586d217", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23f5e23a4eb32efafb44683497d5c0d", "guid": "bfdfe7dc352907fc980b868725387e98547e0706890cbf4447dd0f4e7e619ed8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b833e76d4b8a3049202a5389ddbc49ec", "guid": "bfdfe7dc352907fc980b868725387e98e4cce4264603e2e54fe8a75d8e6d9925", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856390b04118457bcd7a3f1ea3ca3ff32", "guid": "bfdfe7dc352907fc980b868725387e98017d563f3829223968dfc204bc882261", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811c591146da254234e98e4d01ac5ae10", "guid": "bfdfe7dc352907fc980b868725387e98d45ac602457fe38bd5e8358b707d6d1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d967210b2e347ae31fe8d4a149dc842", "guid": "bfdfe7dc352907fc980b868725387e982c56fb139b6102d4d4dde2b9aa693a8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7f3d913c9630221915b3f99507cdcb9", "guid": "bfdfe7dc352907fc980b868725387e98cd6c69292f698b6b0e0a4e621bea6c6e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9811af2ef61d70014f1ec98968e0f4d3f1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b403ab93fb54648c1844cf9303efe29a", "guid": "bfdfe7dc352907fc980b868725387e9804e1e387bd7a44edb0ebef5d2cec346b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc176a6a5efbb5ee7b498a987d9459e9", "guid": "bfdfe7dc352907fc980b868725387e982cf62647c1b5050278884316d8d5eff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982126cb36a00ce5013de80d33d7086e8b", "guid": "bfdfe7dc352907fc980b868725387e98ffce3c1fe58d97c8f2ee0393914e87e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981072acd13a72ed4edea92b883660f789", "guid": "bfdfe7dc352907fc980b868725387e9844c95f39bcf9e1b9b2f435562eff311f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98282442d8fd6c8444bcf17d2c9cecb555", "guid": "bfdfe7dc352907fc980b868725387e98eeef754e5cb72c9d2295d15534720692"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc7e10e8006d17669080bb91c0982f95", "guid": "bfdfe7dc352907fc980b868725387e98b73f9bf85e11df2b5273ae783a6d475a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a5a7ebc498132a7c7263cd859ee2369", "guid": "bfdfe7dc352907fc980b868725387e984f6e1845436bebde9a9b3de5223c3539"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471ad571ee5f958c36c662f29cb67767", "guid": "bfdfe7dc352907fc980b868725387e980bcbc919208417babebfcbdb0cdabd38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1506e433ff56d962ea576c785a8d95c", "guid": "bfdfe7dc352907fc980b868725387e981dc1c729b0ba975f85c50e953adb7057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca333a2925e53add60bdaf5fbc9128da", "guid": "bfdfe7dc352907fc980b868725387e98ccbf070d6cc899c58192977ed7855288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876aa47e69f672255f8e77323a90547fd", "guid": "bfdfe7dc352907fc980b868725387e98f4967c52222d9042891b37fcee9c1dd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b16b7d78336f153023c9992c5e8395", "guid": "bfdfe7dc352907fc980b868725387e98f4f4f4e94d3fbb7eab6bfa3b5fe9d402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6e6bcf6140093c0f6b410b95ea9329", "guid": "bfdfe7dc352907fc980b868725387e98ee3d7ad495f78f583b609037dbc3b123"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb4b0f49fb006b8dfa860bfc9cafcf7", "guid": "bfdfe7dc352907fc980b868725387e983af7239ed327b10f650a29971ebf5e82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9bcd0e7e47d07c5d6d94d4c3751ba1d", "guid": "bfdfe7dc352907fc980b868725387e98935e9b8f99ab0c6ebf91222cd0ecb4a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2aa27cd60013f2aea0c51267062809c", "guid": "bfdfe7dc352907fc980b868725387e98f5084db4f5180050b7c41e0c69e82087"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01fd11ffd557e4dd8caa90533997d4d", "guid": "bfdfe7dc352907fc980b868725387e98baf02d0ce0dcfc50dd6278ad305426d2"}], "guid": "bfdfe7dc352907fc980b868725387e98f058f43412b1cfa2f72676b316aebe74", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e9813ae4d80d7e6dcf3efabadd70f0523ac"}], "guid": "bfdfe7dc352907fc980b868725387e98689e3ceb22785def0e8e3aa4a3f0c5ff", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9829c79da60965f21f0c2875b130dbb9bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}