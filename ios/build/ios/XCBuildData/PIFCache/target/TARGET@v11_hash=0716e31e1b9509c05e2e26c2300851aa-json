{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9877aff9c6e3ffbdcf0d9e7c81230ed5d3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983000b497bb09479468c0390e594d8342", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c94c6cfe6bc7cb9d5198e964fef52a67", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c6c98f0462a9c32dd4b5f1d229426903", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c94c6cfe6bc7cb9d5198e964fef52a67", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980bc205f56ddd18721c867471c3867697", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98703bd704e64166a546e7070b5306d44a", "guid": "bfdfe7dc352907fc980b868725387e98fc20616a15f31ba464c0f9a642f15653", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e5ee5d1a916ad1ad4d33e3ca1bab452", "guid": "bfdfe7dc352907fc980b868725387e9871a5b8d3c22d04974867efb20e22d95c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc8a5869153e75de98ffe8a46590cbe7", "guid": "bfdfe7dc352907fc980b868725387e98b5f12e0ca26a18d9d0945c54a216c1a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874dba4fa655b01ab87f7b6d655d130b7", "guid": "bfdfe7dc352907fc980b868725387e987b1287d3ba1007aa8c9b2f52f3123308", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ffe6cbb51e34be9093ad1a350be1858", "guid": "bfdfe7dc352907fc980b868725387e986fd7bfca36f8ad091101c3916316456d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c475c336439bfb37763ea8372bb2815f", "guid": "bfdfe7dc352907fc980b868725387e989ecc9bd639cdccab5c9468096569e548", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98676f01be2c06372ef55a2ef1a0f43ad8", "guid": "bfdfe7dc352907fc980b868725387e985d4eb7da8eccc55ad0b397b1c1d9e7a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bebcb82729380f94feafd2a6c8dcc713", "guid": "bfdfe7dc352907fc980b868725387e98d822c3029b41266a00c999c718098f3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf3d873272529deff99c6d62b9f02902", "guid": "bfdfe7dc352907fc980b868725387e98d130aaacaabfeca46c2af2e82f0f9d85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870101f8fc5d0d6c0f2559c70a795aa60", "guid": "bfdfe7dc352907fc980b868725387e98d809d133d804c7119759b1c0adbe128f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2bd98942489d3fe09dd766b7a5deae3", "guid": "bfdfe7dc352907fc980b868725387e9885b0ed3a104ec2f6fec14bbe1f0fc71e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849282b58c958c005d872e195f8af7433", "guid": "bfdfe7dc352907fc980b868725387e98730d8c989c76dc7c40f55e78274c7691", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe353c530365028066e171147bd4dea2", "guid": "bfdfe7dc352907fc980b868725387e98420dd556901c3fe6806fb7e8eb299496", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981a370673afe48c5e5b57fb4f303b8a37", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98772e6851cd4007f66e366c8d1e2c4488", "guid": "bfdfe7dc352907fc980b868725387e98251fa87b14a46256a65aea8046382815"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a73468febe41db753c96ae923a562e8", "guid": "bfdfe7dc352907fc980b868725387e9870cf6efe522413c7839ff780f421d9c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5562e2d36d40cb258d94a9cadb9a94b", "guid": "bfdfe7dc352907fc980b868725387e98b67ba1f20965fb090953102d8b9e77e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98584eb80a83357978527c3b43a9439d6a", "guid": "bfdfe7dc352907fc980b868725387e98980ed3b1e3b760fef951475869a19ec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd0a635bc9278f59a4b1df8e2af5913b", "guid": "bfdfe7dc352907fc980b868725387e98ed1034f6f5134817be4a6662d0f26720"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985986b0e12bbf47a2b690ce972937a41d", "guid": "bfdfe7dc352907fc980b868725387e987dc5de714b0573a49d4d22ad02993040"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ca600ed5464a7074773e4216837722e", "guid": "bfdfe7dc352907fc980b868725387e980af47782cf93ebbd9cd4563d099fab2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b635022903ef37077f47e0f9e919dbf", "guid": "bfdfe7dc352907fc980b868725387e981a6cd23d283efd86bfdd1afd05a4bcf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986868f2d305b1f67f9bd2338d1bb21a3e", "guid": "bfdfe7dc352907fc980b868725387e98c936744384390af07aaa6ec0b221696f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98707b7fe6c1946fe4436378c81a3ed512", "guid": "bfdfe7dc352907fc980b868725387e985795bbe0c35984ad12fc18caeafc28bb"}], "guid": "bfdfe7dc352907fc980b868725387e988aa9516a25305f519908a499366583ae", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e98a98357da29796fa7ba184eeb42eeabcf"}], "guid": "bfdfe7dc352907fc980b868725387e98a775a8254b292ef44c8d2229c94936be", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e90c81574e4dc7bcabb9bf8386afb39a", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e988eaff77d0a06402ee4d9c53d81ecc38a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}