{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839003da3264f9314078dd9ead5294857", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e19228be12252edf38dd6ca51b6df219", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98622d0f7f7d8ba3fe7785e8d2549a994b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98641391e496a4a37f3ef5b39b0f291421", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98622d0f7f7d8ba3fe7785e8d2549a994b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cab47b6636e5c33377ce749e179bf40d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d5891b85583fb61dcedafda0185810da", "guid": "bfdfe7dc352907fc980b868725387e98a3aecb2067b7ea581e8574efa04b567d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989940c942d87bf9ecfe0de47419d6dbd0", "guid": "bfdfe7dc352907fc980b868725387e981ea29f833aa0dda8bc7ab2bb78e6c0b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981138a4fe2638f0c13b83f8119e950e37", "guid": "bfdfe7dc352907fc980b868725387e9876175a44f0183a52ef7bf6b805440dc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e0683f1d25ee57e14855a2a48ce748f", "guid": "bfdfe7dc352907fc980b868725387e985c1dfba3bad6a462658b3c17a9fb686e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1df5f73d578e2452632e975063dcd22", "guid": "bfdfe7dc352907fc980b868725387e989bf2af2c6f69b0d9f9a5931a424cb2cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6d26ee69ba806750a3fcbc834dd3ee", "guid": "bfdfe7dc352907fc980b868725387e988d2be310926bf8da28ec061ff92f3751", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824a362bac0a24edbd4d8d64461272200", "guid": "bfdfe7dc352907fc980b868725387e98d20cef43c498c94273e0bb84111ec129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca13aeb9fcc0f48770ca9c246ec8a551", "guid": "bfdfe7dc352907fc980b868725387e98d76a51d826cde3235da18f333ae701e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5a232f52528ce5777625d78145f84bc", "guid": "bfdfe7dc352907fc980b868725387e98f5583ad7a34dbaa73a77e2aed9f54590", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3e7ce475ad804184694bc038e831d15", "guid": "bfdfe7dc352907fc980b868725387e98cab18d408301c355b585974b70da1f62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6a7b95292df3059a63b8645e786285", "guid": "bfdfe7dc352907fc980b868725387e98fea294816f2513e1be22924921b9ca99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c863bf4659717e18330285c69e45b75d", "guid": "bfdfe7dc352907fc980b868725387e9838fdad7d7a0297327e6ecb76edfdfe09", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2dc82ab8ed911bd330f7f2eae217f7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d82384644cf833c462777ce091ce152b", "guid": "bfdfe7dc352907fc980b868725387e98f725d4e8e04b01e297b4f883c6405f63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f52962e84d74ec8d0de7bb95c063ac69", "guid": "bfdfe7dc352907fc980b868725387e989993350d40fd5c55e738a25c0b1ad91a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98288370ed155f3b85619c1544d632a612", "guid": "bfdfe7dc352907fc980b868725387e98eaa7a79ec21127a14401648afdcf52db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896a74331f8c4b7878278119fb995729b", "guid": "bfdfe7dc352907fc980b868725387e98f5a376592f0f7179ae5ec7a2e73ae3dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966aa31e2001b2300808cccabf99e196", "guid": "bfdfe7dc352907fc980b868725387e9882ed80b0c2b6253388aa32bd556834e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891caa1d81652c54e349159392f17fa1d", "guid": "bfdfe7dc352907fc980b868725387e98168fb07d7e182b25b8ee3097ec37a5d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd979cb6d1fbf6d583c1720d6ac0b5ff", "guid": "bfdfe7dc352907fc980b868725387e98d6829ea6b179a80872afa36b474ab699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984482b9f250592875f5293e493d0f3a78", "guid": "bfdfe7dc352907fc980b868725387e984b574aeaee4bf692cc947955032e953b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98349c889b8cca561e09ec7b68a45504fa", "guid": "bfdfe7dc352907fc980b868725387e984ad2a419b86bd1cc394ad9e66d992347"}], "guid": "bfdfe7dc352907fc980b868725387e98da8caa0468a6494c539bcc9a3f56496f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e980d562db8451c55dccc31ac9670df4bb5"}], "guid": "bfdfe7dc352907fc980b868725387e989c121c0173be5443faa5b2120d803293", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c44defa1c51e6619c9bc20d8804a8b84", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e984c8ab30283e75361b6a65cb914f55e53", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}