{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832c234f231fb0fcec6dd09b74a814fc9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1f8693f7c908f5b0affe1f241e6c220", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e01bfbf6cd62b58cb2a33951d10eb03a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b731d65cc4406511fff066edd613cb3e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e01bfbf6cd62b58cb2a33951d10eb03a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d26b1adc32d733906901b1009d9fe42", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f3cb86311f5174c6d0cccc6c6ba3e6bc", "guid": "bfdfe7dc352907fc980b868725387e9826d944e7e5dbe34563446a3e758e3c97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4a5c07ae3c902ae59158f063f6732d8", "guid": "bfdfe7dc352907fc980b868725387e98a7fa45549d850eb8c72c903f1d4df534", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988074a711e878e8f5132be17e90cbb376", "guid": "bfdfe7dc352907fc980b868725387e98c9980ec40e14e533520302c0addfc129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826abd0d3b43bb8e66275d257f1f1d6c8", "guid": "bfdfe7dc352907fc980b868725387e981a0253ca8236b2356ace6497f390b8da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea1a439cec0ea8774f2bcb2ed92b464", "guid": "bfdfe7dc352907fc980b868725387e9883f423328b710e16048e6c1e36f95f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98650a7241a5b578dcd0d5feae3cc2478e", "guid": "bfdfe7dc352907fc980b868725387e98ab72dedf3bdd3d652c3e6660af0d44df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ac694344a1755ade63700cef05db74", "guid": "bfdfe7dc352907fc980b868725387e985e560f967fc36d3f5792db6210cd8d7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd340c33d7968e7d012a826e5d1bb8b", "guid": "bfdfe7dc352907fc980b868725387e98ee8d700f8fdbed195242f6232fa9edb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877830a767c48ba30c1c626cd46f1d86b", "guid": "bfdfe7dc352907fc980b868725387e98ebfbb7e756d1ebaa66324a532de8abdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ce6ec674e17510190e257e2ebf7331", "guid": "bfdfe7dc352907fc980b868725387e98db4cbd66691a075dec236f2f049f783b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae4847b4fd74b7c50e8761366156816b", "guid": "bfdfe7dc352907fc980b868725387e98f4fdd91f6659afc648c05e57c4edf8e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f534421e35ca8d2a298176ed17888646", "guid": "bfdfe7dc352907fc980b868725387e98d38d7209dfbeb30917d103def304e045", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c70137eb68b024135232a21de4388f80", "guid": "bfdfe7dc352907fc980b868725387e98e1b835fe1d12f123de44830b9d39ef33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a8848beddfe973288803db95679bd4", "guid": "bfdfe7dc352907fc980b868725387e9897f4d8054d146697b89e9ae95e6cab1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9981543f4168c2c17dda18575f8f14c", "guid": "bfdfe7dc352907fc980b868725387e98f4cae67c92706a04a914adb1f102c5d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b24283a28cf1cd497f6e28edd130ca8", "guid": "bfdfe7dc352907fc980b868725387e986da3806c1c15c1c19d947c614ab658bd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891b6c87882cff834a6d4722792d27722", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a0749feb24a7a956f60867fc9062dd7", "guid": "bfdfe7dc352907fc980b868725387e980e81720d61a3801d969422a07b3694f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1aac1d8dbe6255bfd5237b6e89ffa3b", "guid": "bfdfe7dc352907fc980b868725387e9852677058a7640559c4feadf4e3c0c5f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5aa89c8ff9216bbe48e8baa4f7fcb4e", "guid": "bfdfe7dc352907fc980b868725387e98f9e123e91d169f5077b5b4c09138b1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98699f42ddaf3942eb488268300b4fc93f", "guid": "bfdfe7dc352907fc980b868725387e98b4dc059e1d83e87ebcefe43ba94df0f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c372110ca28920dc251230450347831b", "guid": "bfdfe7dc352907fc980b868725387e9861a80f10186a44162739a66582e34ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad6919e447e17fba0cef807a6ad07d3b", "guid": "bfdfe7dc352907fc980b868725387e98321228c8645ec8c337f06a4080584f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3cfa1f910e105027bd4ef3e9983429c", "guid": "bfdfe7dc352907fc980b868725387e9876e2fcde8e9bf49d940acd65e1dee028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebc283beba8ad61ef426a3cbd6c11a32", "guid": "bfdfe7dc352907fc980b868725387e98127082dd44fec19b84a6b82a9671f1ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805bd42ad967c40d30b30e1c710b41eb0", "guid": "bfdfe7dc352907fc980b868725387e98e7358517f8700c32c40012ae498f65d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e141a59d2d6c83bed3a697efcc2b2d9", "guid": "bfdfe7dc352907fc980b868725387e986e96d52f7011c1a80218cf8d88c5daf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3568d1a1c16d38835d57b8a389aa911", "guid": "bfdfe7dc352907fc980b868725387e98c2c33a008b1dfed23a9d7d00602717e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982514159a6382d3df64d8b97d5728187c", "guid": "bfdfe7dc352907fc980b868725387e9807681073bd833184cb12f4453cb4aa18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ec1e73a67613ead749dcf2b31422ca", "guid": "bfdfe7dc352907fc980b868725387e981992c38dfd126c35ec1d145b29914f16"}], "guid": "bfdfe7dc352907fc980b868725387e98448c8389dfc199dac83239b7c4d4a87d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e98f30217f2638041bc05fbf64d6676681c"}], "guid": "bfdfe7dc352907fc980b868725387e9837666ca8f131a9306dd950b0c4ffecf0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ceec10663e715758ba9294abee964c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}