{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5e76ea2c81d201a759a3e21755a6d44", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f60ad41630d9e7ebc6257f2b7c9771a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98114d0cb302e43e66bac746397e498bf9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808ebb61cc9b6bf2730a4627e98ee10ff", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98114d0cb302e43e66bac746397e498bf9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984072357f32a9f8fc95b3c02424bde0a8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982794cbf25592a29a6af58a519638ebf4", "guid": "bfdfe7dc352907fc980b868725387e983aca1709c2914ba528ee6563d94f97f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcbad03c03234925a5320bc4e5df315b", "guid": "bfdfe7dc352907fc980b868725387e98871b12316a5633943624813ed21af5ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c67edc2e8076f3fcf89cf0bd7a4071fd", "guid": "bfdfe7dc352907fc980b868725387e983176a37cc299cf070d4555c3f53be83e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989686d8ea1fb3adc0d67fe517e3b11465", "guid": "bfdfe7dc352907fc980b868725387e98226adb9d31cac7fef25f7406d9b1e606", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98601b922239342a85db0c32fea77f7f46", "guid": "bfdfe7dc352907fc980b868725387e987948f465b53b26ef9ddb91e1b04cde92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb846ec3310f1089300070198cd9b2a", "guid": "bfdfe7dc352907fc980b868725387e98dee8c20f77010dda3a84154f506215dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98720dd0c9cd0999fb40eebac83b0778ef", "guid": "bfdfe7dc352907fc980b868725387e98cb8c21e588adf76f4cd163a7ad5d70b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c81b63f5ac486f602093e549f640c926", "guid": "bfdfe7dc352907fc980b868725387e989dc7a742197fbd6107397fd0a2ecc8fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be0b6d05ce7279eaaf42ab8ab8c888c", "guid": "bfdfe7dc352907fc980b868725387e98e150e966dd373d3667c200484a4885b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626832e5f3b2d859c486541bbf74e9b8", "guid": "bfdfe7dc352907fc980b868725387e98bc399afaf55097894f7a689f77e28693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c87c952e881bb6723a2263389ebbfe47", "guid": "bfdfe7dc352907fc980b868725387e98b42e42472dfa234449e2b3d79e02ba09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d9af2aa3b4234db5bf228dc454094a", "guid": "bfdfe7dc352907fc980b868725387e987d62dfffc03c60b9dc2d60f521323230", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9bc7ea7d3876f42893d9d1b4674b65c", "guid": "bfdfe7dc352907fc980b868725387e98dc056b5776d06b6cc15ab11b86fae713", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab17389718d7843b3672f9e0b7785cec", "guid": "bfdfe7dc352907fc980b868725387e981d06f291d4f4c2a362ed6fa1437bb59e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877035be071472de8f4c8b6fc3002876c", "guid": "bfdfe7dc352907fc980b868725387e9895ee41a634c67666b12d1453f2b48928", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c91e6a9d9c985b18b926057ec223a7", "guid": "bfdfe7dc352907fc980b868725387e98d58dcd999d17d0db74ca8dfa7687034b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a39c0f6868bc8d5f0217b20a9c18a500", "guid": "bfdfe7dc352907fc980b868725387e98c592a842684b5a2fe59c5e639726b0f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853360285a52aa3393f440bab7d47c10a", "guid": "bfdfe7dc352907fc980b868725387e985b0b9d2f20f19094322f5203327f6bb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e101533c677a2175907d2bc44a892d7d", "guid": "bfdfe7dc352907fc980b868725387e98c4fda7ec2a25084542e431074a6ee3ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985885522fd10dee1504eedbd34eef118e", "guid": "bfdfe7dc352907fc980b868725387e98275d3ac5240c091f2be445eeb906d49d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c4fe51dc20a29d22f6876866a678cc", "guid": "bfdfe7dc352907fc980b868725387e9820619854233c04b2095e357c18251f08", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98893830191ea338f02e2c1dc91b910130", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9899c0b7d13bada93cc768ac28ced5f876", "guid": "bfdfe7dc352907fc980b868725387e98160635908650f9c74bc67d8c68d41314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cdb57e0befd5f66de62bc1a4f611567", "guid": "bfdfe7dc352907fc980b868725387e98effa69f340bce119eecbf45a140cae7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c247121da906b161b15cfac732eb5034", "guid": "bfdfe7dc352907fc980b868725387e98c5fd1467cf60bde53f389db55f3e4959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fbe373d08eb1c949c51106958a9da32", "guid": "bfdfe7dc352907fc980b868725387e98c0398a7da7dd8f8bceffb1e647a812e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a8cadcad958b93080db11964aac2883", "guid": "bfdfe7dc352907fc980b868725387e98270faf97e9a6e2a4f97b20b8a903c53e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98151899fc338fa063b35e765822efd80d", "guid": "bfdfe7dc352907fc980b868725387e98b033bf8877e84e24719c1d8109e7a0bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ad1002be2c55079844b2981efbd7815", "guid": "bfdfe7dc352907fc980b868725387e9845fd9d2e162c938aaa750b5f4ccd88f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e15119489fda0928f2c47d859cbf27d", "guid": "bfdfe7dc352907fc980b868725387e98e8e6f1bc6044e309234c57df0b2e8672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2216b03bb1d509f27def6e90ec3899e", "guid": "bfdfe7dc352907fc980b868725387e981666b13d3c969b487a0c09e3d31d07f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995d953dc630c5c0964309ee329221aa", "guid": "bfdfe7dc352907fc980b868725387e989d19b25fc650126008d90c768034e918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf9af5df7565dc8029402be3da36c1a", "guid": "bfdfe7dc352907fc980b868725387e98f7f3d8af76f1642f368a6513747712f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c37f62b0ec7ee5c91b7c53d1c69aab", "guid": "bfdfe7dc352907fc980b868725387e98348919e8098559a04d105abd00b29591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a0e04917823bbc46a0ce2f4fadf0fd6", "guid": "bfdfe7dc352907fc980b868725387e98e8cba6d2a881a47dd69c09fbc873bc32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610f479e9953e17ad570ea42f7062ec6", "guid": "bfdfe7dc352907fc980b868725387e98da28585ce2d1cbe084dd95d9d21ab1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863a8e4c128c362a83db8232899f8fc2c", "guid": "bfdfe7dc352907fc980b868725387e98b4302261b52ec8723b19fb7813d707a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e838f07c74c7952e344bf41c4ef2be75", "guid": "bfdfe7dc352907fc980b868725387e986d566ac61c992262c4c30cf236f6fd78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab8cc70fb6fe02f90e172866c1ab006", "guid": "bfdfe7dc352907fc980b868725387e98b6ae7c609e0628004a9aa75905b5ade8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980015684834c50966add7c165a79b5d55", "guid": "bfdfe7dc352907fc980b868725387e985d5b1ae29dbfef023845742ff9330728"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ece13b83d58ac27d0aceeb0d7505241a", "guid": "bfdfe7dc352907fc980b868725387e9842f712402fe25e32e1a33dab27d60b89"}], "guid": "bfdfe7dc352907fc980b868725387e988f2159a3fa518201e99f45201240c014", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e98c55b2fd41ec59641b36bba517fd96ffa"}], "guid": "bfdfe7dc352907fc980b868725387e98f59d14b41d6065eb13a4af8fcfae4a69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983e9e224ef10dec5e1925539f36c732b7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}