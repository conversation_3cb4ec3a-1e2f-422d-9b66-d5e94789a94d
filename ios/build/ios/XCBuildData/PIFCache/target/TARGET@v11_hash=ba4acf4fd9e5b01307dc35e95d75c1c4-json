{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989df1efc902039891e0487cc2313b8690", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808642878fca4a8ec76eb3d10f7b9615b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a6de790e323a4961905727eabe589bf", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9870d3db593cd335c47a1f89b2324dd2d6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a6de790e323a4961905727eabe589bf", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d51a3823e420a6ec554bb3ffa99d51eb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f9591c365218f037b4aa4ed2be3d91fb", "guid": "bfdfe7dc352907fc980b868725387e986e1ee688d9727a5a7bd2881a12c72c8c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8a3c4bf938214ebc38946fb12f69be8", "guid": "bfdfe7dc352907fc980b868725387e98b5363f63b05de990b9b09047cc51371f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987afc3cfc8c25644427cb033e855f2bd5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ab84768027bd5ce45889582068886a41", "guid": "bfdfe7dc352907fc980b868725387e98b6350a94d516d092183729aaab5dc020"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e58b7c7fd0de067f26d00cc8333387", "guid": "bfdfe7dc352907fc980b868725387e98c8d1842975cf90aa467e8f052db200d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa45e3e6729bf23ec7c4798735feebf", "guid": "bfdfe7dc352907fc980b868725387e989fccb936bb3b41dd3488577612099c00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881cd0c28a0466b9042a5cc8f852aabed", "guid": "bfdfe7dc352907fc980b868725387e981359625deaa9078b31e41f81ab1896b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98988411e5438d05fbac257abc923bfb2a", "guid": "bfdfe7dc352907fc980b868725387e9899afa2f69f8a9276a43f6a5e59a9b9c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460c5069a4c1fdfe1473eef7fde605f9", "guid": "bfdfe7dc352907fc980b868725387e987253fa72c42c77c6a94700152d9f1211"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f866666be1c2c8b6b9821ceb6838cb3", "guid": "bfdfe7dc352907fc980b868725387e98883c3d3127d8e64e01be5e21318522fe"}], "guid": "bfdfe7dc352907fc980b868725387e988f2c267f8a6f4f482d4c56fce4dfe847", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e98cf9bf9d02989e65aefbe841c7d1f131e"}], "guid": "bfdfe7dc352907fc980b868725387e983a39a508ba783db2b9b6072faf8e7b3e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9810b792cf431aba48911cdb8942abf8dc", "targetReference": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c"}], "guid": "bfdfe7dc352907fc980b868725387e98aa54382482d8ae8109a98391fb0f4582", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c", "name": "SwiftyGif-SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98290968646de0d07c6f6e2ed9e146ea78", "name": "SwiftyGif.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}