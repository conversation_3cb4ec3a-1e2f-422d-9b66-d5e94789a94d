{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98739c3e72a3ba1fa0013acc36621c4482", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b63c5c72f999edc072df236873781ffc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b63c5c72f999edc072df236873781ffc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980aa47607c91bc2fcb24ddb21ee702f20", "guid": "bfdfe7dc352907fc980b868725387e980ef4dc7532c29cefddc3729f81249df4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c669b0250fe0da777db7f393aece9591", "guid": "bfdfe7dc352907fc980b868725387e989acbdba2b4f57e37a82f82dfbc4b6a88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b57ef031e08938f7045d3d365772631", "guid": "bfdfe7dc352907fc980b868725387e98ccf33296c77afb7d83454b7cd831b969", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980226413f93489e41d089cf9b73a51341", "guid": "bfdfe7dc352907fc980b868725387e98c0e1142d20b85845ce4337738948533f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840cc43a6b7ea716bb81e408026042e42", "guid": "bfdfe7dc352907fc980b868725387e98b4d22474482daf76bebf900c4787a4ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987342462ea1554307822f012fdacafa59", "guid": "bfdfe7dc352907fc980b868725387e9861c163610b2f649ece896910dcb5d3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c55fa97fa6fb7d9586ef01fb11a54cff", "guid": "bfdfe7dc352907fc980b868725387e98827666d018d0cbfa3a29dffc405c37c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831924986a9231bea5f435815c6a4a411", "guid": "bfdfe7dc352907fc980b868725387e98638ce503f76de7fccf81ce51b3c8367d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5baf771b68937d6134cede1983167b3", "guid": "bfdfe7dc352907fc980b868725387e98a2bb664801eec5224376b31fb5cb0d4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f7d34d088c6493435ab059c7b1ba64", "guid": "bfdfe7dc352907fc980b868725387e98eded081ad84d32301f6cab36b236dbd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820809a9f3de37de4fce1201949fe81b9", "guid": "bfdfe7dc352907fc980b868725387e9879609b44de9cccee41b189e59f22d9ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa02cbf490d26dba16fb83e9f0b5948", "guid": "bfdfe7dc352907fc980b868725387e98d75cda691380849a16048fe56a59cc35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd339e6ea24033adbf7ac8b3837d76a6", "guid": "bfdfe7dc352907fc980b868725387e981f3fe2209d00e658a00989ce4051a1fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a982ba86aeaad740f2b0157821c9b6e", "guid": "bfdfe7dc352907fc980b868725387e981a0466bf65d5117c178f7f22ec631484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f648b2361afe0c30e8ec24b13e52a44", "guid": "bfdfe7dc352907fc980b868725387e985d6cd2f9cd7473d0b0f972a846a20dfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e99395ec9365d20014af79a2cf5aaf8", "guid": "bfdfe7dc352907fc980b868725387e98bcacb351ee6587e77fb411198be3d146", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a18ae795b461fede32377580b8e488e", "guid": "bfdfe7dc352907fc980b868725387e98171180797fa255533f9f7994c8560a83", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fbbda7ea3ba800f9071e9aa1ef13ee46", "guid": "bfdfe7dc352907fc980b868725387e986e2976ba8451c24dd02a6c6d9dc052a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da33ce14cbbd56812a0c6d246c69a6f5", "guid": "bfdfe7dc352907fc980b868725387e98941544c221263f8727c5651833ac7bac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6de412608a9e2cac7477b15a37dbe47", "guid": "bfdfe7dc352907fc980b868725387e982a253298e0f3733c1b9507f412559828"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea4b608e1954ff8c01faec1e85f110d4", "guid": "bfdfe7dc352907fc980b868725387e982021aca3f3a1b206818945093d5b26e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c54f45d1eb8486c5f39d4b6d23f7d96", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6995c725ec8d04c7717cfda4daaacbb", "guid": "bfdfe7dc352907fc980b868725387e9824d27b3f537ef9a2045dede749d7c0d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ae3d013f57930621243d07b80bd27f3", "guid": "bfdfe7dc352907fc980b868725387e980bed89934f29d483fb9437c1ee2c0997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca50c5dd12e0d57012797e182c9e0688", "guid": "bfdfe7dc352907fc980b868725387e9837785c89cc68442a317aa11bbdf8c941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7b5fa877ac198b5e44cc30aa32ddd1", "guid": "bfdfe7dc352907fc980b868725387e98ae9cfda66bc1e1bfc9abf45be96f0279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e72b5923c0b1833924ae3f46ad12d9a", "guid": "bfdfe7dc352907fc980b868725387e98f58eb8921a9742656ee627060f43a262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4af4daefbe58b61334525cc7f53defe", "guid": "bfdfe7dc352907fc980b868725387e98d3465a74f12a4301aa2d8c0d61f19e31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c8dbaa7dd434a05269dd3b894c73aa", "guid": "bfdfe7dc352907fc980b868725387e9871ed1b438edafbb39d8629bebe580cde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bfda06f21680f8f4c2d99f1d886fe3c", "guid": "bfdfe7dc352907fc980b868725387e9800ddb06d077111995f419ba9bca7f614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec477dc8547e8b084bd87f9c48f14847", "guid": "bfdfe7dc352907fc980b868725387e98001cf8cf1b1a8df5390c7082c81acf8d"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}