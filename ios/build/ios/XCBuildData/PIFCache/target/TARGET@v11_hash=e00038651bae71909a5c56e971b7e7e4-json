{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d4ca355e57314aa28966a004542eb644", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98604d2e776adf866db3efae01c8ff4220", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98acceee9107b984f22b3f27abd3f4eea4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983e7413ffba1efa280a640a18ef4094e7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98acceee9107b984f22b3f27abd3f4eea4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d511d2a37011e446abc9db6276683e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ccaad780b24e3256b0d2f4eadfda5a56", "guid": "bfdfe7dc352907fc980b868725387e980bc126af0d50c33b848e3d4df147eba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821aedfe05149ada30dd3d4486ac3c8dc", "guid": "bfdfe7dc352907fc980b868725387e981ca15603b15285cb017f21c30281bb92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881c0c0f2e2f28ce95395b978a81e1d88", "guid": "bfdfe7dc352907fc980b868725387e98fb6b7e37229435930de9154a3286013c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b69a7fe80df38e928490f9e9fdb42515", "guid": "bfdfe7dc352907fc980b868725387e98757b3bb2a60f425321166ba3d41dc529", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98754ca059e3ba11b90569e53b54483265", "guid": "bfdfe7dc352907fc980b868725387e9857ff4c86f8be647184ef8a336bfb36aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981941f26961400e9e4c38ce7d0cb2376a", "guid": "bfdfe7dc352907fc980b868725387e98a99d9f2e0e182d25b53e4c4d487dcf8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891bb05fb1d56d331cbdaee107f03b3e8", "guid": "bfdfe7dc352907fc980b868725387e9854994ff5ca73efed5a8dccc130de0b3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bfb5aeaa2b387803da0eed03945eaa", "guid": "bfdfe7dc352907fc980b868725387e984bb8c4b188e3c079e28b981018beb3d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823483f3f1ca805dca33dbcfca192a920", "guid": "bfdfe7dc352907fc980b868725387e98864fa0da1684e4ae480cf9555fcac616", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ee3d3835b2ecf3d5be20c00448d77d", "guid": "bfdfe7dc352907fc980b868725387e98c42ba9108b307fd0dc79a78cfdf30352", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad6c27f9d7c749b456e2d72565b173d", "guid": "bfdfe7dc352907fc980b868725387e9800a4340d964387194f8caae468df01f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee6d8ee842d002f689181903bc02f763", "guid": "bfdfe7dc352907fc980b868725387e98a56c8961b713f77576696b87b5e385a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982222351d28b72db291bb089f2b1379ee", "guid": "bfdfe7dc352907fc980b868725387e9826190ea268930b892c5ea0ab976ef93f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e124fa3a2d927f787048972c246977", "guid": "bfdfe7dc352907fc980b868725387e98f82280d55c8416b3b83ec7d4c804903e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813a93b30fa3033f583bd23532d4bf79", "guid": "bfdfe7dc352907fc980b868725387e98d6ee06b1efe28a4d9404588761999ed3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cca8c86247d579b5423715b8e0b0f98", "guid": "bfdfe7dc352907fc980b868725387e9878109cb5d165bdd12bd3d56c92d0a5f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bfa4e0f5cfcf38940fea68c9d185fc0", "guid": "bfdfe7dc352907fc980b868725387e985c40bc039fc56a1997df86b0e5388485", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1db4c8c75311fd7bd437457ae224bfc", "guid": "bfdfe7dc352907fc980b868725387e986e77d62d9c982dcfc2b06a66beee49e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbd1bae51a63772f1a5d91ccaa84bc0e", "guid": "bfdfe7dc352907fc980b868725387e9827bd198a2b0d74f76aa79029bfbdb9c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864976b536e869f277e1af0c96dc90f1c", "guid": "bfdfe7dc352907fc980b868725387e98aed3413fb0764d3ea679da9ad0eb79fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac44c1441a63c352cdec8b0d3a23d05", "guid": "bfdfe7dc352907fc980b868725387e98a589f08bc84633c06e38d4335493f190", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980588247775d9a4646c6e57c49ea65d98", "guid": "bfdfe7dc352907fc980b868725387e98cceb9b2b981481a423868c4c3978c6fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd3f49db707cd8afe0a6a76bbf488fa8", "guid": "bfdfe7dc352907fc980b868725387e9832130177668ab910d027344f5f8f26a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ed2714f9f3fa8e4a8654c70585e529", "guid": "bfdfe7dc352907fc980b868725387e9808b17a852eb76e71ace8b551f4b8da56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e07ce560fe7d48de33d4e9063b90a4b6", "guid": "bfdfe7dc352907fc980b868725387e98c5bf3360f3cf8db3366045eb6bb41585", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7c895eca1ee345721c5a24bd4dd7e8e", "guid": "bfdfe7dc352907fc980b868725387e98f07567e7a3e15cfbeddb3b1b61af43f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989546511f711340266ba21716891ab1f8", "guid": "bfdfe7dc352907fc980b868725387e98dacb542c669bb0bdc627ad2670436a9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ad92a9c0e1faf9f3e02ca54a0f8239f", "guid": "bfdfe7dc352907fc980b868725387e98f6d40f4660d3b9adfcbdcae7a34f5a07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818d4ca9059fb4b6f67b907548c0a5ea1", "guid": "bfdfe7dc352907fc980b868725387e9877533e4ab9406d96a2586427bf12e534", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a047671b54c38cbf7c000a008bf7e92", "guid": "bfdfe7dc352907fc980b868725387e98a6122e76d9dbde6b41e7eb6580881314", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a89dcd781c0e839dfb398f5acc7e1f", "guid": "bfdfe7dc352907fc980b868725387e98b11117b5c6d6b59b839b5ba3c9b5c07c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6bc6065f3a35ea649279eddd73ccd7", "guid": "bfdfe7dc352907fc980b868725387e98aad7a1aa688219c7eb5540dae7040fe2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b8013fa4819afaa716746fd9dad5849", "guid": "bfdfe7dc352907fc980b868725387e98e2eba1fb0a80de1c41fe92f112e46a0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b72a9d5cd14397f294047b2ba27e21c0", "guid": "bfdfe7dc352907fc980b868725387e988908258bafaa5fa978fe4f612f3c936b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98993ca1332f089bb5916aa79503250967", "guid": "bfdfe7dc352907fc980b868725387e9866d13c12cdd1261ed652fc64e5b9a98d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4e2e6cb14f506503d6b50988529f863", "guid": "bfdfe7dc352907fc980b868725387e985e1faa9bb06604de4c24ad2c65202583", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efd560d0fb70a475f7464a6a721cd44a", "guid": "bfdfe7dc352907fc980b868725387e98eb487c20e34367d3865c5c8190669bec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98378d5faf1cc61ebdb80a20840fa91e8e", "guid": "bfdfe7dc352907fc980b868725387e98159f17ec64730260abf74d3c8325430d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980811bf4f6a9d87c47dea83d7c863acfe", "guid": "bfdfe7dc352907fc980b868725387e98c7f89498bacbc50d4ad578ba808bd591", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a0b77cfe0acfd9b8eacd1e7179ce7d8", "guid": "bfdfe7dc352907fc980b868725387e989078881955b210a7c2e0c00368fed91a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75be32d299303ad3025a6eb7ced2519", "guid": "bfdfe7dc352907fc980b868725387e98a1e0f65fe50d40deb6c04a606e08f942", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98546d1c2ec9cb311f4db552a063fb1c63", "guid": "bfdfe7dc352907fc980b868725387e986bff559e40fe5da84fce2ecc16bf2364", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872c2283735c890e79efa56ba098bf968", "guid": "bfdfe7dc352907fc980b868725387e98d006be67e37ec81940472c03654bfd2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981558679e057524ed738c17168eaedecd", "guid": "bfdfe7dc352907fc980b868725387e986de07e074d8c3f5becb31e15cf4fafa3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981281fc4dcd326a3876f99a19f473fd6e", "guid": "bfdfe7dc352907fc980b868725387e983e3e7c27f2adf38629d5a4be2efb0064", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9846c6b2f7df326fb256cdde26a12e233f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98de27b6f9c7f7efe46ddf2332f519b133", "guid": "bfdfe7dc352907fc980b868725387e98a5648b9081186db166e85cac17d23a6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f20f5607810adf680ca9ba1b20e9b2fb", "guid": "bfdfe7dc352907fc980b868725387e986a3528c9c3e28232f39ac86347327a86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846fcbb70152a3769a452e4b7182daf5f", "guid": "bfdfe7dc352907fc980b868725387e98bd963f9860264537e25b92949fd28b88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c18e2904e3979d7d336fbc0ed86f499", "guid": "bfdfe7dc352907fc980b868725387e982ce5b64449a184a834f87567cc1a64f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847cdf23495acff3b481cbdaaf2f10b80", "guid": "bfdfe7dc352907fc980b868725387e9818e0da3e9a9cd374dd9fac5b161a1ce6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd4859abb658bcc5a337d217464e285f", "guid": "bfdfe7dc352907fc980b868725387e986c0c5d31a31deb997ac5ffd774c096c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a297c98f534c79f9945aa3710ddf973", "guid": "bfdfe7dc352907fc980b868725387e984b0d38cecc626956a13db411295e7f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987338a0cbd18d94d3c6b28cceadcbfc5a", "guid": "bfdfe7dc352907fc980b868725387e98cf1119d1216c5ae8957933ef41771e00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d82c8fb8fa25e6208310bee819ecc2", "guid": "bfdfe7dc352907fc980b868725387e9897ba994fb7fd177e59b87839993d3a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98461a734d3cb816e10973354378a0a569", "guid": "bfdfe7dc352907fc980b868725387e98171b9fefae944ae31e11fdd309128dd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e71a73c122d0430db35f4e3d55abde8d", "guid": "bfdfe7dc352907fc980b868725387e98c68cb341553da18a9ad8bbe166319303"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faee4eda9ed211e5eaa6ec852308c038", "guid": "bfdfe7dc352907fc980b868725387e983ac583f1f547e3a7c184751c981a64f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986075c1d00faa18115ddc2e2208ec9b93", "guid": "bfdfe7dc352907fc980b868725387e985bca98380c04be8f540b8aa4730622d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70e6a08b0d02c8d823136b6005386d1", "guid": "bfdfe7dc352907fc980b868725387e9858f5469e53c6eb704ca502a275e6a6b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc0227ad031af4a72abd7e861530243a", "guid": "bfdfe7dc352907fc980b868725387e9826a05f7301ee9e3061d4940fc87a34c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c9b1f1eb64bef0de9a32319d221ba08", "guid": "bfdfe7dc352907fc980b868725387e98a2f196effe5382f24ec889260b442d3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc2f85c85c24c8ac74208f7614c05f2f", "guid": "bfdfe7dc352907fc980b868725387e9814cb6f649022a81887e5544fba87bf35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c58a56d6ece0001a43b0610d1a859a", "guid": "bfdfe7dc352907fc980b868725387e98c36afd8100388ee352795a5c47d248a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d17f7e8fbec294fdd1f49d2d8ee55170", "guid": "bfdfe7dc352907fc980b868725387e98fdf461cd989237ea6c04a23fdffb89f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f9dbf59139fa55f92f6d546843f1060", "guid": "bfdfe7dc352907fc980b868725387e98a24f1fc1a3933e455c4dcf199bfc1fdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b9e7c30d6fe3840a56e2ac954883a9", "guid": "bfdfe7dc352907fc980b868725387e98500989c389ee0ad0d93bcb2b5ce48af4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891fb7abd7000ce6965e6188f2f6ea71b", "guid": "bfdfe7dc352907fc980b868725387e98142ea60047f99126147226666c395e86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f67ea5889212e50f5c730314832ab09", "guid": "bfdfe7dc352907fc980b868725387e989ffbaa49f89e709fa1f9f5e1413e5dcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cd1c77e15102d52b43452d32615681a", "guid": "bfdfe7dc352907fc980b868725387e986efd791bee701e9f7f1a350aa84e8e57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d629603e18fda8c34ffaa1b970001d8b", "guid": "bfdfe7dc352907fc980b868725387e9857b8053922b8ac7a29f179ac070c1298"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b622a0a4e9032e853b6d43438fc1605", "guid": "bfdfe7dc352907fc980b868725387e98313edb0ca3ceb578e58610ce347dbf6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2a80aa25968359543ff3428c194427", "guid": "bfdfe7dc352907fc980b868725387e98f387132460848715f54a127a4070430d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822268a09bf7c33c3f807b3f85c7ebf85", "guid": "bfdfe7dc352907fc980b868725387e98436f37718c13e842b0021a4a09e0a6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb43a64d1425e1712e129ff6072a9bba", "guid": "bfdfe7dc352907fc980b868725387e981073f7da921d217f8e91a9f34e3af3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff83f0fb6a1fe367a73c844646d34d9", "guid": "bfdfe7dc352907fc980b868725387e98e3440c233bc1cce5c543073c1817adc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae160e59cbdb8a634e4425de12bf42bd", "guid": "bfdfe7dc352907fc980b868725387e98f82327c35e70a4c8f1fd3287cbb32697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112be392dc8f488f5ded9cc5a93cac0b", "guid": "bfdfe7dc352907fc980b868725387e987869b0dc1ea0115c3f32945fa2fee7aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ef7d3d89621dcc0b0be4aa0904606e", "guid": "bfdfe7dc352907fc980b868725387e9871ab7fd758afe96e07ce994494ff4a8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3d6cccae404b53c7eb2590eac61710", "guid": "bfdfe7dc352907fc980b868725387e98374624320f563fa430ce45bc02cee4eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a43255427ee944b71a6a7b0e2682622e", "guid": "bfdfe7dc352907fc980b868725387e98527d91d607a1ac26c4ca6398535c2ee6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887d1177ecb368d212df0ef6c3138ac4b", "guid": "bfdfe7dc352907fc980b868725387e980eec9122b44891305168328f7a58d9ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98138401ef65ba006ba93db93178640eda", "guid": "bfdfe7dc352907fc980b868725387e98d9ab5ed66bce2db3fee941bac5b2b989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adedd47068b8aca8641ba275f5e4e12f", "guid": "bfdfe7dc352907fc980b868725387e984e599cf6715af1d7fabb834b2ce20681"}], "guid": "bfdfe7dc352907fc980b868725387e9853492ae621ed1bde48851eb95822b09e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "guid": "bfdfe7dc352907fc980b868725387e98a2d747c9a6b606cf296f76ca22c5d28a"}], "guid": "bfdfe7dc352907fc980b868725387e98970b78dabbf8d927a006ff56bd237725", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983343457f836ba04799ea32f41febf5e4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}], "guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988d4f056f23e4e16df108000d3c5e64e7", "name": "GoogleMapsUtils.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}