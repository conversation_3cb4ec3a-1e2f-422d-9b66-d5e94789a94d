{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9841694db9191404dab0574df10aa4d92c", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e983e45497ad99b5f0ab9ebcda8afbb048b", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98ab0849b3c8d2627830dfc45f1064e777", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b741a6a793bc9b2e83ee257d5ced6dd8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dfb7dcd697b75181ccef7921d508ea66", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98807eaa38f1eb01f5b2d1597661a89489", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c3d3e9093345ff7e6ea5cf7c631e6ed", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9921ce32e8abb381f64f4debcce16fb", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98275292a886d2ad33d35fd3cb7d67eaa0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e0c15fe3435219d81307a118b2f5652", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f26ebbe5d8d01a4d1bf4c671429335a0", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b41e48e93138916b646177ffa678771", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98247e19e0aab5cc2d757af6334d17bb92", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e9629168ec866731df2b08a90d8ae57", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98898a16af3d3c4bab184ff6aa5cabd56a", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f9c071e7ffdf797f1c28787662e1a57", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a73468febe41db753c96ae923a562e8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPermissionUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e5562e2d36d40cb258d94a9cadb9a94b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98584eb80a83357978527c3b43a9439d6a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/CameraProperties.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd0a635bc9278f59a4b1df8e2af5913b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCam.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985986b0e12bbf47a2b690ce972937a41d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCamMediaSettingsAVWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ca600ed5464a7074773e4216837722e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTSavePhotoDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b635022903ef37077f47e0f9e919dbf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTThreadSafeEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986868f2d305b1f67f9bd2338d1bb21a3e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98707b7fe6c1946fe4436378c81a3ed512", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98703bd704e64166a546e7070b5306d44a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e5ee5d1a916ad1ad4d33e3ca1bab452", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc8a5869153e75de98ffe8a46590cbe7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874dba4fa655b01ab87f7b6d655d130b7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ffe6cbb51e34be9093ad1a350be1858", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraProperties.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c475c336439bfb37763ea8372bb2815f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98676f01be2c06372ef55a2ef1a0f43ad8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bebcb82729380f94feafd2a6c8dcc713", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf3d873272529deff99c6d62b9f02902", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870101f8fc5d0d6c0f2559c70a795aa60", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2bd98942489d3fe09dd766b7a5deae3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTThreadSafeEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849282b58c958c005d872e195f8af7433", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe353c530365028066e171147bd4dea2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/QueueUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b089fa1086ba9a6c3d63a0393c1bed7", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9cabcaa298d3c839207a2fe42ad8862", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bbfff72e6d10bf1fb305b26896a0e71", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a2b0e8c22d3a7e18a857b40681aee69", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bb38525455af81da5e1d2e7056b03ea", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98184da427d7571ae5a4803d8bf2d0b796", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989587a17566601b3c0668bac2ec119eb0", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abccbac2cd8b852a2102882121a4a44c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef34d08819befcdb417934511166e150", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826a756739d84c8b2268500b25435f838", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2d9e94932523d9963379e33b47187e7", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869c5d3b7d900b130b662d8f8b40a3cb9", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5ada985b005854383a125dd791d7ca5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfb7654ada7758192bb4b61678cf9555", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a27e092078337b12476fc838c255078a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984652551cdb95c7e92033cf7973a1e674", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b539e911581edc711c2962d77a0fcbe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e27bf02c258abde3b336ce56abfd06c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fa6c1d28d0347ac575db0439ab63c84", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883c7b56105bfd2a155fbf664d6880970", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e109dfc3e943fbd4c70ca030e091ee91", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9870d5eb2029c947c597afe978c48efcf2", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/CameraPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98003932ef73e36b7d3d472874968f6639", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b5bd0ed16930b12a77371bda362ef7a3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9847b25d6e657ef0455dd8f99ebdc100f7", "path": "camera_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98772e6851cd4007f66e366c8d1e2c4488", "path": "camera_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988fdda8576c84f9792ed1bd20b6282c8d", "path": "camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e63e513586ed48ac80dc7a679c798e31", "path": "camera_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9877aff9c6e3ffbdcf0d9e7c81230ed5d3", "path": "camera_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c94c6cfe6bc7cb9d5198e964fef52a67", "path": "camera_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c6db0dd6e7820a5296d7c7356a94da6b", "path": "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982f33d66ad9f4583cecbd70a7d16e03cd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edc69502ed4b9b6f587bb0d9d4a6e2b4", "name": "camera_avfoundation", "path": "../.symlinks/plugins/camera_avfoundation/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98800339e32b358a7702017a85cb920037", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98624e9687c4de27b178c091536a15cf08", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98949353e80b2a8ab3598c262fac5f691e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FilePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980e55f324c6e7eb655a0a457697b99e56", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FilePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98400f8321d68a9a5e68e0944b2cd3b9f1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98628dbe2edb2d487d091ded5cfb2f9c32", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fd00110471ea45c462eef5c5b84132b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/ImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981252b91b5ae207325f2de8ecf5882f44", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/ImageUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f65f3e8ad987af16ea94e08e32893959", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d18921b7f3ae6b6118892d151637ee0b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98966e9d1305e6094b96cd312de3ac5518", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858a8a00450c47486ecbf93ec416d8f0b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f85e837cc1852f73f5b88b44afb059d8", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c735d28aa6dbd48cad50fa27b774cfe", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889ccf8425d2aace90ee86070123239bd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98359fe16193dc75cd2a07e362be64e7fb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98779123cf397e0ea2360720dfa261a615", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e826508efd01a08e4acb9d3eaa88f88d", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982beb55a78b359c9b92e27959d7fb1cb5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd79e77e8395e8f238455ce9bc37f5ff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988360d5838250cd2d76ef782cb513ca96", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c89ffd35e2e7c7fae18dbb6b476a602", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804d72ba4bf408db5e0705a4d76aeadcd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98079dd795a9609d86e59433133ad1d7cc", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e1ebaf5236d8936feb9dd191c00fb981", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/file_picker.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d6e80f625d31be4be44bc705f7f7377d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c320dd3e94b041e164d96ece33da94b6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f9ef2643986489cabf1fdb304fa7c678", "path": "file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863741bb564702ef21d4b1b250467a2e5", "path": "file_picker-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e933b387a07031eae23235355546fd2e", "path": "file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98136b011c479862977b0b47a03ba4fbfa", "path": "file_picker-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0c393b7cb48e0fa7e77db4cf4ee820b", "path": "file_picker-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bdf6d249f972fd3d07f503f260f90108", "path": "file_picker.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dac3ec37ea6694676bbc08a00aad005b", "path": "file_picker.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98107793106edcf083a2f5a6e9a11b0bcb", "path": "ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b5474439a6dbbf4b1bc542b397945ad3", "name": "Support Files", "path": "../../../../Pods/Target Support Files/file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f4a349a5abc24aa5b2e3f548d53e435", "name": "file_picker", "path": "../.symlinks/plugins/file_picker/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988356c584b0b939fd6a8ba02c53cb1a00", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98513ed68b7eeebdd9c4079640b2f92fe3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b8024f1c9cffe26036ea9b6381c8f7cf", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ced51a6c7ba2bf4f1867148cb0df1212", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2c9e8c41f3e1139e04e2aa8b24119e", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e07c1bef29746f71eb5bd0bdb82f983f", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e286f0686c6c53516132f44eccfcdf9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/CredentialDatabase.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843796d5639731f6e2599af791b033584", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ecb972ae42abab4119d435fe1631f07", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d87d8fd56d84e117c59b0a7fb849053", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/ISettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9849faf617160260908df385be14331193", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/LeakAvoider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988b2157486b02e7ce83c91f2b504a263c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/MyCookieManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d532d27657f8ec52e6ec78de65f6d857", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/MyWebStorageManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988785597fc9a29f3711415059667d0f6a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PlatformUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd761c295c3e7cc12b0eddea3bc4487f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SwiftFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989e487d156d59bbcb8b8f183ef67df2b6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Util.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a3a07865bca7ae765eba3116fa819136", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WKProcessPoolManager.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ede821ce2e967bd8927a74ff8352a1f5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98862f5b09772eb67a09110afdc9c9d8ab", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9894a3c5bffde7d4fb305cde541a8d42f9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988fbf5f59f861c819659d50878b6ee17b", "name": "FindInteraction", "path": "FindInteraction", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f56d8db97ce921ffa023d58a849836f5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessInAppWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b230c94b21ce35074506211ec55f9e29", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessInAppWebViewManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cebc246146e144284c4252162ec26436", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessWebViewChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e844c27a2a278699904fc14a611c8547", "name": "HeadlessInAppWebView", "path": "HeadlessInAppWebView", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bfd6c90db081aac9a3ee9d79c686b639", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a327fafac7051d82d9fc74749f9a7eb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982e0ceada8233e90b8e402dd3e1cacc13", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989236220eb607627f83b26395efee4a3a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserNavigationController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ca0cbf975e7ae472657237f50f867e88", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98681b5f6ca53d0e32e273f716a0d68114", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserWebViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9865aa928b0436f43ead9664e747300f72", "name": "InAppBrowser", "path": "InAppBrowser", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983613aeefbb91e459c494d09f7cae4bea", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/ContextMenuSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989df39d36f5e440cbbe20c346f6cc5422", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/CustomSchemeHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98125b21c2cec23ec2cd8131238faf6710", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/FlutterWebViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987c2211844a6a6b13a406169a51e1bb01", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/FlutterWebViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832e85c69e0b30eee6b45242e596ec257", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98408f4ba3b185a82ff14e6bb46a41735f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebViewManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98433418258e0aa38e43e88cea8da9660d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebViewSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bda77efbedb3ae5b1f6b4f0adca4cb99", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebViewChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98879d7854899fb97c2eb7696fa2f55c20", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebViewChannelDelegateMethods.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987ef785e122028b66127d462bd1e52871", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageChannel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef23594d1fd98bc47d59b1d12717fe8d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageChannelChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985466425f0f3afef490cded24cbe7b564", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842f9175025d2285586a9b090f26e56f8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageListenerChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d02fa9c7ad8c90638e32d03f6f24011", "name": "WebMessage", "path": "WebMessage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0b63a117e4502ccf35de01d2b0d1a63", "name": "InAppWebView", "path": "InAppWebView", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98080a2df1964319748f97af876ad8836c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/CallAsyncJavaScriptBelowIOS14WrapperJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982983581da70f6238d839b9795f37f30d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/ConsoleLogJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c3da7e2cf1cc49d270cf97f93789d699", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/EnableViewportScaleJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985e5a6555bb315e6f7395128bd721ac74", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/FindElementsAtPointJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6e1b31cbf1ab30d6744d8a433ae783d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/FindTextHighlightJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98642c323943333933d126b81e6a7b5022", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/InterceptAjaxRequestJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989ad64abbae1921deeb9a5ac22fcff6e8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/InterceptFetchRequestJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f4537bc826d1c221f22374eeac10b3c1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/JavaScriptBridgeJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984b0fa21bfcc1306163af0cc7d5bf34ea", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/LastTouchedAnchorOrImageJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f0ec9510af104732f841391f5c63f865", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnLoadResourceJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9892e7a509b71f142356514a9b8af5d125", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnWindowBlurEventJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b49f74a457fe89b6d38ea5e383cf7b3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnWindowFocusEventJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9807b7fb2ef39dd82c686d3aa592f27668", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OriginalViewPortMetaTagContentJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9829f4eef44a49a0a5f4962a022713b234", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PluginScriptsUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9864e53c3d655f83371534aa15ac9d987c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PrintJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0ff66fca7c09aff82f3abeb1cb458f1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PromisePolyfillJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9858810eb35656fe3a203800fed7ba3e1a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/SupportZoomJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985d1668823711c18eac1c30d63a096b76", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WebMessageChannelJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98497f5f350f8dcc99718ff57b34ab68f3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WebMessageListenerJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9888b24645f737e64ce813b798c7cef57d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WindowIdJS.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980680bbb616ce41f17cbb2a9f02015834", "name": "PluginScriptsJS", "path": "PluginScriptsJS", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e6260fe708d6e617e4c18d86a030e634", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/CustomUIPrintPageRenderer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9867eac0d18f7fa9b84c88c09f1028656a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintAttributes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c9e14ae7d2e6258270935ff197de7670", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9862370e89ca709df5c216582b0c19c4af", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cf82bab6cf5afe4872db81c45f5af063", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4dc681be7ce553cb462225191a430a1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981bea09b5928d14a1b54a2979e71479d2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9849d9f50a12ec54972f6520732f28b102", "name": "PrintJob", "path": "PrintJob", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980f76cf048f6f501edb761ccf8996c12e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c6547ba2c4871671a22c90eeed5df262", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshControl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980da1b561ad9251e47c9a7d03dc10d3fb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6ebf432e38ee00a4fe49b0947b5bc9a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859fbee9cccc994cb06707483416138e1", "name": "PullToRefresh", "path": "PullToRefresh", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9811c6e01a6b0453fa698074faf43c19ce", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/ChromeSafariBrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fda871f506a98b26ba1c342208c0828b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/CustomUIActivity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cb6dc7845eb661dea6182c107aad30f2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariBrowserSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d6b5d7711bc337c03c0410621456e7e3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98787bbc6ab262dd7259a55de5b34b81d4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariViewControllerChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980eda198cc069ed17a9a02030317556d2", "name": "SafariViewController", "path": "SafariViewController", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985e4b34b15c74c779407830d2d2271f42", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ActivityButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984bf9867757d781b22d3f25be623116f5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/BaseCallbackResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e39a201bbdc67876d0faa4fa6a2aa116", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CallbackResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98346590e184421ea58449953a12888743", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CGRect.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989563ba9b5ce68ba42bbb57c385f47d7b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CGSize.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d805225d97433e1315bd7cbde7865c6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98853743acbca31014b46db10e63540635", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ClientCertChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986db5e4ffb7ce894c69a16ae2d33dee25", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ClientCertResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b09c49e4229502d1745988afcc71b01c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CreateWindowAction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98891f7bb3afce39e6122205951764751c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CustomSchemeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98631b548bb624617a39b40b05624a6f58", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/Disposable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983e3e782f61edbb784048cdfe97903e2c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/DownloadStartRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9849b2367a31abc6bf84660ca4e65388e1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/FlutterMethodCallDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da56ed5d342f75276e1284f18efc79b9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/FlutterMethodChannel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b07f806cba392f74eddc1120e0b74d97", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HitTestResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988ed5bcfdf663ddec79dc35762bc4c927", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HttpAuthenticationChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a18fc3a03d4abb26c9c355dbfe9694b8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HttpAuthResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bd701180fe583dfdd6328dbd17a75bc7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/InAppBrowserMenuItem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dfe0ffd5fbe83c8c2ef53af4104be77e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsAlertResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9815f93ac302688bd3456a8698a8a026eb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsConfirmResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e4eb54e7bf8b0e6a471f19ec03133c43", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsPromptResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98407c611ffc30cf70824373c741e5215e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/MethodChannelResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5dbd985d7d1856dd2d864f7c8d48323", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/NSAttributedString.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988a4bca777621885df551488959523b78", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PermissionRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987be3576110aa4ae6ea55cdff539d6a04", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PermissionResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980e2c7b3940e2efe984969bbfb86c6d8f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PluginScript.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988562d64bacff50c84f5354374f308846", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SecCertificate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98466ce212726076cdab801ab70e94b4d8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ServerTrustAuthResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ffacfd404a8a28c59d3065cd1b9c4193", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ServerTrustChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985a5069a271dc873440d43211ecd4ba1f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/Size2D.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a1cfa333a8fe289b7eadde64567cfaba", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SslCertificate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982edb52143835fc57a95437c034ea8917", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SslError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980c4dd5b2a4f4b96984a3b98f08928e48", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/StringOrInt.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa46e1e26b29ad97621bb3be3bdd9ba3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIColor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd71a3910473e1cae7acced22caa565c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIEdgeInsets.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989cc22c8ac44e0c53fb2cd75b045f76d2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIEventAttribution.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98460a4ed570811f3e92595245d843d94b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIFindSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985119591ca9c83e576bea2106d4a9c886", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9891d23afbb716c0184346997081a3aa33", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLAuthenticationChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9886e4dc087ad38bad9dd9654f6463b6d7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f12a9726bad5a9fb16613bf95eb0621d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLProtectionSpace.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9885a70d71024503520842454591fb1f9f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981c09f5a7819f187556484e59611d9116", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98505c919dca7035677d3fbdff749c4dc1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UserScript.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98211db100b2f817930cc91bed84be548f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebMessage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab24cf9829ca1b798dad91f1461d147a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebMessagePort.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981dfed7d7255a2c0937de7fc232f3fc75", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b21b37fe8807c0897de7d6387dd2596", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9843a1aa7512e9f939bd410c617b9cadcd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f23d324d64d7113701d3a256b57811b0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebViewTransport.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984e7a8927836df1e20a8a5ce960077bec", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKContentWorld.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98887927d052b5e504c04b9cadf75fbfa7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKFrameInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879e8d542b9d0c24521438d9fdd17dc1e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKNavigationAction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fdcfe8058e6d14a332ec47cc2f0e87ad", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKNavigationResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988ae12b6a6f383ef211e702ee465ad2c6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKSecurityOrigin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988d8a6c88001435075102b40f88a8f1e0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKUserContentController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b0462eb54bd690939aab4ee0153f05d3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKWindowFeatures.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d2e864f08ca39bc4f607d1434a7a5c6", "name": "Types", "path": "Types", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f56363db4971182236379b5ba292ef94", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/UIApplication/VisibleViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a57b8b569d97b05540a898a1e41f1f94", "name": "UIApplication", "path": "UIApplication", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cdf0bebe8a2bc63d535fabf2fdf57144", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984a600e5ca495e71abc7822794c265244", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c1e646c427f726398ea3fc2e107d5026", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983de26dee49b33a7810eb099cf6274d7a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983e08015eaa44d07d2f6fa9c91efb4a0f", "name": "WebAuthenticationSession", "path": "WebAuthenticationSession", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98969a388df0161f05ff71548f36948674", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987a1f91b821026dffe8db949775060572", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f66f546789646b4d137197daa31188a0", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "file.storyboard", "guid": "bfdfe7dc352907fc980b868725387e9810e29dfd41878254e57f56afbc44fa54", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Storyboards/WebView.storyboard", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a4060db0f8e63c0ec860446e09f94b03", "name": "Storyboards", "path": "Storyboards", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e19c030bba83051df3f6d3248a993d6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec29c462792efe7a2fd4700db893c220", "name": "flutter_inappwebview_ios", "path": "flutter_inappwebview_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c50af01616b19c3aa54562dc9e6d0704", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c6ba36a0210910334a70d3a70c220b8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c3479fc64488c24624f0ff08501cf2e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c42b399e8ef82d527946d573f6347bdd", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981936690428189096b5ae965698451ac7", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5fdb7717a02a062c6ce5b8a4145b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98321a5100a0dfaf021f62a55ee3adce0c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4cf792e92d7d602a6b6f2a3f7521f8c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876e97d7bac2b899f9b80ac2c698736c4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898f58bdc8b7235de5080ceb39e1206d1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c85e5e03885de3bd7acec9ce23724da", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987e0fc28f0e2fda3e079d8366035a7c2e", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/flutter_inappwebview_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984159595dd83b65ef483e7ee5f38df1bb", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985e2830c229101158def95d31c90bc6e9", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a4f746ab398bd5c51a9c99e55ce49f9a", "path": "flutter_inappwebview_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98208ea5134f1dbf1ba5447b59af04a269", "path": "flutter_inappwebview_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f8e6ed6e4282d883775bb163d9106593", "path": "flutter_inappwebview_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e37072bc8734d33f3994103912ceacf", "path": "flutter_inappwebview_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b4c17346278c6cbffb86859c291f719", "path": "flutter_inappwebview_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9805f110edfb1e301ed066ebb3654ff9e8", "path": "flutter_inappwebview_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a3ed394b96e4849d2824e3281e2bf906", "path": "flutter_inappwebview_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c646853623adc52e8e8342239d45b53f", "path": "ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981a857df7eb5c1ee0d97ba610b50bd520", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_inappwebview_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8179c2f834185b55e8155c42495141d", "name": "flutter_inappwebview_ios", "path": "../.symlinks/plugins/flutter_inappwebview_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db90cfeec3c53d0f01649285711775b8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_mailer-2.1.2/ios/Classes/FlutterMailerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984fedb56f7a7d19a62227356770a62ecd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_mailer-2.1.2/ios/Classes/FlutterMailerPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e5841b3fb5ab86800c2043a811a4dc17", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984939a6b7b989739987ac378954f97d05", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860351444cb43fd69456409a6d1129429", "name": "flutter_mailer", "path": "flutter_mailer", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894716196b98e749a86942bfd915256e8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988dffbb19968ed6b868e5c8afe119bec9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812e13fdb436bf0336c563e3461567066", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e7f540c2dc74ecfc50f3e9d5fabb896", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801fd27e848ae46347c19b4a1f39dcb9c", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5bc2b11ae73efc8532306665001d4f1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dd876412009063590a1ac7485db86d5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a122491c07f0c3a3bfc49e781a3a93fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0b9405fb871784286d950f424881fe7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a531fe14c9b82a02394bf01011b7d0ab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98508a01049baf37703fc8b09ca1f69d6b", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_mailer-2.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988c761a05826f39e4dc1c6384382ae0f8", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_mailer-2.1.2/ios/flutter_mailer.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9839d61a6ffdd84ba5a560564a35b07a8f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_mailer-2.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9895b5b23bf1b0155aa572d7914c4964ae", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f59729dd75bf596f13422555f1ada86c", "path": "flutter_mailer.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a7addd6044acf28e6ac77f001a5ac4d", "path": "flutter_mailer-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a46695d234c69494e514a20e35e3cffa", "path": "flutter_mailer-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98981600d6ce254f074f193561ee9aadfb", "path": "flutter_mailer-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980936cd8fe153c424274550bf884ae064", "path": "flutter_mailer-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985a2de1cb1d66a52d51ea5dab78735917", "path": "flutter_mailer.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98202395c96bd59fe84836029be6f4080c", "path": "flutter_mailer.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9826f4fe14ff09924d57513ecda92e639e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_mailer", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcd6751ac3be21fa51f3021ac70fa62e", "name": "flutter_mailer", "path": "../.symlinks/plugins/flutter_mailer/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c39e25d6af3bea7762dba3ac30d0fff6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/ios/Classes/FlutterSecureStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d496c2d943ce8bf240b98622223699b6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/ios/Classes/FlutterSecureStoragePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc891fb580ae0ce6ccdad5faf0560beb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/ios/Classes/FlutterSecureStoragePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5a3ab0a26c4b6a0fe9ffe4cbdb4b3f3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/ios/Classes/SwiftFlutterSecureStoragePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dbad61ae29123995ba67fdec7152b115", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ed6dcc2345ef9b4a80a1e08f1c46c970", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c449eef6dd8d29918096f30d4bf1f6f8", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896e3da81d2f4102936d9139e3e6f96f6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803e051a1ca5b265ab7702d225df46631", "name": "flutter_secure_storage", "path": "flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982871ce94366251fe41e71f5f691b718a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98022d02ef4ee519ddd645abd6e3a8d88b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855c6f31ebf6c9d6765888009d4189efd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a54be6d4c902a2445540224fd9c5dc5b", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818698925e25cd9cff149f6c5a98f55c8", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896acce87d3693f875223f34496d15cdb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfb1a9d3f77e7e6aa6d2c9cc5bb0d976", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810b42de7a46c7a872fc02e0474003c19", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816d218a702ee3b1501b5ff6ce56d0af4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1aca616ed039a72a032887d65d1a29d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853a29360c18d1444e955a0a89f1f2948", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a77fa2d01688bae23058501ccaa5b5c9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/ios/flutter_secure_storage.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988180cf86515de4e12723b9ceefc60ac1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c3fee233b769a45e91107c9e03b120e7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986f090011906105d6b0fb2ad63d1ef7ee", "path": "flutter_secure_storage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9830ea0fbddb89279575c299976a067e79", "path": "flutter_secure_storage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987c72bc7af8799e15f68522c9b6350faa", "path": "flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d51a4a0aa0d9da4063aa85f1472fb273", "path": "flutter_secure_storage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897bcd8bd49d1c208091744bc538c715a", "path": "flutter_secure_storage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986f0b0fee7f378468262794216e22025b", "path": "flutter_secure_storage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987132a8123283722f80139da03f64f361", "path": "flutter_secure_storage.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a701cf7214629e65fd085f5664f695c4", "path": "ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9898b416d5a61fea5e8ac343568357f5da", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98411d349910f11bdd1e80870a4d35ff25", "name": "flutter_secure_storage", "path": "../.symlinks/plugins/flutter_secure_storage/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5fac690136f7f11767987ff57bf9adf", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_web_auth-0.5.0/ios/Classes/FlutterWebAuthPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989021bd0fd065d48d87c056ac1039a4e3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_web_auth-0.5.0/ios/Classes/FlutterWebAuthPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f2e703b62651045a9426b52e4eb043fc", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_web_auth-0.5.0/ios/Classes/SwiftFlutterWebAuthPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c3ff66216048ebc0e8a6f1651a03245", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f917498a1f9eaa2008c191e2e8108f66", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1142904e889ec32c3b11a46a1159cd2", "name": "flutter_web_auth", "path": "flutter_web_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b948dd74c0916339fb065662843f7b5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9961ff709db7fc234982f5528de5c1e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cea51d122738bc687fcdb43346d3e6e6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98775535306c9b1a1d51f449a54e53869e", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d9020566ddd87b5945a17db6d0f687c", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a39883293ec2cc9ddcff4d777830c261", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c071acc0d89bd86a98f53a2e3a2a305", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853fe3fa77b87cdcb991c9e32c6d58b13", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d01fbf46066bc8d2ee979a2f9d1610e8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828184b86c692ffb33e71051ada48b7f8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852cad4bce09037dd75d6092a00505816", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_web_auth-0.5.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98750adb0091eb1991d8b4e26ec7bbd0ec", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_web_auth-0.5.0/ios/flutter_web_auth.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9821f20db12b55ff04feafc19b6bd457f7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_web_auth-0.5.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d4423a86975ece2001e950c415f7a3f6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ac939bec0ecfd04a5cefc9ab130a6946", "path": "flutter_web_auth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc690e8226e81daaba31131c9d39d7f4", "path": "flutter_web_auth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dc3d260115872c592e2268ad2ed01ef1", "path": "flutter_web_auth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827aedd3a2c14e494a1e9030015c545c4", "path": "flutter_web_auth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd32c8197fda231b08a8c209e12a5415", "path": "flutter_web_auth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989b820d93d463df74670ee3ed34147986", "path": "flutter_web_auth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9822cd87ebaf7050b691abeb9dc829613b", "path": "flutter_web_auth.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829df7fb89e96347d4281a720ba6a071a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_web_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980453022cd85614ce9d4d7f68d6ddbb74", "name": "flutter_web_auth", "path": "../.symlinks/plugins/flutter_web_auth/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98532fb196945787aa4e3230e02c65a6e1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb86be345a94d826110677f5a2fce822", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98182d756466333633627d172899838dab", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f9bc657f95ebd6eb97eb00c3cc14dd4c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c58a603ef130f98d62d9d84d071d589b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/Extensions/CLPlacemarkExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9871d54fd84f9a62a157cd9a4bed1726d5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/Extensions/CLPlacemarkExtensions.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98482ab8ba01ed57eb2af6030f94db174c", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5f1e707ed9a5f90c0f0b16269f5b730", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b33b0187920830afb579611de41240d9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840bf68447c51c1e104a94a6d71138b43", "name": "geocoding_ios", "path": "geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce781dec3f062a868021d43e7f3901c7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e821eedd6099dde78522a4995ac9f2b0", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c55002f64f23dea94961b28d31258f2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984208cecf1728409e66bd1872638d58d9", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984719556d0d3a14ac1744f90b7d76877f", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d096e3c6a83643af6b088e2b49bc2245", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985192a1cd4a3bd50a897a058cdd72708b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b684654ae7d97de298b9882202566362", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988424ba32edc6df5fecdcefcdfa024b6c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873b002f0374fccdc1019ffd2413bac0e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e10c2e63621e3ef155cd639abdb0268f", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98cae407f8d802d27a768d3248af405edf", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/geocoding_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986c51e952de8b02bbf61ef4ce4b2e7eb6", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987755298b26ab0ab4cd0f9077966909ec", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98353201480ed6886d39771c01c3a4bc70", "path": "geocoding_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c9abecb3bc971139d1cb6e8e6a24684a", "path": "geocoding_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cbea69ef12efd3642b589cd1d2b20b9d", "path": "geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883a0e99dd2288eeed0321db467850b51", "path": "geocoding_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98adc0e81dacf4fd13232b8996e4d01568", "path": "geocoding_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981acccee6581ede3aa2a36a033eb73892", "path": "geocoding_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dd6c11a084944e860abf8ac1af5ff340", "path": "geocoding_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9840910b21562d0535f64bc7888afa7c99", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c02ecc8b21e4fa61ca77f3d8554af034", "name": "geocoding_ios", "path": "../.symlinks/plugins/geocoding_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987342462ea1554307822f012fdacafa59", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/geolocator-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c55fa97fa6fb7d9586ef01fb11a54cff", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6995c725ec8d04c7717cfda4daaacbb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831924986a9231bea5f435815c6a4a411", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b57ef031e08938f7045d3d365772631", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Constants/ErrorCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6de412608a9e2cac7477b15a37dbe47", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Constants/ErrorCodes.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f6bf1d8d6e76417e3a5687cf6549e14", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980226413f93489e41d089cf9b73a51341", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/GeolocationHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea4b608e1954ff8c01faec1e85f110d4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/GeolocationHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840cc43a6b7ea716bb81e408026042e42", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/GeolocationHandler_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5baf771b68937d6134cede1983167b3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationAccuracyHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ae3d013f57930621243d07b80bd27f3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationAccuracyHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd339e6ea24033adbf7ac8b3837d76a6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationServiceStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4af4daefbe58b61334525cc7f53defe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationServiceStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a982ba86aeaad740f2b0157821c9b6e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PermissionHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804c8dbaa7dd434a05269dd3b894c73aa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PermissionHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e99395ec9365d20014af79a2cf5aaf8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PositionStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec477dc8547e8b084bd87f9c48f14847", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PositionStreamHandler.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bc5e6b43dde1dd6cebce01151e7e20e5", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980aa47607c91bc2fcb24ddb21ee702f20", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/ActivityTypeMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fbbda7ea3ba800f9071e9aa1ef13ee46", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/ActivityTypeMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c669b0250fe0da777db7f393aece9591", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/AuthorizationStatusMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da33ce14cbbd56812a0c6d246c69a6f5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/AuthorizationStatusMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5f7d34d088c6493435ab059c7b1ba64", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationAccuracyMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca50c5dd12e0d57012797e182c9e0688", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationAccuracyMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820809a9f3de37de4fce1201949fe81b9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationDistanceMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ed7b5fa877ac198b5e44cc30aa32ddd1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationDistanceMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984fa02cbf490d26dba16fb83e9f0b5948", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981e72b5923c0b1833924ae3f46ad12d9a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f648b2361afe0c30e8ec24b13e52a44", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/PermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987bfda06f21680f8f4c2d99f1d886fe3c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/PermissionUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a18ae795b461fede32377580b8e488e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/ServiceStatus.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f6e082b737b6cae7450587bddc58778b", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986da04f326970aafbcfab4578a20b31f2", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e7c79a39676a2bb7c6f4a67e415cd4c3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984c368c8725c33bcc630fdd892fb1cbf7", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984371e732eff7667bd005f7a63bd91bd6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801dc0dcff0f873878eebb1d85e723f5e", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988fd35447d8c2a30e1b559d7411912e78", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa85339f0a54e6ce93019ecca55c75ea", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a540b9283b37a044da40a402ba6180b5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98402248bd1117dcc74f7f5901f71f815f", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823db4178ecfff6c789015b8159410f2d", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987aefede5218fe09136ec922edf32abb8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984228a2d46fb89ef8b85bc0b997f868fb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986287ea7231c0e32478e919b83cb7244a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ad3e3f92f910d02cf4c299f64a7527a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2d9c4fc6f53d8ae8e32bc3897a50d0d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98093424a5fe915b75d8e41fb0cdbe136e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9851a9aa115929e8f1a9e109584c97ee94", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/geolocator_apple.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cbaa7d8eee11ca8c6dedd2eca8df401b", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9832077ff261cb9299b4851ffbc74d2242", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986d98a3e7c23f4d92cd82feefa952afb1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98da385932a58b2b5cce56e22362fb7936", "path": "geolocator_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c54f45d1eb8486c5f39d4b6d23f7d96", "path": "geolocator_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887ffcf65a08433bffff9484d463e4abc", "path": "geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a93c3e838a6c2a9f19abc37b435ff4fa", "path": "geolocator_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98739c3e72a3ba1fa0013acc36621c4482", "path": "geolocator_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b63c5c72f999edc072df236873781ffc", "path": "geolocator_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a4aae300b33963943a844c4a2bb758e4", "path": "ResourceBundle-geolocator_apple_privacy-geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f4651601dcb2a07d3f1b5dea52190411", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983660f6cdd4a38b3bd6277161b568a611", "name": "geolocator_apple", "path": "../.symlinks/plugins/geolocator_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f3cb86311f5174c6d0cccc6c6ba3e6bc", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMClusterManagersController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a0749feb24a7a956f60867fc9062dd7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMClusterManagersController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4a5c07ae3c902ae59158f063f6732d8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMMarkerUserData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1aac1d8dbe6255bfd5237b6e89ffa3b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMMarkerUserData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988074a711e878e8f5132be17e90cbb376", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapHeatmapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5aa89c8ff9216bbe48e8baa4f7fcb4e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapHeatmapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826abd0d3b43bb8e66275d257f1f1d6c8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapJSONConversions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98699f42ddaf3942eb488268300b4fc93f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapJSONConversions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dea1a439cec0ea8774f2bcb2ed92b464", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c372110ca28920dc251230450347831b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98650a7241a5b578dcd0d5feae3cc2478e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapTileOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad6919e447e17fba0cef807a6ad07d3b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapTileOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4ac694344a1755ade63700cef05db74", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/google_maps_flutter_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbd340c33d7968e7d012a826e5d1bb8b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapCircleController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebc283beba8ad61ef426a3cbd6c11a32", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapCircleController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877830a767c48ba30c1c626cd46f1d86b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805bd42ad967c40d30b30e1c710b41eb0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813ce6ec674e17510190e257e2ebf7331", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae4847b4fd74b7c50e8761366156816b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980e141a59d2d6c83bed3a697efcc2b2d9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f534421e35ca8d2a298176ed17888646", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c70137eb68b024135232a21de4388f80", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolygonController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3568d1a1c16d38835d57b8a389aa911", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolygonController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0a8848beddfe973288803db95679bd4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982514159a6382d3df64d8b97d5728187c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9981543f4168c2c17dda18575f8f14c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b24283a28cf1cd497f6e28edd130ca8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889ec1e73a67613ead749dcf2b31422ca", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c1c6f59f338b1d2e868338e565995663", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984b15e607d9e7b0d86e5f835a96713af5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c6394453fe0427712f1546b074e0594", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892e91877f9cde4f86ab9e842f5133543", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d8fa39732b9468140f208a2c24ef08b", "name": "google_maps_flutter_ios", "path": "google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98625b0966f5a286b5e65b856f3661097f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b95a21dba6780517ad29fb51fa758d51", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cffcd392e907dc9db0dc950d641eb38f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875bb68ed768e72bd5fc6e8296a0db659", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cffd68f41f4c0241988d6930344ab50", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd7a3a31a12db0cd9a7283a013f32199", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815fffac639797cef45ba68f97b60b6cc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b996477c67da6b51b765a1778879487e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ec3b0d2336d8cd022de0098ed6362ac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd877112f4a51fe26aa3731246e039c9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a02673582acd63ef63e829cbb26412d", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982c9dcb69b2891fa3f685ba7e0c66155e", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987a9db1f8a8b398ed1a0b4cfd57ee64fa", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/google_maps_flutter_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d13c897487cdb2a1eb946d7eb11d99a2", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f441e505667ba3a1cbd6f9cb24ca779b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985a162fe0381126ee02d4e6f90e06da85", "path": "google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3cfa1f910e105027bd4ef3e9983429c", "path": "google_maps_flutter_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9806fdd4faabc7139fa96492bee8c9c384", "path": "google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2f860d9506dd52698309ec653d8fa3b", "path": "google_maps_flutter_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9832c234f231fb0fcec6dd09b74a814fc9", "path": "google_maps_flutter_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e01bfbf6cd62b58cb2a33951d10eb03a", "path": "google_maps_flutter_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a7d87cc3b1f5bdc3141c815abd8f813", "path": "ResourceBundle-google_maps_flutter_ios_privacy-google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9898f3ec7b285a772152b4d152b9779caf", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985196a1cfefb785dd46cf2b426c20f37e", "name": "google_maps_flutter_ios", "path": "../.symlinks/plugins/google_maps_flutter_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98dd7435d3176763eb9211fde71eceef1e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b56a5a1c062ee68eb1d87a5e57d49b5f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0e4f77efe721d626766c7943640df74", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4c0b81bdbe92cb4162e89fc6b413095", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98380b86af62b105ed504440149fab0dc1", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98192461cff1c720026c84f72ef8a41601", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980253747bdbf0f1175b412d57d50ae403", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af62f6d66c4e85afaf1dacaf65c7a1f1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c20c536d94def7e3b86fd00fda5da7d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98270c660ad3ec0be43969c5a60b9c4f00", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98837c879029e4a813fd27b27db7ebb667", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883188886e075d959c2bd5a7cc83dfc84", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e38f8c4ace58a785b3f4ede20ed9ef4", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e175e52ed34815cdac21154571194d0f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c933697c5dad3e2f58f8bdf1529f88f0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9842096c149fe00fe25b32fb7a9192af1f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841ab556abeb73a592e2798d58c940b9f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8807e1eab9af35acc7eefb27addf868", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98185e943e0400e1106c7b130db4f459a7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98313f8c346ed4470d734e707023ee0d12", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986091a8e847a4d4bbab5a7feb044f71d8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbb9766f11481045e0a5980715abdfbc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810b885a59ab8be8b966b5a38c098ca6a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a83503260444fa8efc64e634bae0b0c4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98912cc23020ea6af111e2bcb3fc0ead33", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6ffb6ef5a3ee7dcf71f17aec332a0b8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a28cff7bf492d91bb5cc5868feebed99", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c4cb61528fbb99299dd9875ff61079c9", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a1ad0d489469a4f3ac7e95899b417de", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ef705ee7b8afeac29a3b2278b20b5d1", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2b83e627c262f4900f277182ee53a13", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98405e4612da89bf38b333706ee01b99e5", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f32faf99f983973a8c41bca7bfb6928", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889620a6dc367aad6eca7a8e7de526911", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884c3b5dfa7dcc117c12413bf229a0939", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f753cb7ea6ab9292e2741060981539a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1d9187a91d0b821cff56acdda567adc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833bc89b068cd967446a8d4bf070d6337", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a459a4ab858496ab3dbdf16efaf6b350", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860c91635e53c779f4e8b85d452eaa180", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4e439733199450d0a71b50aa02a25b4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f6cbe912fea14ea2486e3c4a104ab1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98449be6063b936229f781ebf9678fad15", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5ba32b3eeee4844de05170063a45b7a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e5ef046a07a232f0c142a8a1b099e3d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98948887687d15d76a540e433a618d7c20", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98913c9468cb0e303ca6dccfb9f3ee7f1c", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982745cb1fb763ef8e6d2fdce2cef58dff", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985dc4defc225c0bfe484d06516740c562", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f8617f1a9269b5480ae2dc7708133752", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98835dcbbcb5db30f4f316aa10c82e1841", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ce30e33e7074b08b8e8c6c70ac3bb137", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b77d9444e513b6242b91739c2862d690", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984ccb4a7a7e5329bca9e81dfbfcb26551", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e8deaea9c2dd20f884fd76b802f4bf9", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9899d93ea0a58299e4dff33a6a00b94147", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f9a9e98a97aef7cd4188e35542eb1acf", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a54ee8e969815a82238ded182522cf2", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981997fb258c69d939981d9ae98e608205", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98063cd0804b3a233353edd231298f60a8", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6b27f0b377a5bb83e0b029bbf51d993", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-2.5.0+1/ios/Classes/MapLauncherPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a58d089652ec1dcb04dc94a2faccc599", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-2.5.0+1/ios/Classes/MapLauncherPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983bc98dd07cda5f6ac471ae44fecaae03", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-2.5.0+1/ios/Classes/SwiftMapLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982b96e43514d87b0e78ef517574b49288", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e63d053aa81479733abd18f2e02336", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fca3a6571006261aeaa065e537ad3688", "name": "map_launcher", "path": "map_launcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823d05b3f474387aa3e51d327aa9a5c05", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98620e80247145c96fec8a523913e6350e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98841701f7b3d505fe324d77cc3bbd1496", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98936d062b9d74480a066c533192db8fa4", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdcd51d15bd027b886194722f53e2be2", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ea9139a7c12fbc0dbfcaf89f3af5a50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981246c4857c8b7d37b8228b882c86263c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f98a20f0bf67f60620a70f56a571099", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988282b395a1a11de19fdc88ab43ad807a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981211270699b18c8e3b3e79346c42b7f3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983de9c1e4f5a7d06fa82068252edea3fe", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-2.5.0+1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984511196a515580c9cd9de8f4e3d10fe9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-2.5.0+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9856b564fcf8d3762e89639964693a1e77", "path": "../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-2.5.0+1/ios/map_launcher.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd715d298e74ca34484836714f837900", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9851b011b453f064b8d72fc27e0be72698", "path": "map_launcher.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984dec17413edf78fe69c17aa2553dec2f", "path": "map_launcher-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98618d42e6b65948088ed6f731384cc81c", "path": "map_launcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894790c353686e34a2c28140146a00904", "path": "map_launcher-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831fae58cbb69e7c21599d40f0d4ed52d", "path": "map_launcher-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9815ba3f7c4815b43fd068c71fd0e280be", "path": "map_launcher.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98363fbba1490b5dc689e902b2fb3409e1", "path": "map_launcher.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98949398ca16437cbf9c2917602fe54ff5", "name": "Support Files", "path": "../../../../Pods/Target Support Files/map_launcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c97dc20e8bc000a2b4c0849edece0811", "name": "map_launcher", "path": "../.symlinks/plugins/map_launcher/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a0fc68c363e3b99d81f52439071039a6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9836b62322faf744053db911d5002b8078", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981765fa88d3cef8d5c8fa24a1ef5bec0a", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98388892d21a11bd7ba97a13b44b78a2e5", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822d1182e52c424b6dd1c8b3dcefacb7f", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c471f500f265eecb477239032aa7170", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9db797feef2441bf9eaa60c9afe4fe7", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809f599b5847e93da082ecd10497978f8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d40db30d975aefe3ecb5f32f80416e6", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3473263778cc7a12a24e7c9418b4efb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7334f98420c823a03d93088b86e3099", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981489af54723ce274167479cc0863fd43", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98574952ae352880e138bd66df5ee19275", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98792ec7640cb82a176b20776fa99b0a1e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983f68573b7bc47ea1e7d148cf2b177ec7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c3978586b8f4e75c499c064c04363d2e", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ded5275200881fc637af689b05e8921", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8616a1ae7b9a6277dc2c8761887e22", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a3bd1c5963946cd1827e73e0a384503", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac4ba61f41ffd53cebcb2363802ff8d2", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98016c4f9fb5bffbde2f9d2dce72baa7e2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc2b9cf487a11cfd6dcf12dd4238b904", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3c3ec3e2edc7b711c40b52a360f016a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899c0e10976d74eea04e57af81c658cf9", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836ef5ff0cf7c907e74482e9880ddf3d3", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819566087e4ec0ef5f6e2e79f408aaceb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ca8c1152ff864c08852c145c0db9006", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5699b62213e3aebfea419d4e9e75c80", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802cb23a1fcf53ff9a77eeba1cf93d547", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98168dd9ca596ab89215ad81241330e0b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a635cdcd3625fe5d6a4f59eebb471aef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2b9986fcc4dcd0eb1e10209d472bd55", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f797566c2ea86d0a9e5b27d060a5cde0", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989e958d93ede63dd51cc897054aeaed28", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ed7f02b0595af4843b2ba6352652994a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f05da22c360c1e84f9bf353e4b6ad323", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d31fea5b8d04f8218fb9c0f6c99380fb", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c76dacfc74add7ebfd0c35f0054ed58", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98612d57835a7607db1b99affb3c1f5619", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b86836170f0f09438cb49e6e496f66c2", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d628b668ac97ec42ee564c1055820d39", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d522703061c30ece04c33dcefb96b6ed", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b7e352be47e3889c05ba6164eaafc16e", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c392c62cca962aa87e26ef3a75e334a4", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987fc0218f11c6c6f0a4f6595e17d14a41", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986915d7136d915e2b335d50a60ebaf3a6", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5d9af2aa3b4234db5bf228dc454094a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9bc7ea7d3876f42893d9d1b4674b65c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7c37f62b0ec7ee5c91b7c53d1c69aab", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab17389718d7843b3672f9e0b7785cec", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a0e04917823bbc46a0ce2f4fadf0fd6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982794cbf25592a29a6af58a519638ebf4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899c0b7d13bada93cc768ac28ced5f876", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcbad03c03234925a5320bc4e5df315b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982cdb57e0befd5f66de62bc1a4f611567", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c67edc2e8076f3fcf89cf0bd7a4071fd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c247121da906b161b15cfac732eb5034", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98601b922239342a85db0c32fea77f7f46", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a8cadcad958b93080db11964aac2883", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bb846ec3310f1089300070198cd9b2a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98151899fc338fa063b35e765822efd80d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98720dd0c9cd0999fb40eebac83b0778ef", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ad1002be2c55079844b2981efbd7815", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c81b63f5ac486f602093e549f640c926", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e15119489fda0928f2c47d859cbf27d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980be0b6d05ce7279eaaf42ab8ab8c888c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f2216b03bb1d509f27def6e90ec3899e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98626832e5f3b2d859c486541bbf74e9b8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98995d953dc630c5c0964309ee329221aa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877035be071472de8f4c8b6fc3002876c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848c91e6a9d9c985b18b926057ec223a7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98610f479e9953e17ad570ea42f7062ec6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a39c0f6868bc8d5f0217b20a9c18a500", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863a8e4c128c362a83db8232899f8fc2c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853360285a52aa3393f440bab7d47c10a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e838f07c74c7952e344bf41c4ef2be75", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e101533c677a2175907d2bc44a892d7d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ab8cc70fb6fe02f90e172866c1ab006", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985885522fd10dee1504eedbd34eef118e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980015684834c50966add7c165a79b5d55", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817c4fe51dc20a29d22f6876866a678cc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ece13b83d58ac27d0aceeb0d7505241a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98af5d6663bb3f1a2d7ff5b281efb11ce2", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989686d8ea1fb3adc0d67fe517e3b11465", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fbe373d08eb1c949c51106958a9da32", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ab1aff386553e1ad3c57f189a6521828", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98653ea5da447974efdb3f3e22597544c8", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98657c0db4232e8f1b34cf8e49024ec2ce", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843d2fd3efb7c61c42549f597c3ba880f", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d449feea3f25eff746bf4a6c9141ce27", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b789a643552140fdfaf29fc9274cccf2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d7e719d0d64d179005a600eb4c704c9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984320dd4722f8649fd91507827<PERSON>ea26", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d55240b34cb89998995a5c4bbf170b7", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d148852081701b067780a715146cbfb7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985058042bcdc111067fd4c524d3dddf29", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a74694a30f93bd228d023d3e4334e0d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fb0da935fe324ac91f941b14f05a40d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4356afd5390594bcc846472e3c0bd8a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd6bf27e8068ac8ce7745b979d615c5f", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987f9d19540f1ff3a4e197e2ae9f9b04fd", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982296b425b34fbe84bc1ec1804268b80a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f31fad6352fb10a24996040891c4b9b2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984a5d2d98e5162d30d05b9e626e547446", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981bf9af5df7565dc8029402be3da36c1a", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a945792869e3d08106130f5f7d6ced0", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e64ae6655aff9c4fba416f9ba4754cee", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c87c952e881bb6723a2263389ebbfe47", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c5e76ea2c81d201a759a3e21755a6d44", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98114d0cb302e43e66bac746397e498bf9", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c26d9de0f79a4ef718673f741d83dfba", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985dfa5d6e2a015b67980034920361f083", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c22c2d21373362b703aa175d2d3e987", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a6eba5172ccfdd2dd1714398c486f38", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9875e0051de4994fba52b6e39131c147ec", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a69bc89a74a3478b1d9ce6845c0595f0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9841e097b1402bbbcf7540d6e24a48c907", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/SwiftFlutterQrPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9813ec341caba739606d458a6410e72f28", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee286ad12265440c3d56fa7d47ac81a6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6c62bd22bb86fd61185732f905a19a9", "name": "qr_code_scanner", "path": "qr_code_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c199bb76ae46c2e6a16ed3edb3d95932", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866db7f3773da348ca8a940f965364d60", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98084411d8d5aa791bd88bf02b8564e5a4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd38daa6f2369643df7cb2f8a839abb7", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f7dc831054610ef5278bbf1ef030899", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805fe1942bb02fc0627952ba49c0e73f2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835f51ad85a98ece61cf69e82e0175086", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b6afb0dc965326e8147d9eb308fedb8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c71f0f6261dd4b7787afaec51af2bd2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832d2f596b4d69657a3b614b006861fe3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983653e1c59fbeac56520ef6301f5cc8ce", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c4fb74ff43f9072352d02722f718ea00", "path": "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9873c9cbdc40230cbc647fe21b1f2a2d00", "path": "../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/qr_code_scanner.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981981db29052b23b8992d57d16ab13d0f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9813492d77d64b866bd2a344f2745ddbf7", "path": "qr_code_scanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98844179be7542581589760421debc2ef9", "path": "qr_code_scanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c14319886de7eeb07c6a3f96abb9b556", "path": "qr_code_scanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc4efd9ad6207bf5db2d2d5c7226b1d5", "path": "qr_code_scanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a485de94249e476583859f8e1fc6ff5b", "path": "qr_code_scanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bd99e130f52e32eae17f6f0895103090", "path": "qr_code_scanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981091bd97679c627e17f3345308a1e1b7", "path": "qr_code_scanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98df61dbbfdb83b75e13f15e610ba0c7ab", "name": "Support Files", "path": "../../../../Pods/Target Support Files/qr_code_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c83ce622d6cc19799b2fa311c8e87aa", "name": "qr_code_scanner", "path": "../.symlinks/plugins/qr_code_scanner/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9891e2b2f2170216617373f524ea1c5bcf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cbab6720449c747bd2d650e297d7c50b", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b30235f16fe9d99bf57702b71953cc79", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3b4f63b9a2e1beb7911f3e76cd92ac3", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988824ad5e0b385f0730f0ffda00bd2a99", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c2d55aec2664fe30c1ddef96974b44d", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845dfe8de402746973c40eb68833bbef3", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e28fcd677882f55efe1fa3f9fa97b2ff", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2ef72ae8bb63585c789c78237d9cdda", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8a8be370ffcbbc616db70226ba1ad3e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838ff83856018365a631c6b1cfd514341", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851beeb35c37eba3bfe90b3ed2758d21b", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988124d6b8aece6c278723f07266b5dda0", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a374bc37f87f43755bd1e5b81b87d7f0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f17696e2867056cd458e9032d91b1586", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984ce89201cf5a836653c2d02f7517fc86", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98719ef6d76ae12a9a8bc550c6730b51ef", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851e9ad54d0f188a8ed4b75d9885172d4", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bf736bf868ce1ffdf88a5e07e820714", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98247c5b732d375880be8e7fbd12cf9e9f", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844fc554ae8d71ed393189be8e4d1697c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a980b45a6d9f70b618fbfd1edb32e2b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb2c2ca6d1dd55d32d5dc40748cb6cf1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f091a5ba4cf0be0d272fa0822bf29378", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d29963367c3a36a43297bbc02a3c02b0", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfc0dea69abdcbe235b0365ae96cf8c8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cb7cf98aea568cc20b6576cd3015e33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c2d18b9e2783cee8915f59145399c7a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98536320096f5e9057611b4a32ea504c74", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c1dd46aefd01fb6d6d3a35bd336cd17", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cc8470c0e51ade5854a5f8f7d328adf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98536be9397b8623b69f6ff26f3a686283", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c41cfb804aa4b331bdcf9fdcb8803ea1", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ea49ba4da096200c45059c75a147dffc", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9864f72c9870321c137295281cd2f1007a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9875b3c4fb1a400adc862cd61d7551d487", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9895917183e6e0486f3d31c9917150d0a8", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a02493bea2e18160d854f2eadfd6997d", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a48b2f7ee6075edbd1925ae0925e1d3", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a4904e5ee69fac8538987fb86f0c234d", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cc93158f9fd22e7dd31287f6c6cea6b", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98848583d1107c9c9cda56e81ac1da42df", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b0a3a6d7faa09b6ea22ab35b28f96b30", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984a3e3ae5f8898b906a61b666ec8a9d5f", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987fc4001d96c318ee171b775c7cdce575", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fc61093e9f773995a2343bf3b50ed96", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989940c942d87bf9ecfe0de47419d6dbd0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f52962e84d74ec8d0de7bb95c063ac69", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981138a4fe2638f0c13b83f8119e950e37", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98288370ed155f3b85619c1544d632a612", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982e0683f1d25ee57e14855a2a48ce748f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9896a74331f8c4b7878278119fb995729b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1df5f73d578e2452632e975063dcd22", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98966aa31e2001b2300808cccabf99e196", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b6d26ee69ba806750a3fcbc834dd3ee", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824a362bac0a24edbd4d8d64461272200", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca13aeb9fcc0f48770ca9c246ec8a551", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891caa1d81652c54e349159392f17fa1d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5a232f52528ce5777625d78145f84bc", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd979cb6d1fbf6d583c1720d6ac0b5ff", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3e7ce475ad804184694bc038e831d15", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f6a7b95292df3059a63b8645e786285", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984482b9f250592875f5293e493d0f3a78", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c863bf4659717e18330285c69e45b75d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98349c889b8cca561e09ec7b68a45504fa", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980de6a93e69a160e0132ede53d599de92", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9876003f583ce56dc61b837da007940973", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98401e16b3621cf12d0ffd7bd839ead23e", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a078f2bb4f1428a589ffc7aea964eea", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e8e93e68ca236a8f3fca4b58e365c37", "name": "sqflite", "path": "sqflite", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837b41e14fec8b36549c3dd2f8680a030", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98115f7730441cb3b640bdccf366499261", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf236c782fec854622f002262dec6daf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98662f9f507e8f4cf085503ac02a8cfd48", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef115f22469019e45cd14213c7724666", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c032de3dbb32de528a105aed0f2b5e72", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893e1ae8d749a9c4297b62ea0c0aca90c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d598ddd57ddc300d443a46b07d2dea7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bdf6088589f5f39b093b76aa746498ad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a93c2b7a625a3a637da4b8317cd59603", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd72f806f4fd48e6f7311600a751a3ac", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e6a00f92845cf4c239020c510bf5a065", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e98d901e872bf54b2d5ba39b9a48d81864c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d24d74e1654668c74df5e5686d438d99", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/sqflite.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f95cc77e5701f7672e23e3bd1f9d58a6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9897a189c03f65d7c55b68902050175a43", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9803a779517597af561b43be09f5906ff5", "path": "sqflite.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d82384644cf833c462777ce091ce152b", "path": "sqflite-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981f864a0e4627cdccd4d48d154bfbed1d", "path": "sqflite-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad597ba756cc97a65146a9904265ee76", "path": "sqflite-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5891b85583fb61dcedafda0185810da", "path": "sqflite-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9839003da3264f9314078dd9ead5294857", "path": "sqflite.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98622d0f7f7d8ba3fe7785e8d2549a994b", "path": "sqflite.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98911e8b8c9b0af8b33c6c6077f9579d2c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881d93039390c49b5c09c3f1338ebf48b", "name": "sqflite", "path": "../.symlinks/plugins/sqflite/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b88c55a1342235c551ab25487457ca86", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985050cee0d4963a1f185e7e67c3ea6a33", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4c809d8645948dffe0a9d448354b198", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b64dee807bfc688e52ea11d4349f8d52", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ce06b40d10c81544cc7e52ebb92531e", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccfd0f63716b4202d2760d0d4d5335d7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893890061a88c370a6cf656e051061604", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9aa9e6aca79b59f2dcc2d889015c7be", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c87de3ad9a784b37f2a6bb29352c58f0", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98403b4fb1b9f28817792e74b983e28582", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987991a6adae6073151ebd7a5ffb793aea", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9890c7cfc2c33d901f298496e7036d4cdd", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2943f47fb91949f498910865f89084c", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c619188d14d0ad69a8278d7ab9da97bb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b8020cd364d0c3687421ffcd6e75af1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5fc43f272249e6fae62689265aeae6d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b4838461b7a3e6706c3358438f2c451", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d330304ec245cd2211d1aa4fbb367683", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98394dd7f9a59ab44f9c280c4ed7086926", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98172ce03e6ff396b02d54e4e00d6bdc40", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989989999e9ebd00d0933269b9072eae8b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98097d560f2f7418f10cc49b6f2b38c875", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841d4e26d97d489677ce96d066f2c3e5e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982eeba155cb91cc82ebb94bd58f3ad38e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801a985ce770342381a5b86e18d3afd6e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0c3dfbf8ba791b48942e903b2e8ec9b", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821dd3a14951be5a7e371496e201efb97", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989975d8e9d98558d85f41cb60152cad92", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfa9c0167355fbba3da759e4f238d08e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98895f6be4a5a4046e6141f141d5665fec", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dba6392f7976ef279651c9283b720881", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e37be2b54d616193808e7a6877a4095", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e5f58c87ea59760134046506eeb555e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98beda8f635a682dd1c45b7632eff44286", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809b97eace0368aedc404cd004e98f92d", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98809c470ee7bbc2deb420d8b180735ab4", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983b930d127da068123d0a71bed204166e", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a4513dc21378a0985d2533879239c6d6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986e0a2424af48b2882cbd2b4fead713c0", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e46f6840241795c3d88f9112abc1d923", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b9d740cd536d90e79dac1c542476493", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9853abc62e4b32d92b16e022264d6a6f17", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98805a20f21a00f4690dadb05323e94c80", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d607f9ce23ccbc51d8224aeee405d81", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982c86b4b31f85283f955f08db332546b7", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98368f71a0d959d9169a6b217e7cbe46a6", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983b33d05bee3401d7043d51d7d4c29935", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cecd3214e67323f510abf61a92c12ed6", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f86a15579e3ea253b183bf236b788e5e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_cookie_manager-2.0.6/ios/Classes/SwiftWebviewCookieManagerPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987176d4b94705d4c62bcc8178a6618661", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_cookie_manager-2.0.6/ios/Classes/WebviewCookieManagerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8c86bac34f65778b464123f6ea0b8c9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_cookie_manager-2.0.6/ios/Classes/WebviewCookieManagerPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9854e9d12b80bb984e6f6b50b1fe874134", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd4386a638f5fc649b50822130bdeab4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981664203fb5a0343ee88e4c4e5add24bd", "name": "webview_cookie_manager", "path": "webview_cookie_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a20fffa456d21ee0eff28bf6458ef14", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98002f44501a66119fe02ba39d3b7ea035", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98601722cf9754ee9a8910ffd3db03faaa", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e37fafb4e63640e523f01595fd03d6aa", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98049b2aa37a3aa7a09927d7d9b63cdb12", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b0678733b72c7e56e28ad3d396d5ccb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaf3de011e541800a95fd217473969ee", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984620eac0d0fd2a6c9bd1b6233583c46e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9f418c486161dfeaecb76486d46623f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abca22a2483ee51f55d16b624dea6366", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9e2827ad3aaa2c14658f8fcb800fd1b", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_cookie_manager-2.0.6/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98da5660b838d623375084c26be1cccd0c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_cookie_manager-2.0.6/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f25c28a98c0e54477dc36f9b7c326dc5", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_cookie_manager-2.0.6/ios/webview_cookie_manager.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f0e707ea463418e4231c972dcb94d5b4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9897ea052339ea4a34acb7442f38d50304", "path": "webview_cookie_manager.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc262daaacb19f4e7ebf2b01c742d3a8", "path": "webview_cookie_manager-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982decffc8807314b4dee267fa10c1ebf2", "path": "webview_cookie_manager-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866bd4539379a4447e6f5182b3535c1a5", "path": "webview_cookie_manager-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ecb33acfaef85acc37c78c0f6aeedf0", "path": "webview_cookie_manager-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ad7df0d4e1b8954cff2c8ba1c4b05c75", "path": "webview_cookie_manager.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989a38ea21cfbc6e8586e67739c94b37ac", "path": "webview_cookie_manager.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c6181aa4c16631e3ed320a503dde23ae", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_cookie_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c27ec57bd18e843583a113c6cb783b32", "name": "webview_cookie_manager", "path": "../.symlinks/plugins/webview_cookie_manager/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984104cde65c1795a747d2d75522618708", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FLTWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b403ab93fb54648c1844cf9303efe29a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FLTWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981867283f415acf4566c1e43d538b12f2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFDataConverters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc176a6a5efbb5ee7b498a987d9459e9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFDataConverters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6eeae3a3077cb849d934826dd6bbe4d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFGeneratedWebKitApis.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982126cb36a00ce5013de80d33d7086e8b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFGeneratedWebKitApis.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ef3334f1df54f391aa10e382bbf5b4f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFHTTPCookieStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981072acd13a72ed4edea92b883660f789", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFHTTPCookieStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837d5075fd244823e35936e027f8e07e2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFInstanceManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98282442d8fd6c8444bcf17d2c9cecb555", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFInstanceManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98106dc6f461b1576f2fe0dd866c0bd7b8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFInstanceManager_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f05e4473d39fe36a426c2ab925f7b6c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFNavigationDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc7e10e8006d17669080bb91c0982f95", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFNavigationDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872ba70815de5e88d745ddeadb395b3c2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFObjectHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988a5a7ebc498132a7c7263cd859ee2369", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFObjectHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808fcbfff9f2dad48e8e7c3f87e39b32c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFPreferencesHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98471ad571ee5f958c36c662f29cb67767", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFPreferencesHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c206ac8f596c48f9187b9a6d329c6063", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFScriptMessageHandlerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1506e433ff56d962ea576c785a8d95c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFScriptMessageHandlerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855caf15d7d502632ee98c0e84b124a26", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFScrollViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca333a2925e53add60bdaf5fbc9128da", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFScrollViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee1671b1b8b6947ec2dd7f60f32296c2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFUIDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876aa47e69f672255f8e77323a90547fd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFUIDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a23f5e23a4eb32efafb44683497d5c0d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFUIViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9810b16b7d78336f153023c9992c5e8395", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFUIViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b833e76d4b8a3049202a5389ddbc49ec", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFUserContentControllerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f6e6bcf6140093c0f6b410b95ea9329", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFUserContentControllerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856390b04118457bcd7a3f1ea3ca3ff32", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFWebsiteDataStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcb4b0f49fb006b8dfa860bfc9cafcf7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFWebsiteDataStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811c591146da254234e98e4d01ac5ae10", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFWebViewConfigurationHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9bcd0e7e47d07c5d6d94d4c3751ba1d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFWebViewConfigurationHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d967210b2e347ae31fe8d4a149dc842", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFWebViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f2aa27cd60013f2aea0c51267062809c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FWFWebViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7f3d913c9630221915b3f99507cdcb9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/webview-umbrella.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b7c3863d199ab542ccea7a58170aa454", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986370313fc51be05d6d848aae5a24eb74", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898a90448b0e077e73e5738ef81ec7192", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab575103d56a005cb3b021816dc66eea", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d2fef237006a1e0f035d18d986db68a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f25e2dce84a20c13fdd00c0d3a819694", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af0532d6280b087d9413d347acb66525", "name": "core-tamobileapp", "path": "core-tamobileapp", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae45390a93c5bb61bfe74b6413ad53a4", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f2a8cd0b92deaf4c13d87ac4f70218a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f26f52c14c9e47243715a669edcd2d18", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b69c594cc10eaf30a200046ef736d75", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98380e1a48bd2739c1b28944a766206152", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5464bb0c8c7a3500597f3825cb9cf5c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872e6c291a9e910ef1b83aba07c82b2ee", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9881553ea087a6bab248f1d33d74ee1065", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/Classes/FlutterWebView.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98626540b246d5eb2aec931db55aab2a77", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98218cf6f420dea10a6f6079af1d11735f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/ios/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f84f99e5751b12a3b7c7a8fc912ad49c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b7ee3e6c5679d3d8e711172db294ac71", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e01fd11ffd557e4dd8caa90533997d4d", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9809eb0ecf826c6b567c5f7c4eef7c18b0", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a6a01c5429c64ebd5833a58d780a6306", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980573bbfece4afb1bf5a76ddd765311d0", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988be877de4ab86d4e80aedfa4cbf8ae32", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9830361a196328097cbd568e4a26a69403", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840ac31083c467577320e626dd5a1621f", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7ed8907a0e18f01a69180279a7204aa", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e981e734e2f21f8b53bfcfc90a84d85fe3e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98c821778e0140b12be669d582cafeb4af", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b6af358f530ce0bb42747af25a14e6e4", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98737d069c43d1c38530065febc0969ac6", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98efe88d914a31660f0a3cdfa55dbb3135", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98dd45b5219c765ecf2ff23ba4264e5cab", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e989f0d9556536b9a1c4c34f63a34277dbb", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9845a9446d498987610bc1fbd7d2de2a63", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818a9377e250b9d121296a9203d9c1d90", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848fdcf39af7f4ff915efa47a7648ecfc", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupCellItemProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b1ddbe2ede86301046dd23eb46c8a2d", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailBaseCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9892ad6699fd07a6451ce3260e6be489f7", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailCameraCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9865ed671f4a5023ff1ab21311a38b2803", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailImageCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9814509eba36aedaa94035c4ff4ea9d501", "path": "Sources/DKImagePickerController/View/DKAssetGroupDetailVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd2cef52895219cd19030ca7c29a9523", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailVideoCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b2141c932eb009ca23fc077bf2933dd6", "path": "Sources/DKImagePickerController/View/DKAssetGroupGridLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c50cd23de751aa3da57750295fb58d2a", "path": "Sources/DKImagePickerController/View/DKAssetGroupListVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f4816d56db35d95b3dd6e2727256d8ee", "path": "Sources/DKImagePickerController/DKImageAssetExporter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982153ef8d2f0c0271c93017e50cdec43a", "path": "Sources/DKImagePickerController/DKImageExtensionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981a53c6adfa7a3a994ebbd4850add3c76", "path": "Sources/DKImagePickerController/DKImagePickerController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98581422b7e24d2c6373dd747069b77dda", "path": "Sources/DKImagePickerController/DKImagePickerControllerBaseUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df3c6da0e6c6a2e0ccbefe9bcea405fa", "path": "Sources/DKImagePickerController/View/DKPermissionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9854e2ffef50ba2b261212e5ee9ccd777e", "path": "Sources/DKImagePickerController/DKPopoverViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98823975d41194ee06b88d2ce701b90873", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98106137453875fe76fd7387a3c6c9a6e8", "path": "Sources/DKImageDataManager/Model/DKAsset.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832de54aee3fafd5a8e6320c160d0b683", "path": "Sources/DKImageDataManager/Model/DKAsset+Export.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9dcd2936c06460fa2b353f69e8513da", "path": "Sources/DKImageDataManager/Model/DKAsset+Fetch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848b9b1bfe88b2cb011bba21eb06269c2", "path": "Sources/DKImageDataManager/Model/DKAssetGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989bad45e942244847f6cd4facf7e523dd", "path": "Sources/DKImageDataManager/DKImageBaseManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98454e440a4b087a4c0d090812abfe0f3e", "path": "Sources/DKImageDataManager/DKImageDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98203aecfd686e48983d3cd539e8705aaa", "path": "Sources/DKImageDataManager/DKImageGroupDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983fe8b81b9a6f8244644aa920ff65f65d", "name": "ImageDataManager", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d8d27c283059c1e8496180d323d67aec", "path": "Sources/Extensions/DKImageExtensionGallery.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e1d6c4b3a8726a942715dd2c901fbb3a", "name": "PhotoGallery", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9892806feb6c3b0eab774359e31c86b332", "path": "Sources/DKImagePickerController/Resource/DKImagePickerControllerResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ad972f2e3c063da1fb2a751c39a8ee5d", "path": "Sources/DKImagePickerController/Resource/Resources/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98367d5f42c788fe02bf281c1274c2821c", "path": "Sources/DKImagePickerController/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e7cc2de13f71b814064fae689794230b", "path": "Sources/DKImagePickerController/Resource/Resources/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9817a05beb68fae536a1808776eca154ed", "path": "Sources/DKImagePickerController/Resource/Resources/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9896846096d9608a0ecac1f070786eb8a5", "path": "Sources/DKImagePickerController/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98fbb0e4f8d7e09d37e5fba8310e5233c4", "path": "Sources/DKImagePickerController/Resource/Resources/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980755197ea121de47c77334ac040b3790", "path": "Sources/DKImagePickerController/Resource/Resources/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a0ebed5b3fb0ef1af351024f975b50bc", "path": "Sources/DKImagePickerController/Resource/Resources/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e9801cd378138086d82804bd990eed323c1", "path": "Sources/DKImagePickerController/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b7af2fddbb7f958e85b48e03581e3484", "path": "Sources/DKImagePickerController/Resource/Resources/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9873526fd3e455e7ef49748039ba45f2ef", "path": "Sources/DKImagePickerController/Resource/Resources/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98623a20d3e4039a747e582b8b229a9a87", "path": "Sources/DKImagePickerController/Resource/Resources/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a46be5ea03e68f187de35bdc6831f9f7", "path": "Sources/DKImagePickerController/Resource/Resources/nb-NO.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d0110e63990b03e82b9cb043cbeae774", "path": "Sources/DKImagePickerController/Resource/Resources/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98968bf9d31ec56a3a8b2521be490d24cc", "path": "Sources/DKImagePickerController/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9886e4be31441430e4ada8de7cb1ef5dff", "path": "Sources/DKImagePickerController/Resource/Resources/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983251e033340a6622bd52298bd81ff8f0", "path": "Sources/DKImagePickerController/Resource/Resources/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98cefe369a4b3921a3f967328f1ba935fd", "path": "Sources/DKImagePickerController/Resource/Resources/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98bc4766e5998f246534daf9cd00f8cb00", "path": "Sources/DKImagePickerController/Resource/Resources/ur.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9885342dbc2952bc5fa582010e98d8445a", "path": "Sources/DKImagePickerController/Resource/Resources/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98fe35c733977bbb8684a21beeb13db39c", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e988fb775c3680f4d18689f667d6e275978", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hant.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9811f21ba71d10a9ea8271a1fa7cee3540", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea1ef7a0cda500743e90bbea981dc627", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a30ee5f8435a3baf6be82d0f9610fc62", "path": "DKImagePickerController.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981cb03b9f4a226619a63aa39a5864e9af", "path": "DKImagePickerController-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98654df834fa64322220fb44a2ed833080", "path": "DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98463e2e7b90ef086d6f8efa07db66c673", "path": "DKImagePickerController-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c652da181163c37269c2c90232a0e7f", "path": "DKImagePickerController-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9893e289850434a92aeefc97799eef0066", "path": "DKImagePickerController.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ae387dc2ffa6ca7feafc12b90fc121db", "path": "DKImagePickerController.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98323d85c74b47bd66826434942e11637a", "path": "ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d467796bf8f2270eddaec9c22a8a5db8", "name": "Support Files", "path": "../Target Support Files/DKImagePickerController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98152cc8a6993abd4d057224a396607b8a", "name": "DKImagePickerController", "path": "DKImagePickerController", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98451d8ae3428c6684f7d3dd469123e7ce", "path": "DKPhotoGallery/DKPhotoGallery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986d94a6a7ef0b5a836ff3827a1238f1ec", "path": "DKPhotoGallery/DKPhotoGalleryContentVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b9bc425d767dfdbae62aa69d00a653f", "path": "DKPhotoGallery/Transition/DKPhotoGalleryInteractiveTransition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f952b82a98085a07a23601dcbf9bfaf2", "path": "DKPhotoGallery/DKPhotoGalleryScrollView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870da69b0232d73c3b74a570ff30c8326", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860078ff4ba9d7865cddbeb14b3755704", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionDismiss.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eb704545a2ce16d122039b1500059852", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionPresent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98701988e2feaacb8b83eb10656924d54a", "path": "DKPhotoGallery/DKPhotoIncrementalIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9830c675afa3cbfb4c78dbc1cc20bbba5c", "path": "DKPhotoGallery/DKPhotoPreviewFactory.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980ee6d061c70dc7fe56ba1eb7a8561f53", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9818ffec39455c314b2f3e7beeb5a693cf", "path": "DKPhotoGallery/DKPhotoGalleryItem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ac8ae527ad6e930c347feb1b4e28a8ca", "name": "Model", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f43d2e6fd60b2bb4534493d8ac77e78e", "path": "DKPhotoGallery/Preview/PDFPreview/DKPDFView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eda5062b89ee1c5611f9e3d6ef284ba0", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoBaseImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ae806c02799c721cce1254dd439f2613", "path": "DKPhotoGallery/Preview/DKPhotoBasePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98abad59ec87311035c600a7526bf3a746", "path": "DKPhotoGallery/Preview/DKPhotoContentAnimationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4e5c44ea5ab2c25d470a784f37131e4", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageDownloader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985293a5cd69c0ae455eb8452ffa2db3c4", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b066cf419c3853ab2c71de19b0765306", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageUtility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884ff816893cf12fb91221ed83a87fcf9", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9814b2c932c984cfd070c592daedf39657", "path": "DKPhotoGallery/Preview/PDFPreview/DKPhotoPDFPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dd93ce744c11fd4942eddc3b7c5ebb2b", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPhotoPlayerPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832476b5103984fd05427264db0f71a82", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c617c1763800cc84250ac783c5ea36ac", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicatorProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f129e4d06159cde3ddc9b33d5371bf0", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoQRCodeResultVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9868e7f84ee383c3e5315da4dd3f655a30", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoWebVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98347b13753e835e308beb2c8b2891f8ea", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d3061b3af5aaa3d78b165e20e27c15a", "name": "Preview", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9875b80bc26c90991a61f132bbad0da7b0", "path": "DKPhotoGallery/Resource/DKPhotoGalleryResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982a1629ba6273b8bc3d6b67340d4a9b80", "path": "DKPhotoGallery/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98382fa4682d57176b34e0ad2034b656ff", "path": "DKPhotoGallery/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e98e8075da2ec1118758b36ac6598a7524c", "path": "DKPhotoGallery/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9825e76a292b27d40fb697a93e89437d95", "path": "DKPhotoGallery/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b569becc67e5b96b7ba36d5a5a63502f", "path": "DKPhotoGallery/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c27a231fb1f61f40f9f23196cecb196", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98190b361d5227b6d8e8588c430d975333", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980b6c5e215c967faf6616597fc46ba2b0", "path": "DKPhotoGallery.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3d33e85b6072b30dcad9f7de2d17a1d", "path": "DKPhotoGallery-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9889887a83f0d286150350617c0021d86d", "path": "DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862066d248270b4c5ebcb616ce3dbe7d9", "path": "DKPhotoGallery-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851faa29213dac0f701d68b807a0648d4", "path": "DKPhotoGallery-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988a5026ae05d33b7d0696f3f30dec1f88", "path": "DKPhotoGallery.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cb2120cb900d8af4bab1bab6ac99e9d5", "path": "DKPhotoGallery.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987a57710f7b52430b98aaa5a88b893920", "path": "ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98577df05d100207c035a25e404a9d770f", "name": "Support Files", "path": "../Target Support Files/DKPhotoGallery", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b05dd939fad88722422a339b975cf2", "name": "DKPhotoGallery", "path": "DKPhotoGallery", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de27b6f9c7f7efe46ddf2332f519b133", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/CartesianPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f20f5607810adf680ca9ba1b20e9b2fb", "path": "Sources/GoogleMapsUtils/GeometryUtils/CLLocationCoordinate2D+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ccaad780b24e3256b0d2f4eadfda5a56", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846fcbb70152a3769a452e4b7182daf5f", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984c18e2904e3979d7d336fbc0ed86f499", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPath+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9847cdf23495acff3b481cbdaaf2f10b80", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolygon+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cd4859abb658bcc5a337d217464e285f", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolyline+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821aedfe05149ada30dd3d4486ac3c8dc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881c0c0f2e2f28ce95395b978a81e1d88", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b69a7fe80df38e928490f9e9fdb42515", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98754ca059e3ba11b90569e53b54483265", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981941f26961400e9e4c38ce7d0cb2376a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988a297c98f534c79f9945aa3710ddf973", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9891bb05fb1d56d331cbdaee107f03b3e8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840bfb5aeaa2b387803da0eed03945eaa", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9823483f3f1ca805dca33dbcfca192a920", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987338a0cbd18d94d3c6b28cceadcbfc5a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856ee3d3835b2ecf3d5be20c00448d77d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ad6c27f9d7c749b456e2d72565b173d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4d82c8fb8fa25e6208310bee819ecc2", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee6d8ee842d002f689181903bc02f763", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982222351d28b72db291bb089f2b1379ee", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98461a734d3cb816e10973354378a0a569", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7e124fa3a2d927f787048972c246977", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e71a73c122d0430db35f4e3d55abde8d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98813a93b30fa3033f583bd23532d4bf79", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985cca8c86247d579b5423715b8e0b0f98", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98faee4eda9ed211e5eaa6ec852308c038", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980bfa4e0f5cfcf38940fea68c9d185fc0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1db4c8c75311fd7bd437457ae224bfc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986075c1d00faa18115ddc2e2208ec9b93", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbd1bae51a63772f1a5d91ccaa84bc0e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864976b536e869f277e1af0c96dc90f1c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b70e6a08b0d02c8d823136b6005386d1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ac44c1441a63c352cdec8b0d3a23d05", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc0227ad031af4a72abd7e861530243a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980588247775d9a4646c6e57c49ea65d98", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c9b1f1eb64bef0de9a32319d221ba08", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd3f49db707cd8afe0a6a76bbf488fa8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc2f85c85c24c8ac74208f7614c05f2f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829ed2714f9f3fa8e4a8654c70585e529", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e07ce560fe7d48de33d4e9063b90a4b6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5c58a56d6ece0001a43b0610d1a859a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7c895eca1ee345721c5a24bd4dd7e8e", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d17f7e8fbec294fdd1f49d2d8ee55170", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989546511f711340266ba21716891ab1f8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUMarkerClustering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ad92a9c0e1faf9f3e02ca54a0f8239f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f9dbf59139fa55f92f6d546843f1060", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818d4ca9059fb4b6f67b907548c0a5ea1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4b9e7c30d6fe3840a56e2ac954883a9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a047671b54c38cbf7c000a008bf7e92", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891fb7abd7000ce6965e6188f2f6ea71b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883a89dcd781c0e839dfb398f5acc7e1f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f67ea5889212e50f5c730314832ab09", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a6bc6065f3a35ea649279eddd73ccd7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986cd1c77e15102d52b43452d32615681a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b8013fa4819afaa716746fd9dad5849", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d629603e18fda8c34ffaa1b970001d8b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b72a9d5cd14397f294047b2ba27e21c0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b622a0a4e9032e853b6d43438fc1605", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98993ca1332f089bb5916aa79503250967", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e2a80aa25968359543ff3428c194427", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4e2e6cb14f506503d6b50988529f863", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822268a09bf7c33c3f807b3f85c7ebf85", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98efd560d0fb70a475f7464a6a721cd44a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb43a64d1425e1712e129ff6072a9bba", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98378d5faf1cc61ebdb80a20840fa91e8e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ff83f0fb6a1fe367a73c844646d34d9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a0b77cfe0acfd9b8eacd1e7179ce7d8", "path": "Sources/GoogleMapsUtilsObjC/include/GoogleMapsUtils-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f75be32d299303ad3025a6eb7ced2519", "path": "Sources/GoogleMapsUtilsObjC/include/GQTBounds.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98546d1c2ec9cb311f4db552a063fb1c63", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872c2283735c890e79efa56ba098bf968", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98112be392dc8f488f5ded9cc5a93cac0b", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981558679e057524ed738c17168eaedecd", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7ef7d3d89621dcc0b0be4aa0904606e", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981281fc4dcd326a3876f99a19f473fd6e", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc3d6cccae404b53c7eb2590eac61710", "path": "Sources/GoogleMapsUtils/Heatmap/HeatmapInterpolationPoints.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a43255427ee944b71a6a7b0e2682622e", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/LatLngRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887d1177ecb368d212df0ef6c3138ac4b", "path": "Sources/GoogleMapsUtils/GeometryUtils/MapPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98138401ef65ba006ba93db93178640eda", "path": "Sources/GoogleMapsUtils/GeometryUtils/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98adedd47068b8aca8641ba275f5e4e12f", "path": "Sources/GoogleMapsUtils/Helper/MockMapView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984cb42269b80f806f98f648e18cc395c1", "path": "Google-Maps-iOS-Utils.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae160e59cbdb8a634e4425de12bf42bd", "path": "Google-Maps-iOS-Utils-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b71eb97f39309441ead9155a86e0282d", "path": "Google-Maps-iOS-Utils-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b357217e9de96d9df67039ea3b4c2a6e", "path": "Google-Maps-iOS-Utils-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980811bf4f6a9d87c47dea83d7c863acfe", "path": "Google-Maps-iOS-Utils-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d4ca355e57314aa28966a004542eb644", "path": "Google-Maps-iOS-Utils.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98acceee9107b984f22b3f27abd3f4eea4", "path": "Google-Maps-iOS-Utils.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a5253c8855aefd28396f83e6d6b82f5", "name": "Support Files", "path": "../Target Support Files/Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc7c847d56ae46dd65c15330262a1a14", "name": "Google-Maps-iOS-Utils", "path": "Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838a340857ba50d424d1e98d11e9f4f3b", "path": "Maps/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98d9fae3932fc18ef12247a9e942fecf6c", "path": "Maps/Frameworks/GoogleMaps.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a9bff40a622132300e685229c1875480", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.plug-in", "guid": "bfdfe7dc352907fc980b868725387e98b4442b32e684597f56f10ce052cd86fa", "path": "Maps/Resources/GoogleMapsResources/GoogleMaps.bundle", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d02a137f502f2f34ed9afb0369cca4", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af2eb76af563de2e676043b117e384c3", "name": "Maps", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e984bae5490aa814d2f27e0da1c9c747d12", "path": "GoogleMaps-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f66a927bd5ad5da1355bb717ba567986", "path": "GoogleMaps.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e8054a1a3491d30fd2460c58ee768deb", "path": "GoogleMaps.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988c56eb30597093d8f38277a54b8ceaec", "path": "ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c7284356371515f2118ca9889fc02e5", "name": "Support Files", "path": "../Target Support Files/GoogleMaps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833bc29157bbd72cc699039665aba1461", "name": "GoogleMaps", "path": "GoogleMaps", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e0c39b04b4a988c003b378980ab5fa4", "path": "Classes/ios/Scanners/MTBBarcodeScanner.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e89d6c699439e49e313da89e9e34c9e5", "path": "Classes/ios/Scanners/MTBBarcodeScanner.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984f948e252a518c3beefc3e4e0739c158", "path": "MTBBarcodeScanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98768ad082a9c7bd4e0a48e9191fa64af6", "path": "MTBBarcodeScanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98221cc360ad953f4b85602e74132785a4", "path": "MTBBarcodeScanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c9ab92b5af6634de0ee9a5104fc4df3", "path": "MTBBarcodeScanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983290f9c591c824fc540645a8c27a495f", "path": "MTBBarcodeScanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c8edd96b8836a7279c1812c6ebca1e20", "path": "MTBBarcodeScanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980b164ae66dd4331895036a994e49372f", "path": "MTBBarcodeScanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad495f205c9fb0fd0202a00349280659", "name": "Support Files", "path": "../Target Support Files/MTBBarcodeScanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983417f498bda964fc920fb468571b174c", "name": "MTBBarcodeScanner", "path": "MTBBarcodeScanner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b52239f94363d5ef22814892ef4976cb", "path": "Sources/OrderedSet.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982b3a344464e535a716e75df117b260b4", "path": "Framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982bb92972ab6d720ed5843bca0c596940", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980f73dbf6605b32b45083eb68f9698992", "path": "OrderedSet.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9867a26d99fe935703e0d0cf8bdff7677a", "path": "OrderedSet-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98512f33633548228a1326fc4300c02100", "path": "OrderedSet-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdeb14aa6e48ee68307a3173c7ad3333", "path": "OrderedSet-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e9abc889a692bfba17479f8f5990fb3", "path": "OrderedSet-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b38be3c8b8f7a07a9d37170231e6dbfe", "path": "OrderedSet.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982ba6e7b1dea4b0b9dce4e5b6952311f4", "path": "OrderedSet.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98686dde0bb76099a647e64ad01ff23a09", "path": "ResourceBundle-OrderedSet_privacy-OrderedSet-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982f2000b6b5187d40f5d4bb2dcae4f0d7", "name": "Support Files", "path": "../Target Support Files/OrderedSet", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed20036e971706286a75bc32015e4448", "name": "OrderedSet", "path": "OrderedSet", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854d00b8b043e8d6947f5779388430529", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ccec51ed09ec50c8ac6c66c1ea92b58e", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98839a79f14e504223ea2f801d5723244f", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b58313d184d50999fd89dc2fc7c13b15", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fb9b9f96376291df705c35ccac3eec5", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c148b55898b7137ff8622ec34f36c420", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f61eefd322b45a28b0f25c869eb6451d", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985fce18ec1edbb77850fac5bb4e9713c8", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d59234e9479b468332e01c9b89ae2700", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7b970028422cf22deaf23ec26f61057", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983afadd2f892d14237e0cbe20dd7e266d", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a8370b14e319859db1af417fd0d6c5e4", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98191192c2114ee514dbd5057426e335eb", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ea7de7aba67ce7d082b6fa062df22de", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b87d714a6c9a58f4b9685a89a1e5a49", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98230bf312512cee16f7978e3def86ffcd", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4a4c0b2e9f221c31077f5b70763b4ce", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986159bb2a1e43a377716e28eec3292d88", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a965173c754f09ea0c4ead477b4dc8e", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd1b2f2e9ca4638f99950b7975246ce7", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fad2d4832c5e7855e6677e260ac62dba", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ddddeb28a9fe6829c605ad963fa920ca", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf5f8f8f0b42b3e3cbcdb793597c8262", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98644f0b3d74d6c99bf80de0d5c3f0f89c", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f937d2bf9c2dad125e0a8a2960797970", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b9f6216225cd0689fdc5be7b7c6a042", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7b536ab4d8239cadeb1ac5c6dc63abc", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809dabe4bcb5b50346a5cebd87d0d3e68", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c0d7076299a97aaea35f3587730a9c5", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d4178b1b295911d3346c7f7d9d0b384c", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c48a032c77b60bc2e87e5818b9fca347", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9852f8a899d4e3375e66d5ba1441bf7529", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98130d57556d2541a95fd678ae424a7252", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9896f801e931e1bb2b59fd81134f92c158", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989147ed3d0756fbd7ade874bd65505506", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c05c165bbf7a07160a0efc380444e28b", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986900ea66dc69b66d85234a05618e62df", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d18a9dd50c3eec46a20baaa69fe58aa7", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98126b8ae343b0c921698921c32dd2f929", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98204526a1cb0c81e18b3276315fe37cd1", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6d73e217edffc805209cd26f890f1b3", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8e03e2bc6d83588e26de436f1c4c7a3", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984337c60f9eae2b6634e20ec007cb6df0", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e574096585e34750ecaca481289c2c86", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d913775eff5a5dca59c9b1879df63f8", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d570ef00dd2db55218eb0f1e6691ec26", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cc0bae6906db39d4383fe59847a5a26", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887175a5d40c0ca93aa05511aa087cb1a", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d747d0c64489d1baf2added8b75b415", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec2faa9653b07d7fdab511a42832bf5b", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98963bf4403360b3ce49a893099557a9d4", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890e2f3ba009123b419e44967c5178d41", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989acfe32778f711f41712d85d78215d7a", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98538f71e8114848291e74e2f380e09811", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a2bc8002301f2a6feb44f96583f349f", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b96d718bbf0d08c84eeaf02fb48d2bf6", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c15be1df969439df6465ea27e915998e", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98938cb1f21bfe204794db79d63881c4e3", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822dea8c3ee7e008be957b684961d5b98", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b4f50ba867a1dd0acb4b793c6b510059", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983acb4754fca86fac5d8d0cc565a35d69", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c277236406abeba895a166db65f76f3c", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802af55a02768ab4393b0d2b9c681e994", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d38b7339d1dad44f148059d2eab139ad", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a94cb64e97cd416f5ac1c58027fdc8fc", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c1cbcf4c8acc2a7f4557d4a6099ffb26", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f95b91c7d14eaa046aeda4a454afa70", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c74a9c0cfbb5196fc432f5df26e4180d", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98894eb142b6990cd75c5bfd6aa1f91a28", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984196dfb17ed2b36c69cca908997903a0", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825e39498af543d0483ae28f8cdd67ac1", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e59768e9f9f79956312526b3416f2132", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804fef9b806fa56009af499c078172e9d", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98819f355350b2e21cf4cf6f198913ceb3", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891a3482d44dc0c12fa0beac4af981e62", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98754cab5a7ce5f2b2c73e4f86e5ae076e", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d20dafeab1563f4cbd32619d59333c9", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98795d93bd56a67ea7ff6a044b1e4e2637", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980de9d3f543e3f271e59e281f325cc1ab", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a6057ac782830d5dd18310ea66cd85b", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811570ae79320b939b42fc63625a22e92", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd08e44b6d291a66e3eadd60a75db46e", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c648daede6985e124e2d7c1a4a410ff", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac9ced75f56f574fbd59bddc892016d4", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887045239860a0c21701a5ee2c31bea0f", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdbc6697605a479a45abd7774f9fe48c", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3daa44e03b867fdc4961ac28fbc6429", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837600a554e0c196c0e667d8fcf33dd2e", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba41907c7372371c4f2c1c72a886e3f9", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f0008e822d6c92fd568d34ff8e9b176", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eff91a485fb91e04e60d528129523dbf", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830e007de95bd37957ddb5d6c24e64e56", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815eff7bf263094f682eff3687e18d569", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832dbed1a9eeceaea0885d7014c7f567f", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989470ff3637017ae192c8a49daa4045be", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98731440e20857032b66f4181e7a8b835e", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac1548909440338fbfa8cc8f8ff138b6", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d665287f2875f16fc3e32947391ef6e6", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a5d9d70ecaaadefb8b7603e6b42b640", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984077be7f3a33aac0cd7faa616479872f", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c659b2add19aecc4ec054b442460f55", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984fcfcf2368800b4a4e12e271adbc63b7", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853a4e162c6a66e6721f6c021d2dc06ec", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987db02129591d2bcd135ef99ee5626933", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ffd16fd272eeecf6b148f81a6959c21b", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b962ec1f29169364890ede112e7cbf57", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2ee19f4fb37ecac022efd4b9fc1e9c7", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986820bdd8b662b225f99e5c127a393cfd", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c7a83c951c5cbc98bfc249e02398715", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e58b5d0d2237b8d1385427274c1e2599", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809c9cf8fa3da54948040086657debe3f", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98661abffe6f47cf2bfc1e557932a60ba5", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802f1dcfa78bd80781f16cb4615206bb0", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aef9c5f0780a77cf0369077158011a96", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7a97fcf82840582919ca9f6831ed3c6", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcbc703ac7cc2e6b289863875956b496", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e87d3b01461d060ec40583ea2bd353c3", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f633dc552ead769ea66ab5fca0418d8", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866733bc2b64daf087cbeb56a625ffbfa", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809fd7b999e393388e379b1d5aaabb07c", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d32fcca8553ff363aa7d78efc02371a6", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98caf5cbe42c973bedcd0ab3190a1ab4b0", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826993db7a6eb23155665da65bc44e87e", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e789b4a5cbb9c29e46b2ced7dcd644a", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b978ade36c10d791e1f6dc03cbd5ca37", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1051e384d502f94af487b893239afbc", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813c7e56b0f1235f2c8c105fab226b17a", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9852fbdebfe2ee2e796b9ca3e0a4f1120f", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b387489cece979d8cab5a90126c2602", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ab09de48a6a68601eb6bcb7cf940484c", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6c7139e008792a9b4028a20801f6811", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f6c745e4a63e419cfe04f1bd3dd14ba", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839131fecef2529af92cbb556cacba580", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fd81f7813a94e6d7e6222eb4cb9e067", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a59f36f0a05c32572dfc28d35eb33ea", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981819872e3e64c6cd8d1ea385310d75a3", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e44b64e3058d198995b0815d8f2b756", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d46069be0017be1547dc99d7afd2bdf5", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0e600422d4eacd242cb050925aecff0", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98613e7db1a8a4ffacc2b4d338c888f874", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987eda8b01567cdcc95b2fd128ff59b323", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b8274f1c5b11e9b22f9175184b30928f", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e03a9955e098b622769241d59340917", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b8368b4f84c7949cbf296a603a37140", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de162e90ca3c100225f4778f7dd0ca92", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ba1d7e767c341cc332cb7125b956a34", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c81bc364106e42fc06d7671335cef411", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe66c7f05430d365d6e6d6e5e9da44e7", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982c6a9301a22e94a98417a413411f9843", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e02007cdf85b19cda2227f6166789f41", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a065693481d9596cfb04d188057bf0b", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a1c87f9bb3f67f4657f8f9c3ad14a15f", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981b09992bfbb66be1b4c9a94a23733443", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0af303477785e45c4969773bbcd1a49", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb50378cfd3c8c4009ed774367923c3b", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a5479517659dd2795f7994875b53b75", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879fcd05a2459c3cca09b2668b20d6b2b", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983b1e853d0cf5cbb02112c5768dadd8e1", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b17961e6d1e43cce4d7e4bc602390329", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9834cca705d2a86b08098f7910cacba9e2", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987763ea2535f1edfef2bc1978e080628c", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab84768027bd5ce45889582068886a41", "path": "SwiftyGif/NSImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c5e58b7c7fd0de067f26d00cc8333387", "path": "SwiftyGif/NSImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980fa45e3e6729bf23ec7c4798735feebf", "path": "SwiftyGif/ObjcAssociatedWeakObject.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9591c365218f037b4aa4ed2be3d91fb", "path": "SwiftyGif/SwiftyGif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98988411e5438d05fbac257abc923bfb2a", "path": "SwiftyGif/SwiftyGifManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98460c5069a4c1fdfe1473eef7fde605f9", "path": "SwiftyGif/UIImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985f866666be1c2c8b6b9821ceb6838cb3", "path": "SwiftyGif/UIImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9885a87e3242586396ba1f9d34cafc4054", "path": "SwiftyGif/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fbef5b50eabaaa0221a180a6cacc1c07", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98990245af76c67830c5a63802567b6258", "path": "ResourceBundle-SwiftyGif-SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98baae6c0d4589ec52d4b386bd2f0b016c", "path": "SwiftyGif.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881cd0c28a0466b9042a5cc8f852aabed", "path": "SwiftyGif-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9808e6ce69f2228fd2a934c2a27d6f1350", "path": "SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c623d13dd0dde6c084538ba2a6f4282b", "path": "SwiftyGif-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8a3c4bf938214ebc38946fb12f69be8", "path": "SwiftyGif-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989df1efc902039891e0487cc2313b8690", "path": "SwiftyGif.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982a6de790e323a4961905727eabe589bf", "path": "SwiftyGif.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9805c30d7bf4a0f043e46355e54afe608a", "name": "Support Files", "path": "../Target Support Files/SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f46164594ccdb18fc36600334f42a3ea", "name": "SwiftyGif", "path": "SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1799f39cfbf29e7831aaac79b5f32bd", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98066f5af1cd691a625b724ef875f792fb", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98bc9b33687cf974a825db433d5a4ce66f", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987edd064bed57826d8d017ae149a3d52e", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987fb378bbcd6c5ed58c790a44f8de1ad8", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d314b0e0b0622c4b6805a741f2686226", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98c868b1c7c8b6f1d4f98ebdaeb296a675", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986fd78883b66d5d9077a96975628773ce", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e983a42af4f91479a2ff6a94702f9452725", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847c36cf1fe2165ccb62332bdeff26348", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985f6ec3891a0be6525111807007bbcf76", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988db807c74690161e03dc575ee7464945", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98778a51cd43710d278531fd4c4e5ed80f", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9863ac39dbb3c5e6697e1e483c96fdcf12", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d87df373aa945c7cffc56572b5b5c1d0", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/work/core-tamobileapp/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/work/core-tamobileapp/ios/Pods", "targets": ["TARGET@v11_hash=0716e31e1b9509c05e2e26c2300851aa", "TARGET@v11_hash=122f9b14ab41f8658954ca3980e7fa13", "TARGET@v11_hash=53c3bcb0c37c14488edda8b9c840a128", "TARGET@v11_hash=eb560e8f9a41e307aab7afcb4d0a0232", "TARGET@v11_hash=a6917a34fa1b49a28f52d6c98417928a", "TARGET@v11_hash=d53b0aa374c65106fbb61ea06a3bec0b", "TARGET@v11_hash=83b222196e7f2361a08193b9a2c41679", "TARGET@v11_hash=c043b736af11b2826b092fb51bbcf115", "TARGET@v11_hash=130c3c6c6bb9641476d10028a0de2dfb", "TARGET@v11_hash=d592cef1fbd126cf06f1c533a53bb07a", "TARGET@v11_hash=d52ea8a4593ee2e64c379fd68f0b6f63", "TARGET@v11_hash=dda7c13a14b61f506294ea7d92f53d45", "TARGET@v11_hash=b98b9ab1313f781a454ee557956e47e7", "TARGET@v11_hash=fea7f5971f8756f683d039de2b486e1f", "TARGET@v11_hash=95424f39ed16aebc891e89682a27a73e", "TARGET@v11_hash=83d74d7c524860b0bb7b587b356adb42", "TARGET@v11_hash=40880d9ded52d6cdb3d98eb1bbea0ae7", "TARGET@v11_hash=e65599715c8061caf2595134540221a3", "TARGET@v11_hash=e00038651bae71909a5c56e971b7e7e4", "TARGET@v11_hash=5d451d125d04fef34940e709b5977b31", "TARGET@v11_hash=ae88a4aaac63e36543f716fa94c11348", "TARGET@v11_hash=62f391731e352cc41b8318bd496c3b85", "TARGET@v11_hash=f3622ab608d1545f4f8487001ff9f0af", "TARGET@v11_hash=48666409e044f6fb07a913add7fb19f2", "TARGET@v11_hash=636c2b19d1da4fa1a3ba3beb868b367c", "TARGET@v11_hash=d204dce4e2de21da7e287508fbf82a57", "TARGET@v11_hash=eb415841e5dd29179444ef34311aedf2", "TARGET@v11_hash=9c43cbf9cf08feb9043b5cb5884b8ff2", "TARGET@v11_hash=a2cb67f5a548734639f3d5a7b9c3532c", "TARGET@v11_hash=5f967b64507e8142f03a962c56588435", "TARGET@v11_hash=acb7429e79ba1dcce20f2b8b1b949616", "TARGET@v11_hash=c0035083abac3d8d67a718a4da5a361d", "TARGET@v11_hash=edf8ec0eaba1796a7e6cfc1635bd1f47", "TARGET@v11_hash=c290b7ffb3939213540ab3a29a4d3661", "TARGET@v11_hash=02e75cc28ae89c49943624c40b3131f5", "TARGET@v11_hash=7b84cef2f79cfdabfd9de1e643298aff", "TARGET@v11_hash=35f2422a67cfa7a999bee79d97036023", "TARGET@v11_hash=1deb0e957e1730ebee997dca2d218552", "TARGET@v11_hash=3ac12375bb2cbc4f01a60ce48f5e00e0", "TARGET@v11_hash=ad18c5187ea9f2f699c267e13b4f9076", "TARGET@v11_hash=ba4acf4fd9e5b01307dc35e95d75c1c4", "TARGET@v11_hash=ea5e3fe0b44dadad4a142c29b716edef", "TARGET@v11_hash=f837cd6d764bff9a5865c27ccb0b15c7", "TARGET@v11_hash=cd22d52b99bfd7d6c6a91d33404a13f7", "TARGET@v11_hash=45e8e26ea5e744c8967a7800f71ef89e", "TARGET@v11_hash=0cabc48802bb055ab085c334007a9fc2"]}