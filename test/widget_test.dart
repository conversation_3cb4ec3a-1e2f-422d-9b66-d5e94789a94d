// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility that <PERSON>lut<PERSON> provides. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:tangoworkplace/main.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp());

    // Verify that our counter starts at 0.
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);

    // Tap the '+' icon and trigger a frame.
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();

    // Verify that our counter has incremented.
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });

  testWidgets('App bar title is displayed correctly',
      (WidgetTester tester) async {
    // Build the app
    await tester.pumpWidget(MyApp());

    // Verify app bar title exists
    expect(find.byType(AppBar), findsOneWidget);
    // Note: Replace 'Your App Title' with your actual app title
    expect(find.text('Your App Title'), findsOneWidget);
  });

  testWidgets('Widget parser renders text widgets correctly',
      (WidgetTester tester) async {
    // Build the app
    await tester.pumpWidget(MyApp());
    await tester.pumpAndSettle();

    // Find text widgets from the widget parser
    expect(find.text('HELLO THIS FIRST TEXT WIDGET'), findsOneWidget);
    expect(find.text('THIS SECOND TEXT WIDGET'), findsOneWidget);
  });

  testWidgets('Input fields are rendered and editable',
      (WidgetTester tester) async {
    // Build the app
    await tester.pumpWidget(MyApp());
    await tester.pumpAndSettle();

    // Find text input fields
    final textFieldFinder = find.byType(TextField);
    expect(textFieldFinder, findsWidgets);

    // Enter text in the first field
    await tester.enterText(textFieldFinder.first, 'New input text');
    await tester.pump();

    // Verify text was entered
    expect(find.text('New input text'), findsOneWidget);
  });
}
