# Tango Workplace

A Flutter-based mobile application for Tango workplace management.

## Overview

Tango Workplace is a comprehensive workplace management solution that provides various features including:

- Project management
- Space management and reservations
- Service requests
- Document management
- Meeting minutes and action items
- Map viewing and location services
- QR code scanning
- And more...

## Getting Started

### Prerequisites

- Flutter SDK (>=3.3.0 <4.0.0)
- Dart SDK
- Android Studio or VS Code with Flutter extensions

### Installation

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Connect a device or start an emulator
4. Run `flutter run` to start the application

## Features

- **Authentication**: Secure login with session management
- **Project Management**: Create, view, and manage projects
- **Space Management**: Search and manage spaces, make reservations
- **Service Requests**: Create and track service requests
- **Document Viewer**: View and manage documents
- **Maps Integration**: View maps and locate spaces
- **QR Code Scanner**: Scan QR codes for quick access to information

## Dependencies

The application uses several key packages:

- `get`: For state management and navigation
- `provider`: For state management
- `dio`: For HTTP requests
- `flutter_secure_storage`: For secure data storage
- `camera` and `image_picker`: For camera functionality
- `google_maps_flutter`: For maps integration
- `qr_code_scanner`: For QR code scanning
- And many more (see pubspec.yaml for complete list)

## Development

This project follows Flutter best practices and uses:

- GetX for state management and navigation
- Provider pattern for some components
- Material Design for UI components
- Custom widgets for consistent UI

## Contact

For help or questions about the application, please contact the development team.
