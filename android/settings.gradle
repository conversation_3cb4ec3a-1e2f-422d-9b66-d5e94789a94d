pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
        assert localPropertiesFile.exists(), "local.properties file not found"
        localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }
        def flutterSdk = properties.getProperty("flutter.sdk")
        assert flutterSdk != null, "flutter.sdk not set in local.properties"
        return flutterSdk
    }()
    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.2"
    id "com.android.application" version "8.7.2" apply false
    id "org.jetbrains.kotlin.android" version "2.0.20" apply false
}

include ':app'