<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- <PERSON><PERSON><PERSON> needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
   <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.CAMERA" /> 
    <meta-data android:name="com.google.android.geo.API_KEY"
               android:value="AIzaSyBVbCx9NEcWcEkTP6bTfnNr756mjWYlu_I"/>

</manifest>
