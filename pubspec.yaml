name: tangoworkplace
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 4.8.0+162


environment:
  sdk: '>=3.3.0 <4.0.0'
 

dependencies:
  flutter:
    sdk: flutter
  camera: 
  dwds: ^24.1.0
  dio: ^5.3.3
  path_provider:
  path:
  image_picker: ^1.1.2
  file_picker: ^8.0.3
  json_annotation: ^4.8.1
  qr_code_scanner: ^1.0.1
  provider: ^6.0.2
  get: ^4.6.6 
  cupertino_icons: ^1.0.0
  flutter_secure_storage: ^9.2.2
  local_auth: ^2.3.0
  shared_preferences: ^2.0.10
  sqflite: ^2.0.2
  hive: ^2.2.3
  http: ^1.2.2
  url_launcher: ^6.1.11
  flutter_staggered_grid_view: ^0.7.0
  external_path: ^2.0.1
  flutter_html: 
  webview_flutter: ^4.13.0
  flutter_web_auth: ^0.6.0
  flutter_inappwebview: ^6.1.5
  open_file: ^3.2.1
  flutter_downloader: ^1.11.7
  webview_cookie_manager: ^2.0.6
  geolocator: ^13.0.2
  geocoding: ^3.0.0
  google_maps_flutter: ^2.7.0
  map_launcher: ^3.5.0
  auto_size_text:
  carousel_slider:
  multi_select_flutter: ^4.0.0  
  intl: ^0.19.0
  image_painter:
  clippy_flutter: ^2.0.0-nullsafety.1
  flutter_mailer: ^3.0.0
  timezone: ^0.10.0
  badges: 
  #flutter_email_sender: ^5.1.0
  universal_html: ^2.2.3
  xml: any
  path_drawing: ^1.0.1
  flutter_svg: ^2.0.9
  svg_path_parser: ^1.1.2
  touchable: ^1.0.2
  collection: ^1.17.1
  cached_network_image: ^3.3.1
  toggle_switch: ^2.3.0
  html_editor_enhanced: ^2.6.0
  permission_handler: ^12.0.1

dev_dependencies:
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.3
  plugin_platform_interface: '>=2.0.0'
  flutter_lints: ^4.0.0

  
flutter_icons:
  android: true
  ios: true
  image_path: "lib/icons/appicon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
     - lib/icons/locate.png
     - lib/icons/tango-logo.png
     - lib/icons/map.png
     - lib/icons/project.png
     - lib/icons/approvals.png
     - lib/icons/transactions.png
     - lib/icons/facilities.png
     - lib/icons/lease.png
     - lib/icons/no_image_icon.gif
     - lib/icons/tangologo.png
     - lib/icons/talogo_new.png
     - lib/icons/way_finding.png
     - lib/icons/new_MAC_requests.png
     - lib/icons/classic_search.png
     - lib/icons/reservations.png
     - lib/icons/bidding.png
     - lib/icons/suppliernew.png
     - lib/icons/dashboard.png
     - lib/icons/programs.png
     - lib/icons/cadviewer.png
     - lib/icons/plan-icon.png
     - lib/icons/cancel.png
     - lib/icons/projector.png
     - lib/icons/standup.png
     - lib/icons/ta-platform.png
     - lib/icons/servicereq.png
     - lib/icons/site.png
     - lib/icons/target.png
     - lib/icons/survey.png
     - lib/icons/locations.png
     - lib/icons/find.png
     - lib/icons/locate_loc.png
     - lib/icons/aq_reserve.png
     - lib/icons/aq_reserve_add.png
     - lib/icons/aq_tango_rsrv.jpg
     - lib/icons/project/create_project.png
     - lib/icons/project/confidential.png
     - lib/icons/project/high_profile_projects.png
     - lib/icons/project/suppliers.png
     - lib/icons/test/test.svg
     - lib/icons/half_selected_star.svg
     - lib/icons/less_half_selected.svg
     - lib/icons/more_half_selected.svg
     - lib/icons/star_selected.svg
     - lib/icons/star_unselected.svg 

     

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
